/*
 * COMBINED FILES OUTPUT
 * Generated: 2025-08-27T10:55:26.219Z
 * Source: /Users/<USER>/Desktop/Workspace/TapTap/taptap-web-zalo/taptap-mobile/src/scenes/myrewardV3/screens/MyRewardDetail
 * Total files: 12
 * Total size: 58.29 KB
 */

/* TABLE OF CONTENTS */
 * 1. index.tsx
 * 2. SectionAutoRedeem/ContentBottomWarning.tsx
 * 3. SectionAutoRedeem/index.tsx
 * 4. SectionAutoRedeem/SectionCountDownReward.tsx
 * 5. SectionAutoRedeem/SectionWarningUseVoucher.tsx
 * 6. SectionLink711.tsx
 * 7. SectionMarkUsed.tsx
 * 8. SectionSurveyForm.tsx
 * 9. SectionTimerReward.tsx
 * 10. SectionVoucherTransfer.tsx
 * 11. styles.ts
 * 12. Tutorial.tsx

/* FILES CONTENT */
================================================================================
FILE 1/12: index.tsx
Size: 14.85 KB | Modified: 2025-08-26T09:03:38.132Z
================================================================================
import React, { useEffect, useState, FC, useRef } from 'react';
import { View } from 'react-native';
import { l10n } from 'languages';
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import { resWidth } from 'utils/Screen';
import { RouteProp } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';

/** Components */
import { Container } from 'components/Container';
import DetailHeader from 'components/Card/MyReward/DetailHeader';
import BackgroundAnimation from '../../components/BackgroundAnimation';
import HeaderAnimation from '../../components/HeaderAnimation';
import CircleButton from 'components/CircleButtonV3';
import { Icon18px, Icon20px, Icon24px } from 'components/Icon/Icon';
import { ButtonSecondary, ButtonTertiary } from 'components/Button';
import { ImageGallery } from 'components/Image';
import {
  goBack,
  navigateScreen,
} from 'navigation/action/RootNavigation';
import MerchantInfo from 'scenes/rewardV3/components/MerchantInfo';
import StoreNearbyV2 from 'scenes/store/components/StoreNearbyV2';
import EventConstantsV3 from 'manager/trackingManager/EventConstantsV3';
import {
  BottomPanel,
  BottomPanelRef,
  Section,
} from 'components/Navigation';
import BasedText from 'components/BasedText';
import StatusOnScreen, {
  IErrorStatus,
} from 'components/Layouts/StatusOnScreen';
import SectionSurveyForm from './SectionSurveyForm';
import SectionLink711 from './SectionLink711';
import SectionMarkUsed from 'scenes/myrewardV3/screens/MyRewardDetail/SectionMarkUsed';
import SectionAutoRedeem from './SectionAutoRedeem';
import SectionVoucherTransfer from './SectionVoucherTransfer';
import SectionTimerReward from './SectionTimerReward';
import SectionVendorInfo from 'scenes/rewardV3/components/SectionVendorInfo';
import Spacing from 'components/Spacing';
import Tutorial, { TutorialRef } from './Tutorial';
import TutorialElement from 'components/TutorialV4/TutorialElement';

// Utils & Constants & style
import TrackingManagerV3 from 'manager/trackingManager/TrackingManagerV3';
import { AppStackParamList } from 'navigation/navigators/types';
import { BRAND_7_ELEVEN } from 'scenes/merchant/helpers/constants';
import Logger from 'utils/Logger';
import styles from './styles';
import { COLORS, LAYOUTS, SPACING, TYPOGRAPHY } from 'styleGuide';
import { ESurveyType } from 'scenes/myrewardV3/helpers/constants';
import { HIGHLIGHT_ID } from 'components/TutorialV4/constants';
import { BUTTON_HEIGHT } from 'styleGuide/sizes';

// Action & redux & selector & slice & type
import { EVoucherStatus, MerchantType } from 'constant/CommonTypes';
import { useSelector } from 'react-redux';
import MerchantSelectors from 'scenes/merchant/redux/selectors';
import merchantApi from 'scenes/merchant/redux/api';
import useFetchMyRewardDetail from 'scenes/myrewardV3/helpers/useFetchMyRewardDetail';
import { OwnershipStatus } from 'scenes/voucherTransfer/models';
import { get } from 'lodash';
import Platform from 'utils/Platform';
import HtmlReader from 'components/HtmlReader';

interface Props {
  route: RouteProp<AppStackParamList, 'myReward_detail'>;
}

const MyRewardDetail: FC<Props> = ({ route }) => {
  const { qrCodeText: qrCode711 } = useSelector(
    MerchantSelectors.selectLinked7eleven,
  );

  const { isUnread, codeId, source } = route.params;

  const {
    detail,
    loading,
    resetMyRewardData,
    setDetail,
    updateMarkAdUsed,
  } = useFetchMyRewardDetail({
    codeId,
    isUnread,
    hasFetchUserConsent: qrCode711.length === 0,
    source,
  });
  const {
    merchantCode = '',
    merchantName = '',
    merchantLogo = '',
    name = '',
    endTime,
    code = '',
    tnc = '',
    startTime,
    redeemBrands = [],
    redeemPerUser = 0,
    remainRedeemCount = 0,
    posIdentifier,
    status,
    markAsUsed,
    enableMarkAsUsed,
    autoRedeem,
    timeRedeemAuto,
    activeAutoRedeemDate,
    executeAutoRedeemDate,
    ownershipStatus = OwnershipStatus.OWNED,
    avatarImage,
    // Time
    settingRedeemData,
  } = detail || {};

  const positionY = useSharedValue(0);
  const bottomPanelRef = useRef<BottomPanelRef>(null);
  const tutorialRef = useRef<TutorialRef>(null);

  const [merchantInfo, setMerchantInfo] = useState<MerchantType>();
  const backgroundColor = detail?.bgColor || COLORS.primaryYellow;
  const imageList = merchantInfo?.images || [];
  const paramsTracking = {
    merchant_code: merchantCode,
    reward_name: detail?.name || '',
  };

  const scrollHander = useAnimatedScrollHandler(event => {
    // @ts-ignore
    positionY.value = event?.contentOffset.y;
  });
  const fetchMerchant = async () => {
    try {
      const result = await merchantApi.getMerchantByCode(
        merchantCode,
      );
      if (result.status.code === 200 && result.data) {
        setMerchantInfo(result.data);
      }
    } catch (e) {
      Logger.log('fetch merchant detail error', e);
    }
  };

  useEffect(() => {
    if (merchantCode && merchantCode !== 'TAPTAP') fetchMerchant();
  }, [merchantCode]);

  useEffect(() => {
    if (detail?.allowTransfer && status === EVoucherStatus.ACTIVE) {
      bottomPanelRef.current?.show();
    }
  }, [detail]);

  const onPressMemberCode = () => {
    TrackingManagerV3.getInstance()
      ?.trackMyRewardDetail()
      .mrDetailBarcode(paramsTracking);
    navigateScreen('barcode');
  };

  const onGoImageGallery = (index: number) => {
    navigateScreen('image', {
      images: imageList,
      selectedImage: index,
    });
  };

  const onPressViewAllImage = () => {
    TrackingManagerV3.getInstance()
      ?.trackComponent()
      .imageGallery(
        EventConstantsV3.MY_REWARD_DETAIL.MR_DETAIL_IMAGE_VIEW_ALL,
        paramsTracking,
      );
    onGoImageGallery(0);
  };

  const onGoViewImage = (index: number) => {
    TrackingManagerV3.getInstance()
      ?.trackComponent()
      .imageGallery(
        EventConstantsV3.MY_REWARD_DETAIL.MR_DETAIL_IMAGE_THUMBNAIL,
        paramsTracking,
      );
    onGoImageGallery(index);
  };

  const onGoVoucherTransfer = () => {
    TrackingManagerV3.getInstance()?.trackingTransfer().btnTransfer({
      merchant_code: merchantCode,
      reward_name: detail?.name,
    });
    navigateScreen('voucherTransferStack', {
      voucherInfo: detail,
    });
  };

  const transformPostID = (string?: string) => {
    const arr = string?.split?.(',');
    return {
      type: String(arr?.[0] || '')?.trim(),
      link: String(arr?.[1] || '')?.trim(),
    };
  };

  const is711Consent = React.useMemo(() => {
    const linked7Eleven = qrCode711 && qrCode711?.length > 0;
    return (
      redeemBrands &&
      redeemBrands.length === 1 &&
      redeemBrands[0] === BRAND_7_ELEVEN &&
      !linked7Eleven
    );
  }, [redeemBrands, qrCode711]);

  const expiryData = {
    redeemPerUser,
    remainRedeemCount,
    startTime,
    endTime,
    // Time
    settingRedeemData,
  };

  const renderMiddleTopSection = React.useMemo(() => {
    if (autoRedeem && activeAutoRedeemDate) {
      return <></>;
    }
    return <SectionTimerReward expiryData={expiryData} />;
  }, [detail]);

  const renderMiddleBottomSection = React.useMemo(() => {
    const { link, type } = transformPostID(posIdentifier);
    if (
      type === ESurveyType.MERCHANDISE ||
      type === ESurveyType.FORM_ACCESS_TRADE
    ) {
      return (
        <SectionSurveyForm
          code={code}
          type={type}
          link={link}
          merchantName={merchantName}
          resetMyRewardData={resetMyRewardData}
          submitted={status === EVoucherStatus.DONE}
          endTime={endTime}
          merchantCode={merchantCode}
          rewardName={name}
        />
      );
    }

    /** condition only is 771 campaign and not yet linked */
    if (is711Consent) {
      return (
        <SectionLink711
          merchantCode={merchantCode}
          rewardName={detail?.name || ''}
        />
      );
    }
    if (autoRedeem) {
      return (
        <SectionAutoRedeem
          timeRedeemAuto={timeRedeemAuto || 0}
          codeId={codeId}
          code={code}
          status={status}
          executeAutoRedeemDate={executeAutoRedeemDate}
          setDetail={setDetail}
          paramsTracking={paramsTracking}
          endTime={endTime}
          rewardName={name}
        />
      );
    }
    return undefined;
  }, [
    status,
    is711Consent,
    autoRedeem,
    activeAutoRedeemDate,
    timeRedeemAuto,
    endTime,
  ]);

  const renderHeader = () => {
    return (
      <>
        <View>
          <BackgroundAnimation
            {...{
              positionY,
              backgroundHeader: backgroundColor,
            }}
          />

          <View style={styles.header}>
            <CircleButton hasShadow onPress={goBack}>
              <Icon24px.OutlineBack />
            </CircleButton>
            <ButtonTertiary
              content={l10n.member_code}
              onPress={onPressMemberCode}
              type="tiny"
              containerStyle={{ width: resWidth(109) }}
            />
          </View>
        </View>

        <View style={styles.cardVoucherDetail}>
          <DetailHeader
            merchantName={merchantName}
            merchantLogo={merchantLogo}
            merchantCode={merchantCode}
            merchant={merchantInfo}
            voucherName={name}
            code={code}
            renderMiddleTopSection={renderMiddleTopSection}
            renderMiddleBottonSection={renderMiddleBottomSection}
            autoRedeem={autoRedeem}
            ownershipStatus={ownershipStatus}
          />
        </View>
        <StoreNearbyV2
          merchantCode={merchantCode}
          logo={merchantLogo}
          couponCodeId={codeId}
          event={
            EventConstantsV3.MY_REWARD_DETAIL.MR_DETAIL_OFFLINE_STORE
          }
        />
      </>
    );
  };

  const renderContent = () => {
    return (
      <View style={styles.content}>
        {detail && source === 'THIRD_PARTY' && (
          <SectionVendorInfo data={detail} style={styles.vendor} />
        )}
        <SectionMarkUsed
          disabled={ownershipStatus === OwnershipStatus.TRANSFERRED}
          enableMarkAsUsed={enableMarkAsUsed}
          markAsUsed={markAsUsed}
          updateMarkAdUsed={updateMarkAdUsed}
          status={status}
          endTime={endTime}
          merchantCode={merchantCode}
          rewardName={detail?.name || ''}
        />
        <Section
          title={l10n.reward.detail.useGuide}
          backgroundColor={COLORS.primaryWhite}>
          {tnc !== '' ? (
            <HtmlReader content={tnc || '<p/>'} />
          ) : (
            <BasedText>{l10n.errors.updating}</BasedText>
          )}
        </Section>

        {merchantInfo && (
          <MerchantInfo
            description={merchantInfo?.description || ''}
            contact={merchantInfo?.contact}
            merchantCode={merchantInfo?.code || ''}
            event={
              EventConstantsV3.MY_REWARD_DETAIL.MR_DETAIL_CONTACT_INFO
            }
            paramsTracking={paramsTracking}
          />
        )}

        {imageList.length > 0 && (
          <ImageGallery
            imageList={imageList}
            onPressViewAll={onPressViewAllImage}
            onPressViewItem={onGoViewImage}
          />
        )}

        {detail?.allowTransfer && <Spacing height={80} />}
      </View>
    );
  };

  if (!detail)
    return (
      <View style={styles.loadingContainer}>
        <StatusOnScreen
          type={
            loading
              ? IErrorStatus.loading
              : IErrorStatus.loading_error
          }
        />
        <LinearGradient
          colors={[COLORS.grey3, COLORS.grey4]}
          style={styles.loadingHeader}>
          <CircleButton hasShadow onPress={goBack}>
            <Icon24px.OutlineBack />
          </CircleButton>
        </LinearGradient>
      </View>
    );

  const renderBottom = () => {
    const isMaxTransfer =
      typeof detail.remainTransferDailyPerUser === 'number' &&
      detail.remainTransferDailyPerUser <= 0;

    const onDidShowBottomPanel = () => {
      tutorialRef.current?.showTutorial();
    };

    const bottomPanelHeight =
      BUTTON_HEIGHT +
      (Platform.isIos ? SPACING.s_12 : SPACING.l_24) +
      SPACING.m_16;

    return (
      <>
        <TutorialElement
          id={HIGHLIGHT_ID.btn_transfer_reward}
          options={{
            mode: 'rectangle',
            padding: bottomPanelHeight,
            offset: { x: 0, y: 0 },
          }}
        />
        <BottomPanel
          ref={bottomPanelRef}
          onFinished={onDidShowBottomPanel}>
          {isMaxTransfer && (
            <View style={[LAYOUTS.rowCenter, styles.maxTransfer]}>
              <Icon20px.OutlineLimitedVoucher />
              <BasedText
                style={TYPOGRAPHY.statusRegular}
                color={COLORS.red}>
                {l10n.myReward.maxTransfer.content_1}
                <BasedText
                  fontWeight="bold"
                  style={TYPOGRAPHY.statusRegular}
                  color={COLORS.red}>
                  {detail.maxTransferDailyPerUser}
                </BasedText>
                {l10n.myReward.maxTransfer.content_2}
              </BasedText>
            </View>
          )}
          <ButtonSecondary
            type="large"
            disabled={isMaxTransfer}
            content={l10n.voucherTransfer.giveToFriend}
            onPress={onGoVoucherTransfer}
            renderLeftIcon={() => <Icon18px.GiftTransferIcon />}
            containerStyle={styles.giftTransferButton}
          />
        </BottomPanel>
      </>
    );
  };

  const renderVoucherTransferInfo = () => {
    if (ownershipStatus === OwnershipStatus.OWNED) return null;

    const phoneNumber = get(
      detail,
      ownershipStatus === OwnershipStatus.RECEIVED
        ? 'giverPhoneNumber'
        : 'receiverPhoneNumber',
      '',
    );
    const transferName = get(
      detail,
      ownershipStatus === OwnershipStatus.RECEIVED
        ? 'giverName'
        : 'receiverName',
      '',
    );

    return (
      <SectionVoucherTransfer
        ownershipStatus={ownershipStatus}
        phoneNumber={phoneNumber}
        transferName={transferName}
        avatarImage={avatarImage}
        receivedDate={detail.receivedDate}
      />
    );
  };

  return (
    <Container edges={['bottom']} style={styles.container}>
      <Animated.ScrollView
        onScroll={scrollHander}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={1}
        contentContainerStyle={styles.scrollView}>
        {renderHeader()}

        {detail && (
          <View style={styles.sectionWrapper}>
            {renderVoucherTransferInfo()}
            {renderContent()}
          </View>
        )}
      </Animated.ScrollView>
      <HeaderAnimation
        {...{
          positionY,
          backgroundColor,
          onPress: onPressMemberCode,
        }}
      />

      {detail?.allowTransfer && <Tutorial ref={tutorialRef} />}
      {renderBottom()}
    </Container>
  );
};

export default MyRewardDetail;


================================================================================
FILE 2/12: SectionAutoRedeem/ContentBottomWarning.tsx
Size: 4.55 KB | Modified: 2025-08-26T09:03:38.131Z
================================================================================
import BasedText from 'components/BasedText';
import { ButtonPrimary } from 'components/Button';
import LineBreak from 'components/LineBreak';
import Mascot from 'components/Mascot';
import Plain from 'components/Plain';
import { MyRewardType } from 'constant/CommonTypes';
import { l10n } from 'languages';
import TrackingManagerV3 from 'manager/trackingManager/TrackingManagerV3';
import React, { FC, useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAutoRedeemVoucher } from 'scenes/myrewardV3/helpers/useAutoRedeemVoucher';
import { hideModal } from 'services/globalModalV2/modalHandler';
import { COLORS, SPACING, TYPOGRAPHY } from 'styleGuide';
import { padding } from 'styleGuide/mixins';
import Platform from 'utils/Platform';
import { resWidth } from 'utils/Screen';

const styles = StyleSheet.create({
  line: {
    marginTop: SPACING.l_32,
    marginBottom: SPACING.l_24,
    borderBottomColor: COLORS.grey3,
    opacity: 1,
  },
  warningContainer: {
    ...padding(SPACING.l_24, SPACING.m_16, 0, SPACING.m_16),
  },
  mascotContainer: {
    alignItems: 'center',
  },
  mascot: {
    width: resWidth(120),
    height: resWidth(86),
  },
  confirmContainer: {
    marginTop: SPACING.s_12,
    marginBottom: SPACING.l_24,
  },
  row: {
    flexDirection: 'row',
  },
});
type IPropsContentBottom = {
  time: number;
  codeId: string;
  setRewardDetail: (reward: MyRewardType) => void;
  paramsTracking: {
    merchant_code: string;
    reward_name: string;
  };
};
const ContentBottomWarning: FC<IPropsContentBottom> = ({
  time,
  codeId,
  setRewardDetail,
  paramsTracking,
}): JSX.Element => {
  const { bottom } = useSafeAreaInsets();
  const marginBottom =
    bottom + (Platform.isAndroid ? SPACING.m_16 : 0);
  const onHideWarning = () => {
    hideModal();
    TrackingManagerV3.getInstance()
      ?.trackMyRewardDetail()
      .mrDetailAutoRedeemConfirm({
        ...paramsTracking,
        action: 'later',
      });
  };

  const contentWarning = [
    l10n.myReward.autoRedeem.warningContentFrist,
    l10n.myReward.autoRedeem.warningContentSecond,
  ];

  const { loading, myReward, onConfirmRedeemVoucher } =
    useAutoRedeemVoucher(codeId);

  const onConfirm = async () => {
    TrackingManagerV3.getInstance()
      ?.trackMyRewardDetail()
      .mrDetailAutoRedeemConfirm({
        ...paramsTracking,
        action: 'confirm',
      });
    await onConfirmRedeemVoucher();
    hideModal();
  };

  useEffect(() => {
    if (myReward) {
      hideModal();
      setRewardDetail(myReward);
    }
  }, [myReward]);

  return (
    <View style={[styles.warningContainer, { marginBottom }]}>
      <BasedText style={TYPOGRAPHY.header2Bold} textAlign="center">
        {l10n.myReward.autoRedeem.warningTitle}
      </BasedText>
      <LineBreak style={styles.line} />
      <View style={styles.mascotContainer}>
        <Mascot name="warning" imageStyle={styles.mascot} />
      </View>
      <BasedText style={TYPOGRAPHY.bodyDemi}>
        {l10n.myReward.autoRedeem.warningNote}
      </BasedText>
      {contentWarning.map((e, index) => {
        let content = e;
        if (index === 1) {
          content = l10n.formatString(e, {
            contenTime: (
              <BasedText
                color={COLORS.red}
                style={TYPOGRAPHY.bodyDemi}>
                {l10n.formatString(
                  l10n.myReward.autoRedeem.contenTime,
                  {
                    time,
                  },
                )}
              </BasedText>
            ),
            notRestore: (
              <BasedText
                color={COLORS.red}
                style={TYPOGRAPHY.bodyDemi}>
                {l10n.myReward.autoRedeem.notRestore}
              </BasedText>
            ),
          }) as string;
        }
        return (
          <View key={index} style={styles.row}>
            <BasedText style={TYPOGRAPHY.bodyRegular}>{`${
              index + 1
            }. `}</BasedText>
            <BasedText style={TYPOGRAPHY.bodyRegular}>
              {content}
            </BasedText>
          </View>
        );
      })}

      <ButtonPrimary
        type="large"
        testID="id_btn_confirm"
        isLoading={loading}
        containerStyle={styles.confirmContainer}
        content={l10n.myReward.autoRedeem.confirm}
        onPress={onConfirm}
      />
      <Plain
        disabled={loading}
        testID="id_btn_hide_warning"
        text={l10n.myReward.autoRedeem.later}
        onPress={onHideWarning}
      />
    </View>
  );
};
export default ContentBottomWarning;


================================================================================
FILE 3/12: SectionAutoRedeem/index.tsx
Size: 1.18 KB | Modified: 2025-08-26T09:03:38.131Z
================================================================================
import React, { FC } from 'react';
import { EVoucherStatus, MyRewardType } from 'constant/CommonTypes';

import SectionWarningUseVoucher from './SectionWarningUseVoucher';
import SectionCountDownReward from './SectionCountDownReward';

type IProps = {
  timeRedeemAuto: number;
  codeId: string;
  code: string;
  executeAutoRedeemDate: string | null | undefined;
  setDetail: (val: MyRewardType) => void;
  paramsTracking: {
    merchant_code: string;
    reward_name: string;
  };
  status: EVoucherStatus | undefined;
  endTime: string | undefined;
  rewardName: string;
};
const SectionAutoRedeem: FC<IProps> = ({
  timeRedeemAuto,
  codeId,
  code,
  executeAutoRedeemDate,
  paramsTracking,
  setDetail,
  status,
  endTime,
  rewardName,
}) => {
  return executeAutoRedeemDate ? (
    <SectionCountDownReward
      codeId={codeId}
      code={code}
      status={status}
      executeAutoRedeemDate={executeAutoRedeemDate}
      rewardName={rewardName}
    />
  ) : (
    <SectionWarningUseVoucher
      timeRedeemAuto={timeRedeemAuto}
      codeId={codeId}
      setRewardDetail={setDetail}
      paramsTracking={paramsTracking}
      endTime={endTime}
    />
  );
};
export default SectionAutoRedeem;


================================================================================
FILE 4/12: SectionAutoRedeem/SectionCountDownReward.tsx
Size: 6.99 KB | Modified: 2025-08-26T09:03:38.131Z
================================================================================
import React, { FC, useCallback, useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { l10n } from 'languages';
/**
 * component
 */
import Barcode from 'components/BarcodeV3';
import BasedText from 'components/BasedText';
import { Icon20px } from 'components/Icon/Icon';
/**
 * style & utils
 */
import { COLORS, SPACING, TYPOGRAPHY } from 'styleGuide';
import { hexToRGB, padding } from 'styleGuide/mixins';
import { useCountdown } from 'utils/hooks';
import { perWidth, resWidth } from 'utils/Screen';
import { clockify, copyToClipboard } from 'utils/Utility';
import moment from 'moment';
import LineSvg from 'components/LineBreak/LineSvg';
import useFetchMyActiveRewardList from 'scenes/myrewardV3/helpers/useFetchMyActiveRewardList';
import { useAppSelector } from 'storeConfig/hook';
import SocketSelector from 'services/socketServices/selectors';
import { EVoucherStatus } from 'constant/CommonTypes';
import { ButtonSecondary } from 'components/Button';
import SpecialToast, {
  SpecialToastRef,
} from 'components/Toast/SpecialToast';
import TrackingManagerV3 from 'manager/trackingManager/TrackingManagerV3';

type IPropsCountDown = {
  executeAutoRedeemDate: string;
  codeId: string;
  code: string;
  status: EVoucherStatus | undefined;
  rewardName: string;
};
const HEADER_WIDTH = perWidth(100) - SPACING.m_16 * 2;
const MAXIMUM_CODE_LENGTH = 20;
const MAXIMUM_BARCODE_WIDTH = resWidth(1.8);
const styles = StyleSheet.create({
  contentContainer: {
    paddingVertical: SPACING.s_12,
    marginTop: -SPACING.m_16, // update again for reward default using style middleTopSection
    backgroundColor: hexToRGB(COLORS.orange, 0.15),
    borderRadius: SPACING.s_6,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  iconContainer: {
    width: SPACING.l_24,
    height: SPACING.l_24,
    borderRadius: SPACING.l_24,
    backgroundColor: COLORS.orange,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.s_8,
  },
  valueBarcode: {
    ...TYPOGRAPHY.header3Uppercase,
    marginTop: SPACING.s_6,
    textTransform: 'none',
  },
  barcodeContainer: {
    paddingVertical: 0,
  },
  contentStyle: {
    paddingVertical: SPACING.s_12,
    ...TYPOGRAPHY.statusRegular,
    textAlign: 'center',
  },
  countDownStyle: {
    paddingRight: SPACING.s_6,
    paddingLeft: SPACING.s_4,
    ...TYPOGRAPHY.statusDemi,
  },
  expireContainer: {
    paddingVertical: resWidth(52),
    width: HEADER_WIDTH,
  },
  expireContentContainer: {
    height: SPACING.l_48,
    marginHorizontal: SPACING.l_24,
    alignItems: 'center',
    backgroundColor: COLORS.bgGreen003,
    borderRadius: SPACING.s_8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  iconCheck: {
    backgroundColor: COLORS.green,
    borderRadius: resWidth(10),
    overflow: 'hidden',
    marginRight: SPACING.s_8,
    width: resWidth(24),
    height: resWidth(24),
    alignItems: 'center',
  },
  btnCopy: {
    marginTop: SPACING.m_16,
    marginBottom: SPACING.s_12,
    alignSelf: 'center',
  },
  expireContent: {
    ...TYPOGRAPHY.statusRegular,
    textAlign: 'center',
    ...padding(SPACING.s_12, SPACING.l_24, 0, SPACING.l_24),
  },
});
const SectionCountDownReward: FC<IPropsCountDown> = ({
  executeAutoRedeemDate,
  codeId,
  status,
  code,
  rewardName,
}) => {
  const refToast = React.useRef<SpecialToastRef>(null);

  const isExpire = moment(new Date()).isAfter(executeAutoRedeemDate);
  const statusDataVoucher = useAppSelector(
    SocketSelector.getStatusVoucherAutoRedeem,
  );
  let statusVoucher = status;
  if (statusDataVoucher?.codeId === codeId) {
    statusVoucher = statusDataVoucher.status;
  }

  const isExpireOrUsed =
    statusVoucher === EVoucherStatus.DONE || isExpire;
  const { onRefresh } = useFetchMyActiveRewardList();
  const [isUsed, setIsUsed] = useState<boolean>(isExpireOrUsed);

  const paramsTracking = {
    reward_name: rewardName,
    merchant_code: code || 'TAPTAP',
  };

  const onPressCopy = () => {
    TrackingManagerV3.getInstance()
      ?.trackMyRewardDetail()
      .mrDetailCopyCode(paramsTracking);
    copyToClipboard(code);
    refToast?.current?.runToast();
  };

  const onReadyUsed = () => {
    setIsUsed && setIsUsed(true);
  };
  const { secondsLeft: secondsRemaining, startCountdown } =
    useCountdown(onReadyUsed);

  useEffect(() => {
    startCountdown(executeAutoRedeemDate);
  }, []);

  useEffect(() => {
    if (isUsed) {
      onRefresh();
    }
  }, [isUsed]);

  const barcodeWidthRatio =
    MAXIMUM_CODE_LENGTH / code?.length > MAXIMUM_BARCODE_WIDTH
      ? MAXIMUM_BARCODE_WIDTH
      : MAXIMUM_CODE_LENGTH / code?.length;

  const { displayHours, displayMinutes, displaySecs } =
    clockify(secondsRemaining);

  const onBlockCopyCode = useCallback(() => {
    return (
      <>
        <ButtonSecondary
          type="tiny"
          content={l10n.copyCode}
          containerStyle={styles.btnCopy}
          onPress={onPressCopy}
        />

        <SpecialToast
          ref={refToast}
          position="bottom"
          type="SUCCESS"
          message={l10n.copiedCode}
          dynamicBottomPosition={SPACING.xl_72}
        />
      </>
    );
  }, []);

  const onBlockCountDown = useCallback(
    () => (
      <View style={styles.contentContainer}>
        <View style={styles.iconContainer}>
          <Icon20px.OutlineExclamationMark fill={COLORS.white} />
        </View>
        <BasedText style={styles.countDownStyle}>
          {l10n.myReward.autoRedeem.timeUsing}
        </BasedText>
        <BasedText style={TYPOGRAPHY.bodyBold} color={COLORS.orange}>
          {displayHours}:{displayMinutes}:{displaySecs}
        </BasedText>
      </View>
    ),
    [displayHours, displayMinutes, displaySecs],
  );

  const onBlockContent = useCallback(
    () => (
      <BasedText style={styles.contentStyle} textAlign="center">
        {l10n.myReward.autoRedeem.contentHasRedeem}
      </BasedText>
    ),
    [],
  );

  const onBlockQRCode = () => (
    <Barcode
      value={code}
      barWidth={barcodeWidthRatio}
      barHeight={resWidth(60)}
      valueStyle={styles.valueBarcode}
      containerStyle={styles.barcodeContainer}
    />
  );

  if (isUsed || statusVoucher === EVoucherStatus.DONE) {
    return (
      <>
        <LineSvg
          x1={0}
          x2={HEADER_WIDTH - SPACING.s_12}
          width="100%"
        />
        <View style={styles.expireContainer}>
          <View style={styles.expireContentContainer}>
            <Icon20px.OutlineCheckMark
              style={styles.iconCheck}
              fill={COLORS.white}
            />
            <BasedText style={TYPOGRAPHY.statusDemi}>
              {l10n.myReward.autoRedeem.used}
            </BasedText>
          </View>
          <BasedText style={styles.expireContent}>
            {l10n.myReward.autoRedeem.contentHasExpired}
          </BasedText>
        </View>
      </>
    );
  }

  return (
    <View>
      {onBlockCountDown()}
      {onBlockContent()}
      {onBlockQRCode()}
      {onBlockCopyCode()}
    </View>
  );
};

export default SectionCountDownReward;


================================================================================
FILE 5/12: SectionAutoRedeem/SectionWarningUseVoucher.tsx
Size: 3.6 KB | Modified: 2025-08-26T09:03:38.131Z
================================================================================
import React, { FC, useCallback } from 'react';
import { StyleSheet, View } from 'react-native';
import { l10n } from 'languages';
/**
 * component
 */
import BasedText from 'components/BasedText';
import { ButtonPrimary } from 'components/Button';
import { Icon20px } from 'components/Icon/Icon';
import { MyRewardType } from 'constant/CommonTypes';
import ContentBottomWarning from './ContentBottomWarning';
import { showBottomModalV3 } from 'services/globalModalV2/modalHandler';
/**
 * style & utils
 */
import { COLORS, SPACING, TYPOGRAPHY } from 'styleGuide';
import { hexToRGB, padding } from 'styleGuide/mixins';
import { resWidth } from 'utils/Screen';
import TrackingManager from 'manager/trackingManager/TrackingManagerV3';
import moment from 'moment';

const styles = StyleSheet.create({
  container: {
    paddingTop: SPACING.s_12,
    paddingBottom: SPACING.m_16,
    borderRadius: SPACING.s_6,
    alignItems: 'center',
    flexDirection: 'row',
  },
  contentContainer: {
    ...padding(resWidth(5), SPACING.s_8, resWidth(5), SPACING.s_12),
    backgroundColor: hexToRGB(COLORS.orange, 0.15),
    flexDirection: 'row',
    borderRadius: 6,
  },
  iconContainer: {
    width: SPACING.l_24,
    height: SPACING.l_24,
    borderRadius: SPACING.l_24,
    marginRight: 8,
    alignSelf: 'center',
    backgroundColor: COLORS.orange,
    alignItems: 'center',
    justifyContent: 'center',
  },
  flex1: { flex: 1 },
});

type IProps = {
  timeRedeemAuto: number;
  codeId: string;
  setRewardDetail: (reward: MyRewardType) => void;
  paramsTracking: {
    merchant_code: string;
    reward_name: string;
  };
  endTime: string | undefined;
};

const SectionWarningUseVoucher: FC<IProps> = ({
  timeRedeemAuto,
  codeId,
  paramsTracking,
  endTime,
  setRewardDetail,
}) => {
  const isExpire = moment(new Date()).isAfter(endTime);
  const onContent = () => (
    <ContentBottomWarning
      time={timeRedeemAuto}
      codeId={codeId}
      setRewardDetail={setRewardDetail}
      paramsTracking={paramsTracking}
    />
  );
  const showBottomWarning = useCallback(() => {
    TrackingManager.getInstance()
      ?.trackMyRewardDetail()
      .mrDetailAutoRedeem(paramsTracking);
    showBottomModalV3({
      content: onContent,
    });
  }, []);

  const onRenderVoucherConfirm = useCallback(
    () => (
      <>
        <BasedText style={TYPOGRAPHY.bodyRegular}>
          {l10n.myReward.autoRedeem.title}
        </BasedText>
        <View style={styles.container}>
          <View style={styles.contentContainer}>
            <View style={styles.iconContainer}>
              <Icon20px.OutlineExclamationMark fill={COLORS.white} />
            </View>
            <View style={styles.flex1}>
              <BasedText>
                {l10n.formatString(l10n.myReward.autoRedeem.content, {
                  used: (
                    <BasedText style={TYPOGRAPHY.bodyDemi}>
                      {`"${l10n.myReward.autoRedeem.used}"`}
                    </BasedText>
                  ),
                  time: (
                    <BasedText style={TYPOGRAPHY.bodyDemi}>
                      {timeRedeemAuto}{' '}
                      {l10n.myReward.autoRedeem.minute}
                    </BasedText>
                  ),
                })}
              </BasedText>
            </View>
          </View>
        </View>
        <ButtonPrimary
          testID="id_btn_warning"
          disabled={isExpire}
          type="small"
          content={l10n.myReward.autoRedeem.useNow}
          onPress={showBottomWarning}
        />
      </>
    ),
    [timeRedeemAuto],
  );
  return onRenderVoucherConfirm();
};

export default SectionWarningUseVoucher;


================================================================================
FILE 6/12: SectionLink711.tsx
Size: 2.61 KB | Modified: 2025-08-26T09:03:38.131Z
================================================================================
import React, { useCallback } from 'react';
import { StyleSheet, ImageBackground } from 'react-native';
import { l10n } from 'languages';
/** Component */
import BasedText from 'components/BasedText';
import { ButtonPrimary } from 'components/Button';
import BottomSheetLink711 from 'scenes/merchant/screens/MerchantDetail/SeventEleven/BottomSheetLink711';
// Utils & Constants & style
import { SPACING, TYPOGRAPHY } from 'styleGuide';
import { resWidth } from 'utils/Screen';
import Images from 'utils/Images';
// redux
import { useAppDispatch } from 'storeConfig/hook';
import TrackingManagerV3 from 'manager/trackingManager/TrackingManagerV3';
import { merchantActions } from 'scenes/merchant/redux/slice';
import {
  hideModal,
  showBottomModalV3,
} from 'services/globalModalV2/modalHandler';

interface Props {
  merchantCode: string;
  rewardName: string;
}

const styles = StyleSheet.create({
  barcodeBlur: {
    height: resWidth(90),
    width: resWidth(300),
    marginTop: resWidth(1),
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: resWidth(35),
  },
  barcodeBlurText: {
    ...TYPOGRAPHY.bodyBold,
    textAlign: 'center',
  },
  bottomPanelBtn: {
    marginTop: SPACING.s_12,
  },
});

const SectionLink711: React.VFC<Props> = ({
  rewardName,
  merchantCode,
}) => {
  const dispatch = useAppDispatch();

  React.useEffect(() => {
    return () => {
      hideModal();
    };
  }, []);

  const paramsTracking = {
    merchant_code: merchantCode,
    reward_name: rewardName || '',
  };

  const hideBottomSheetLink711 = useCallback(() => {
    hideModal();
  }, []);

  const renderBottomSheet = useCallback(() => {
    return (
      <BottomSheetLink711 cancelConfirm={hideBottomSheetLink711} />
    );
  }, []);

  const openBottomSheetLink711 = useCallback(() => {
    TrackingManagerV3.getInstance()
      ?.trackMyRewardDetail()
      .mrDetailLinkAccount(paramsTracking);

    dispatch(merchantActions.resetUserConsentError());
    showBottomModalV3({
      content: renderBottomSheet,
      snapsBottom: [resWidth(445)],
    });
  }, []);

  return (
    <>
      <ImageBackground
        style={styles.barcodeBlur}
        source={Images.merchant.seventEleven.barcodeBlur}>
        <BasedText style={styles.barcodeBlurText}>
          {l10n.merchant.seventElevent.link711ShowRewardCode}
        </BasedText>
      </ImageBackground>
      <ButtonPrimary
        type="small"
        content={l10n.merchant.seventElevent.linkingNow}
        containerStyle={styles.bottomPanelBtn}
        onPress={openBottomSheetLink711}
      />
    </>
  );
};

export default React.memo(SectionLink711);


================================================================================
FILE 7/12: SectionMarkUsed.tsx
Size: 3.99 KB | Modified: 2025-08-26T09:03:38.131Z
================================================================================
import React, { useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { l10n } from 'languages';

// Components
import Switch from 'components/Switch';
import BasedText from 'components/BasedText';
import DialogConfirm from 'scenes/myrewardV3/components/DialogConfirm';

// Styles & utils
import { COLORS, SPACING, TYPOGRAPHY } from 'styleGuide';
import {
  hideModal,
  showDialogModal,
} from 'services/globalModalV2/modalHandler';
import { EVoucherStatus } from 'constant/CommonTypes';
import TrackingManagerV3 from 'manager/trackingManager/TrackingManagerV3';
import moment from 'moment';

const styles = StyleSheet.create({
  section: {
    paddingVertical: SPACING.s_12,
    paddingHorizontal: SPACING.m_16,
    backgroundColor: COLORS.primaryWhite,
    marginBottom: SPACING.s_8,

    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    ...TYPOGRAPHY.bodyDemi,
  },
});

interface IProps {
  enableMarkAsUsed?: boolean;
  status?: EVoucherStatus;
  markAsUsed?: boolean;
  endTime?: string;
  updateMarkAdUsed: (used: boolean) => void;
  merchantCode: string;
  rewardName: string;
  disabled?: boolean;
}

const SectionMarkUsed: React.VFC<IProps> = ({
  enableMarkAsUsed,
  status,
  markAsUsed,
  endTime,
  updateMarkAdUsed,
  merchantCode,
  rewardName,
  disabled,
}) => {
  if (
    !enableMarkAsUsed ||
    status === EVoucherStatus.DONE ||
    status === EVoucherStatus.EXPIRED ||
    moment().isAfter(endTime)
  )
    return null;

  const onOkDialog = () => {
    TrackingManagerV3.getInstance()
      ?.trackMyRewardDetail()
      .mrDetailDialogBtn({
        merchant_code: merchantCode,
        reward_name: rewardName,
        action: 'confirm',
        dialog_name: markAsUsed
          ? 'markAsUsed_turnOff'
          : 'markAsUsed_turnOn',
      });

    updateMarkAdUsed(!markAsUsed);
    hideModal();
  };

  const onReturnDialog = () => {
    TrackingManagerV3.getInstance()
      ?.trackMyRewardDetail()
      .mrDetailDialogBtn({
        merchant_code: merchantCode,
        reward_name: rewardName,
        action: 'return',
        dialog_name: markAsUsed
          ? 'markAsUsed_turnOff'
          : 'markAsUsed_turnOn',
      });

    hideModal();
  };

  const showDialogConfim = useCallback(() => {
    return (
      <DialogConfirm
        message={
          markAsUsed
            ? l10n.myReward.messageConfirmUsed
            : l10n.myReward.messageConfirmUnUsed
        }
        btnCancelTitle={l10n.back}
        btnOkTitle={l10n.homeV3.tutorial.ok}
        onCancel={onReturnDialog}
        onOk={onOkDialog}
      />
    );
  }, [markAsUsed]);

  const onSwitch = () => {
    showDialogModal({
      content: showDialogConfim,
    });

    if (markAsUsed) {
      TrackingManagerV3.getInstance()
        ?.trackMyRewardDetail()
        .mrDetailMarkAsUsedTurnOff({
          merchant_code: merchantCode,
          reward_name: rewardName,
        });
      TrackingManagerV3.getInstance()
        ?.trackMyRewardDetail()
        .mrDetailDialogView({
          merchant_code: merchantCode,
          reward_name: rewardName,
          dialog_name: 'markAsUsed_turnOff',
        });
    } else {
      TrackingManagerV3.getInstance()
        ?.trackMyRewardDetail()
        .mrDetailMarkAsUsedTurnOn({
          merchant_code: merchantCode,
          reward_name: rewardName,
        });
      TrackingManagerV3.getInstance()
        ?.trackMyRewardDetail()
        .mrDetailDialogView({
          merchant_code: merchantCode,
          reward_name: rewardName,
          dialog_name: 'markAsUsed_turnOn',
        });
    }
  };

  return (
    <View style={styles.section}>
      <BasedText style={styles.sectionTitle}>
        {l10n.myReward.markAsUsed}
      </BasedText>
      <Switch
        isOn={markAsUsed || false}
        disabled={disabled}
        onSwitch={onSwitch}
        onSwitchBackgroundColor={
          disabled ? COLORS.grey3 : COLORS.green
        }
      />
    </View>
  );
};

export default React.memo(SectionMarkUsed);


================================================================================
FILE 8/12: SectionSurveyForm.tsx
Size: 6.42 KB | Modified: 2025-08-27T08:01:49.981Z
================================================================================
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { l10n } from 'languages';
import moment from 'moment';
/** Component */
import BasedText from 'components/BasedText';
import { ButtonSecondary, ButtonPrimary } from 'components/Button';
import { Icon32px } from 'components/Icon/Icon';
// Utils & Constants & style
import { COLORS, SPACING, TYPOGRAPHY } from 'styleGuide';
import { resWidth } from 'utils/Screen';
import { openWebBrowser } from 'utils/InAppBrowser';
import Logger from 'utils/Logger';
import { validateUrl } from 'utils/Utility';
import { ESurveyType } from 'scenes/myrewardV3/helpers/constants';
import { URL } from 'utils/url';
import TrackingManagerV3 from 'manager/trackingManager/TrackingManagerV3';

// redux
import myRewardApi from 'scenes/myrewardV3/redux/api';
import { useAppSelector } from 'storeConfig/hook';
import AuthSelector from 'scenes/auth/redux/selectors';

interface Props {
  code: string;
  type: ESurveyType;
  link?: string;
  submitted: boolean;
  merchantName: string;
  resetMyRewardData: () => void;
  endTime?: string;
  merchantCode: string;
  rewardName: string;
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
  },
  formAccesstradeContent: {
    justifyContent: 'center',
  },
  descForm: {
    ...TYPOGRAPHY.bodyRegular,
    textAlign: 'center',
  },
  btnFillInfo: {
    marginTop: SPACING.m_16,
    width: resWidth(208),
  },
  btnViewGift: {
    width: resWidth(167),
    marginTop: SPACING.s_12,
  },
  statusSubmitted: {
    flexDirection: 'row',
    backgroundColor: COLORS.grey4,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: resWidth(30),
    height: resWidth(30),
    paddingHorizontal: SPACING.s_12,
    marginBottom: SPACING.s_12,
  },
  descSubmitted: {
    ...TYPOGRAPHY.statusDemi,
    textAlign: 'center',
  },
  iconChecked: {
    marginRight: SPACING.s_6,
  },
  voucherCodeLabel: {
    ...TYPOGRAPHY.statusDemi,
    color: COLORS.grey2,
    textAlign: 'center',
  },
});

// eslint-disable-next-line react/display-name
const EntrySurveyForm: React.VFC<Props> = React.memo(
  ({
    code,
    submitted,
    type,
    link,
    endTime,
    resetMyRewardData,
    merchantCode,
    rewardName,
  }) => {
    const [loading, setLoading] = React.useState(false);
    const profileUser = useAppSelector(AuthSelector.getProfile);

    const expired = !submitted && moment().isAfter(endTime);

    const getLinkMerchandise = async (): Promise<string> => {
      try {
        setLoading(true);
        const result = await myRewardApi.getLinkSurveyForm({
          gameType: 'merchandise',
          userId: profileUser?.userId,
          mobile: profileUser?.mobile,
          voucherCode: code,
        });
        if (result?.status?.code === 200 && result?.data) {
          return result.data;
        }
        return '';
      } catch (e) {
        Logger.log('getLinkMerchandise error', e);
        return '';
      } finally {
        setLoading(false);
      }
    };

    /** input type = merch/formAccesstrade
     *  voucher merchandise is get link form
     *  voucher formAccesstrade is open link from props
     */
    const onPressForm = React.useCallback(async () => {
      let linkForm = link || '';

      switch (type) {
        case ESurveyType.MERCHANDISE:
          linkForm = await getLinkMerchandise();
          break;
        case ESurveyType.FORM_ACCESS_TRADE:
          const url = new URL(link || '');
          url.setParams('utm_source', 'taptap_citibank');
          url.setParams('utm_campaign', 'voucher');
          url.setParams('utm_medium', code);
          url.setParams('utm_content', profileUser?.userId);
          linkForm = url.toJSON();
          break;
        default:
          break;
      }

      /** check link and open web browser */
      if (linkForm && validateUrl(linkForm)) {
         
        void openWebBrowser(linkForm, false, () => {
          /** update status after submit form success */
          if (!submitted) {
            resetMyRewardData();
          }
        });
      }
    }, [submitted]);

    const onOpenForm = async () => {
      TrackingManagerV3.getInstance()
        ?.trackMyRewardDetail()
        .mrDetailFillForm({
          merchant_code: merchantCode,
          reward_name: rewardName,
          campaign: type,
        });

      await onPressForm();
    };

    const onCheckInfo = async () => {
      TrackingManagerV3.getInstance()
        ?.trackMyRewardDetail()
        .mrDetailCheckInfo({
          merchant_code: merchantCode,
          reward_name: rewardName,
        });

      await onPressForm();
    };

    if (submitted) {
      return (
        <>
          {/* @ts-ignore*/}
          <View style={[styles.content, styles[`${type}Content`]]}>
            <View style={styles.statusSubmitted}>
              <Icon32px.SolidCheck style={styles.iconChecked} />
              <BasedText style={styles.descSubmitted}>
                {l10n.myReward.surveyForm.submitted}
              </BasedText>
            </View>

            <BasedText style={styles.descSubmitted} numberOfLines={2}>
              {l10n.myReward.surveyForm[`${type}DescSubmitted`]}
            </BasedText>

            {type === 'merch' && (
              <ButtonSecondary
                type="tiny"
                content={l10n.myReward.surveyForm.viewInfoGift}
                containerStyle={styles.btnViewGift}
                onPress={onCheckInfo}
                isLoading={loading}
              />
            )}
          </View>

          <BasedText style={styles.voucherCodeLabel}>
            {l10n.formatString(l10n.myReward.surveyForm.voucherCode, {
              voucherCode: code,
            })}
          </BasedText>
        </>
      );
    }
    return (
      <>
        <View style={styles.content}>
          <BasedText style={styles.descForm} numberOfLines={2}>
            {l10n.myReward.surveyForm[`${type}DescInfo`]}
          </BasedText>

          <View style={styles.btnFillInfo}>
            <ButtonPrimary
              type="large"
              content={l10n.myReward.surveyForm.fillInfo}
              onPress={onOpenForm}
              isLoading={loading}
              disabled={expired}
            />
          </View>
        </View>

        <BasedText style={styles.voucherCodeLabel}>
          {l10n.formatString(l10n.myReward.surveyForm.voucherCode, {
            voucherCode: code,
          })}
        </BasedText>
      </>
    );
  },
);

export default EntrySurveyForm;


================================================================================
FILE 9/12: SectionTimerReward.tsx
Size: 7.23 KB | Modified: 2025-08-26T09:03:38.132Z
================================================================================
import BasedText from 'components/BasedText';
import LineSvg from 'components/LineBreak/LineSvg';
/* ###BlockStart */
import { ListDataNetWork } from 'config/DataNetWork/ListNetworkData';
/* ###BlockEnd */
import {
  IMyRewardExpiryData,
  RewardRedeemStartTypeEnum,
  RewardRedeemExpireTypeEnum,
} from 'constant/CommonTypes';
import { APP_DATE, DATE_TIME } from 'constant/DateTimeConstant';
import { l10n } from 'languages';
import moment from 'moment';
import React, { FC, ReactNode, useMemo } from 'react';
import { StyleSheet } from 'react-native';
import Config from 'react-native-config';
import AppConfigSelector from 'services/appConfig/selectors';
import { useAppSelector } from 'storeConfig/hook';
import { COLORS, SPACING, TYPOGRAPHY } from 'styleGuide';
import AESUtil from 'utils/AESUtil';
import Platform from 'utils/Platform';
import { perWidth } from 'utils/Screen';
// Utils & styles
import { parseDateTimeCenterDot } from 'utils/Utility';

const HEADER_WIDTH = perWidth(100) - SPACING.m_16 * 2;

const styles = StyleSheet.create({
  expiredLabel: {
    ...TYPOGRAPHY.statusDemi,
    color: COLORS.grey2,
    paddingBottom: SPACING.m_16,
    textAlign: 'center',
  },
});

export type IPropsSectionTimer = {
  redeemPerUser: number;
  remainRedeemCount: number;
  startTime?: string;
  endTime?: string;
  // Time
  settingRedeemData?: string;
};

interface IProps {
  expiryData: IPropsSectionTimer;
}

const SectionTimerReward: FC<IProps> = ({ expiryData }) => {
  const {
    redeemPerUser,
    remainRedeemCount,
    startTime,
    endTime,
    // Time
    settingRedeemData,
  } = expiryData || {};
  const isDebugMode = useAppSelector(
    AppConfigSelector.selectDebugMode,
  );

  const voucherData = useMemo((): IMyRewardExpiryData | null => {
    let obj: IMyRewardExpiryData = {};
    let strResult = '';
    try {
      strResult = AESUtil.decrypt(
        Config.CRYPTO_PHRASE,
        settingRedeemData,
      );
      if (typeof strResult === 'string' && strResult.length > 0) {
        obj = JSON.parse(strResult);
      }
    } catch (_) {
      //
    }
    /* ###BlockStart */
    if (isDebugMode || Platform.isDev) {
      ListDataNetWork.getInstance()?.addWebViewData({
        url: `MyReward Detai - settingRedeemData`,
        duration: 'Redeem Encrypt',
        dataRequest: settingRedeemData || '',
        dataRespsone: obj,
        isSuccess: !!obj,
        time: moment().format(DATE_TIME),
      });
    }
    /* ###BlockEnd */

    return obj;
  }, [isDebugMode, settingRedeemData]);

  const isBeforeStartTime = moment().isBefore(startTime);
  const startDate = moment(startTime || '').format(APP_DATE);
  const endDate = moment(endTime || '').format(APP_DATE);
  const startDateHour = parseDateTimeCenterDot(startTime || '');
  const endDateHour = parseDateTimeCenterDot(endTime || '');
  const hideHour =
    moment(endTime || '').format('HH:mm:ss') === '23:59:59';

  const renderHighlightNote = (
    highlight: string,
    color: string = COLORS.orange,
  ) => {
    return (
      <BasedText style={TYPOGRAPHY.statusDemi} color={color}>
        {highlight}
      </BasedText>
    );
  };

  const getNote = () => {
    if (Math.abs(redeemPerUser) === 1) {
      return isBeforeStartTime
        ? l10n.formatString(l10n.myReward.startDateNoRemain, {
            start: renderHighlightNote(startDate),
            end: renderHighlightNote(endDate),
          })
        : l10n.formatString(l10n.myReward.expiredDateWith, {
            date: renderHighlightNote(endDate),
          });
    }

    return isBeforeStartTime
      ? l10n.formatString(l10n.myReward.startDateHasRemain, {
          remain: renderHighlightNote(
            `${remainRedeemCount}/${redeemPerUser}`,
            COLORS.green,
          ),
          start: renderHighlightNote(startDate),
        })
      : l10n.formatString(l10n.myReward.endDateHasRemain, {
          remain: renderHighlightNote(
            `${remainRedeemCount}/${redeemPerUser}`,
            COLORS.green,
          ),
          end: renderHighlightNote(endDate),
        });
  };

  const getTime = (): ReactNode => {
    let time: string | (string | Element)[] = '';

    if (
      voucherData?.redeemStartCalculationType ===
      RewardRedeemStartTypeEnum.RIGHT_IMMEDIATELY_AFTER_ISSUAL
    ) {
      switch (voucherData?.redeemExpireCalculationType) {
        case RewardRedeemExpireTypeEnum.AFTER_X_HOURS_FROM_ISSUAL:
        case RewardRedeemExpireTypeEnum.AFTER_X_MINUTES_FROM_ISSUAL:
          if (Math.abs(redeemPerUser) === 1) {
            time = l10n.formatString(l10n.myReward.expiredDateWith, {
              date: renderHighlightNote(
                hideHour ? endDate : endDateHour,
              ),
            });
          } else {
            time = l10n.formatString(l10n.myReward.endDateHasRemain, {
              remain: renderHighlightNote(
                `${remainRedeemCount}/${redeemPerUser}`,
                COLORS.green,
              ),
              end: renderHighlightNote(
                hideHour ? endDate : endDateHour,
              ),
            });
          }
          break;

        case RewardRedeemExpireTypeEnum.SPECIFIC_DATE:
        case RewardRedeemExpireTypeEnum.AFTER_X_DAYS_FROM_ISSUAL:
        case RewardRedeemExpireTypeEnum.AFTER_X_MONTHS_FROM_ISSUAL:
          time = getNote();
          break;

        default:
          time = getNote();
          break;
      }
    } else if (
      voucherData?.redeemStartCalculationType ===
      RewardRedeemStartTypeEnum.SPECIFIC_DATE
    ) {
      switch (voucherData?.redeemExpireCalculationType) {
        case RewardRedeemExpireTypeEnum.SPECIFIC_DATE:
        case RewardRedeemExpireTypeEnum.AFTER_X_DAYS_FROM_ISSUAL:
        case RewardRedeemExpireTypeEnum.AFTER_X_MONTHS_FROM_ISSUAL:
          if (Math.abs(redeemPerUser) === 1) {
            time = l10n.formatString(
              l10n.myReward.startDateNoRemain,
              {
                start: renderHighlightNote(
                  hideHour ? startDate : startDateHour,
                ),
                end: renderHighlightNote(
                  hideHour ? endDate : endDateHour,
                ),
              },
            );
          } else {
            time = isBeforeStartTime
              ? l10n.formatString(l10n.myReward.startDateHasRemain, {
                  remain: renderHighlightNote(
                    `${remainRedeemCount}/${redeemPerUser}`,
                    COLORS.green,
                  ),
                  start: renderHighlightNote(
                    hideHour ? startDate : startDateHour,
                  ),
                })
              : l10n.formatString(l10n.myReward.endDateHasRemain, {
                  remain: renderHighlightNote(
                    `${remainRedeemCount}/${redeemPerUser}`,
                    COLORS.green,
                  ),
                  end: renderHighlightNote(
                    hideHour ? endDate : endDateHour,
                  ),
                });
          }
          break;

        default:
          time = getNote();
          break;
      }
    } else {
      time = getNote();
    }

    return time as ReactNode;
  };

  return (
    <>
      <BasedText style={styles.expiredLabel}>{getTime()}</BasedText>
      <LineSvg x1={0} x2={HEADER_WIDTH - SPACING.s_12} width="100%" />
    </>
  );
};

export default SectionTimerReward;


================================================================================
FILE 10/12: SectionVoucherTransfer.tsx
Size: 3.02 KB | Modified: 2025-08-26T09:03:38.132Z
================================================================================
import { StyleSheet, View } from 'react-native';
import React from 'react';
import { Section } from 'components/Navigation';
import BasedText from 'components/BasedText';
import { l10n } from 'languages';
import { COLORS, LAYOUTS, SPACING, TYPOGRAPHY } from 'styleGuide';
import { OwnershipStatus } from 'scenes/voucherTransfer/models';
import { resWidth } from 'utils/Screen';
import getRandomBGColor from 'scenes/voucherTransfer/helpers/getRandomBGColor';
import BasedImage from 'components/Image/BasedImage';
import { parseDateTimeCenterDot } from 'utils/Utility';

interface IProps {
  transferName: string;
  avatarImage?: string;
  phoneNumber: string;
  ownershipStatus: OwnershipStatus;
  receivedDate: string;
}

const SectionVoucherTransfer: React.FC<IProps> = ({
  transferName,
  avatarImage = '',
  phoneNumber,
  ownershipStatus,
  receivedDate,
}) => {
  const voucherTransferInfo = { title: '', name: '' };

  switch (ownershipStatus) {
    case OwnershipStatus.RECEIVED:
      Object.assign(voucherTransferInfo, {
        title: l10n.voucherTransfer.transferInfo.giverInfo,
        name: l10n.voucherTransfer.transferInfo.giverName,
      });
      break;
    case OwnershipStatus.TRANSFERRED:
      Object.assign(voucherTransferInfo, {
        title: l10n.voucherTransfer.transferInfo.receiverInfo,
        name: l10n.voucherTransfer.transferInfo.receiverName,
      });
      break;
    default:
      break;
  }

  return (
    <Section
      title={voucherTransferInfo.title}
      backgroundColor={COLORS.primaryWhite}>
      <View style={styles.wrapper}>
        <View style={LAYOUTS.rowBetween}>
          <BasedText style={TYPOGRAPHY.bodyRegular}>
            {voucherTransferInfo.name}
          </BasedText>
          <View style={LAYOUTS.rowCenter}>
            <View
              style={[
                styles.wrapAvatar,
                { backgroundColor: getRandomBGColor() },
              ]}>
              <BasedImage
                source={{ uri: avatarImage }}
                style={styles.wrapAvatar}
              />
            </View>
            <BasedText style={TYPOGRAPHY.bodyDemi}>
              {transferName}
            </BasedText>
          </View>
        </View>
        <View style={LAYOUTS.rowBetween}>
          <BasedText style={TYPOGRAPHY.bodyRegular}>
            {l10n.phoneNumber}
          </BasedText>
          <BasedText style={TYPOGRAPHY.bodyDemi}>
            {phoneNumber}
          </BasedText>
        </View>
        <View style={LAYOUTS.rowBetween}>
          <BasedText style={TYPOGRAPHY.bodyRegular}>
            {l10n.voucherTransfer.transferInfo.transferDate}
          </BasedText>
          <BasedText style={TYPOGRAPHY.bodyDemi}>
            {parseDateTimeCenterDot(receivedDate)}
          </BasedText>
        </View>
      </View>
    </Section>
  );
};

export default React.memo(SectionVoucherTransfer);

const styles = StyleSheet.create({
  wrapper: {
    gap: resWidth(13),
  },
  wrapAvatar: {
    width: SPACING.l_24,
    aspectRatio: 1,
    borderRadius: 999,
    marginRight: SPACING.s_4,
  },
});


================================================================================
FILE 11/12: styles.ts
Size: 1.22 KB | Modified: 2025-08-26T09:03:38.132Z
================================================================================
import { StyleSheet } from 'react-native';
import { COLORS, SPACING } from 'styleGuide';
import { perWidth, resWidth } from 'utils/Screen';

export default StyleSheet.create({
  container: {
    backgroundColor: COLORS.primaryWhite,
  },
  header: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.m_16,
    position: 'absolute',
    bottom: resWidth(137),
  },
  scrollView: { backgroundColor: COLORS.grey4 },
  cardVoucherDetail: {
    marginTop: -resWidth(100),
    alignItems: 'center',
  },
  content: {
    backgroundColor: COLORS.primaryWhite,
  },
  sectionWrapper: {
    marginTop: SPACING.m_16,
    gap: SPACING.s_12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: SPACING.l_32,
  },
  loadingHeader: {
    position: 'absolute',
    top: 0,
    width: perWidth(100),
    height: resWidth(92),
    paddingLeft: SPACING.m_16,
    paddingBottom: SPACING.s_8,
    justifyContent: 'flex-end',
  },
  vendor: {
    marginBottom: SPACING.s_12,
  },
  giftTransferButton: {
    gap: SPACING.s_6,
  },
  maxTransfer: {
    height: resWidth(38),
    marginTop: -SPACING.m_16,
    marginBottom: SPACING.m_16,
    gap: SPACING.s_8,
  },
});


================================================================================
FILE 12/12: Tutorial.tsx
Size: 2.63 KB | Modified: 2025-08-26T09:03:38.132Z
================================================================================
import React, {
  PropsWithChildren,
  useCallback,
  useEffect,
} from 'react';
import { l10n } from 'languages';
/**
 * Scenes
 */

/**
 *  Utils & helpers
 */
import { countNumberOfOccurrences } from 'utils/FirstTime';

/**
 *  Constants & Styles & helper
 */
import { HIGHLIGHT_ID } from 'components/TutorialV4/constants';
import { tooltipTextStyle } from 'components/TutorialV4/Tooltip';

/**
 *  Components
 */
import BasedText from 'components/BasedText';
import useTutorial from 'components/TutorialV4/useTutorial';
import TrackingManagerV3 from 'manager/trackingManager/TrackingManagerV3';
import EventConstantsV3 from 'manager/trackingManager/EventConstantsV3';

/**
 *  Action & Types & Tracking
 */

const TIMES_SHOWED_BTN_TRANSFER_TUTORIAL =
  'TIMES_SHOWED_BTN_TRANSFER_TUTORIAL';
const LIMIT_TIMES = 1;

interface Props {
  onFinishTutorials?: () => void;
}

export interface TutorialRef {
  showTutorial: () => void;
}

const Tutorial = React.forwardRef<
  TutorialRef,
  PropsWithChildren<Props>
>((props, ref) => {
  const { onFinishTutorials } = props;
  const [canPrepareTutorial, setCanPrepareTutorial] =
    React.useState(false);
  const { showTutorials } = useTutorial();

  React.useImperativeHandle(ref, () => {
    return {
      showTutorial: onTutorials,
    };
  });

  const onTutorials = useCallback(() => {
    if (!canPrepareTutorial) return;
    const tooltipContent = (
      <BasedText style={tooltipTextStyle.text}>
        {l10n.myReward.tutorialRewardTransfer}
      </BasedText>
    );

    showTutorials([
      {
        highlightElementId: HIGHLIGHT_ID.btn_transfer_reward,
        arrowXPosPercent: 30,
        content: tooltipContent,
        buttonContent: l10n.merchant.tutorial.ok,
        onShow: () => {
          TrackingManagerV3.getInstance()?.trackingDialog().view({
            dialog_name:
              EventConstantsV3.MY_REWARD_DETAIL.TUTORIAL
                .VOUCHER_DETAIL_TRANSFER_INTRO,
          });
        },
        onAction: () => {
          TrackingManagerV3.getInstance()?.trackingDialog().button({
            dialog_name:
              EventConstantsV3.MY_REWARD_DETAIL.TUTORIAL
                .VOUCHER_DETAIL_TRANSFER_INTRO,
            action: 'close',
          });
        },
      },
    ]);
  }, [canPrepareTutorial, showTutorials]);

  useEffect(() => {
    countNumberOfOccurrences(
      TIMES_SHOWED_BTN_TRANSFER_TUTORIAL,
      LIMIT_TIMES,
      count => {
        if (count > -1) {
          setCanPrepareTutorial(true);
        } else {
          onFinishTutorials && onFinishTutorials();
        }
      },
    );
  }, []);

  return null;
});

Tutorial.displayName = 'Tutorial';
export default Tutorial;


