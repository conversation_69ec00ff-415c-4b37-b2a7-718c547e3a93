# Mobile MyRewardDetail Architecture Rules & Patterns

## Overview
This document defines the complete architecture rules and patterns for implementing MyRewardDetail functionality based on the mobile codebase (`taptap-mobile/src/scenes/myrewardV3/screens/MyRewardDetail/`). These rules MUST be followed when creating web equivalents.

## 🏗️ Core Architecture Patterns

### 1. Main Component Structure
```typescript
// Mobile: MyRewardDetail/index.tsx
const MyRewardDetail: FC<Props> = ({ route }) => {
  // 1. Data fetching with custom hook
  const { detail, loading, setDetail, updateMarkAdUsed } = useFetchMyRewardDetail({
    codeId, isUnread, hasFetchUserConsent, source
  });

  // 2. Section rendering functions
  const renderMiddleTopSection = React.useMemo(() => { ... }, [detail]);
  const renderMiddleBottomSection = React.useMemo(() => { ... }, [dependencies]);
  const renderHeader = () => { ... };
  const renderContent = () => { ... };
  const renderBottom = () => { ... };

  // 3. Conditional rendering structure
  return (
    <Container>
      <Animated.ScrollView>
        {renderHeader()}
        {detail && (
          <View>
            {renderVoucherTransferInfo()}
            {renderContent()}
          </View>
        )}
      </Animated.ScrollView>
      {renderBottom()}
    </Container>
  );
};
```

**Web Implementation Rule**: Follow exact same structure with equivalent hooks and rendering functions.

### 2. Section-Based Rendering Pattern
```typescript
// Mobile pattern - renderMiddleBottomSection
const renderMiddleBottomSection = React.useMemo(() => {
  const { link, type } = transformPostID(posIdentifier);
  
  // Priority order (MUST follow this exact sequence):
  if (type === ESurveyType.MERCHANDISE || type === ESurveyType.FORM_ACCESS_TRADE) {
    return <SectionSurveyForm {...props} />;
  }
  
  if (is711Consent) {
    return <SectionLink711 {...props} />;
  }
  
  if (autoRedeem) {
    return <SectionAutoRedeem {...props} />;
  }
  
  return undefined;
}, [status, is711Consent, autoRedeem, activeAutoRedeemDate, timeRedeemAuto, endTime]);
```

**Web Implementation Rule**: Use identical condition priority and component selection logic.

## 🔄 Auto-Redeem Flow Rules

### 1. Parent-Level Auto-Redeem Check
```typescript
// Mobile: MyRewardDetail/index.tsx lines 266-280
if (autoRedeem) {
  return (
    <SectionAutoRedeem
      timeRedeemAuto={timeRedeemAuto || 0}
      codeId={codeId}
      code={code}
      status={status}
      executeAutoRedeemDate={executeAutoRedeemDate}
      setDetail={setDetail}
      paramsTracking={paramsTracking}
      endTime={endTime}
      rewardName={name}
    />
  );
}
```

**Rule**: Parent only checks `if (autoRedeem)` - NO additional validation at this level.

### 2. SectionAutoRedeem Decision Logic
```typescript
// Mobile: SectionAutoRedeem/index.tsx lines 32-48
return executeAutoRedeemDate ? (
  <SectionCountDownReward
    codeId={codeId}
    code={code}
    status={status}
    executeAutoRedeemDate={executeAutoRedeemDate}
    rewardName={rewardName}
  />
) : (
  <SectionWarningUseVoucher
    timeRedeemAuto={timeRedeemAuto}
    codeId={codeId}
    setRewardDetail={setDetail}
    paramsTracking={paramsTracking}
    endTime={endTime}
  />
);
```

**Rule**: Decision based ONLY on `executeAutoRedeemDate` existence.

### 3. Critical Expiration Check Rule
```typescript
// Mobile: SectionWarningUseVoucher.tsx line 67
const isExpire = moment(new Date()).isAfter(endTime);

// Mobile: lines 115-121
<ButtonPrimary
  testID="id_btn_warning"
  disabled={isExpire}  // ← CRITICAL: Component-level expiration check
  type="small"
  content={l10n.myReward.autoRedeem.useNow}
  onPress={showBottomWarning}
/>
```

**CRITICAL RULE**: 
- ✅ Expiration check happens AT THE COMPONENT LEVEL (SectionWarningUseVoucher)
- ✅ Parent passes `endTime` prop, child component decides button state
- ❌ NEVER do expiration check at parent level for auto-redeem buttons

## 📊 Data Flow Rules

### 1. Hook Usage Pattern
```typescript
// Mobile: useFetchMyRewardDetail hook
const {
  detail,           // Main voucher data
  loading,          // Loading state
  resetMyRewardData, // Reset function  
  setDetail,        // Update voucher state
  updateMarkAdUsed, // Mark as used functionality
} = useFetchMyRewardDetail({
  codeId,
  isUnread,
  hasFetchUserConsent: qrCode711.length === 0,
  source,
});
```

**Web Rule**: Create equivalent hook with same return structure.

### 2. State Management Pattern
```typescript
// Mobile: Destructuring pattern from detail
const {
  merchantCode = '',
  merchantName = '',
  merchantLogo = '',
  name = '',
  endTime,
  code = '',
  tnc = '',
  startTime,
  // ... all other fields with defaults
  autoRedeem,
  timeRedeemAuto,
  activeAutoRedeemDate,
  executeAutoRedeemDate,
  ownershipStatus = OwnershipStatus.OWNED,
} = detail || {};
```

**Rule**: Use identical destructuring pattern with same default values.

## 🎯 Component Mapping Rules

### Mobile → Web Component Mapping
| Mobile Component | Web Equivalent | Purpose |
|-----------------|----------------|---------|
| `MyRewardDetail` | `MyRewardDetailPage` | Main detail page |
| `SectionAutoRedeem` | `EnhancedBarcodeCard` (auto-redeem mode) | Auto-redeem logic container |
| `SectionWarningUseVoucher` | `EnhancedBarcodeCard` (internal logic) | "Dùng ngay" button with expiration |
| `SectionCountDownReward` | `EnhancedBarcodeCard` (countdown mode) | Post-activation countdown |
| `SectionMarkUsed` | `MarkAsUsedToggle` | Mark as used functionality |
| `SectionTimerReward` | `SectionTimerReward` | Timer/expiry display |
| `DetailHeader` | `EnhancedBarcodeCard` | Main voucher card display |

### 3. Props Passing Rules
```typescript
// Mobile: SectionAutoRedeem props (MUST match exactly)
<SectionAutoRedeem
  timeRedeemAuto={timeRedeemAuto || 0}  // Default to 0
  codeId={codeId}
  code={code}
  status={status}
  executeAutoRedeemDate={executeAutoRedeemDate}
  setDetail={setDetail}                  // State setter callback
  paramsTracking={paramsTracking}        // Tracking object
  endTime={endTime}                      // For expiration check
  rewardName={name}
/>
```

**Rule**: Pass exact same props with same names and default values.

## 🎨 UI/UX Pattern Rules

### 1. Loading States
```typescript
// Mobile: StatusOnScreen with loading types
if (!detail) {
  return (
    <View style={styles.loadingContainer}>
      <StatusOnScreen
        type={loading ? IErrorStatus.loading : IErrorStatus.loading_error}
      />
    </View>
  );
}
```

**Rule**: Implement equivalent loading and error states.

### 2. Animation Patterns
```typescript
// Mobile: Animated scroll with position tracking
const positionY = useSharedValue(0);
const scrollHandler = useAnimatedScrollHandler(event => {
  positionY.value = event?.contentOffset.y;
});

<Animated.ScrollView
  onScroll={scrollHandler}
  showsVerticalScrollIndicator={false}
  scrollEventThrottle={1}
>
```

**Web Rule**: Implement equivalent scroll animations where applicable.

### 3. Bottom Panel Pattern
```typescript
// Mobile: Conditional bottom panel for transfers
const renderBottom = () => {
  const isMaxTransfer = /* validation logic */;
  return (
    <BottomPanel ref={bottomPanelRef}>
      <ButtonSecondary
        disabled={isMaxTransfer}
        content={l10n.voucherTransfer.giveToFriend}
        onPress={onGoVoucherTransfer}
      />
    </BottomPanel>
  );
};

// Show panel based on conditions
useEffect(() => {
  if (detail?.allowTransfer && status === EVoucherStatus.ACTIVE) {
    bottomPanelRef.current?.show();
  }
}, [detail]);
```

**Rule**: Implement equivalent bottom sheet/modal for transfer functionality.

## 🔍 Validation & Business Logic Rules

### 1. 7-Eleven Special Case
```typescript
// Mobile: is711Consent logic
const is711Consent = React.useMemo(() => {
  const linked7Eleven = qrCode711 && qrCode711?.length > 0;
  return (
    redeemBrands &&
    redeemBrands.length === 1 &&
    redeemBrands[0] === BRAND_7_ELEVEN &&
    !linked7Eleven
  );
}, [redeemBrands, qrCode711]);
```

**Rule**: Implement identical 7-Eleven linking validation.

### 2. Transfer Validation
```typescript
// Mobile: Transfer button conditions
const isMaxTransfer = 
  typeof detail.remainTransferDailyPerUser === 'number' &&
  detail.remainTransferDailyPerUser <= 0;

// Show transfer panel
if (detail?.allowTransfer && status === EVoucherStatus.ACTIVE) {
  // Show transfer UI
}
```

**Rule**: Use same transfer validation logic.

### 3. Survey/Form Handling
```typescript
// Mobile: Survey type checking
const { link, type } = transformPostID(posIdentifier);
if (
  type === ESurveyType.MERCHANDISE ||
  type === ESurveyType.FORM_ACCESS_TRADE
) {
  return <SectionSurveyForm {...props} />;
}
```

**Rule**: Implement identical survey/form type handling.

## 📱 Tracking & Analytics Rules

### 1. Tracking Structure
```typescript
// Mobile: Consistent tracking object
const paramsTracking = {
  merchant_code: merchantCode,
  reward_name: detail?.name || '',
};

// Usage in components
TrackingManagerV3.getInstance()
  ?.trackMyRewardDetail()
  .mrDetailAutoRedeem(paramsTracking);
```

**Web Rule**: Implement equivalent tracking with same parameter structure.

### 2. Event Tracking Points
- Barcode view/copy
- Auto-redeem activation
- Image gallery interactions  
- Transfer button clicks
- Mark as used toggles
- Member code access

**Rule**: Track identical user interaction points.

## 🚫 Critical DON'Ts

### ❌ NEVER Do These:
1. **Don't add expiration checks at parent level for auto-redeem**
   ```typescript
   // ❌ WRONG
   canUseNow={reward.autoRedeem ? canUseAutoRedeemVoucher(reward) : canUseVoucher(reward)}
   
   // ✅ CORRECT  
   canUseNow={canUseVoucher(reward)}
   // Let component handle expiration internally
   ```

2. **Don't change the section rendering priority**
   ```typescript
   // ✅ MUST maintain this exact order:
   // 1. Survey/Form sections
   // 2. 7-Eleven consent
   // 3. Auto-redeem
   // 4. Default (undefined)
   ```

3. **Don't modify component decision logic**
   ```typescript
   // ✅ SectionAutoRedeem MUST only check executeAutoRedeemDate
   return executeAutoRedeemDate ? <CountDown /> : <Warning />;
   ```

4. **Don't add custom business logic not in mobile**
   - Every validation rule must exist in mobile
   - Every UI condition must match mobile
   - Every data transformation must be identical

## ✅ Implementation Checklist

### For Web MyRewardDetailPage:
- [ ] Use exact same renderMiddleBottomSection pattern
- [ ] Implement identical section priority logic
- [ ] Pass same props to auto-redeem components
- [ ] Use equivalent data fetching hook
- [ ] Implement same loading/error states
- [ ] Add identical tracking points
- [ ] Handle 7-Eleven special case
- [ ] Support transfer functionality
- [ ] Process survey/form types

### For Auto-Redeem Components:
- [ ] Component-level expiration check: `const isExpire = now > endTime`
- [ ] Button disabled logic: `disabled={isExpire}`
- [ ] Parent passes endTime, component decides
- [ ] Two-state flow: warning → countdown
- [ ] Modal confirmation pattern
- [ ] Proper state callbacks

### For Shared Components:
- [ ] Pure components, no business logic
- [ ] Receive all data via props
- [ ] Handle platform differences via props
- [ ] Maintain mobile UI patterns
- [ ] Use design system consistently

## 📋 Testing Requirements

### Must Test:
1. **Auto-Redeem Flow**:
   - Button shows when `autoRedeem: true`
   - Button disabled when expired
   - Countdown shows after activation
   - State updates correctly

2. **Section Rendering**:
   - Correct priority order
   - Conditional display logic
   - Props passed correctly

3. **Transfer Functionality**:
   - Bottom panel shows when applicable
   - Transfer limits respected
   - Ownership status handling

4. **Edge Cases**:
   - 7-Eleven linking scenarios
   - Survey/form type handling
   - Loading and error states
   - Data validation

---

## 🎯 Summary

**Golden Rule**: When in doubt, copy the mobile behavior exactly. Every deviation from mobile patterns must be justified by platform constraints, not developer preference.

**Implementation Priority**:
1. ✅ Copy mobile business logic exactly
2. ✅ Use shared components where available  
3. ✅ Adapt only for web platform differences
4. ✅ Maintain mobile UX patterns
5. ✅ Never add custom logic not in mobile