#!/bin/bash

echo "🎥 Available test videos and screenshots:"
echo "========================================="

# Find all videos
videos=$(find test-results -name "*.webm" | sort)
screenshots=$(find test-results -name "*.png" | sort)

echo ""
echo "📹 VIDEOS:"
for video in $videos; do
    test_name=$(echo $video | sed 's|test-results/||' | sed 's|/video.webm||')
    echo "  - $test_name"
    echo "    File: $video"
    echo ""
done

echo "📸 SCREENSHOTS:"
for screenshot in $screenshots; do
    test_name=$(echo $screenshot | sed 's|test-results/||' | sed 's|/[^/]*\.png||')
    filename=$(basename $screenshot)
    echo "  - $test_name ($filename)"
    echo "    File: $screenshot"
    echo ""
done

echo "🌐 Open HTML Report:"
echo "  yarn playwright show-report"
echo ""
echo "🎬 Open specific video:"
echo "  open test-results/[test-folder]/video.webm"
echo ""
echo "🖼️  Open specific screenshot:"  
echo "  open test-results/[test-folder]/test-finished-1.png"