#!/bin/bash

# Script để gom tất cả file trong một folder thành một file duy nhất
# Usage: ./combine-files.sh <source-folder> [output-file]
# 
# Ví dụ:
# ./combine-files.sh ./src
# ./combine-files.sh ./shared/components combined-components.txt
# ./combine-files.sh ../my-project ./output/all-files.txt

# C<PERSON>u hình mặc định
DEFAULT_OUTPUT="combined-files-output.txt"

# Pattern để ignore (sử dụng trong find -name)
IGNORE_PATTERNS=(
    "node_modules"
    ".git"
    "dist"
    "build"
    ".DS_Store" 
    "*.env"
    "*.log"
    "package-lock.json"
    "yarn.lock"
    "pnpm-lock.yaml"
)

# Extension của binary files
BINARY_EXTENSIONS=(
    "png" "jpg" "jpeg" "gif" "bmp" "ico" "svg"
    "mp3" "mp4" "avi" "mov" "wmv" 
    "pdf" "doc" "docx" "xls" "xlsx"
    "zip" "rar" "7z" "tar" "gz"
    "exe" "dll" "so" "dylib"
    "woff" "woff2" "ttf" "eot" "otf"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function để hiển thị help
show_help() {
    echo -e "\n${BLUE}📋 COMBINE FILES SCRIPT${NC}\n"
    echo "Usage:"
    echo "  ./combine-files.sh <source-folder> [output-file]"
    echo ""
    echo "Arguments:"
    echo "  source-folder    Path to folder containing files to combine (required)"
    echo "  output-file      Output file name (optional, default: $DEFAULT_OUTPUT)"
    echo ""
    echo "Examples:"
    echo "  ./combine-files.sh ./src"
    echo "  ./combine-files.sh ./shared/components combined-components.txt"
    echo "  ./combine-files.sh ../my-project ./output/all-files.txt"
    echo ""
    echo "Features:"
    echo "  ✅ Recursively scans all subdirectories"
    echo "  ✅ Ignores common build/dependency folders (node_modules, dist, etc.)"
    echo "  ✅ Handles binary files gracefully"
    echo "  ✅ Shows file paths, sizes, and modification dates"
    echo "  ✅ Includes table of contents"
    echo "  ✅ Progress indicator"
    echo "  ✅ Human-readable file sizes"
    echo ""
    echo "Ignored patterns:"
    echo "  • node_modules, .git, dist, build folders"
    echo "  • .DS_Store, .env, log files"
    echo "  • package-lock.json, yarn.lock files"
    echo "  • Binary files (images, videos, executables, etc.)"
    echo ""
}

# Function để format file size
format_file_size() {
    local bytes=$1
    local sizes=("Bytes" "KB" "MB" "GB")
    local i=0
    
    if [ "$bytes" -eq 0 ]; then
        echo "0 Bytes"
        return
    fi
    
    while [ "$bytes" -ge 1024 ] && [ "$i" -lt 3 ]; do
        bytes=$((bytes / 1024))
        i=$((i + 1))
    done
    
    echo "${bytes} ${sizes[i]}"
}

# Function để kiểm tra file có phải binary không
is_binary_file() {
    local file_path="$1"
    local extension="${file_path##*.}"
    extension=$(echo "$extension" | tr '[:upper:]' '[:lower:]')
    
    for bin_ext in "${BINARY_EXTENSIONS[@]}"; do
        if [ "$extension" = "$bin_ext" ]; then
            return 0
        fi
    done
    return 1
}

# Function để tạo find command với ignore patterns
build_find_command() {
    local source_folder="$1"
    local find_cmd="find \"$source_folder\" -type f"
    
    # Thêm các pattern ignore
    for pattern in "${IGNORE_PATTERNS[@]}"; do
        find_cmd="$find_cmd -not -path \"*/$pattern\" -not -path \"*/$pattern/*\" -not -name \"$pattern\""
    done
    
    echo "$find_cmd"
}

# Function để đọc file content
read_file_content() {
    local file_path="$1"
    
    if is_binary_file "$file_path"; then
        local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null || echo "0")
        local formatted_size=$(format_file_size "$file_size")
        echo "[BINARY FILE - Size: $formatted_size]"
    else
        if [ -r "$file_path" ]; then
            cat "$file_path" 2>/dev/null || echo "[ERROR READING FILE: Permission denied or file not found]"
        else
            echo "[ERROR READING FILE: Permission denied or file not found]"
        fi
    fi
}

# Function để tạo separator cho file
create_file_separator() {
    local file_path="$1"
    local index="$2"
    local total="$3"
    local file_size="$4"
    local file_date="$5"
    
    local separator=$(printf '=%.0s' {1..80})
    local formatted_size=$(format_file_size "$file_size")
    
    echo "$separator"
    echo "FILE $((index + 1))/$total: $file_path"
    echo "Size: $formatted_size | Modified: $file_date"
    echo "$separator"
    echo ""
}

# Function chính
combine_files() {
    local source_folder="$1"
    local output_file="$2"
    
    echo -e "${BLUE}🔍 Scanning folder: $source_folder${NC}"
    
    # Kiểm tra source folder tồn tại
    if [ ! -d "$source_folder" ]; then
        echo -e "${RED}❌ Error: Source folder \"$source_folder\" does not exist!${NC}"
        exit 1
    fi
    
    # Build find command và lấy danh sách files
    local find_cmd=$(build_find_command "$source_folder")
    local temp_file=$(mktemp)
    
    # Execute find command và save vào temp file
    eval "$find_cmd" | sort > "$temp_file"
    
    # Đếm số files
    local file_count=$(wc -l < "$temp_file")
    
    if [ "$file_count" -eq 0 ]; then
        echo -e "${YELLOW}⚠️  No files found in \"$source_folder\"${NC}"
        rm "$temp_file"
        return
    fi
    
    echo -e "${GREEN}📁 Found $file_count files${NC}"
    
    # Tạo header cho output file
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")
    local absolute_path=$(cd "$source_folder" && pwd)
    
    # Tính tổng size
    local total_size=0
    while IFS= read -r file_path; do
        if [ -f "$file_path" ]; then
            local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null || echo "0")
            total_size=$((total_size + file_size))
        fi
    done < "$temp_file"
    
    local formatted_total_size=$(format_file_size "$total_size")
    
    # Tạo file output với header
    cat > "$output_file" << EOF
/*
 * COMBINED FILES OUTPUT
 * Generated: $timestamp
 * Source: $absolute_path
 * Total files: $file_count
 * Total size: $formatted_total_size
 */

/* TABLE OF CONTENTS */
EOF
    
    # Thêm table of contents
    local index=0
    while IFS= read -r file_path; do
        index=$((index + 1))
        local relative_path=$(realpath --relative-to="$source_folder" "$file_path" 2>/dev/null || \
                             python -c "import os.path; print(os.path.relpath('$file_path', '$source_folder'))" 2>/dev/null || \
                             echo "$file_path")
        echo " * $index. $relative_path" >> "$output_file"
    done < "$temp_file"
    
    echo "" >> "$output_file"
    echo "/* FILES CONTENT */" >> "$output_file"
    echo "" >> "$output_file"
    
    # Process từng file
    echo -e "${BLUE}📝 Processing files...${NC}"
    
    index=0
    while IFS= read -r file_path; do
        if [ -f "$file_path" ]; then
            local relative_path=$(realpath --relative-to="$source_folder" "$file_path" 2>/dev/null || \
                                 python -c "import os.path; print(os.path.relpath('$file_path', '$source_folder'))" 2>/dev/null || \
                                 echo "$file_path")
            
            # Progress indicator
            local progress=$(( (index + 1) * 100 / file_count ))
            printf "\r   Progress: %d%% (%d/%d) %s" "$progress" "$((index + 1))" "$file_count" "$relative_path"
            
            # Get file info
            local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null || echo "0")
            local file_date=$(stat -f%Sm -t "%Y-%m-%dT%H:%M:%S.000Z" "$file_path" 2>/dev/null || \
                            stat -c%y "$file_path" 2>/dev/null | sed 's/ /T/' | sed 's/\.[0-9]* //' || \
                            echo "Unknown")
            
            # Thêm separator và content
            create_file_separator "$relative_path" "$index" "$file_count" "$file_size" "$file_date" >> "$output_file"
            read_file_content "$file_path" >> "$output_file"
            echo -e "\n" >> "$output_file"
            
            index=$((index + 1))
        fi
    done < "$temp_file"
    
    echo "" # New line after progress
    
    # Clean up
    rm "$temp_file"
    
    # Hiển thị kết quả
    echo -e "${BLUE}💾 Writing to: $output_file${NC}"
    
    local output_size=$(stat -f%z "$output_file" 2>/dev/null || stat -c%s "$output_file" 2>/dev/null || echo "0")
    local formatted_output_size=$(format_file_size "$output_size")
    local absolute_output_path=$(realpath "$output_file" 2>/dev/null || echo "$output_file")
    
    echo -e "${GREEN}✅ Successfully created combined file!${NC}"
    echo -e "   📄 Output: $absolute_output_path"
    echo -e "   📊 Size: $formatted_output_size"
    echo -e "   📁 Files: $file_count"
}

# Main execution
main() {
    # Parse arguments
    if [ $# -eq 0 ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        show_help
        exit 0
    fi
    
    local source_folder="$1"
    local output_file="${2:-$DEFAULT_OUTPUT}"
    
    echo -e "${BLUE}🚀 Starting combine files process...${NC}"
    combine_files "$source_folder" "$output_file"
}

# Check if script is being run directly
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi