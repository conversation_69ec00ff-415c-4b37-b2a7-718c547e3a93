#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * <PERSON>ript để gom tất cả file trong một hoặc nhiều folder thành một file duy nhất
 * Usage: node combine-files.js <source-folder1> [source-folder2] ... [--output output-file]
 * 
 * Ví dụ:
 * node combine-files.js ./src --output combined-output.txt
 * node combine-files.js ./shared/components ./apps/web/src
 * node combine-files.js ./src ./shared ./apps --output all-files.txt
 */

// Cấu hình mặc định
const DEFAULT_OUTPUT = 'combined-files-output.txt';
const IGNORE_PATTERNS = [
  /node_modules/,
  /\.git/,
  /dist/,
  /build/,
  /\.DS_Store/,
  /\.env/,
  /\.log$/,
  /package-lock\.json$/,
  /yarn\.lock$/,
  /pnpm-lock\.yaml$/
];

const BINARY_EXTENSIONS = [
  '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.ico', '.svg',
  '.mp3', '.mp4', '.avi', '.mov', '.wmv',
  '.pdf', '.doc', '.docx', '.xls', '.xlsx',
  '.zip', '.rar', '.7z', '.tar', '.gz',
  '.exe', '.dll', '.so', '.dylib',
  '.woff', '.woff2', '.ttf', '.eot', '.otf'
];

/**
 * Kiểm tra xem file có nên bị ignore không
 */
function shouldIgnore(filePath) {
  return IGNORE_PATTERNS.some(pattern => pattern.test(filePath));
}

/**
 * Kiểm tra xem file có phải là binary không
 */
function isBinaryFile(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return BINARY_EXTENSIONS.includes(ext);
}

/**
 * Đọc content của file, xử lý đặc biệt cho binary files
 */
function readFileContent(filePath) {
  try {
    if (isBinaryFile(filePath)) {
      const stats = fs.statSync(filePath);
      return `[BINARY FILE - Size: ${formatFileSize(stats.size)}]`;
    }
    
    const content = fs.readFileSync(filePath, 'utf-8');
    return content;
  } catch (error) {
    return `[ERROR READING FILE: ${error.message}]`;
  }
}

/**
 * Format file size thành human readable
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Duyệt qua tất cả file trong folder đệ quy
 */
function getAllFiles(dirPath, baseDir = dirPath) {
  let results = [];
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const relativePath = path.relative(baseDir, fullPath);
      
      // Bỏ qua file/folder theo pattern
      if (shouldIgnore(relativePath)) {
        continue;
      }
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Đệ quy vào thư mục con
        results = results.concat(getAllFiles(fullPath, baseDir));
      } else if (stat.isFile()) {
        results.push({
          fullPath,
          relativePath,
          size: stat.size,
          modified: stat.mtime
        });
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dirPath}:`, error.message);
  }
  
  return results;
}

/**
 * Tạo separator cho mỗi file
 */
function createFileSeparator(fileInfo, index, total) {
  const separator = '='.repeat(80);
  const header = `FILE ${index + 1}/${total}: ${fileInfo.relativePath}`;
  const details = `Size: ${formatFileSize(fileInfo.size)} | Modified: ${fileInfo.modified.toISOString()}`;
  
  return [
    separator,
    header,
    details,
    separator,
    ''
  ].join('\n');
}

/**
 * Hàm chính để combine files từ nhiều folder
 */
function combineFiles(sourceFolders, outputFile = DEFAULT_OUTPUT) {
  // Đảm bảo sourceFolders là array
  if (!Array.isArray(sourceFolders)) {
    sourceFolders = [sourceFolders];
  }
  
  console.log(`🔍 Scanning ${sourceFolders.length} folder(s):`);
  sourceFolders.forEach(folder => console.log(`   📂 ${folder}`));
  
  let allFiles = [];
  
  // Duyệt qua từng folder
  for (const sourceFolder of sourceFolders) {
    // Kiểm tra source folder tồn tại
    if (!fs.existsSync(sourceFolder)) {
      console.error(`❌ Error: Source folder "${sourceFolder}" does not exist!`);
      process.exit(1);
    }
    
    const stat = fs.statSync(sourceFolder);
    if (!stat.isDirectory()) {
      console.error(`❌ Error: "${sourceFolder}" is not a directory!`);
      process.exit(1);
    }
    
    // Lấy danh sách tất cả file từ folder này
    const files = getAllFiles(sourceFolder);
    console.log(`📁 Found ${files.length} files in ${sourceFolder}`);
    
    // Thêm thông tin source folder vào mỗi file
    const filesWithSource = files.map(file => ({
      ...file,
      sourceFolder: sourceFolder,
      // Cập nhật relativePath để bao gồm tên folder gốc
      relativePath: path.join(path.basename(sourceFolder), file.relativePath)
    }));
    
    allFiles = allFiles.concat(filesWithSource);
  }
  
  if (allFiles.length === 0) {
    console.log(`⚠️  No files found in any source folders`);
    return;
  }
  
  console.log(`📁 Total: ${allFiles.length} files from ${sourceFolders.length} folder(s)`);
  
  // Sắp xếp file theo đường dẫn
  allFiles.sort((a, b) => a.relativePath.localeCompare(b.relativePath));
  
  // Tạo nội dung output
  let outputContent = '';
  
  // Header thông tin tổng quan
  const timestamp = new Date().toISOString();
  const totalSize = allFiles.reduce((sum, file) => sum + file.size, 0);
  
  outputContent += [
    '/*',
    ' * COMBINED FILES OUTPUT',
    ` * Generated: ${timestamp}`,
    ` * Sources: ${sourceFolders.map(f => path.resolve(f)).join(', ')}`,
    ` * Total files: ${allFiles.length}`,
    ` * Total size: ${formatFileSize(totalSize)}`,
    ' */',
    '',
    '/* TABLE OF CONTENTS */',
    ...allFiles.map((file, index) => ` * ${index + 1}. ${file.relativePath}`),
    '',
    '/* FILES CONTENT */',
    ''
  ].join('\n');
  
  // Nội dung từng file
  console.log(`📝 Processing files...`);
  
  for (let i = 0; i < allFiles.length; i++) {
    const file = allFiles[i];
    
    // Hiển thị progress
    const progress = Math.round(((i + 1) / allFiles.length) * 100);
    process.stdout.write(`\r   Progress: ${progress}% (${i + 1}/${allFiles.length}) ${file.relativePath}`);
    
    // Thêm separator và header
    outputContent += createFileSeparator(file, i, allFiles.length);
    
    // Thêm nội dung file
    const content = readFileContent(file.fullPath);
    outputContent += content;
    
    // Thêm newlines cuối file
    outputContent += '\n\n';
  }
  
  console.log(); // New line after progress
  
  // Ghi file output
  console.log(`💾 Writing to: ${outputFile}`);
  
  try {
    fs.writeFileSync(outputFile, outputContent, 'utf-8');
    
    const outputStats = fs.statSync(outputFile);
    console.log(`✅ Successfully created combined file!`);
    console.log(`   📄 Output: ${path.resolve(outputFile)}`);
    console.log(`   📊 Size: ${formatFileSize(outputStats.size)}`);
    console.log(`   📁 Files: ${allFiles.length}`);
    
  } catch (error) {
    console.error(`❌ Error writing output file:`, error.message);
    process.exit(1);
  }
}

/**
 * Parse command line arguments
 */
function parseArguments(args) {
  const sourceFolders = [];
  let outputFile = DEFAULT_OUTPUT;
  
  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--output' || args[i] === '-o') {
      if (i + 1 < args.length) {
        outputFile = args[i + 1];
        i++; // Skip next argument as it's the output file
      }
    } else if (!args[i].startsWith('-')) {
      sourceFolders.push(args[i]);
    }
  }
  
  return { sourceFolders, outputFile };
}

/**
 * Hiển thị help
 */
function showHelp() {
  console.log(`
📋 COMBINE FILES SCRIPT

Usage:
  node combine-files.js <source-folder1> [source-folder2] ... [--output output-file]

Arguments:
  source-folder1   Path to first folder containing files to combine (required)
  source-folder2   Additional folders to include (optional)
  --output, -o     Output file name (optional, default: ${DEFAULT_OUTPUT})

Examples:
  node combine-files.js ./src
  node combine-files.js ./src ./shared --output combined-output.txt
  node combine-files.js ./shared/components ./apps/web/src -o web-components.txt
  node combine-files.js ./src ./shared ./apps --output all-project-files.txt

Features:
  ✅ Support multiple source folders
  ✅ Recursively scans all subdirectories
  ✅ Ignores common build/dependency folders (node_modules, dist, etc.)
  ✅ Handles binary files gracefully 
  ✅ Shows file paths, sizes, and modification dates
  ✅ Includes table of contents
  ✅ Progress indicator
  ✅ Human-readable file sizes
  ✅ Prefixes files with source folder name for clarity

Ignored patterns:
  • node_modules, .git, dist, build folders
  • .DS_Store, .env, log files
  • package-lock.json, yarn.lock files
  • Binary files (images, videos, executables, etc.)
`);
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
  }
  
  const { sourceFolders, outputFile } = parseArguments(args);
  
  if (sourceFolders.length === 0) {
    console.error('❌ Error: At least one source folder is required!');
    showHelp();
    process.exit(1);
  }
  
  console.log(`🚀 Starting combine files process...`);
  combineFiles(sourceFolders, outputFile);
}

// Chạy script
if (require.main === module) {
  main();
}

module.exports = { combineFiles, getAllFiles };