import { chromium, FullConfig } from '@playwright/test';
import { loginUser } from './helpers/auth';

async function globalSetup(config: FullConfig) {
  console.log('🔐 Setting up global authentication...');
  
  // Get base URL from config
  const baseURL = config.projects[0]?.use?.baseURL || 'http://localhost:9070';
  
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {    
    // Attempt login with proper baseURL
    await loginUser(page, undefined, baseURL);
    console.log('✅ Login successful, saving authentication state');
    
    // Save signed-in state to file
    await page.context().storageState({ path: 'e2e/auth-state.json' });
    
  } catch (error) {
    console.log('⚠️ Login failed, creating mock authentication state');
    
    // Navigate to homepage and set mock auth
    await page.goto(`${baseURL}/`);
    await page.evaluate(() => {
      localStorage.setItem('token', 'test-token');
      localStorage.setItem('authToken', 'test-token'); 
      localStorage.setItem('user', JSON.stringify({ 
        id: 1, 
        name: 'Test User', 
        mobile: '0356432507' 
      }));
    });
    
    // Save mock auth state
    await page.context().storageState({ path: 'e2e/auth-state.json' });
    console.log('✅ Mock authentication state saved');
  }
  
  await browser.close();
  console.log('🎯 Global setup completed');
}

export default globalSetup;