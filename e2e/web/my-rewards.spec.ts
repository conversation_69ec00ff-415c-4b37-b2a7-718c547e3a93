import { test, expect } from '@playwright/test';

test.describe('My Rewards Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to my rewards page - authentication is already set up globally
    await page.goto('/my-rewards');
    await page.waitForLoadState('networkidle');
  });

  test('should load my rewards page', async ({ page }) => {
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check page title or main heading - use specific selector to avoid conflicts
    const pageTitle = page.locator('[data-testid="page-title"]');
    await expect(pageTitle).toBeVisible();
  });

  test('should display rewards list or empty state', async ({ page }) => {
    // Wait for content to load
    await page.waitForLoadState('networkidle');
    
    // Check for either rewards list or empty state message
    const rewardsList = page.locator('[data-testid="rewards-list"], .rewards-list');
    const emptyState = page.locator('[data-testid="empty-state"], .empty-state');
    
    // One of these should be visible
    await expect(
      rewardsList.or(emptyState)
    ).toBeVisible();
  });

  test('should handle loading states properly', async ({ page }) => {
    // Navigate to page and check for loading indicators
    await page.goto('/my-rewards');
    
    // Loading spinner or skeleton should appear first
    const loadingIndicator = page.locator(
      '[data-testid="loading"], .loading, .skeleton, .spinner'
    );
    
    // Wait for content to load
    await page.waitForLoadState('networkidle');
    
    // Loading indicator should disappear
    await expect(loadingIndicator).not.toBeVisible();
  });

  test('should be accessible on mobile viewport', async ({ page }) => {
    // Set mobile viewport (matching project's mobile-first approach)
    await page.setViewportSize({ width: 414, height: 896 });
    
    // Check if content is properly displayed on mobile - use specific selector
    const mainContent = page.locator('main').first();
    await expect(mainContent).toBeVisible();
    
    // Verify no horizontal scrolling issues
    const scrollWidth = await page.evaluate(() => document.documentElement.scrollWidth);
    const clientWidth = await page.evaluate(() => document.documentElement.clientWidth);
    expect(scrollWidth).toBeLessThanOrEqual(clientWidth + 1);
  });
});