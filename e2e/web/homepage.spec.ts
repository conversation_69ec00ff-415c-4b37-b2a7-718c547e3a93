import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to homepage - authentication is already set up globally
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should load homepage successfully', async ({ page }) => {
    // Check if the page loads without errors
    await expect(page).toHaveTitle(/TapTap/i);
    
    // Wait for the main content to be visible
    await page.waitForSelector('body', { state: 'visible' });
  });

  test('should display main navigation', async ({ page }) => {
    // Wait for page to load fully first
    await page.waitForLoadState('networkidle');
    
    // Wait for authentication to complete and page to render
    await page.waitForTimeout(1000);
    
    // Check for bottom navigation (common in mobile-first design)
    const navigation = page.locator('nav');
    await expect(navigation).toBeVisible({ timeout: 10000 });
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 414, height: 896 });
    
    // Check if main content is visible and properly sized - use specific selector
    const mainContent = page.locator('main');
    await expect(mainContent).toBeVisible();
    
    // Verify no horizontal scrolling
    const scrollWidth = await page.evaluate(() => document.documentElement.scrollWidth);
    const clientWidth = await page.evaluate(() => document.documentElement.clientWidth);
    expect(scrollWidth).toBeLessThanOrEqual(clientWidth + 1); // Allow 1px tolerance
  });
});