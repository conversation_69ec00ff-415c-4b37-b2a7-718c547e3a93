import { test, expect } from '@playwright/test';
import { waitForPageLoad, mockApiResponse, clickAndWait, checkForErrors } from '../helpers/page-helpers';

test.describe('Rewards System Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await waitForPageLoad(page);
  });

  test.describe('Exchange Page', () => {
    test('should display reward categories and items', async ({ page }) => {
      await page.goto('/exchange');
      await waitForPageLoad(page);

      // Check page title or header
      const pageHeader = page.locator('h1, h2, .page-title, [data-testid="page-title"]');
      const hasHeader = await pageHeader.isVisible().catch(() => false);
      if (hasHeader) {
        await expect(pageHeader.first()).toBeVisible();
      }

      // Check for reward categories
      const categories = page.locator('.category, [data-testid*="category"], .reward-category');
      const categoryCount = await categories.count();
      
      if (categoryCount > 0) {
        await expect(categories.first()).toBeVisible();
      }

      // Check for reward items
      const rewards = page.locator('.reward-card, [data-testid*="reward"], .card');
      const rewardCount = await rewards.count();
      
      if (rewardCount > 0) {
        await expect(rewards.first()).toBeVisible();
      } else {
        // Check for empty state
        const emptyState = page.locator('.empty-state, [data-testid="empty"], .no-data');
        const hasEmptyState = await emptyState.isVisible().catch(() => false);
        
        if (hasEmptyState) {
          await expect(emptyState.first()).toBeVisible();
        }
      }
    });

    test('should filter rewards by category', async ({ page }) => {
      await page.goto('/exchange');
      await waitForPageLoad(page);

      // Look for category filters
      const categoryFilters = page.locator('.category-filter, .filter-btn, [data-testid*="category"]');
      const filterCount = await categoryFilters.count();

      if (filterCount > 1) {
        // Click on a category filter
        const secondCategory = categoryFilters.nth(1);
        await secondCategory.click();
        await waitForPageLoad(page);

        // Verify URL or content changed
        const currentUrl = page.url();
        const hasUrlChange = currentUrl.includes('category') || currentUrl.includes('filter');
        
        if (hasUrlChange) {
          expect(currentUrl).toMatch(/category|filter/);
        }
      }
    });

    test('should search for rewards', async ({ page }) => {
      await page.goto('/exchange');
      await waitForPageLoad(page);

      // Look for search input
      const searchInput = page.locator('input[type="search"], input[placeholder*="tìm"], input[placeholder*="search"]');
      const hasSearch = await searchInput.isVisible().catch(() => false);

      if (hasSearch) {
        await searchInput.fill('test');
        await page.keyboard.press('Enter');
        await waitForPageLoad(page);

        // Results should update or show search results page
        const currentUrl = page.url();
        const hasSearchResults = currentUrl.includes('search') || currentUrl.includes('q=');
        
        if (hasSearchResults) {
          expect(currentUrl).toMatch(/search|q=/);
        }
      }
    });
  });

  test.describe('Reward Detail Page', () => {
    test('should display reward information', async ({ page }) => {
      // Mock reward detail API
      await mockApiResponse(page, /\/api.*\/rewards?\/\d+/, {
        data: {
          id: '123',
          name: 'Test Reward',
          description: 'Test reward description',
          points: 1000,
          category: 'Test Category',
          images: ['test-image.jpg'],
          brand: { name: 'Test Brand', logo: 'brand-logo.jpg' }
        }
      });

      await page.goto('/rewards/123');
      await waitForPageLoad(page);

      // Check reward details
      const rewardTitle = page.locator('h1, .reward-title, [data-testid="reward-title"]');
      await expect(rewardTitle.first()).toBeVisible();

      // Check for reward image
      const rewardImage = page.locator('img[src*="image"], .reward-image, [data-testid="reward-image"]');
      const hasImage = await rewardImage.isVisible().catch(() => false);
      if (hasImage) {
        await expect(rewardImage.first()).toBeVisible();
      }

      // Check for exchange button
      const exchangeBtn = page.locator('button:has-text("Đổi"), button:has-text("Exchange"), .exchange-btn');
      const hasExchangeBtn = await exchangeBtn.isVisible().catch(() => false);
      if (hasExchangeBtn) {
        await expect(exchangeBtn.first()).toBeVisible();
      }
    });

    test('should handle reward exchange', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/rewards\/\d+\/exchange/, {
        data: { success: true, voucherCode: 'TEST123' }
      });

      await page.goto('/rewards/123');
      await waitForPageLoad(page);

      const exchangeBtn = page.locator('button:has-text("Đổi"), button:has-text("Exchange"), .exchange-btn');
      const hasExchangeBtn = await exchangeBtn.isVisible().catch(() => false);

      if (hasExchangeBtn) {
        await exchangeBtn.click();
        await waitForPageLoad(page);

        // Check for success message or voucher code
        const successElements = [
          '.success-message',
          '.voucher-code', 
          'text="TEST123"',
          '[data-testid="success"]'
        ];

        let hasSuccess = false;
        for (const selector of successElements) {
          const element = page.locator(selector);
          if (await element.isVisible().catch(() => false)) {
            hasSuccess = true;
            break;
          }
        }

        if (!hasSuccess) {
          // Check if redirected to a success page
          const currentUrl = page.url();
          expect(currentUrl).toMatch(/success|voucher|my-rewards/);
        }
      }
    });

    test('should handle insufficient points', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/rewards\/\d+\/exchange/, {
        status: 400,
        data: { error: 'Insufficient points' }
      });

      await page.goto('/rewards/123');
      await waitForPageLoad(page);

      const exchangeBtn = page.locator('button:has-text("Đổi"), button:has-text("Exchange"), .exchange-btn');
      const hasExchangeBtn = await exchangeBtn.isVisible().catch(() => false);

      if (hasExchangeBtn) {
        await exchangeBtn.click();
        await waitForPageLoad(page);

        // Should show error message
        const errorMessage = await checkForErrors(page);
        expect(errorMessage).toBeTruthy();
      }
    });
  });

  test.describe('My Rewards Page', () => {
    test('should display user rewards', async ({ page }) => {
      // Mock user rewards API
      await mockApiResponse(page, /\/api.*\/rewards.*\/my/, {
        data: [
          {
            id: '1',
            name: 'User Reward 1',
            status: 'active',
            expiryDate: '2024-12-31'
          },
          {
            id: '2', 
            name: 'User Reward 2',
            status: 'used',
            expiryDate: '2024-12-31'
          }
        ]
      });

      await page.goto('/my-rewards');
      await waitForPageLoad(page);

      // Check for rewards list
      const rewardsList = page.locator('.rewards-list, .reward-item, .my-reward-card');
      const hasRewards = await rewardsList.first().isVisible().catch(() => false);

      if (hasRewards) {
        const rewardCount = await rewardsList.count();
        expect(rewardCount).toBeGreaterThan(0);
      } else {
        // Check for empty state
        const emptyState = page.locator('.empty-state, [data-testid="empty"]');
        await expect(emptyState.first()).toBeVisible();
      }
    });

    test('should filter rewards by status', async ({ page }) => {
      await page.goto('/my-rewards');
      await waitForPageLoad(page);

      // Look for filter tabs
      const filterTabs = page.locator('.tab, .filter-tab, [role="tab"]');
      const tabCount = await filterTabs.count();

      if (tabCount > 1) {
        // Click on different status filter
        const usedTab = page.locator('text="Đã sử dụng", text="Used", text="Expired"').first();
        const hasUsedTab = await usedTab.isVisible().catch(() => false);

        if (hasUsedTab) {
          await usedTab.click();
          await waitForPageLoad(page);

          // Content should update
          const currentUrl = page.url();
          const hasStatusFilter = currentUrl.includes('status') || currentUrl.includes('filter');
          
          if (hasStatusFilter) {
            expect(currentUrl).toMatch(/status|filter|used|expired/);
          }
        }
      }
    });

    test('should navigate to reward detail', async ({ page }) => {
      await page.goto('/my-rewards');
      await waitForPageLoad(page);

      const firstReward = page.locator('.reward-item, .my-reward-card, .card').first();
      const hasRewards = await firstReward.isVisible().catch(() => false);

      if (hasRewards) {
        await firstReward.click();
        await waitForPageLoad(page);

        // Should navigate to reward detail
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/reward|detail/);
      }
    });
  });

  test.describe('Reward Categories', () => {
    test('should display category collection page', async ({ page }) => {
      await page.goto('/exchange/super-deals');
      await waitForPageLoad(page);

      // Check for category content
      const content = page.locator('.category-content, .collection-content, .deals-content');
      const hasContent = await content.first().isVisible().catch(() => false);

      if (hasContent) {
        await expect(content.first()).toBeVisible();
      }

      // Check for rewards in category
      const rewards = page.locator('.reward-card, .deal-card, .card');
      const rewardCount = await rewards.count();

      if (rewardCount > 0) {
        await expect(rewards.first()).toBeVisible();
      }
    });

    test('should navigate between categories', async ({ page }) => {
      await page.goto('/exchange');
      await waitForPageLoad(page);

      const categoryLinks = page.locator('a[href*="category"], a[href*="collection"]');
      const linkCount = await categoryLinks.count();

      if (linkCount > 0) {
        const firstCategory = categoryLinks.first();
        const categoryHref = await firstCategory.getAttribute('href');
        
        await firstCategory.click();
        await waitForPageLoad(page);

        if (categoryHref) {
          expect(page.url()).toContain(categoryHref);
        }
      }
    });
  });

  test.describe('Favorites', () => {
    test('should add reward to favorites', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/favorites/, {
        data: { success: true }
      });

      await page.goto('/rewards/123');
      await waitForPageLoad(page);

      const favoriteBtn = page.locator('button[aria-label*="favorite"], .favorite-btn, .heart-btn');
      const hasFavoriteBtn = await favoriteBtn.isVisible().catch(() => false);

      if (hasFavoriteBtn) {
        await favoriteBtn.click();
        await waitForPageLoad(page);

        // Button state should change or show success message
        const isActive = await favoriteBtn.evaluate(btn => 
          btn.classList.contains('active') || 
          btn.classList.contains('favorited') ||
          btn.getAttribute('aria-pressed') === 'true'
        );

        expect(isActive).toBeTruthy();
      }
    });

    test('should display favorites page', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/favorites/, {
        data: [
          { id: '1', name: 'Favorite Reward 1' },
          { id: '2', name: 'Favorite Reward 2' }
        ]
      });

      await page.goto('/favorites');
      await waitForPageLoad(page);

      const favoritesList = page.locator('.favorites-list, .favorite-item, .card');
      const hasItems = await favoritesList.first().isVisible().catch(() => false);

      if (hasItems) {
        const itemCount = await favoritesList.count();
        expect(itemCount).toBeGreaterThan(0);
      } else {
        const emptyState = page.locator('.empty-state, [data-testid="empty"]');
        await expect(emptyState.first()).toBeVisible();
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/rewards/, {
        status: 500,
        data: { error: 'Internal server error' }
      });

      await page.goto('/exchange');
      await waitForPageLoad(page);

      // Should show error state
      const errorState = page.locator('.error-state, .error-message, [data-testid="error"]');
      const hasError = await errorState.isVisible().catch(() => false);

      if (hasError) {
        await expect(errorState.first()).toBeVisible();
      }
    });

    test('should handle network errors', async ({ page }) => {
      // Simulate network failure
      await page.route(/\/api.*\/rewards/, route => route.abort());

      await page.goto('/exchange');
      await waitForPageLoad(page);

      // Should show network error or retry option
      const networkError = page.locator('text="Network error", text="No connection", .network-error');
      const retryBtn = page.locator('button:has-text("Retry"), button:has-text("Thử lại")');

      const hasNetworkError = await networkError.first().isVisible().catch(() => false);
      const hasRetryBtn = await retryBtn.first().isVisible().catch(() => false);

      expect(hasNetworkError || hasRetryBtn).toBeTruthy();
    });

    test('should handle non-existent rewards', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/rewards\/999/, {
        status: 404,
        data: { error: 'Reward not found' }
      });

      await page.goto('/rewards/999');
      await waitForPageLoad(page);

      // Should show 404 or not found message
      const notFound = page.locator('text="404", text="Not found", text="Không tìm thấy"');
      await expect(notFound.first()).toBeVisible();
    });
  });
});