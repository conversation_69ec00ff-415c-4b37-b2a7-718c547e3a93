import { test, expect } from '@playwright/test';
import { waitForPageLoad, mockApiResponse, handleDialog, checkForErrors } from '../helpers/page-helpers';

test.describe('Bill <PERSON>ing System Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await waitForPageLoad(page);
  });

  test.describe('Camera Page', () => {
    test('should request camera permission', async ({ page }) => {
      // Grant camera permissions
      const context = page.context();
      await context.grantPermissions(['camera']);

      await page.goto('/bill-scan/camera');
      await waitForPageLoad(page);

      // Check for camera interface
      const cameraElements = [
        'video',
        '.camera-preview',
        '[data-testid="camera"]',
        '.camera-container'
      ];

      let hasCameraInterface = false;
      for (const selector of cameraElements) {
        const element = page.locator(selector);
        if (await element.isVisible().catch(() => false)) {
          hasCameraInterface = true;
          break;
        }
      }

      // Should have camera interface or permission request
      expect(hasCameraInterface).toBeTruthy();
    });

    test('should handle camera permission denied', async ({ page }) => {
      // Deny camera permissions
      const context = page.context();
      await context.clearPermissions();

      await page.goto('/bill-scan/camera');
      await waitForPageLoad(page);

      // Should show permission denied message or fallback
      const permissionElements = [
        'text="Camera permission required"',
        'text="Cần quyền truy cập camera"', 
        '.permission-denied',
        '.camera-error'
      ];

      let hasPermissionMessage = false;
      for (const selector of permissionElements) {
        const element = page.locator(selector);
        if (await element.isVisible().catch(() => false)) {
          hasPermissionMessage = true;
          break;
        }
      }

      if (!hasPermissionMessage) {
        // Check for file upload fallback
        const fileInput = page.locator('input[type="file"]');
        const hasFileInput = await fileInput.isVisible().catch(() => false);
        expect(hasFileInput).toBeTruthy();
      }
    });

    test('should display camera controls', async ({ page }) => {
      const context = page.context();
      await context.grantPermissions(['camera']);

      await page.goto('/bill-scan/camera');
      await waitForPageLoad(page);

      // Check for capture button
      const captureBtn = page.locator('button:has-text("Capture"), button:has-text("Chụp"), .capture-btn, .shutter-btn');
      const hasCaptureBtn = await captureBtn.first().isVisible().catch(() => false);

      if (hasCaptureBtn) {
        await expect(captureBtn.first()).toBeVisible();
      }

      // Check for flash toggle
      const flashBtn = page.locator('button[aria-label*="flash"], .flash-btn, .flash-toggle');
      const hasFlashBtn = await flashBtn.first().isVisible().catch(() => false);

      if (hasFlashBtn) {
        await expect(flashBtn.first()).toBeVisible();
      }

      // Check for camera switch (front/back)
      const switchBtn = page.locator('button[aria-label*="switch"], .camera-switch, .flip-camera');
      const hasSwitchBtn = await switchBtn.first().isVisible().catch(() => false);

      if (hasSwitchBtn) {
        await expect(switchBtn.first()).toBeVisible();
      }
    });

    test('should capture and process receipt image', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/bills.*\/upload/, {
        data: {
          success: true,
          billId: 'bill123',
          processedData: {
            merchant: 'Test Store',
            amount: 150000,
            date: '2024-01-15'
          }
        }
      });

      const context = page.context();
      await context.grantPermissions(['camera']);

      await page.goto('/bill-scan/camera');
      await waitForPageLoad(page);

      const captureBtn = page.locator('button:has-text("Capture"), button:has-text("Chụp"), .capture-btn');
      const hasCaptureBtn = await captureBtn.first().isVisible().catch(() => false);

      if (hasCaptureBtn) {
        await captureBtn.first().click();
        await waitForPageLoad(page);

        // Should show preview or processing state
        const previewElements = [
          '.image-preview',
          '.captured-image',
          'img[src*="blob"]',
          '.bill-preview'
        ];

        let hasPreview = false;
        for (const selector of previewElements) {
          const element = page.locator(selector);
          if (await element.isVisible().catch(() => false)) {
            hasPreview = true;
            break;
          }
        }

        if (hasPreview) {
          // Check for submit/confirm button
          const submitBtn = page.locator('button:has-text("Submit"), button:has-text("Gửi"), button:has-text("Confirm")');
          const hasSubmitBtn = await submitBtn.first().isVisible().catch(() => false);
          
          if (hasSubmitBtn) {
            await submitBtn.first().click();
            await waitForPageLoad(page);

            // Should navigate to success page or show success message
            const currentUrl = page.url();
            const isSuccessPage = currentUrl.includes('success') || 
                                 currentUrl.includes('result') || 
                                 currentUrl.includes('/bills');
            
            expect(isSuccessPage).toBeTruthy();
          }
        }
      }
    });

    test('should handle file upload fallback', async ({ page }) => {
      await page.goto('/bill-scan/camera');
      await waitForPageLoad(page);

      // Look for file upload option
      const fileUploadBtn = page.locator('button:has-text("Upload"), button:has-text("Tải lên"), .upload-btn');
      const fileInput = page.locator('input[type="file"]');

      const hasUploadBtn = await fileUploadBtn.first().isVisible().catch(() => false);
      const hasFileInput = await fileInput.first().isVisible().catch(() => false);

      if (hasUploadBtn) {
        await fileUploadBtn.first().click();
        await waitForPageLoad(page);
      }

      if (hasFileInput) {
        // Simulate file selection
        const filePath = 'test-receipt.jpg';
        await fileInput.first().setInputFiles({
          name: 'receipt.jpg',
          mimeType: 'image/jpeg',
          buffer: Buffer.from('fake-image-data')
        });

        await waitForPageLoad(page);

        // Should show preview or upload progress
        const uploadProgress = page.locator('.upload-progress, .uploading, .progress');
        const hasProgress = await uploadProgress.first().isVisible().catch(() => false);
        
        if (hasProgress) {
          await expect(uploadProgress.first()).toBeVisible();
        }
      }
    });
  });

  test.describe('Bill List Page', () => {
    test('should display list of scanned bills', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/bills/, {
        data: [
          {
            id: 'bill1',
            merchantName: 'Coffee Shop',
            amount: 45000,
            scanDate: '2024-01-15T10:30:00Z',
            status: 'processed',
            points: 45
          },
          {
            id: 'bill2', 
            merchantName: 'Restaurant',
            amount: 150000,
            scanDate: '2024-01-14T19:20:00Z',
            status: 'pending',
            points: 0
          }
        ]
      });

      await page.goto('/bill-scan');
      await waitForPageLoad(page);

      // Check for bills list
      const billItems = page.locator('.bill-item, .bill-card, [data-testid*="bill"]');
      const billCount = await billItems.count();

      if (billCount > 0) {
        await expect(billItems.first()).toBeVisible();

        // Check bill details
        const merchantNames = page.locator('.merchant-name, .store-name');
        const hasNames = await merchantNames.first().isVisible().catch(() => false);
        if (hasNames) {
          await expect(merchantNames.first()).toBeVisible();
        }

        // Check amounts
        const amounts = page.locator('.amount, .bill-amount');
        const hasAmounts = await amounts.first().isVisible().catch(() => false);
        if (hasAmounts) {
          await expect(amounts.first()).toBeVisible();
        }

        // Check status indicators
        const statusElements = page.locator('.status, .bill-status');
        const hasStatus = await statusElements.first().isVisible().catch(() => false);
        if (hasStatus) {
          await expect(statusElements.first()).toBeVisible();
        }
      } else {
        // Check for empty state
        const emptyState = page.locator('.empty-state, [data-testid="empty"]');
        await expect(emptyState.first()).toBeVisible();
      }
    });

    test('should filter bills by status', async ({ page }) => {
      await page.goto('/bill-scan');
      await waitForPageLoad(page);

      // Look for status filter tabs
      const filterTabs = page.locator('.filter-tab, .status-filter, [role="tab"]');
      const hasFilters = await filterTabs.first().isVisible().catch(() => false);

      if (hasFilters && await filterTabs.count() > 1) {
        const pendingTab = page.locator('text="Pending", text="Đang xử lý", text="Chờ duyệt"').first();
        const hasPendingTab = await pendingTab.isVisible().catch(() => false);

        if (hasPendingTab) {
          await pendingTab.click();
          await waitForPageLoad(page);

          // Should filter results
          const currentUrl = page.url();
          const hasStatusFilter = currentUrl.includes('status') || currentUrl.includes('filter');
          
          if (hasStatusFilter) {
            expect(currentUrl).toMatch(/status|filter|pending/);
          }
        }
      }
    });

    test('should navigate to bill detail', async ({ page }) => {
      await page.goto('/bill-scan');
      await waitForPageLoad(page);

      const firstBill = page.locator('.bill-item, .bill-card').first();
      const hasBills = await firstBill.isVisible().catch(() => false);

      if (hasBills) {
        await firstBill.click();
        await waitForPageLoad(page);

        // Should navigate to bill detail
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/bill-scan\/\w+|bill.*detail/);
      }
    });

    test('should show scan new bill button', async ({ page }) => {
      await page.goto('/bill-scan');
      await waitForPageLoad(page);

      const scanBtn = page.locator('button:has-text("Scan"), button:has-text("Quét"), .scan-btn, .add-bill-btn');
      const hasScanBtn = await scanBtn.first().isVisible().catch(() => false);

      if (hasScanBtn) {
        await expect(scanBtn.first()).toBeVisible();

        // Click should navigate to camera
        await scanBtn.first().click();
        await waitForPageLoad(page);

        const currentUrl = page.url();
        expect(currentUrl).toContain('camera');
      }
    });
  });

  test.describe('Bill Detail Page', () => {
    test('should display bill information', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/bills\/\w+/, {
        data: {
          id: 'bill123',
          merchantName: 'Test Restaurant',
          merchantAddress: '123 Test Street',
          amount: 250000,
          tax: 25000,
          scanDate: '2024-01-15T14:30:00Z',
          status: 'approved',
          points: 250,
          receiptImage: 'receipt123.jpg',
          extractedData: {
            items: [
              { name: 'Coffee', price: 50000, quantity: 2 },
              { name: 'Sandwich', price: 150000, quantity: 1 }
            ]
          }
        }
      });

      await page.goto('/bill-scan/bill123');
      await waitForPageLoad(page);

      // Check bill details
      const merchantName = page.locator('.merchant-name, h1, .store-name');
      await expect(merchantName.first()).toBeVisible();

      // Check amount
      const amount = page.locator('.total-amount, .amount');
      const hasAmount = await amount.first().isVisible().catch(() => false);
      if (hasAmount) {
        await expect(amount.first()).toBeVisible();
      }

      // Check receipt image
      const receiptImage = page.locator('img[src*="receipt"], .receipt-image');
      const hasImage = await receiptImage.first().isVisible().catch(() => false);
      if (hasImage) {
        await expect(receiptImage.first()).toBeVisible();
      }

      // Check status
      const status = page.locator('.bill-status, .status');
      const hasStatus = await status.first().isVisible().catch(() => false);
      if (hasStatus) {
        await expect(status.first()).toBeVisible();
      }

      // Check points earned
      const points = page.locator('.points-earned, .points');
      const hasPoints = await points.first().isVisible().catch(() => false);
      if (hasPoints) {
        await expect(points.first()).toBeVisible();
      }
    });

    test('should display extracted bill items', async ({ page }) => {
      await page.goto('/bill-scan/bill123');
      await waitForPageLoad(page);

      // Check for items list
      const itemsList = page.locator('.bill-items, .items-list, .extracted-items');
      const hasItems = await itemsList.first().isVisible().catch(() => false);

      if (hasItems) {
        const items = page.locator('.item-row, .bill-item-row');
        const itemCount = await items.count();
        
        if (itemCount > 0) {
          await expect(items.first()).toBeVisible();

          // Check item details
          const itemNames = page.locator('.item-name, .product-name');
          const hasPrices = await itemNames.first().isVisible().catch(() => false);
          if (hasPrices) {
            await expect(itemNames.first()).toBeVisible();
          }
        }
      }
    });

    test('should handle bill editing or correction', async ({ page }) => {
      await page.goto('/bill-scan/bill123');
      await waitForPageLoad(page);

      // Look for edit button
      const editBtn = page.locator('button:has-text("Edit"), button:has-text("Sửa"), .edit-btn');
      const hasEditBtn = await editBtn.first().isVisible().catch(() => false);

      if (hasEditBtn) {
        await editBtn.first().click();
        await waitForPageLoad(page);

        // Should show edit form
        const editForm = page.locator('form, .edit-form, .bill-edit');
        const hasForm = await editForm.first().isVisible().catch(() => false);
        
        if (hasForm) {
          await expect(editForm.first()).toBeVisible();

          // Check for editable fields
          const editableFields = page.locator('input[type="text"], input[type="number"], textarea');
          const fieldCount = await editableFields.count();
          expect(fieldCount).toBeGreaterThan(0);
        }
      }
    });

    test('should handle bill deletion', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/bills\/\w+/, {
        method: 'DELETE',
        data: { success: true }
      });

      await page.goto('/bill-scan/bill123');
      await waitForPageLoad(page);

      const deleteBtn = page.locator('button:has-text("Delete"), button:has-text("Xóa"), .delete-btn');
      const hasDeleteBtn = await deleteBtn.first().isVisible().catch(() => false);

      if (hasDeleteBtn) {
        // Handle confirmation dialog
        handleDialog(page, 'accept', 'delete');

        await deleteBtn.first().click();
        await waitForPageLoad(page);

        // Should redirect to bills list
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/bill-scan$|bills$/);
      }
    });
  });

  test.describe('Captured Receipts Gallery', () => {
    test('should display captured receipt images', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/bills.*\/captured/, {
        data: [
          {
            id: 'receipt1',
            imageUrl: 'receipt1.jpg',
            captureDate: '2024-01-15T10:30:00Z',
            status: 'processed'
          },
          {
            id: 'receipt2',
            imageUrl: 'receipt2.jpg', 
            captureDate: '2024-01-14T15:20:00Z',
            status: 'pending'
          }
        ]
      });

      await page.goto('/bill-scan/captured');
      await waitForPageLoad(page);

      // Check for image gallery
      const receiptImages = page.locator('.receipt-image, .captured-receipt, img[src*="receipt"]');
      const imageCount = await receiptImages.count();

      if (imageCount > 0) {
        await expect(receiptImages.first()).toBeVisible();

        // Check for image grid or list
        const gallery = page.locator('.image-gallery, .receipt-grid, .captured-list');
        const hasGallery = await gallery.first().isVisible().catch(() => false);
        
        if (hasGallery) {
          await expect(gallery.first()).toBeVisible();
        }
      } else {
        const emptyState = page.locator('.empty-state, [data-testid="empty"]');
        await expect(emptyState.first()).toBeVisible();
      }
    });

    test('should preview receipt image', async ({ page }) => {
      await page.goto('/bill-scan/captured');
      await waitForPageLoad(page);

      const firstImage = page.locator('.receipt-image, .captured-receipt').first();
      const hasImages = await firstImage.isVisible().catch(() => false);

      if (hasImages) {
        await firstImage.click();
        await waitForPageLoad(page);

        // Should show image preview modal or navigate to detail
        const modal = page.locator('.modal, .image-modal, .preview-modal');
        const hasModal = await modal.first().isVisible().catch(() => false);

        if (hasModal) {
          await expect(modal.first()).toBeVisible();

          // Check for close button
          const closeBtn = page.locator('.modal button:has-text("Close"), .close-btn, .modal-close');
          const hasCloseBtn = await closeBtn.first().isVisible().catch(() => false);
          
          if (hasCloseBtn) {
            await closeBtn.first().click();
            await page.waitForTimeout(500);
          }
        } else {
          // Should navigate to detail page
          const currentUrl = page.url();
          expect(currentUrl).toMatch(/receipt|detail|bill/);
        }
      }
    });
  });

  test.describe('Bill Scan Instructions', () => {
    test('should display scanning instructions', async ({ page }) => {
      await page.goto('/bill-scan/info');
      await waitForPageLoad(page);

      // Check for instruction content
      const instructions = page.locator('.instructions, .scan-guide, .how-to');
      const hasInstructions = await instructions.first().isVisible().catch(() => false);

      if (hasInstructions) {
        await expect(instructions.first()).toBeVisible();
      }

      // Check for instruction steps
      const steps = page.locator('.step, .instruction-step, li');
      const stepCount = await steps.count();
      
      if (stepCount > 0) {
        expect(stepCount).toBeGreaterThan(0);
      }

      // Check for example images
      const exampleImages = page.locator('img[src*="example"], .example-image');
      const hasExamples = await exampleImages.first().isVisible().catch(() => false);
      
      if (hasExamples) {
        await expect(exampleImages.first()).toBeVisible();
      }

      // Check for start scanning button
      const startBtn = page.locator('button:has-text("Start"), button:has-text("Bắt đầu"), .start-scan-btn');
      const hasStartBtn = await startBtn.first().isVisible().catch(() => false);
      
      if (hasStartBtn) {
        await startBtn.first().click();
        await waitForPageLoad(page);

        // Should navigate to camera
        const currentUrl = page.url();
        expect(currentUrl).toContain('camera');
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle OCR processing errors', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/bills.*\/upload/, {
        status: 422,
        data: { error: 'Unable to process receipt image' }
      });

      const context = page.context();
      await context.grantPermissions(['camera']);

      await page.goto('/bill-scan/camera');
      await waitForPageLoad(page);

      const captureBtn = page.locator('.capture-btn, button:has-text("Capture")');
      const hasCaptureBtn = await captureBtn.first().isVisible().catch(() => false);

      if (hasCaptureBtn) {
        await captureBtn.first().click();
        await waitForPageLoad(page);

        const submitBtn = page.locator('button:has-text("Submit"), button:has-text("Gửi")');
        const hasSubmitBtn = await submitBtn.first().isVisible().catch(() => false);
        
        if (hasSubmitBtn) {
          await submitBtn.first().click();
          await waitForPageLoad(page);

          // Should show error message
          const errorMessage = await checkForErrors(page);
          expect(errorMessage).toBeTruthy();
        }
      }
    });

    test('should handle network errors during upload', async ({ page }) => {
      await page.route(/\/api.*\/bills/, route => route.abort());

      const context = page.context();
      await context.grantPermissions(['camera']);

      await page.goto('/bill-scan/camera');
      await waitForPageLoad(page);

      // Try to upload
      const uploadBtn = page.locator('.upload-btn, button:has-text("Upload")');
      const hasUploadBtn = await uploadBtn.first().isVisible().catch(() => false);

      if (hasUploadBtn) {
        await uploadBtn.first().click();
        await waitForPageLoad(page);

        // Should show network error
        const networkError = page.locator('text="Network error", text="Upload failed"');
        const retryBtn = page.locator('button:has-text("Retry"), button:has-text("Thử lại")');

        const hasError = await networkError.first().isVisible().catch(() => false);
        const hasRetry = await retryBtn.first().isVisible().catch(() => false);

        expect(hasError || hasRetry).toBeTruthy();
      }
    });

    test('should handle unsupported file formats', async ({ page }) => {
      await page.goto('/bill-scan/camera');
      await waitForPageLoad(page);

      const fileInput = page.locator('input[type="file"]');
      const hasFileInput = await fileInput.first().isVisible().catch(() => false);

      if (hasFileInput) {
        // Try to upload unsupported file
        await fileInput.first().setInputFiles({
          name: 'document.txt',
          mimeType: 'text/plain',
          buffer: Buffer.from('not an image')
        });

        await waitForPageLoad(page);

        // Should show file format error
        const formatError = page.locator('text="Unsupported format", text="Định dạng không hỗ trợ"');
        const hasError = await formatError.first().isVisible().catch(() => false);
        
        if (hasError) {
          await expect(formatError.first()).toBeVisible();
        }
      }
    });
  });
});