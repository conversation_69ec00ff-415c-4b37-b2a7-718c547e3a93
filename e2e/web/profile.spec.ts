import { test, expect } from '@playwright/test';
import { waitForPageLoad, mockApiResponse, fillFormField, checkForErrors } from '../helpers/page-helpers';

test.describe('Profile Management Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await waitForPageLoad(page);
  });

  test.describe('Profile Page', () => {
    test('should display user profile information', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/profile/, {
        data: {
          id: '123',
          firstname: '<PERSON>',
          lastname: '<PERSON><PERSON>',
          mobile: '0356432501',
          email: '<EMAIL>',
          avatar: 'avatar.jpg',
          vuiCurrency: 1500,
          membershipTier: 'Gold',
          joinDate: '2024-01-01'
        }
      });

      await page.goto('/profile');
      await waitForPageLoad(page);

      // Check for user avatar
      const avatar = page.locator('.user-avatar, .profile-avatar, img[src*="avatar"]');
      const hasAvatar = await avatar.first().isVisible().catch(() => false);
      if (hasAvatar) {
        await expect(avatar.first()).toBeVisible();
      }

      // Check for user name
      const userName = page.locator('.user-name, .profile-name, h1, h2');
      await expect(userName.first()).toBeVisible();

      // Check for phone number
      const phoneNumber = page.locator('text="0356432501", .phone-number, .mobile');
      const hasPhone = await phoneNumber.first().isVisible().catch(() => false);
      if (hasPhone) {
        await expect(phoneNumber.first()).toBeVisible();
      }

      // Check for points/currency
      const points = page.locator('.points, .currency, .vui-currency');
      const hasPoints = await points.first().isVisible().catch(() => false);
      if (hasPoints) {
        await expect(points.first()).toBeVisible();
      }

      // Check for membership tier
      const tier = page.locator('.membership-tier, .tier, .level');
      const hasTier = await tier.first().isVisible().catch(() => false);
      if (hasTier) {
        await expect(tier.first()).toBeVisible();
      }
    });

    test('should display profile menu options', async ({ page }) => {
      await page.goto('/profile');
      await waitForPageLoad(page);

      const menuOptions = [
        'My Rewards',
        'Transaction History', 
        'Membership',
        'Point History',
        'Settings',
        'Contact',
        'Ưu đãi của tôi',
        'Lịch sử giao dịch',
        'Thành viên',
        'Liên hệ'
      ];

      let foundOptions = 0;
      for (const option of menuOptions) {
        const menuItem = page.locator(`text="${option}"`, { exact: false });
        if (await menuItem.first().isVisible().catch(() => false)) {
          foundOptions++;
        }
      }

      expect(foundOptions).toBeGreaterThan(0);
    });

    test('should navigate to profile submenu items', async ({ page }) => {
      await page.goto('/profile');
      await waitForPageLoad(page);

      // Test navigation to My Rewards
      const myRewardsLink = page.locator('text="My Rewards", text="Ưu đãi của tôi", a[href*="rewards"]');
      const hasMyRewards = await myRewardsLink.first().isVisible().catch(() => false);

      if (hasMyRewards) {
        await myRewardsLink.first().click();
        await waitForPageLoad(page);

        const currentUrl = page.url();
        expect(currentUrl).toMatch(/my-rewards|rewards/);

        // Go back to profile
        await page.goBack();
        await waitForPageLoad(page);
      }

      // Test navigation to Transaction History
      const transactionLink = page.locator('text="Transaction", text="Lịch sử", a[href*="transaction"]');
      const hasTransaction = await transactionLink.first().isVisible().catch(() => false);

      if (hasTransaction) {
        await transactionLink.first().click();
        await waitForPageLoad(page);

        const currentUrl = page.url();
        expect(currentUrl).toMatch(/transaction|history/);
      }
    });

    test('should display member code or QR code', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/member.*\/code/, {
        data: {
          memberCode: 'MB123456789',
          qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'
        }
      });

      await page.goto('/profile');
      await waitForPageLoad(page);

      // Look for member code section
      const memberCodeSection = page.locator('.member-code, .qr-code, [data-testid="member-code"]');
      const hasCodeSection = await memberCodeSection.first().isVisible().catch(() => false);

      if (hasCodeSection) {
        await expect(memberCodeSection.first()).toBeVisible();
      } else {
        // Look for navigation to member code page
        const memberCodeLink = page.locator('text="Member Code", text="Mã thành viên", a[href*="member"]');
        const hasMemberLink = await memberCodeLink.first().isVisible().catch(() => false);

        if (hasMemberLink) {
          await memberCodeLink.first().click();
          await waitForPageLoad(page);

          const currentUrl = page.url();
          expect(currentUrl).toMatch(/member|code/);

          // Should show QR code or barcode
          const qrCode = page.locator('img[src*="qr"], img[src*="barcode"], .qr-image, .member-qr');
          const hasQR = await qrCode.first().isVisible().catch(() => false);
          
          if (hasQR) {
            await expect(qrCode.first()).toBeVisible();
          }
        }
      }
    });
  });

  test.describe('Points and Currency', () => {
    test('should display points balance', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/point/, {
        data: {
          vuiCurrency: 2500,
          totalEarned: 5000,
          totalSpent: 2500,
          currencies: [
            { name: 'VUI Points', balance: 2500, icon: 'vui.png' },
            { name: 'Brand Points', balance: 150, icon: 'brand.png' }
          ]
        }
      });

      await page.goto('/point');
      await waitForPageLoad(page);

      // Check for main points balance
      const pointsBalance = page.locator('.points-balance, .currency-balance, .balance');
      const hasBalance = await pointsBalance.first().isVisible().catch(() => false);

      if (hasBalance) {
        await expect(pointsBalance.first()).toBeVisible();

        // Should show numeric value
        const balanceText = await pointsBalance.first().textContent();
        expect(balanceText).toMatch(/\d+/);
      }

      // Check for currency breakdown
      const currencyItems = page.locator('.currency-item, .points-item, .balance-item');
      const currencyCount = await currencyItems.count();

      if (currencyCount > 0) {
        await expect(currencyItems.first()).toBeVisible();
      }

      // Check for earnings/spending summary
      const summary = page.locator('.summary, .points-summary, .earnings-summary');
      const hasSummary = await summary.first().isVisible().catch(() => false);

      if (hasSummary) {
        const earnedText = page.locator('text="Earned", text="Đã kiếm", .total-earned');
        const spentText = page.locator('text="Spent", text="Đã tiêu", .total-spent');

        const hasEarned = await earnedText.first().isVisible().catch(() => false);
        const hasSpent = await spentText.first().isVisible().catch(() => false);

        expect(hasEarned || hasSpent).toBeTruthy();
      }
    });

    test('should display brand currency details', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/point\/brand/, {
        data: {
          brandName: 'Test Brand',
          balance: 250,
          exchangeRate: 1000,
          expiryDate: '2024-12-31',
          transactions: [
            { date: '2024-01-15', amount: 100, type: 'earned' },
            { date: '2024-01-10', amount: -50, type: 'spent' }
          ]
        }
      });

      await page.goto('/point/brand/test-brand');
      await waitForPageLoad(page);

      // Check brand information
      const brandName = page.locator('.brand-name, h1, h2');
      await expect(brandName.first()).toBeVisible();

      // Check brand currency balance
      const brandBalance = page.locator('.brand-balance, .currency-balance');
      const hasBalance = await brandBalance.first().isVisible().catch(() => false);
      if (hasBalance) {
        await expect(brandBalance.first()).toBeVisible();
      }

      // Check exchange rate
      const exchangeRate = page.locator('.exchange-rate, .rate');
      const hasRate = await exchangeRate.first().isVisible().catch(() => false);
      if (hasRate) {
        await expect(exchangeRate.first()).toBeVisible();
      }

      // Check transaction history
      const transactions = page.locator('.transaction-item, .transaction-row');
      const transactionCount = await transactions.count();

      if (transactionCount > 0) {
        await expect(transactions.first()).toBeVisible();
      }
    });

    test('should navigate to VUI points page', async ({ page }) => {
      await page.goto('/point/vui');
      await waitForPageLoad(page);

      // Should display VUI-specific points information
      const vuiPoints = page.locator('text="VUI", .vui-points, .vui-currency');
      const hasVuiPoints = await vuiPoints.first().isVisible().catch(() => false);

      if (hasVuiPoints) {
        await expect(vuiPoints.first()).toBeVisible();
      }

      // Check for VUI point history
      const pointHistory = page.locator('.point-history, .transaction-history');
      const hasHistory = await pointHistory.first().isVisible().catch(() => false);

      if (hasHistory) {
        const historyItems = page.locator('.history-item, .transaction-item');
        const itemCount = await historyItems.count();

        if (itemCount > 0) {
          await expect(historyItems.first()).toBeVisible();
        }
      }
    });
  });

  test.describe('Transaction History', () => {
    test('should display transaction history', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/transactions/, {
        data: [
          {
            id: 'txn1',
            type: 'earned',
            amount: 100,
            description: 'Bill scan reward',
            date: '2024-01-15T10:30:00Z',
            status: 'completed'
          },
          {
            id: 'txn2',
            type: 'spent',
            amount: -50,
            description: 'Reward exchange',
            date: '2024-01-14T15:20:00Z',
            status: 'completed'
          }
        ]
      });

      await page.goto('/transaction-history');
      await waitForPageLoad(page);

      // Check for transaction list
      const transactionItems = page.locator('.transaction-item, .transaction-row, [data-testid*="transaction"]');
      const transactionCount = await transactionItems.count();

      if (transactionCount > 0) {
        await expect(transactionItems.first()).toBeVisible();

        // Check transaction details
        const amounts = page.locator('.amount, .transaction-amount');
        const hasAmounts = await amounts.first().isVisible().catch(() => false);
        if (hasAmounts) {
          await expect(amounts.first()).toBeVisible();
        }

        // Check descriptions
        const descriptions = page.locator('.description, .transaction-description');
        const hasDescriptions = await descriptions.first().isVisible().catch(() => false);
        if (hasDescriptions) {
          await expect(descriptions.first()).toBeVisible();
        }

        // Check dates
        const dates = page.locator('.date, .transaction-date');
        const hasDates = await dates.first().isVisible().catch(() => false);
        if (hasDates) {
          await expect(dates.first()).toBeVisible();
        }
      } else {
        const emptyState = page.locator('.empty-state, [data-testid="empty"]');
        await expect(emptyState.first()).toBeVisible();
      }
    });

    test('should filter transactions by type', async ({ page }) => {
      await page.goto('/transaction-history');
      await waitForPageLoad(page);

      // Look for filter options
      const filterTabs = page.locator('.filter-tab, [role="tab"], .transaction-filter');
      const hasFilters = await filterTabs.first().isVisible().catch(() => false);

      if (hasFilters && await filterTabs.count() > 1) {
        const earnedTab = page.locator('text="Earned", text="Đã kiếm", text="Thu nhập"').first();
        const hasEarnedTab = await earnedTab.isVisible().catch(() => false);

        if (hasEarnedTab) {
          await earnedTab.click();
          await waitForPageLoad(page);

          // Should filter to earned transactions
          const currentUrl = page.url();
          const hasFilter = currentUrl.includes('type') || currentUrl.includes('filter');

          if (hasFilter) {
            expect(currentUrl).toMatch(/type|filter|earned/);
          }
        }
      }
    });

    test('should filter transactions by date range', async ({ page }) => {
      await page.goto('/transaction-history');
      await waitForPageLoad(page);

      // Look for month filter
      const monthFilter = page.locator('.month-filter, .date-filter, select');
      const hasMonthFilter = await monthFilter.first().isVisible().catch(() => false);

      if (hasMonthFilter) {
        // Select different month
        const filterOptions = monthFilter.locator('option');
        const optionCount = await filterOptions.count();

        if (optionCount > 1) {
          await monthFilter.first().selectOption({ index: 1 });
          await waitForPageLoad(page);

          // Should update transaction list
          const currentUrl = page.url();
          const hasDateFilter = currentUrl.includes('month') || currentUrl.includes('date');

          if (hasDateFilter) {
            expect(currentUrl).toMatch(/month|date|filter/);
          }
        }
      }
    });
  });

  test.describe('Membership Information', () => {
    test('should display membership tier details', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/membership/, {
        data: {
          currentTier: 'Gold',
          nextTier: 'Platinum',
          progress: 75,
          benefits: [
            'Extra points on purchases',
            'Priority customer support',
            'Exclusive offers'
          ],
          requirements: {
            pointsNeeded: 500,
            transactionsNeeded: 5
          }
        }
      });

      await page.goto('/membership');
      await waitForPageLoad(page);

      // Check current tier
      const currentTier = page.locator('.current-tier, .tier-name, h1');
      await expect(currentTier.first()).toBeVisible();

      // Check progress to next tier
      const progress = page.locator('.progress, .tier-progress, [role="progressbar"]');
      const hasProgress = await progress.first().isVisible().catch(() => false);

      if (hasProgress) {
        await expect(progress.first()).toBeVisible();
      }

      // Check benefits list
      const benefits = page.locator('.benefit-item, .benefits li, .perk');
      const benefitCount = await benefits.count();

      if (benefitCount > 0) {
        await expect(benefits.first()).toBeVisible();
        expect(benefitCount).toBeGreaterThan(0);
      }

      // Check requirements for next tier
      const requirements = page.locator('.requirement, .next-tier-requirement');
      const hasRequirements = await requirements.first().isVisible().catch(() => false);

      if (hasRequirements) {
        await expect(requirements.first()).toBeVisible();
      }
    });

    test('should display tier member list', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/membership.*\/list/, {
        data: [
          { tier: 'Bronze', count: 1000, description: 'Entry level' },
          { tier: 'Silver', count: 500, description: 'Regular member' },
          { tier: 'Gold', count: 200, description: 'Premium member' },
          { tier: 'Platinum', count: 50, description: 'VIP member' }
        ]
      });

      await page.goto('/membership/tier-list');
      await waitForPageLoad(page);

      // Check tier list
      const tierItems = page.locator('.tier-item, .membership-tier, [data-testid*="tier"]');
      const tierCount = await tierItems.count();

      if (tierCount > 0) {
        await expect(tierItems.first()).toBeVisible();

        // Check tier details
        const tierNames = page.locator('.tier-name, h3');
        const hasTierNames = await tierNames.first().isVisible().catch(() => false);
        if (hasTierNames) {
          await expect(tierNames.first()).toBeVisible();
        }

        // Check member counts
        const memberCounts = page.locator('.member-count, .count');
        const hasCounts = await memberCounts.first().isVisible().catch(() => false);
        if (hasCounts) {
          await expect(memberCounts.first()).toBeVisible();
        }
      }
    });

    test('should handle merchant-specific membership', async ({ page }) => {
      await page.goto('/membership/test-merchant');
      await waitForPageLoad(page);

      // Should display merchant-specific membership info
      const merchantName = page.locator('.merchant-name, h1, h2');
      const hasName = await merchantName.first().isVisible().catch(() => false);

      if (hasName) {
        await expect(merchantName.first()).toBeVisible();
      }

      // Check for membership benefits at this merchant
      const benefits = page.locator('.benefit, .merchant-benefit, .perk');
      const hasBenefits = await benefits.first().isVisible().catch(() => false);

      if (hasBenefits) {
        const benefitCount = await benefits.count();
        expect(benefitCount).toBeGreaterThan(0);
      }

      // Check for member-specific offers
      const offers = page.locator('.offer, .member-offer, .exclusive-deal');
      const hasOffers = await offers.first().isVisible().catch(() => false);

      if (hasOffers) {
        await expect(offers.first()).toBeVisible();
      }
    });
  });

  test.describe('Profile Settings', () => {
    test('should display profile edit form', async ({ page }) => {
      // Navigate through profile menu to settings
      await page.goto('/profile');
      await waitForPageLoad(page);

      const settingsLink = page.locator('text="Settings", text="Cài đặt", a[href*="settings"]');
      const hasSettingsLink = await settingsLink.first().isVisible().catch(() => false);

      if (hasSettingsLink) {
        await settingsLink.first().click();
        await waitForPageLoad(page);
      } else {
        // Try direct navigation to edit profile
        await page.goto('/profile/edit');
        await waitForPageLoad(page);
      }

      // Check for profile edit form
      const editForm = page.locator('form, .profile-form, .edit-form');
      const hasForm = await editForm.first().isVisible().catch(() => false);

      if (hasForm) {
        await expect(editForm.first()).toBeVisible();

        // Check for editable fields
        const nameInput = page.locator('input[type="text"]').first();
        const hasNameInput = await nameInput.isVisible().catch(() => false);
        if (hasNameInput) {
          await expect(nameInput).toBeVisible();
        }

        // Check for save button
        const saveBtn = page.locator('button:has-text("Save"), button:has-text("Lưu"), button[type="submit"]');
        const hasSaveBtn = await saveBtn.first().isVisible().catch(() => false);
        if (hasSaveBtn) {
          await expect(saveBtn.first()).toBeVisible();
        }
      }
    });

    test('should handle profile updates', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/profile/, {
        method: 'PUT',
        data: { success: true, message: 'Profile updated successfully' }
      });

      await page.goto('/profile/edit');
      await waitForPageLoad(page);

      const nameInput = page.locator('input[placeholder*="name"], input[placeholder*="tên"]').first();
      const hasNameInput = await nameInput.isVisible().catch(() => false);

      if (hasNameInput) {
        await nameInput.fill('Updated Name');

        const saveBtn = page.locator('button[type="submit"], button:has-text("Save")');
        const hasSaveBtn = await saveBtn.first().isVisible().catch(() => false);

        if (hasSaveBtn) {
          await saveBtn.first().click();
          await waitForPageLoad(page);

          // Should show success message or redirect
          const successMessage = page.locator('.success-message, [data-testid="success"]');
          const hasSuccess = await successMessage.first().isVisible().catch(() => false);

          if (hasSuccess) {
            await expect(successMessage.first()).toBeVisible();
          } else {
            // Should redirect back to profile
            const currentUrl = page.url();
            expect(currentUrl).toMatch(/profile/);
          }
        }
      }
    });

    test('should handle avatar upload', async ({ page }) => {
      await page.goto('/profile/edit');
      await waitForPageLoad(page);

      const avatarUpload = page.locator('input[type="file"], .avatar-upload, .upload-avatar');
      const hasUpload = await avatarUpload.first().isVisible().catch(() => false);

      if (hasUpload) {
        // Simulate file selection
        await avatarUpload.first().setInputFiles({
          name: 'avatar.jpg',
          mimeType: 'image/jpeg', 
          buffer: Buffer.from('fake-image-data')
        });

        await waitForPageLoad(page);

        // Should show preview or upload progress
        const preview = page.locator('.avatar-preview, img[src*="blob"]');
        const uploadProgress = page.locator('.upload-progress, .uploading');

        const hasPreview = await preview.first().isVisible().catch(() => false);
        const hasProgress = await uploadProgress.first().isVisible().catch(() => false);

        expect(hasPreview || hasProgress).toBeTruthy();
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle profile API errors', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/profile/, {
        status: 500,
        data: { error: 'Profile service unavailable' }
      });

      await page.goto('/profile');
      await waitForPageLoad(page);

      // Should show error state or fallback content
      const errorState = page.locator('.error-state, [data-testid="error"]');
      const hasError = await errorState.first().isVisible().catch(() => false);

      if (hasError) {
        await expect(errorState.first()).toBeVisible();
      }
    });

    test('should handle profile update errors', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/profile/, {
        method: 'PUT',
        status: 400,
        data: { error: 'Invalid profile data' }
      });

      await page.goto('/profile/edit');
      await waitForPageLoad(page);

      const saveBtn = page.locator('button[type="submit"], button:has-text("Save")');
      const hasSaveBtn = await saveBtn.first().isVisible().catch(() => false);

      if (hasSaveBtn) {
        await saveBtn.first().click();
        await waitForPageLoad(page);

        // Should show error message
        const errorMessage = await checkForErrors(page);
        expect(errorMessage).toBeTruthy();
      }
    });
  });
});