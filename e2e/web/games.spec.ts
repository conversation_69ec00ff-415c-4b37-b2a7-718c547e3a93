import { test, expect } from '@playwright/test';
import { waitForPageLoad, clickAndWait, mockApiResponse, handleDialog } from '../helpers/page-helpers';

test.describe('Games System Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await waitForPageLoad(page);
  });

  test.describe('Games Page', () => {
    test('should display available games', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/games/, {
        data: [
          {
            id: '1',
            name: 'Wheel Game',
            description: 'Spin to win',
            thumbnail: 'wheel-game.jpg',
            type: 'wheel',
            isActive: true
          },
          {
            id: '2', 
            name: 'Daily Mission',
            description: 'Complete daily tasks',
            thumbnail: 'mission.jpg',
            type: 'mission',
            isActive: true
          }
        ]
      });

      await page.goto('/games');
      await waitForPageLoad(page);

      // Check for games list
      const gameCards = page.locator('.game-card, .card, [data-testid*="game"]');
      const gameCount = await gameCards.count();

      if (gameCount > 0) {
        await expect(gameCards.first()).toBeVisible();
        
        // Check for game details
        const gameTitle = page.locator('.game-title, h3, .card-title').first();
        await expect(gameTitle).toBeVisible();

        // Check for game image
        const gameImage = page.locator('img[src*="game"], .game-thumbnail').first();
        const hasImage = await gameImage.isVisible().catch(() => false);
        if (hasImage) {
          await expect(gameImage).toBeVisible();
        }
      } else {
        // Check for empty state
        const emptyState = page.locator('.empty-state, [data-testid="empty"]');
        await expect(emptyState.first()).toBeVisible();
      }
    });

    test('should navigate to game detail', async ({ page }) => {
      await page.goto('/games');
      await waitForPageLoad(page);

      const firstGame = page.locator('.game-card, .card').first();
      const hasGames = await firstGame.isVisible().catch(() => false);

      if (hasGames) {
        await firstGame.click();
        await waitForPageLoad(page);

        // Should navigate to game detail page
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/games\/\d+|game-detail/);
      }
    });

    test('should display game categories or filters', async ({ page }) => {
      await page.goto('/games');
      await waitForPageLoad(page);

      // Look for category filters
      const categoryFilters = page.locator('.category-filter, .game-filter, .filter-tab');
      const hasFilters = await categoryFilters.first().isVisible().catch(() => false);

      if (hasFilters) {
        const filterCount = await categoryFilters.count();
        expect(filterCount).toBeGreaterThan(0);

        // Test filter functionality
        const secondFilter = categoryFilters.nth(1);
        const hasSecondFilter = await secondFilter.isVisible().catch(() => false);
        
        if (hasSecondFilter) {
          await secondFilter.click();
          await waitForPageLoad(page);
          
          // URL or content should change
          const currentUrl = page.url();
          const hasFilterInUrl = currentUrl.includes('category') || currentUrl.includes('filter');
          
          if (hasFilterInUrl) {
            expect(currentUrl).toMatch(/category|filter|type/);
          }
        }
      }
    });
  });

  test.describe('Game Detail Page', () => {
    test('should display game information', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/games\/\d+/, {
        data: {
          id: '1',
          name: 'Test Game',
          description: 'Test game description',
          rules: 'Game rules here',
          rewards: [
            { name: 'First Prize', points: 1000 },
            { name: 'Second Prize', points: 500 }
          ],
          playCount: 5,
          maxPlays: 10
        }
      });

      await page.goto('/games/1');
      await waitForPageLoad(page);

      // Check game title
      const gameTitle = page.locator('h1, .game-title, [data-testid="game-title"]');
      await expect(gameTitle.first()).toBeVisible();

      // Check game description
      const description = page.locator('.game-description, .description, p');
      const hasDescription = await description.first().isVisible().catch(() => false);
      if (hasDescription) {
        await expect(description.first()).toBeVisible();
      }

      // Check play button
      const playBtn = page.locator('button:has-text("Play"), button:has-text("Chơi"), .play-btn');
      const hasPlayBtn = await playBtn.first().isVisible().catch(() => false);
      if (hasPlayBtn) {
        await expect(playBtn.first()).toBeVisible();
      }

      // Check rewards list
      const rewardsList = page.locator('.rewards-list, .prize-list, .reward-item');
      const hasRewards = await rewardsList.first().isVisible().catch(() => false);
      if (hasRewards) {
        const rewardCount = await rewardsList.count();
        expect(rewardCount).toBeGreaterThan(0);
      }
    });

    test('should show play count and limits', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/games\/\d+/, {
        data: {
          id: '1',
          name: 'Limited Game',
          playCount: 3,
          maxPlays: 5,
          cooldown: 3600000 // 1 hour
        }
      });

      await page.goto('/games/1');
      await waitForPageLoad(page);

      // Check for play count display
      const playCountElements = [
        'text="3/5"',
        'text="Còn lại"',
        'text="remaining"',
        '.play-count',
        '[data-testid="play-count"]'
      ];

      let hasPlayCount = false;
      for (const selector of playCountElements) {
        const element = page.locator(selector).first();
        if (await element.isVisible().catch(() => false)) {
          hasPlayCount = true;
          break;
        }
      }

      expect(hasPlayCount).toBeTruthy();
    });

    test('should handle game rules display', async ({ page }) => {
      await page.goto('/games/1');
      await waitForPageLoad(page);

      // Look for rules button or section
      const rulesElements = [
        'button:has-text("Rules")',
        'button:has-text("Luật chơi")',
        '.rules-btn',
        '.game-rules'
      ];

      for (const selector of rulesElements) {
        const element = page.locator(selector).first();
        if (await element.isVisible().catch(() => false)) {
          await element.click();
          await waitForPageLoad(page);

          // Should show rules modal or expand rules section
          const rulesContent = page.locator('.rules-content, .modal-content, .rules-text');
          const hasRulesContent = await rulesContent.first().isVisible().catch(() => false);
          
          if (hasRulesContent) {
            await expect(rulesContent.first()).toBeVisible();
          }
          break;
        }
      }
    });
  });

  test.describe('Wheel Game', () => {
    test('should display wheel game interface', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/games\/wheel\/\d+/, {
        data: {
          id: '1',
          name: 'Lucky Wheel',
          segments: [
            { id: 1, label: '10 Points', value: 10, color: '#ff0000' },
            { id: 2, label: '50 Points', value: 50, color: '#00ff00' },
            { id: 3, label: 'Try Again', value: 0, color: '#0000ff' }
          ],
          canPlay: true,
          playsRemaining: 3
        }
      });

      await page.goto('/games/wheel/1');
      await waitForPageLoad(page);

      // Check for wheel interface
      const wheelElement = page.locator('.wheel, .game-wheel, [data-testid="wheel"]');
      const hasWheel = await wheelElement.first().isVisible().catch(() => false);

      if (hasWheel) {
        await expect(wheelElement.first()).toBeVisible();

        // Check for spin button
        const spinBtn = page.locator('button:has-text("Spin"), button:has-text("Quay"), .spin-btn');
        const hasSpinBtn = await spinBtn.first().isVisible().catch(() => false);
        
        if (hasSpinBtn) {
          await expect(spinBtn.first()).toBeVisible();
          
          // Button should be enabled if can play
          const isDisabled = await spinBtn.first().isDisabled();
          expect(isDisabled).toBeFalsy();
        }
      }

      // Check for wheel segments/prizes
      const segments = page.locator('.wheel-segment, .prize-segment');
      const hasSegments = await segments.first().isVisible().catch(() => false);
      
      if (hasSegments) {
        const segmentCount = await segments.count();
        expect(segmentCount).toBeGreaterThan(0);
      }
    });

    test('should handle wheel spin interaction', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/games\/wheel\/\d+\/spin/, {
        data: {
          result: {
            segmentId: 2,
            label: '50 Points',
            value: 50
          },
          playsRemaining: 2,
          totalWinnings: 50
        }
      });

      await page.goto('/games/wheel/1');
      await waitForPageLoad(page);

      const spinBtn = page.locator('button:has-text("Spin"), button:has-text("Quay"), .spin-btn');
      const hasSpinBtn = await spinBtn.first().isVisible().catch(() => false);

      if (hasSpinBtn) {
        // Handle potential confirmation dialog
        handleDialog(page, 'accept');

        await spinBtn.first().click();
        await waitForPageLoad(page);

        // Should show spinning animation or result
        const resultElements = [
          '.spin-result',
          '.game-result', 
          'text="50 Points"',
          '.congratulations'
        ];

        let hasResult = false;
        for (const selector of resultElements) {
          const element = page.locator(selector).first();
          if (await element.isVisible({ timeout: 10000 }).catch(() => false)) {
            hasResult = true;
            break;
          }
        }

        expect(hasResult).toBeTruthy();
      }
    });

    test('should handle insufficient plays', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/games\/wheel\/\d+/, {
        data: {
          id: '1',
          name: 'Lucky Wheel',
          canPlay: false,
          playsRemaining: 0,
          reason: 'No plays remaining'
        }
      });

      await page.goto('/games/wheel/1');
      await waitForPageLoad(page);

      const spinBtn = page.locator('button:has-text("Spin"), button:has-text("Quay"), .spin-btn');
      const hasSpinBtn = await spinBtn.first().isVisible().catch(() => false);

      if (hasSpinBtn) {
        const isDisabled = await spinBtn.first().isDisabled();
        expect(isDisabled).toBeTruthy();
      }

      // Should show message about no plays remaining
      const noPlaysMessage = page.locator('text="No plays", text="Hết lượt", .no-plays');
      const hasMessage = await noPlaysMessage.first().isVisible().catch(() => false);
      
      if (hasMessage) {
        await expect(noPlaysMessage.first()).toBeVisible();
      }
    });
  });

  test.describe('Missions System', () => {
    test('should display missions list', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/missions/, {
        data: [
          {
            id: '1',
            title: 'Daily Login',
            description: 'Login every day',
            progress: 5,
            target: 7,
            reward: { points: 100 },
            status: 'active'
          },
          {
            id: '2',
            title: 'Scan Bills', 
            description: 'Scan 3 bills',
            progress: 2,
            target: 3,
            reward: { points: 200 },
            status: 'active'
          }
        ]
      });

      await page.goto('/missions');
      await waitForPageLoad(page);

      // Check for missions list
      const missionItems = page.locator('.mission-item, .mission-card, [data-testid*="mission"]');
      const missionCount = await missionItems.count();

      if (missionCount > 0) {
        await expect(missionItems.first()).toBeVisible();

        // Check mission details
        const missionTitle = page.locator('.mission-title, h3').first();
        await expect(missionTitle).toBeVisible();

        // Check progress bar
        const progressBar = page.locator('.progress-bar, .progress, [role="progressbar"]');
        const hasProgress = await progressBar.first().isVisible().catch(() => false);
        
        if (hasProgress) {
          await expect(progressBar.first()).toBeVisible();
        }
      } else {
        const emptyState = page.locator('.empty-state, [data-testid="empty"]');
        await expect(emptyState.first()).toBeVisible();
      }
    });

    test('should display mission progress', async ({ page }) => {
      await page.goto('/missions');
      await waitForPageLoad(page);

      const firstMission = page.locator('.mission-item').first();
      const hasMissions = await firstMission.isVisible().catch(() => false);

      if (hasMissions) {
        // Check for progress indicators
        const progressElements = [
          '.progress-text',
          'text="5/7"',
          'text="2/3"',
          '.progress-bar'
        ];

        let hasProgressIndicator = false;
        for (const selector of progressElements) {
          const element = page.locator(selector).first();
          if (await element.isVisible().catch(() => false)) {
            hasProgressIndicator = true;
            break;
          }
        }

        expect(hasProgressIndicator).toBeTruthy();
      }
    });

    test('should handle mission completion', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/missions\/\d+\/claim/, {
        data: {
          success: true,
          reward: { points: 100 },
          message: 'Mission completed!'
        }
      });

      await page.goto('/missions');
      await waitForPageLoad(page);

      // Look for completed mission with claim button
      const claimBtn = page.locator('button:has-text("Claim"), button:has-text("Nhận"), .claim-btn');
      const hasClaimBtn = await claimBtn.first().isVisible().catch(() => false);

      if (hasClaimBtn) {
        await claimBtn.first().click();
        await waitForPageLoad(page);

        // Should show success message
        const successMessage = page.locator('.success-message, [data-testid="success"]');
        const hasSuccess = await successMessage.first().isVisible().catch(() => false);
        
        if (hasSuccess) {
          await expect(successMessage.first()).toBeVisible();
        }
      }
    });

    test('should navigate to mission detail', async ({ page }) => {
      await page.goto('/missions');
      await waitForPageLoad(page);

      const firstMission = page.locator('.mission-item, .mission-card').first();
      const hasMissions = await firstMission.isVisible().catch(() => false);

      if (hasMissions) {
        await firstMission.click();
        await waitForPageLoad(page);

        // Should navigate to mission detail
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/missions\/\d+|mission-detail/);
      }
    });
  });

  test.describe('Games History', () => {
    test('should display games history', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/games.*\/history/, {
        data: [
          {
            id: '1',
            gameName: 'Lucky Wheel',
            playDate: '2024-01-15T10:30:00Z',
            result: 'Won 50 points',
            status: 'completed'
          },
          {
            id: '2',
            gameName: 'Daily Mission',
            playDate: '2024-01-14T15:20:00Z', 
            result: 'Mission completed',
            status: 'completed'
          }
        ]
      });

      await page.goto('/games/history');
      await waitForPageLoad(page);

      // Check for history list
      const historyItems = page.locator('.history-item, .game-history-item, [data-testid*="history"]');
      const historyCount = await historyItems.count();

      if (historyCount > 0) {
        await expect(historyItems.first()).toBeVisible();

        // Check history details
        const gameNames = page.locator('.game-name, .history-title');
        const hasGameNames = await gameNames.first().isVisible().catch(() => false);
        
        if (hasGameNames) {
          await expect(gameNames.first()).toBeVisible();
        }

        // Check dates
        const dates = page.locator('.play-date, .history-date, .date');
        const hasDates = await dates.first().isVisible().catch(() => false);
        
        if (hasDates) {
          await expect(dates.first()).toBeVisible();
        }
      } else {
        const emptyState = page.locator('.empty-state, [data-testid="empty"]');
        await expect(emptyState.first()).toBeVisible();
      }
    });

    test('should filter history by game type', async ({ page }) => {
      await page.goto('/games/history');
      await waitForPageLoad(page);

      // Look for filter options
      const filterTabs = page.locator('.filter-tab, .game-type-filter, [role="tab"]');
      const hasFilters = await filterTabs.first().isVisible().catch(() => false);

      if (hasFilters && await filterTabs.count() > 1) {
        const wheelFilter = page.locator('text="Wheel", text="Vòng quay"').first();
        const hasWheelFilter = await wheelFilter.isVisible().catch(() => false);

        if (hasWheelFilter) {
          await wheelFilter.click();
          await waitForPageLoad(page);

          // Content should be filtered
          const currentUrl = page.url();
          const hasFilterInUrl = currentUrl.includes('filter') || currentUrl.includes('type');
          
          if (hasFilterInUrl) {
            expect(currentUrl).toMatch(/filter|type|wheel/);
          }
        }
      }
    });
  });

  test.describe('Gift Code System', () => {
    test('should display gift code input', async ({ page }) => {
      await page.goto('/gift-code');
      await waitForPageLoad(page);

      // Check for gift code input
      const giftCodeInput = page.locator('input[placeholder*="gift"], input[placeholder*="mã"], .gift-code-input');
      await expect(giftCodeInput.first()).toBeVisible();

      // Check for submit button
      const submitBtn = page.locator('button:has-text("Submit"), button:has-text("Gửi"), button[type="submit"]');
      await expect(submitBtn.first()).toBeVisible();
    });

    test('should handle valid gift code redemption', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/gift-code.*redeem/, {
        data: {
          success: true,
          reward: { points: 500, items: ['Bonus points'] },
          message: 'Gift code redeemed successfully!'
        }
      });

      await page.goto('/gift-code');
      await waitForPageLoad(page);

      const giftCodeInput = page.locator('input[placeholder*="gift"], input[placeholder*="mã"]');
      await giftCodeInput.fill('TESTCODE123');

      const submitBtn = page.locator('button[type="submit"]');
      await submitBtn.click();
      await waitForPageLoad(page);

      // Should show success message
      const successMessage = page.locator('.success-message, [data-testid="success"]');
      const hasSuccess = await successMessage.first().isVisible().catch(() => false);
      
      if (hasSuccess) {
        await expect(successMessage.first()).toBeVisible();
      }
    });

    test('should handle invalid gift code', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/gift-code.*redeem/, {
        status: 400,
        data: { error: 'Invalid gift code' }
      });

      await page.goto('/gift-code');
      await waitForPageLoad(page);

      const giftCodeInput = page.locator('input[placeholder*="gift"], input[placeholder*="mã"]');
      await giftCodeInput.fill('INVALIDCODE');

      const submitBtn = page.locator('button[type="submit"]');
      await submitBtn.click();
      await waitForPageLoad(page);

      // Should show error message
      const errorMessage = page.locator('.error-message, [data-testid="error"]');
      const hasError = await errorMessage.first().isVisible().catch(() => false);
      
      if (hasError) {
        await expect(errorMessage.first()).toBeVisible();
      }
    });

    test('should validate gift code format', async ({ page }) => {
      await page.goto('/gift-code');
      await waitForPageLoad(page);

      const giftCodeInput = page.locator('input[placeholder*="gift"], input[placeholder*="mã"]');
      const submitBtn = page.locator('button[type="submit"]');

      // Test empty input
      await submitBtn.click();
      
      // Should show validation message
      const validationMessage = page.locator('.validation-error, .error-message');
      const hasValidation = await validationMessage.first().isVisible().catch(() => false);
      
      if (hasValidation) {
        await expect(validationMessage.first()).toBeVisible();
      }

      // Test short input
      await giftCodeInput.fill('123');
      await submitBtn.click();
      
      const shortCodeError = await validationMessage.first().isVisible().catch(() => false);
      if (shortCodeError) {
        await expect(validationMessage.first()).toBeVisible();
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle game API errors', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/games/, {
        status: 500,
        data: { error: 'Game service unavailable' }
      });

      await page.goto('/games');
      await waitForPageLoad(page);

      // Should show error state
      const errorState = page.locator('.error-state, [data-testid="error"]');
      const retryBtn = page.locator('button:has-text("Retry"), button:has-text("Thử lại")');

      const hasError = await errorState.first().isVisible().catch(() => false);
      const hasRetry = await retryBtn.first().isVisible().catch(() => false);

      expect(hasError || hasRetry).toBeTruthy();
    });

    test('should handle network connectivity issues', async ({ page }) => {
      await page.route(/\/api.*\/games/, route => route.abort());

      await page.goto('/games');
      await waitForPageLoad(page);

      // Should show network error
      const networkError = page.locator('text="Network error", text="Connection error"');
      const hasNetworkError = await networkError.first().isVisible().catch(() => false);
      
      if (hasNetworkError) {
        await expect(networkError.first()).toBeVisible();
      }
    });
  });
});