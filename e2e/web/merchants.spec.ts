import { test, expect } from '@playwright/test';
import { waitForPageLoad, mockApiResponse, scrollToElement } from '../helpers/page-helpers';

test.describe('Merchants System Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await waitForPageLoad(page);
  });

  test.describe('Merchant List Page', () => {
    test('should display merchants list', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/merchants/, {
        data: [
          {
            id: 'merchant1',
            name: 'Coffee Shop',
            logo: 'coffee-logo.jpg',
            category: 'Food & Beverage',
            rating: 4.5,
            distance: '0.5km',
            description: 'Best coffee in town'
          },
          {
            id: 'merchant2',
            name: 'Fashion Store',
            logo: 'fashion-logo.jpg', 
            category: 'Fashion',
            rating: 4.2,
            distance: '1.2km',
            description: 'Trendy clothing'
          }
        ]
      });

      await page.goto('/merchants');
      await waitForPageLoad(page);

      // Check for merchant cards
      const merchantCards = page.locator('.merchant-card, .store-card, [data-testid*="merchant"]');
      const merchantCount = await merchantCards.count();

      if (merchantCount > 0) {
        await expect(merchantCards.first()).toBeVisible();

        // Check merchant details
        const merchantNames = page.locator('.merchant-name, .store-name, h3');
        const hasNames = await merchantNames.first().isVisible().catch(() => false);
        if (hasNames) {
          await expect(merchantNames.first()).toBeVisible();
        }

        // Check merchant logos
        const merchantLogos = page.locator('img[src*="logo"], .merchant-logo, .store-logo');
        const hasLogos = await merchantLogos.first().isVisible().catch(() => false);
        if (hasLogos) {
          await expect(merchantLogos.first()).toBeVisible();
        }

        // Check ratings
        const ratings = page.locator('.rating, .star-rating, .score');
        const hasRatings = await ratings.first().isVisible().catch(() => false);
        if (hasRatings) {
          await expect(ratings.first()).toBeVisible();
        }
      } else {
        const emptyState = page.locator('.empty-state, [data-testid="empty"]');
        await expect(emptyState.first()).toBeVisible();
      }
    });

    test('should filter merchants by category', async ({ page }) => {
      await page.goto('/merchants');
      await waitForPageLoad(page);

      // Look for category filters
      const categoryFilters = page.locator('.category-filter, .filter-btn, [data-testid*="category"]');
      const filterCount = await categoryFilters.count();

      if (filterCount > 1) {
        const foodFilter = page.locator('text="Food", text="Ẩm thực", text="Food & Beverage"').first();
        const hasFoodFilter = await foodFilter.isVisible().catch(() => false);

        if (hasFoodFilter) {
          await foodFilter.click();
          await waitForPageLoad(page);

          // Should filter merchants
          const currentUrl = page.url();
          const hasFilter = currentUrl.includes('category') || currentUrl.includes('filter');
          
          if (hasFilter) {
            expect(currentUrl).toMatch(/category|filter|food/);
          }
        }
      }
    });

    test('should search merchants', async ({ page }) => {
      await page.goto('/merchants');
      await waitForPageLoad(page);

      const searchInput = page.locator('input[type="search"], input[placeholder*="tìm"], input[placeholder*="search"]');
      const hasSearch = await searchInput.first().isVisible().catch(() => false);

      if (hasSearch) {
        await searchInput.first().fill('coffee');
        await page.keyboard.press('Enter');
        await waitForPageLoad(page);

        // Should show filtered results
        const currentUrl = page.url();
        const hasSearchQuery = currentUrl.includes('q=') || currentUrl.includes('search');
        
        if (hasSearchQuery) {
          expect(currentUrl).toMatch(/q=|search/);
        }
      }
    });

    test('should navigate to merchant detail', async ({ page }) => {
      await page.goto('/merchants');
      await waitForPageLoad(page);

      const firstMerchant = page.locator('.merchant-card, .store-card').first();
      const hasMerchants = await firstMerchant.isVisible().catch(() => false);

      if (hasMerchants) {
        await firstMerchant.click();
        await waitForPageLoad(page);

        // Should navigate to merchant detail
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/merchants\/\w+|merchant|deals/);
      }
    });
  });

  test.describe('Merchant Detail Page', () => {
    test('should display merchant information', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/merchants\/\w+/, {
        data: {
          id: 'merchant1',
          name: 'Test Restaurant',
          logo: 'restaurant-logo.jpg',
          banner: 'restaurant-banner.jpg',
          category: 'Food & Beverage',
          rating: 4.5,
          totalReviews: 150,
          description: 'Delicious food with great service',
          address: '123 Test Street, Test City',
          phone: '+84 123 456 789',
          openingHours: '9:00 AM - 10:00 PM',
          website: 'https://testrestaurant.com',
          social: {
            facebook: 'testrestaurant',
            instagram: 'testrestaurant'
          }
        }
      });

      await page.goto('/merchants/merchant1');
      await waitForPageLoad(page);

      // Check merchant name
      const merchantName = page.locator('h1, .merchant-name, .store-name');
      await expect(merchantName.first()).toBeVisible();

      // Check merchant banner/logo
      const banner = page.locator('img[src*="banner"], .merchant-banner, .hero-image');
      const logo = page.locator('img[src*="logo"], .merchant-logo');
      
      const hasBanner = await banner.first().isVisible().catch(() => false);
      const hasLogo = await logo.first().isVisible().catch(() => false);
      
      expect(hasBanner || hasLogo).toBeTruthy();

      // Check rating and reviews
      const rating = page.locator('.rating, .star-rating');
      const hasRating = await rating.first().isVisible().catch(() => false);
      if (hasRating) {
        await expect(rating.first()).toBeVisible();
      }

      // Check description
      const description = page.locator('.description, .merchant-description, p');
      const hasDescription = await description.first().isVisible().catch(() => false);
      if (hasDescription) {
        await expect(description.first()).toBeVisible();
      }

      // Check contact information
      const contactInfo = [
        '.address, .location',
        '.phone, .contact-phone',
        '.hours, .opening-hours'
      ];

      for (const selector of contactInfo) {
        const element = page.locator(selector);
        const isVisible = await element.first().isVisible().catch(() => false);
        if (isVisible) {
          await expect(element.first()).toBeVisible();
          break;
        }
      }
    });

    test('should display merchant offers and deals', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/merchants\/\w+.*\/offers/, {
        data: [
          {
            id: 'offer1',
            title: '20% Off All Items',
            description: 'Get 20% discount on all menu items',
            validUntil: '2024-12-31',
            terms: 'Valid for dine-in only'
          },
          {
            id: 'offer2',
            title: 'Free Dessert',
            description: 'Free dessert with main course',
            validUntil: '2024-11-30',
            terms: 'Minimum order $20'
          }
        ]
      });

      await page.goto('/merchants/merchant1');
      await waitForPageLoad(page);

      // Look for offers section
      const offersSection = page.locator('.offers, .deals, .promotions, [data-testid*="offer"]');
      const hasOffers = await offersSection.first().isVisible().catch(() => false);

      if (hasOffers) {
        const offerItems = page.locator('.offer-item, .deal-card, .promotion-card');
        const offerCount = await offerItems.count();

        if (offerCount > 0) {
          await expect(offerItems.first()).toBeVisible();

          // Check offer details
          const offerTitles = page.locator('.offer-title, .deal-title');
          const hasTitles = await offerTitles.first().isVisible().catch(() => false);
          if (hasTitles) {
            await expect(offerTitles.first()).toBeVisible();
          }
        }
      }
    });

    test('should display store locations', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/merchants\/\w+.*\/stores/, {
        data: [
          {
            id: 'store1',
            name: 'Main Branch',
            address: '123 Main St',
            phone: '+84 123 456 789',
            coordinates: { lat: 10.762622, lng: 106.660172 }
          },
          {
            id: 'store2',
            name: 'Mall Branch',
            address: '456 Mall Ave',
            phone: '+84 987 654 321',
            coordinates: { lat: 10.782622, lng: 106.680172 }
          }
        ]
      });

      await page.goto('/merchants/merchant1');
      await waitForPageLoad(page);

      // Look for stores section
      const storesSection = page.locator('.stores, .locations, .branches');
      const hasStores = await storesSection.first().isVisible().catch(() => false);

      if (hasStores) {
        const storeItems = page.locator('.store-item, .location-item, .branch-card');
        const storeCount = await storeItems.count();

        if (storeCount > 0) {
          await expect(storeItems.first()).toBeVisible();

          // Check store details
          const storeNames = page.locator('.store-name, .branch-name');
          const hasNames = await storeNames.first().isVisible().catch(() => false);
          if (hasNames) {
            await expect(storeNames.first()).toBeVisible();
          }

          // Check addresses
          const addresses = page.locator('.store-address, .branch-address');
          const hasAddresses = await addresses.first().isVisible().catch(() => false);
          if (hasAddresses) {
            await expect(addresses.first()).toBeVisible();
          }
        }
      } else {
        // Check for "View All Stores" button
        const viewStoresBtn = page.locator('button:has-text("View Stores"), a:has-text("Stores"), .view-stores-btn');
        const hasViewStores = await viewStoresBtn.first().isVisible().catch(() => false);

        if (hasViewStores) {
          await viewStoresBtn.first().click();
          await waitForPageLoad(page);

          // Should navigate to stores page
          const currentUrl = page.url();
          expect(currentUrl).toMatch(/stores|locations|branches/);
        }
      }
    });

    test('should handle merchant contact actions', async ({ page }) => {
      await page.goto('/merchants/merchant1');
      await waitForPageLoad(page);

      // Check for contact buttons
      const contactButtons = [
        'a[href^="tel:"], .call-btn, .phone-btn',
        'a[href^="mailto:"], .email-btn',
        'a[href*="facebook"], .facebook-btn',
        'a[href*="instagram"], .instagram-btn',
        'a[href^="http"], .website-btn'
      ];

      let foundContactMethod = false;
      for (const selector of contactButtons) {
        const button = page.locator(selector);
        const isVisible = await button.first().isVisible().catch(() => false);
        
        if (isVisible) {
          foundContactMethod = true;
          
          // Check href attribute for proper format
          const href = await button.first().getAttribute('href');
          if (href) {
            if (selector.includes('tel:')) {
              expect(href).toMatch(/^tel:\+?\d+/);
            } else if (selector.includes('mailto:')) {
              expect(href).toMatch(/^mailto:/);
            } else if (selector.includes('http')) {
              expect(href).toMatch(/^https?:\/\//);
            }
          }
          break;
        }
      }

      // At least one contact method should be available
      if (!foundContactMethod) {
        // Check for general contact information display
        const contactInfo = page.locator('.contact-info, .merchant-contact');
        const hasContactInfo = await contactInfo.first().isVisible().catch(() => false);
        expect(hasContactInfo).toBeTruthy();
      }
    });

    test('should display membership program information', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/merchants\/\w+.*\/membership/, {
        data: {
          hasProgram: true,
          programName: 'VIP Member',
          benefits: [
            '10% discount on all purchases',
            'Priority booking',
            'Free delivery'
          ],
          requirements: 'Spend $100 per month'
        }
      });

      await page.goto('/merchants/merchant1');
      await waitForPageLoad(page);

      // Look for membership section
      const membershipSection = page.locator('.membership, .loyalty, .vip-program');
      const hasMembership = await membershipSection.first().isVisible().catch(() => false);

      if (hasMembership) {
        const membershipInfo = page.locator('.membership-info, .loyalty-info');
        await expect(membershipInfo.first()).toBeVisible();

        // Check benefits list
        const benefits = page.locator('.benefit-item, .membership-benefit, li');
        const benefitCount = await benefits.count();

        if (benefitCount > 0) {
          await expect(benefits.first()).toBeVisible();
        }

        // Check for join/learn more button
        const joinBtn = page.locator('button:has-text("Join"), button:has-text("Learn More"), .join-membership');
        const hasJoinBtn = await joinBtn.first().isVisible().catch(() => false);
        
        if (hasJoinBtn) {
          await expect(joinBtn.first()).toBeVisible();
        }
      }
    });
  });

  test.describe('All Stores Page', () => {
    test('should display all store locations', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/merchants\/\w+.*\/stores/, {
        data: [
          {
            id: 'store1',
            name: 'Downtown Branch',
            address: '123 Downtown St',
            phone: '+84 123 456 789',
            hours: '9:00 AM - 9:00 PM',
            coordinates: { lat: 10.762622, lng: 106.660172 }
          },
          {
            id: 'store2',
            name: 'Mall Branch', 
            address: '456 Shopping Mall',
            phone: '+84 987 654 321',
            hours: '10:00 AM - 10:00 PM',
            coordinates: { lat: 10.782622, lng: 106.680172 }
          }
        ]
      });

      await page.goto('/merchants/merchant1/stores');
      await waitForPageLoad(page);

      // Check for stores list
      const storeItems = page.locator('.store-item, .location-card, [data-testid*="store"]');
      const storeCount = await storeItems.count();

      if (storeCount > 0) {
        await expect(storeItems.first()).toBeVisible();

        // Check store information
        const storeNames = page.locator('.store-name, h3');
        const hasNames = await storeNames.first().isVisible().catch(() => false);
        if (hasNames) {
          await expect(storeNames.first()).toBeVisible();
        }

        const addresses = page.locator('.store-address, .address');
        const hasAddresses = await addresses.first().isVisible().catch(() => false);
        if (hasAddresses) {
          await expect(addresses.first()).toBeVisible();
        }

        const phones = page.locator('.store-phone, .phone, a[href^="tel:"]');
        const hasPhones = await phones.first().isVisible().catch(() => false);
        if (hasPhones) {
          await expect(phones.first()).toBeVisible();
        }
      }
    });

    test('should display map view of stores', async ({ page }) => {
      await page.goto('/merchants/merchant1/stores');
      await waitForPageLoad(page);

      // Look for map container or map toggle
      const mapContainer = page.locator('.map-container, .store-map, #map');
      const mapToggle = page.locator('button:has-text("Map"), .map-toggle, .view-map');

      const hasMap = await mapContainer.first().isVisible().catch(() => false);
      const hasMapToggle = await mapToggle.first().isVisible().catch(() => false);

      if (hasMap) {
        await expect(mapContainer.first()).toBeVisible();
      } else if (hasMapToggle) {
        await mapToggle.first().click();
        await waitForPageLoad(page);

        // Map should appear after toggle
        const mapAfterToggle = page.locator('.map-container, .store-map');
        const hasMapAfterToggle = await mapAfterToggle.first().isVisible().catch(() => false);
        
        if (hasMapAfterToggle) {
          await expect(mapAfterToggle.first()).toBeVisible();
        }
      }
    });

    test('should filter stores by location', async ({ page }) => {
      await page.goto('/merchants/merchant1/stores');
      await waitForPageLoad(page);

      // Look for location filters
      const locationFilters = page.locator('.location-filter, .area-filter, .district-filter');
      const hasFilters = await locationFilters.first().isVisible().catch(() => false);

      if (hasFilters && await locationFilters.count() > 1) {
        const firstFilter = locationFilters.nth(1); // Skip "All" option
        await firstFilter.click();
        await waitForPageLoad(page);

        // Should filter store list
        const currentUrl = page.url();
        const hasLocationFilter = currentUrl.includes('location') || currentUrl.includes('area');
        
        if (hasLocationFilter) {
          expect(currentUrl).toMatch(/location|area|district|filter/);
        }
      }
    });

    test('should handle store contact actions', async ({ page }) => {
      await page.goto('/merchants/merchant1/stores');
      await waitForPageLoad(page);

      // Look for call button on first store
      const callBtn = page.locator('a[href^="tel:"], .call-btn, .phone-btn').first();
      const hasCallBtn = await callBtn.isVisible().catch(() => false);

      if (hasCallBtn) {
        const href = await callBtn.getAttribute('href');
        expect(href).toMatch(/^tel:/);

        // Don't actually click to avoid triggering phone call
        await expect(callBtn).toBeVisible();
      }

      // Look for directions button
      const directionsBtn = page.locator('.directions-btn, .get-directions, a[href*="maps"]').first();
      const hasDirections = await directionsBtn.isVisible().catch(() => false);

      if (hasDirections) {
        await expect(directionsBtn).toBeVisible();
      }
    });
  });

  test.describe('Flash Sale Integration', () => {
    test('should display flash sale items from merchants', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/flash-sale/, {
        data: [
          {
            id: 'flash1',
            merchantName: 'Test Restaurant',
            itemName: 'Lunch Special',
            originalPrice: 100000,
            salePrice: 70000,
            discount: 30,
            validUntil: '2024-12-31T23:59:59Z',
            quantity: 50,
            sold: 12
          }
        ]
      });

      await page.goto('/flash-sale');
      await waitForPageLoad(page);

      // Check for flash sale items
      const flashItems = page.locator('.flash-sale-item, .sale-card, [data-testid*="flash"]');
      const itemCount = await flashItems.count();

      if (itemCount > 0) {
        await expect(flashItems.first()).toBeVisible();

        // Check merchant name
        const merchantName = page.locator('.merchant-name, .store-name');
        const hasName = await merchantName.first().isVisible().catch(() => false);
        if (hasName) {
          await expect(merchantName.first()).toBeVisible();
        }

        // Check prices
        const prices = page.locator('.price, .sale-price, .original-price');
        const hasPrices = await prices.first().isVisible().catch(() => false);
        if (hasPrices) {
          await expect(prices.first()).toBeVisible();
        }

        // Check countdown timer
        const timer = page.locator('.countdown, .timer, .time-left');
        const hasTimer = await timer.first().isVisible().catch(() => false);
        if (hasTimer) {
          await expect(timer.first()).toBeVisible();
        }
      }
    });

    test('should navigate from flash sale to merchant', async ({ page }) => {
      await page.goto('/flash-sale');
      await waitForPageLoad(page);

      const merchantLink = page.locator('a[href*="merchant"], .merchant-link, .store-link').first();
      const hasLink = await merchantLink.isVisible().catch(() => false);

      if (hasLink) {
        await merchantLink.click();
        await waitForPageLoad(page);

        // Should navigate to merchant detail
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/merchant|deals/);
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle merchant API errors', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/merchants/, {
        status: 500,
        data: { error: 'Merchant service unavailable' }
      });

      await page.goto('/merchants');
      await waitForPageLoad(page);

      // Should show error state
      const errorState = page.locator('.error-state, [data-testid="error"]');
      const retryBtn = page.locator('button:has-text("Retry"), button:has-text("Thử lại")');

      const hasError = await errorState.first().isVisible().catch(() => false);
      const hasRetry = await retryBtn.first().isVisible().catch(() => false);

      expect(hasError || hasRetry).toBeTruthy();
    });

    test('should handle non-existent merchant', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/merchants\/nonexistent/, {
        status: 404,
        data: { error: 'Merchant not found' }
      });

      await page.goto('/merchants/nonexistent');
      await waitForPageLoad(page);

      // Should show 404 or not found message
      const notFound = page.locator('text="404", text="Not found", text="Không tìm thấy"');
      await expect(notFound.first()).toBeVisible();
    });

    test('should handle network connectivity issues', async ({ page }) => {
      await page.route(/\/api.*\/merchants/, route => route.abort());

      await page.goto('/merchants');
      await waitForPageLoad(page);

      // Should show network error
      const networkError = page.locator('text="Network error", text="Connection error"');
      const hasError = await networkError.first().isVisible().catch(() => false);
      
      if (hasError) {
        await expect(networkError.first()).toBeVisible();
      }
    });
  });

  test.describe('Responsive Design', () => {
    test('should display properly on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await page.goto('/merchants');
      await waitForPageLoad(page);

      // Check mobile-specific layout
      const merchantCards = page.locator('.merchant-card, .store-card');
      const cardCount = await merchantCards.count();

      if (cardCount > 0) {
        // Cards should be visible and properly sized
        await expect(merchantCards.first()).toBeVisible();
        
        // Check if cards stack vertically on mobile
        const firstCardBounds = await merchantCards.first().boundingBox();
        if (cardCount > 1) {
          const secondCardBounds = await merchantCards.nth(1).boundingBox();
          
          if (firstCardBounds && secondCardBounds) {
            // Second card should be below first card on mobile
            expect(secondCardBounds.y).toBeGreaterThan(firstCardBounds.y);
          }
        }
      }
    });

    test('should handle touch interactions on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      await page.goto('/merchants');
      await waitForPageLoad(page);

      const firstMerchant = page.locator('.merchant-card').first();
      const hasMerchants = await firstMerchant.isVisible().catch(() => false);

      if (hasMerchants) {
        // Simulate touch tap
        await firstMerchant.tap();
        await waitForPageLoad(page);

        // Should navigate to merchant detail
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/merchant|deals/);
      }
    });
  });
});