import { test, expect } from '@playwright/test';
import { waitForPageLoad, navigateToTab, verifyNavigation, testResponsive } from '../helpers/page-helpers';

test.describe('Navigation Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Start from authenticated homepage
    await page.goto('/');
    await waitForPageLoad(page);
  });

  test('should display bottom navigation', async ({ page }) => {
    const bottomNav = page.locator('[data-testid="bottom-navigation"], .bottom-navigation, nav').last();
    await expect(bottomNav).toBeVisible();
    
    // Check for navigation items (Vietnamese labels from BottomNavigation.tsx)
    const navItems = ['Trang chủ', 'Ưu đãi', 'Đổi thưởng', 'VUI chơi', 'Tài khoản'];
    
    for (const item of navItems) {
      const navItem = page.locator(`text="${item}"`);
      await expect(navItem).toBeVisible();
    }
  });

  test('should navigate between main tabs', async ({ page }) => {
    const navigationTests = [
      { tab: 'Ưu đãi', expectedUrl: '/merchants', fallbackUrls: ['/deals', '/offers'] },
      { tab: 'Đổi thưởng', expectedUrl: '/exchange', fallbackUrls: ['/rewards'] },
      { tab: 'VUI chơi', expectedUrl: '/games', fallbackUrls: [] },
      { tab: 'Tài khoản', expectedUrl: '/profile', fallbackUrls: ['/account'] },
      { tab: 'Trang chủ', expectedUrl: '/', fallbackUrls: ['/home'] }
    ];

    for (const nav of navigationTests) {
      await navigateToTab(page, nav.tab);
      
      // Check primary URL or fallbacks
      let urlMatched = false;
      const urls = [nav.expectedUrl, ...nav.fallbackUrls];
      
      for (const url of urls) {
        if (page.url().includes(url)) {
          urlMatched = true;
          break;
        }
      }
      
      if (!urlMatched) {
        // For debugging - show actual URL
        const actualUrl = page.url();
        console.log(`Expected URLs: ${urls.join(', ')}, Actual: ${actualUrl}`);
      }
      
      expect(urlMatched).toBeTruthy();
      await waitForPageLoad(page);
    }
  });

  test('should handle back navigation', async ({ page }) => {
    // Navigate to a specific page
    await page.goto('/exchange');
    await waitForPageLoad(page);

    // Go to reward detail (if available)
    const firstReward = page.locator('[data-testid*="reward"], .reward-card, .card').first();
    const hasRewards = await firstReward.isVisible().catch(() => false);
    
    if (hasRewards) {
      await firstReward.click();
      await waitForPageLoad(page);
      
      // Use browser back
      await page.goBack();
      await waitForPageLoad(page);
      
      // Should be back on exchange page
      expect(page.url()).toContain('exchange');
    }
  });

  test('should maintain navigation state on refresh', async ({ page }) => {
    // Navigate to specific page
    await navigateToTab(page, 'Đổi thưởng');
    await waitForPageLoad(page);
    
    const currentUrl = page.url();
    
    // Refresh page
    await page.reload();
    await waitForPageLoad(page);
    
    // Should stay on same page
    expect(page.url()).toBe(currentUrl);
  });

  test('should be responsive on mobile', async ({ page }) => {
    await testResponsive(page, async () => {
      // Check bottom navigation is still accessible
      const bottomNav = page.locator('[data-testid="bottom-navigation"], .bottom-navigation, nav').last();
      await expect(bottomNav).toBeVisible();
      
      // Test navigation still works
      await navigateToTab(page, 'Đổi thưởng');
      await waitForPageLoad(page);
      
      expect(page.url()).toMatch(/exchange|rewards|deals/);
    });
  });

  test('should handle deep linking', async ({ page }) => {
    const deepLinks = [
      '/rewards/123',
      '/merchants/abc',
      '/games/456', 
      '/my-rewards',
      '/profile'
    ];

    for (const link of deepLinks) {
      await page.goto(link);
      await waitForPageLoad(page);
      
      // Should not redirect to home (unless resource doesn't exist)
      const currentUrl = page.url();
      if (!currentUrl.includes('/login')) {
        // Either on the intended page or a valid error page
        const isOnIntendedPage = currentUrl.includes(link.split('/')[1]);
        const isOnErrorPage = currentUrl.includes('404') || currentUrl.includes('not-found');
        
        expect(isOnIntendedPage || isOnErrorPage).toBeTruthy();
      }
    }
  });

  test('should handle navigation with loading states', async ({ page }) => {
    // Navigate to data-heavy page
    await navigateToTab(page, 'Đổi thưởng');
    
    // Should show loading state briefly
    const loadingIndicators = [
      '.animate-spin',
      '[class*="skeleton"]',
      '[data-testid="loading"]',
      '.loading'
    ];
    
    // Check if any loading indicator appears (might be too fast to catch)
    for (const indicator of loadingIndicators) {
      const element = page.locator(indicator);
      const isVisible = await element.isVisible().catch(() => false);
      if (isVisible) {
        // Wait for it to disappear
        await element.waitFor({ state: 'hidden', timeout: 10000 });
        break;
      }
    }
    
    await waitForPageLoad(page);
    
    // Content should be loaded
    const content = page.locator('main, .main-content, .page-content, .content');
    await expect(content.first()).toBeVisible();
  });

  test('should show active navigation state', async ({ page }) => {
    const tabs = ['Trang chủ', 'Ưu đãi', 'Đổi thưởng', 'VUI chơi', 'Tài khoản'];
    
    for (const tab of tabs) {
      await navigateToTab(page, tab);
      await waitForPageLoad(page);
      
      // Find the tab element
      const tabElement = page.locator(`text="${tab}"`).first();
      
      // Check for active state indicators
      const activeIndicators = [
        'class*="active"',
        'class*="selected"', 
        'aria-current="page"',
        'data-active="true"'
      ];
      
      let hasActiveState = false;
      for (const indicator of activeIndicators) {
        const hasIndicator = await tabElement.locator(`[${indicator}]`).isVisible().catch(() => false);
        if (hasIndicator) {
          hasActiveState = true;
          break;
        }
      }
      
      // Or check parent for active state
      if (!hasActiveState) {
        const parent = tabElement.locator('..');
        for (const indicator of activeIndicators) {
          const hasIndicator = await parent.locator(`[${indicator}]`).isVisible().catch(() => false);
          if (hasIndicator) {
            hasActiveState = true;
            break;
          }
        }
      }
      
      // Active state might be visual (color change), so we'll just ensure tab is visible
      await expect(tabElement).toBeVisible();
    }
  });

  test('should handle navigation errors gracefully', async ({ page }) => {
    // Try navigating to non-existent routes
    const invalidRoutes = ['/invalid-page', '/does-not-exist', '/error-page'];
    
    for (const route of invalidRoutes) {
      await page.goto(route);
      await waitForPageLoad(page);
      
      const currentUrl = page.url();
      
      // Should either show 404 page or redirect to valid page
      const isOn404 = currentUrl.includes('404') || 
                     currentUrl.includes('not-found') ||
                     await page.locator('text="404"').isVisible().catch(() => false) ||
                     await page.locator('text="Not Found"').isVisible().catch(() => false) ||
                     await page.locator('text="Không tìm thấy"').isVisible().catch(() => false);
      
      const isRedirected = currentUrl !== route && !currentUrl.includes(route);
      
      expect(isOn404 || isRedirected).toBeTruthy();
    }
  });

  test('should support keyboard navigation', async ({ page }) => {
    // Focus on first navigation item
    const firstNavItem = page.locator('[data-testid="bottom-navigation"] button, .bottom-navigation button').first();
    await firstNavItem.focus();
    await expect(firstNavItem).toBeFocused();
    
    // Use Tab key to navigate through nav items
    await page.keyboard.press('Tab');
    
    // Second nav item should be focused
    const secondNavItem = page.locator('[data-testid="bottom-navigation"] button, .bottom-navigation button').nth(1);
    const isFocused = await secondNavItem.evaluate(el => el === document.activeElement);
    
    // If sequential focus navigation is supported
    if (isFocused) {
      await expect(secondNavItem).toBeFocused();
      
      // Enter to activate
      await page.keyboard.press('Enter');
      await waitForPageLoad(page);
      
      // Should navigate to corresponding page
      const currentUrl = page.url();
      expect(currentUrl).not.toBe('/'); // Should have navigated away from home
    }
  });
});