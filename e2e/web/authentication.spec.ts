import { test, expect } from '@playwright/test';
import { loginUser, logout, testCredentials } from '../helpers/auth';
import { waitForPageLoad, checkForErrors } from '../helpers/page-helpers';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing authentication
    await logout(page);
  });

  test('should redirect unauthenticated user to login', async ({ page }) => {
    // Try to access protected homepage
    await page.goto('/');
    
    // Should be redirected to login
    await expect(page).toHaveURL(/.*\/login/);
    
    // Login form should be visible
    await expect(page.locator('input[type="tel"]')).toBeVisible();
  });

  test('should login with phone and password', async ({ page }) => {
    await page.goto('/login');
    await waitForPageLoad(page);

    // Step 1: Enter phone number
    const phoneInput = page.locator('input[type="tel"]').first();
    await phoneInput.fill(testCredentials.username);
    
    // Submit phone
    const submitBtn = page.locator('button[type="submit"]').first();
    await submitBtn.click();
    
    // Wait for response
    await waitForPageLoad(page);
    
    // Check if password field appears (existing user)
    const passwordInput = page.locator('input[type="password"]');
    const isPasswordVisible = await passwordInput.isVisible().catch(() => false);
    
    if (isPasswordVisible) {
      // Password flow
      await passwordInput.fill(testCredentials.password);
      await submitBtn.click();
    } else {
      // OTP flow - use test OTP
      const otpInput = page.locator('input[placeholder*="OTP"], input[placeholder*="mã"]');
      await expect(otpInput).toBeVisible();
      await otpInput.fill('999999');
      await submitBtn.click();
    }
    
    // Should redirect to homepage after successful login
    await expect(page).toHaveURL('/', { timeout: 15000 });
    
    // Verify authentication token exists
    const token = await page.evaluate(() => 
      localStorage.getItem('token') || localStorage.getItem('authToken')
    );
    expect(token).toBeTruthy();
  });

  test('should handle invalid phone number', async ({ page }) => {
    await page.goto('/login');
    await waitForPageLoad(page);

    // Enter invalid phone
    await page.locator('input[type="tel"]').fill('123');
    await page.locator('button[type="submit"]').click();
    
    // Should show validation error
    const errorMessage = await checkForErrors(page);
    expect(errorMessage).toBeTruthy();
  });

  test('should handle invalid password', async ({ page }) => {
    await page.goto('/login');
    await waitForPageLoad(page);

    // Enter valid phone
    await page.locator('input[type="tel"]').fill(testCredentials.username);
    await page.locator('button[type="submit"]').click();
    await waitForPageLoad(page);

    // Check if password field appears
    const passwordInput = page.locator('input[type="password"]');
    const isPasswordVisible = await passwordInput.isVisible().catch(() => false);
    
    if (isPasswordVisible) {
      // Enter wrong password
      await passwordInput.fill('wrongpassword');
      await page.locator('button[type="submit"]').click();
      await waitForPageLoad(page);
      
      // Should show error or stay on login page
      const currentUrl = page.url();
      if (currentUrl.includes('/login')) {
        const errorMessage = await checkForErrors(page);
        expect(errorMessage).toBeTruthy();
      }
    } else {
      // Skip this test if OTP flow
      test.skip();
    }
  });

  test('should logout user', async ({ page }) => {
    // Login first
    await loginUser(page);
    await expect(page).toHaveURL('/');

    // Navigate to profile/logout (implementation depends on app structure)
    try {
      // Try common logout patterns
      const logoutSelectors = [
        'button:has-text("Đăng xuất")',
        'button:has-text("Logout")', 
        '[data-testid="logout"]',
        '.logout-btn'
      ];
      
      let logoutFound = false;
      for (const selector of logoutSelectors) {
        const logoutBtn = page.locator(selector).first();
        if (await logoutBtn.isVisible().catch(() => false)) {
          await logoutBtn.click();
          logoutFound = true;
          break;
        }
      }
      
      if (!logoutFound) {
        // Manual logout via localStorage clear
        await logout(page);
        await page.reload();
      }
      
      // Should redirect to login
      await expect(page).toHaveURL(/.*\/login/);
      
      // Token should be cleared
      const token = await page.evaluate(() => localStorage.getItem('token'));
      expect(token).toBeFalsy();
      
    } catch (error) {
      // If logout UI not found, just verify manual logout works
      await logout(page);
      await page.reload();
      await expect(page).toHaveURL(/.*\/login/);
    }
  });

  test('should maintain authentication across page refreshes', async ({ page }) => {
    await loginUser(page);
    await expect(page).toHaveURL('/');
    
    // Refresh page
    await page.reload();
    await waitForPageLoad(page);
    
    // Should still be authenticated
    await expect(page).toHaveURL('/');
    
    const token = await page.evaluate(() => localStorage.getItem('token'));
    expect(token).toBeTruthy();
  });

  test('should handle session expiry gracefully', async ({ page }) => {
    await loginUser(page);
    await expect(page).toHaveURL('/');
    
    // Simulate expired token by clearing storage
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    
    // Try to access protected resource
    await page.goto('/my-rewards');
    
    // Should redirect to login
    await expect(page).toHaveURL(/.*\/login/);
  });
});

test.describe('Protected Routes', () => {
  const protectedRoutes = [
    '/',
    '/my-rewards', 
    '/profile',
    '/membership',
    '/flash-sale',
    '/point',
    '/transaction-history',
    '/inbox'
  ];

  protectedRoutes.forEach(route => {
    test(`should protect route: ${route}`, async ({ page }) => {
      await logout(page);
      await page.goto(route);
      
      // Should redirect to login
      await expect(page).toHaveURL(/.*\/login/);
    });
  });
});

test.describe('Authentication State Persistence', () => {
  test.use({ storageState: { cookies: [], origins: [] } }); // Start without auth

  test('should remember login state in new tab', async ({ context }) => {
    const page1 = await context.newPage();
    
    // Login in first tab
    await loginUser(page1);
    await expect(page1).toHaveURL('/');
    
    // Open new tab
    const page2 = await context.newPage();
    await page2.goto('/');
    
    // Should be authenticated in new tab
    await expect(page2).toHaveURL('/');
    
    const token = await page2.evaluate(() => localStorage.getItem('token'));
    expect(token).toBeTruthy();
  });
});