import { test, expect } from '@playwright/test';
import { waitForPageLoad, fillFormField, mockApiResponse, verifyNavigation } from '../helpers/page-helpers';

test.describe('Search Functionality Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await waitForPageLoad(page);
  });

  test.describe('Search Interface', () => {
    test('should access search from homepage', async ({ page }) => {
      // Look for search input or search button on homepage
      const searchElements = [
        'input[type="search"]',
        'input[placeholder*="tìm"]',
        'input[placeholder*="search"]',
        'button[aria-label*="search"]',
        '.search-btn',
        '[data-testid="search"]'
      ];

      let searchFound = false;
      for (const selector of searchElements) {
        const element = page.locator(selector);
        if (await element.isVisible().catch(() => false)) {
          await element.click();
          searchFound = true;
          break;
        }
      }

      if (searchFound) {
        await waitForPageLoad(page);
        
        // Should navigate to search page or show search interface
        const currentUrl = page.url();
        const hasSearchInterface = currentUrl.includes('/search') || 
                                 await page.locator('input[type="search"]').isVisible().catch(() => false);
        
        expect(hasSearchInterface).toBeTruthy();
      } else {
        // Try navigating directly to search page
        await page.goto('/search');
        await waitForPageLoad(page);
        await expect(page).toHaveURL(/.*\/search/);
      }
    });

    test('should display search input', async ({ page }) => {
      await page.goto('/search');
      await waitForPageLoad(page);

      const searchInput = page.locator('input[type="search"], input[placeholder*="tìm"], input[placeholder*="search"]');
      await expect(searchInput.first()).toBeVisible();

      // Check placeholder text
      const placeholder = await searchInput.first().getAttribute('placeholder');
      expect(placeholder).toBeTruthy();
    });

    test('should show search suggestions or recent searches', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/search.*suggestions/, {
        data: {
          popular: ['Coffee', 'Pizza', 'Discount'],
          recent: ['Restaurant', 'Shopping']
        }
      });

      await page.goto('/search');
      await waitForPageLoad(page);

      // Check for popular searches
      const popularSearches = page.locator('.popular-search, .search-suggestion, [data-testid*="popular"]');
      const hasPopular = await popularSearches.first().isVisible().catch(() => false);

      if (hasPopular) {
        const itemCount = await popularSearches.count();
        expect(itemCount).toBeGreaterThan(0);
      }

      // Check for recent searches
      const recentSearches = page.locator('.recent-search, [data-testid*="recent"]');
      const hasRecent = await recentSearches.first().isVisible().catch(() => false);

      if (hasRecent) {
        const recentCount = await recentSearches.count();
        expect(recentCount).toBeGreaterThan(0);
      }
    });
  });

  test.describe('Search Execution', () => {
    test('should perform basic search', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/search/, {
        data: {
          merchants: [
            { id: '1', name: 'Coffee Shop', category: 'Food' },
            { id: '2', name: 'Pizza Place', category: 'Food' }
          ],
          rewards: [
            { id: '1', name: 'Coffee Discount', points: 100 }
          ],
          totalResults: 3
        }
      });

      await page.goto('/search');
      await waitForPageLoad(page);

      const searchInput = page.locator('input[type="search"]').first();
      await fillFormField(page, 'input[type="search"]', 'coffee');

      // Submit search
      const submitMethods = [
        () => page.keyboard.press('Enter'),
        () => page.locator('button[type="submit"], .search-btn').first().click(),
        () => page.locator('form').first().dispatchEvent('submit')
      ];

      for (const submit of submitMethods) {
        try {
          await submit();
          break;
        } catch {
          continue;
        }
      }

      await waitForPageLoad(page);

      // Should show search results or navigate to results page
      const isOnResultsPage = page.url().includes('/search/results') || page.url().includes('q=');
      const hasResults = await page.locator('.search-results, .result-item, [data-testid*="result"]')
                                 .first().isVisible().catch(() => false);

      expect(isOnResultsPage || hasResults).toBeTruthy();
    });

    test('should handle empty search', async ({ page }) => {
      await page.goto('/search');
      await waitForPageLoad(page);

      const searchInput = page.locator('input[type="search"]').first();
      await searchInput.fill('');
      await page.keyboard.press('Enter');

      // Should show validation message or remain on search page
      const currentUrl = page.url();
      const hasValidationMessage = await page.locator('.error, .validation-error')
                                           .isVisible().catch(() => false);

      // Either stays on search page or shows validation
      expect(currentUrl.includes('/search') || hasValidationMessage).toBeTruthy();
    });

    test('should handle search with no results', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/search/, {
        data: {
          merchants: [],
          rewards: [],
          totalResults: 0
        }
      });

      await page.goto('/search');
      await waitForPageLoad(page);

      await fillFormField(page, 'input[type="search"]', 'nonexistentitem');
      await page.keyboard.press('Enter');
      await waitForPageLoad(page);

      // Should show empty state
      const emptyState = page.locator('.empty-state, .no-results, [data-testid="empty"]');
      const emptyText = page.locator('text="Không tìm thấy", text="No results", text="No data"');

      const hasEmptyState = await emptyState.first().isVisible().catch(() => false);
      const hasEmptyText = await emptyText.first().isVisible().catch(() => false);

      expect(hasEmptyState || hasEmptyText).toBeTruthy();
    });
  });

  test.describe('Search Results', () => {
    test('should display search results with categories', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/search/, {
        data: {
          merchants: [
            { id: '1', name: 'Test Merchant', category: 'Food', image: 'merchant.jpg' }
          ],
          rewards: [
            { id: '1', name: 'Test Reward', points: 100, category: 'Discount' }
          ],
          totalResults: 2
        }
      });

      // Go directly to results page or perform search
      await page.goto('/search/results?q=test');
      await waitForPageLoad(page);

      // Check for result categories
      const categories = ['merchants', 'rewards', 'Thương hiệu', 'Phần thưởng'];
      let foundCategories = 0;

      for (const category of categories) {
        const categorySection = page.locator(`text="${category}"`).first();
        if (await categorySection.isVisible().catch(() => false)) {
          foundCategories++;
        }
      }

      expect(foundCategories).toBeGreaterThan(0);

      // Check for result items
      const resultItems = page.locator('.result-item, .search-result, .merchant-card, .reward-card');
      const itemCount = await resultItems.count();

      if (itemCount > 0) {
        await expect(resultItems.first()).toBeVisible();
      }
    });

    test('should filter results by category', async ({ page }) => {
      await page.goto('/search/results?q=test');
      await waitForPageLoad(page);

      // Look for category filter tabs
      const filterTabs = page.locator('.filter-tab, .category-filter, [role="tab"]');
      const tabCount = await filterTabs.count();

      if (tabCount > 1) {
        // Click on a filter tab
        const merchantTab = page.locator('text="Merchant", text="Thương hiệu"').first();
        const hasMerchantTab = await merchantTab.isVisible().catch(() => false);

        if (hasMerchantTab) {
          await merchantTab.click();
          await waitForPageLoad(page);

          // Results should be filtered
          const currentUrl = page.url();
          const hasFilter = currentUrl.includes('filter') || currentUrl.includes('category');

          if (hasFilter) {
            expect(currentUrl).toMatch(/filter|category|merchant/);
          }
        }
      }
    });

    test('should navigate to result details', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/search/, {
        data: {
          merchants: [
            { id: 'merchant1', name: 'Test Merchant' }
          ],
          rewards: [
            { id: 'reward1', name: 'Test Reward' }
          ]
        }
      });

      await page.goto('/search/results?q=test');
      await waitForPageLoad(page);

      // Click on first result
      const firstResult = page.locator('.result-item, .merchant-card, .reward-card').first();
      const hasResults = await firstResult.isVisible().catch(() => false);

      if (hasResults) {
        await firstResult.click();
        await waitForPageLoad(page);

        // Should navigate to detail page
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/merchant|reward|detail/);
      }
    });

    test('should handle pagination', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/search.*page=1/, {
        data: {
          merchants: Array.from({length: 10}, (_, i) => ({
            id: `merchant${i}`,
            name: `Merchant ${i}`
          })),
          totalResults: 25,
          currentPage: 1,
          totalPages: 3
        }
      });

      await page.goto('/search/results?q=test');
      await waitForPageLoad(page);

      // Look for pagination controls
      const paginationControls = [
        'button:has-text("Next")',
        'button:has-text("Tiếp")',
        '.pagination button',
        '[data-testid="pagination"]'
      ];

      for (const selector of paginationControls) {
        const paginationBtn = page.locator(selector).first();
        if (await paginationBtn.isVisible().catch(() => false)) {
          // Mock next page
          await mockApiResponse(page, /\/api.*\/search.*page=2/, {
            data: {
              merchants: Array.from({length: 10}, (_, i) => ({
                id: `merchant${i + 10}`,
                name: `Merchant ${i + 10}`
              })),
              totalResults: 25,
              currentPage: 2,
              totalPages: 3
            }
          });

          await paginationBtn.click();
          await waitForPageLoad(page);

          // Should load next page
          const currentUrl = page.url();
          expect(currentUrl).toMatch(/page=2|offset=/);
          break;
        }
      }
    });
  });

  test.describe('Search History', () => {
    test('should save search history', async ({ page }) => {
      await page.goto('/search');
      await waitForPageLoad(page);

      // Perform multiple searches
      const searches = ['coffee', 'pizza', 'discount'];
      
      for (const searchTerm of searches) {
        await fillFormField(page, 'input[type="search"]', searchTerm);
        await page.keyboard.press('Enter');
        await waitForPageLoad(page);
        
        // Go back to search page
        await page.goto('/search');
        await waitForPageLoad(page);
      }

      // Check if recent searches appear
      const recentSearches = page.locator('.recent-search, [data-testid*="recent"]');
      const hasRecent = await recentSearches.first().isVisible().catch(() => false);

      if (hasRecent) {
        // Should show at least one recent search
        const recentCount = await recentSearches.count();
        expect(recentCount).toBeGreaterThan(0);
      }
    });

    test('should clear search history', async ({ page }) => {
      await page.goto('/search');
      await waitForPageLoad(page);

      // Look for clear history button
      const clearBtn = page.locator('button:has-text("Clear"), button:has-text("Xóa"), .clear-history');
      const hasClearBtn = await clearBtn.first().isVisible().catch(() => false);

      if (hasClearBtn) {
        await clearBtn.first().click();
        await waitForPageLoad(page);

        // Recent searches should be gone
        const recentSearches = page.locator('.recent-search');
        const hasRecent = await recentSearches.first().isVisible().catch(() => false);
        
        expect(hasRecent).toBeFalsy();
      }
    });
  });

  test.describe('Search Suggestions', () => {
    test('should show autocomplete suggestions', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/search.*suggestions/, {
        data: {
          suggestions: ['Coffee Shop', 'Coffee Bean', 'Coffee Discount']
        }
      });

      await page.goto('/search');
      await waitForPageLoad(page);

      const searchInput = page.locator('input[type="search"]').first();
      await searchInput.fill('coff');

      // Wait for suggestions to appear
      await page.waitForTimeout(1000);

      const suggestions = page.locator('.suggestion-item, .autocomplete-item, [data-testid*="suggestion"]');
      const hasSuggestions = await suggestions.first().isVisible().catch(() => false);

      if (hasSuggestions) {
        const suggestionCount = await suggestions.count();
        expect(suggestionCount).toBeGreaterThan(0);

        // Click on first suggestion
        await suggestions.first().click();
        await waitForPageLoad(page);

        // Should perform search with selected suggestion
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/search|results|q=/);
      }
    });

    test('should handle popular search tags', async ({ page }) => {
      await page.goto('/search');
      await waitForPageLoad(page);

      const popularTags = page.locator('.popular-tag, .search-tag, [data-testid*="popular"]');
      const hasPopularTags = await popularTags.first().isVisible().catch(() => false);

      if (hasPopularTags) {
        // Click on first popular tag
        await popularTags.first().click();
        await waitForPageLoad(page);

        // Should perform search
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/search|results|q=/);
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle search API errors', async ({ page }) => {
      await mockApiResponse(page, /\/api.*\/search/, {
        status: 500,
        data: { error: 'Search service unavailable' }
      });

      await page.goto('/search');
      await waitForPageLoad(page);

      await fillFormField(page, 'input[type="search"]', 'test');
      await page.keyboard.press('Enter');
      await waitForPageLoad(page);

      // Should show error message
      const errorMessage = page.locator('.error-message, [data-testid="error"]');
      const hasError = await errorMessage.first().isVisible().catch(() => false);

      if (hasError) {
        await expect(errorMessage.first()).toBeVisible();
      }
    });

    test('should handle network connectivity issues', async ({ page }) => {
      await page.route(/\/api.*\/search/, route => route.abort());

      await page.goto('/search');
      await waitForPageLoad(page);

      await fillFormField(page, 'input[type="search"]', 'test');
      await page.keyboard.press('Enter');
      await waitForPageLoad(page);

      // Should show network error
      const networkError = page.locator('text="Network error", text="Connection error", .network-error');
      const retryBtn = page.locator('button:has-text("Retry"), button:has-text("Thử lại")');

      const hasError = await networkError.first().isVisible().catch(() => false);
      const hasRetry = await retryBtn.first().isVisible().catch(() => false);

      expect(hasError || hasRetry).toBeTruthy();
    });
  });
});