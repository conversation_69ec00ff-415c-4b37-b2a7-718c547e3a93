import { Page } from '@playwright/test';

export interface AuthCredentials {
  username: string;
  password: string;
}

export const testCredentials: AuthCredentials = {
  username: process.env.E2E_TEST_USERNAME || '0356432501', // Test phone from CLAUDE.md
  password: process.env.E2E_TEST_PASSWORD || '939393'      // Test password from CLAUDE.md
};

export async function loginUser(page: Page, credentials: AuthCredentials = testCredentials, baseURL?: string) {
  try {
    // Navigate to login page
    const loginUrl = baseURL ? `${baseURL}/login` : '/login';
    await page.goto(loginUrl, { timeout: 30000 });
    
    // Wait for login form to load
    await page.waitForLoadState('networkidle', { timeout: 15000 });
    
    // Step 1: Fill phone number (username is actually phone number)
    const phoneInput = page.locator('input[type="tel"], input[placeholder*="đi<PERSON>n thoại"], input[placeholder*="phone"]').first();
    await phoneInput.waitFor({ state: 'visible', timeout: 10000 });
    await phoneInput.fill(credentials.username);
    
    // Click submit to proceed to next step
    const submitButton = page.locator('button[type="submit"]').first();
    await submitButton.click();
    
    // Wait for response and check which step we're on
    await page.waitForLoadState('networkidle', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // Check if password input is visible (existing user with password)
    const passwordInput = page.locator('input[type="password"], input[placeholder*="mật khẩu"]');
    const isPasswordVisible = await passwordInput.isVisible().catch(() => false);
    
    if (isPasswordVisible) {
      // Step 2a: Password flow for existing user
      await passwordInput.fill(credentials.password);
      await submitButton.click();
      
      // Wait for login completion
      await page.waitForLoadState('networkidle', { timeout: 15000 });
      await page.waitForTimeout(3000);
      
    } else {
      // Step 2b: OTP flow for new user or OTP login
      const otpInput = page.locator('input[placeholder*="OTP"], input[placeholder*="mã"]');
      const isOtpVisible = await otpInput.isVisible().catch(() => false);
      
      if (isOtpVisible) {
        // Fill OTP (using test OTP from CLAUDE.md)
        await otpInput.fill('999999'); // Test OTP from CLAUDE.md
        await submitButton.click();
        
        // Wait for OTP verification
        await page.waitForLoadState('networkidle', { timeout: 15000 });
        await page.waitForTimeout(3000);
      }
    }
    
    // Verify login success by checking current URL and tokens
    const currentUrl = page.url();
    if (currentUrl.includes('/login')) {
      // Check if there's an error message on the login page
      const errorElement = page.locator('.bg-red-50, .text-red-600, [class*="error"]').first();
      const hasError = await errorElement.isVisible().catch(() => false);
      if (hasError) {
        const errorText = await errorElement.textContent();
        throw new Error(`Login failed with error: ${errorText}`);
      }
      throw new Error('Login failed - still on login page');
    }
    
    console.log('Login successful, redirected to:', currentUrl);
  } catch (error) {
    console.error('Login attempt failed:', error);
    throw error;
  }
}

export async function getAuthToken(page: Page): Promise<string | null> {
  // Try different possible token storage methods
  const token = await page.evaluate(() => {
    // Check localStorage for common token keys
    const tokenKeys = ['token', 'authToken', 'accessToken', 'auth_token', 'access_token'];
    for (const key of tokenKeys) {
      const token = localStorage.getItem(key);
      if (token) return token;
    }
    
    // Check sessionStorage
    for (const key of tokenKeys) {
      const token = sessionStorage.getItem(key);
      if (token) return token;
    }
    
    return null;
  });
  
  return token;
}

export async function setAuthToken(page: Page, token: string) {
  await page.evaluate((token) => {
    localStorage.setItem('token', token);
    localStorage.setItem('authToken', token);
    localStorage.setItem('accessToken', token);
  }, token);
}

export async function isLoggedIn(page: Page): Promise<boolean> {
  const token = await getAuthToken(page);
  return !!token;
}

export async function logout(page: Page) {
  await page.evaluate(() => {
    // Clear all possible token storage
    const tokenKeys = ['token', 'authToken', 'accessToken', 'auth_token', 'access_token', 'user'];
    tokenKeys.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });
  });
}