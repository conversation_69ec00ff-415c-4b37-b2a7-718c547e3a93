import { Page, expect } from '@playwright/test';

/**
 * Common page interaction helpers for TapTap web app testing
 */

export interface ElementSelectors {
  // Loading indicators
  loadingSpinner: string;
  loadingSkeleton: string;
  
  // Navigation
  bottomNavigation: string;
  backButton: string;
  
  // Common UI elements
  buttons: {
    primary: string;
    secondary: string;
    submit: string;
  };
  
  // Form elements
  inputs: {
    text: string;
    phone: string;
    password: string;
    otp: string;
    search: string;
  };
  
  // Content elements
  cards: string;
  lists: string;
  headers: string;
  
  // Error/success states
  errorMessages: string;
  successMessages: string;
}

export const commonSelectors: ElementSelectors = {
  loadingSpinner: '.animate-spin, [class*="loading"], [data-testid="loading"]',
  loadingSkeleton: '[class*="skeleton"], [data-testid="skeleton"]',
  
  bottomNavigation: '[data-testid="bottom-navigation"], .bottom-navigation',
  backButton: 'button[aria-label*="back"], button[title*="back"], [data-testid="back-button"]',
  
  buttons: {
    primary: 'button[class*="primary"], button[class*="bg-blue"], .btn-primary',
    secondary: 'button[class*="secondary"], .btn-secondary',
    submit: 'button[type="submit"], .btn-submit'
  },
  
  inputs: {
    text: 'input[type="text"]',
    phone: 'input[type="tel"], input[placeholder*="phone"], input[placeholder*="điện thoại"]',
    password: 'input[type="password"]',
    otp: 'input[placeholder*="OTP"], input[placeholder*="mã"]',
    search: 'input[placeholder*="search"], input[placeholder*="tìm"], input[type="search"]'
  },
  
  cards: '[class*="card"], [data-testid*="card"]',
  lists: '[class*="list"], ul, ol',
  headers: 'h1, h2, h3, [class*="header"], [data-testid*="header"]',
  
  errorMessages: '.text-red-600, [class*="error"], .bg-red-50, [data-testid="error"]',
  successMessages: '.text-green-600, [class*="success"], .bg-green-50, [data-testid="success"]'
};

/**
 * Wait for page to be fully loaded and interactive
 */
export async function waitForPageLoad(page: Page, timeout = 30000) {
  await page.waitForLoadState('networkidle', { timeout });
  
  // Wait for any loading indicators to disappear
  try {
    await page.waitForSelector(commonSelectors.loadingSpinner, { 
      state: 'hidden', 
      timeout: 10000 
    });
  } catch {
    // No loading spinner found, continue
  }
  
  try {
    await page.waitForSelector(commonSelectors.loadingSkeleton, { 
      state: 'hidden', 
      timeout: 10000 
    });
  } catch {
    // No loading skeleton found, continue
  }
}

/**
 * Navigate using bottom navigation
 */
export async function navigateToTab(page: Page, tabName: string) {
  const bottomNav = page.locator(commonSelectors.bottomNavigation);
  await bottomNav.waitFor({ state: 'visible' });
  
  // Try different ways to identify the tab
  const tabSelectors = [
    `button:has-text("${tabName}")`,
    `a:has-text("${tabName}")`,
    `[aria-label*="${tabName}"]`,
    `[title*="${tabName}"]`,
    `[data-testid*="${tabName.toLowerCase()}"]`
  ];
  
  for (const selector of tabSelectors) {
    try {
      const tab = bottomNav.locator(selector).first();
      if (await tab.isVisible()) {
        await tab.click();
        await waitForPageLoad(page);
        return;
      }
    } catch {
      continue;
    }
  }
  
  throw new Error(`Could not find tab: ${tabName}`);
}

/**
 * Check if user is authenticated by looking for common indicators
 */
export async function isUserAuthenticated(page: Page): Promise<boolean> {
  // Check for authentication indicators
  const authIndicators = [
    // Token in localStorage
    async () => {
      const token = await page.evaluate(() => localStorage.getItem('token') || localStorage.getItem('authToken'));
      return !!token;
    },
    
    // User profile elements
    async () => {
      const profileElements = page.locator('.profile, [data-testid="profile"], .user-avatar, .user-info');
      return await profileElements.first().isVisible().catch(() => false);
    },
    
    // Not on login page
    async () => {
      return !page.url().includes('/login');
    }
  ];
  
  for (const check of authIndicators) {
    if (await check()) {
      return true;
    }
  }
  
  return false;
}

/**
 * Wait for and verify successful navigation
 */
export async function verifyNavigation(page: Page, expectedPath: string, timeout = 15000) {
  await page.waitForFunction(
    (path) => window.location.pathname.includes(path),
    expectedPath,
    { timeout }
  );
  
  expect(page.url()).toContain(expectedPath);
}

/**
 * Fill form with retry mechanism
 */
export async function fillFormField(page: Page, selector: string, value: string, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const field = page.locator(selector);
      await field.waitFor({ state: 'visible', timeout: 10000 });
      await field.clear();
      await field.fill(value);
      
      // Verify the value was set
      const actualValue = await field.inputValue();
      if (actualValue === value) {
        return;
      }
    } catch (error) {
      if (i === retries - 1) throw error;
      await page.waitForTimeout(1000);
    }
  }
}

/**
 * Click with retry and wait for response
 */
export async function clickAndWait(
  page: Page, 
  selector: string, 
  waitForSelector?: string,
  timeout = 15000
) {
  const element = page.locator(selector);
  await element.waitFor({ state: 'visible' });
  
  // Wait for potential network requests
  const responsePromise = page.waitForResponse(response => 
    response.status() === 200 || response.status() === 201,
    { timeout: 5000 }
  ).catch(() => null);
  
  await element.click();
  
  // Wait for response if any
  await responsePromise;
  
  if (waitForSelector) {
    await page.waitForSelector(waitForSelector, { timeout });
  } else {
    await waitForPageLoad(page, timeout);
  }
}

/**
 * Scroll to element and ensure it's in viewport
 */
export async function scrollToElement(page: Page, selector: string) {
  const element = page.locator(selector);
  await element.scrollIntoViewIfNeeded();
  await element.waitFor({ state: 'visible' });
}

/**
 * Handle modal/dialog interactions
 */
export async function handleDialog(
  page: Page, 
  action: 'accept' | 'dismiss' = 'accept',
  message?: string
) {
  page.on('dialog', async (dialog) => {
    if (message) {
      expect(dialog.message()).toContain(message);
    }
    
    if (action === 'accept') {
      await dialog.accept();
    } else {
      await dialog.dismiss();
    }
  });
}

/**
 * Check for error messages on page
 */
export async function checkForErrors(page: Page) {
  const errorElement = page.locator(commonSelectors.errorMessages).first();
  const hasError = await errorElement.isVisible().catch(() => false);
  
  if (hasError) {
    const errorText = await errorElement.textContent();
    console.warn(`Error found on page: ${errorText}`);
    return errorText;
  }
  
  return null;
}

/**
 * Wait for success message
 */
export async function waitForSuccessMessage(page: Page, timeout = 10000) {
  const successElement = page.locator(commonSelectors.successMessages).first();
  await successElement.waitFor({ state: 'visible', timeout });
  return await successElement.textContent();
}

/**
 * Mock API responses for testing
 */
export async function mockApiResponse(
  page: Page, 
  url: string | RegExp, 
  response: any,
  status = 200
) {
  await page.route(url, async (route) => {
    await route.fulfill({
      status,
      contentType: 'application/json',
      body: JSON.stringify(response)
    });
  });
}

/**
 * Check if element contains specific text
 */
export async function expectElementToContainText(
  page: Page, 
  selector: string, 
  expectedText: string
) {
  const element = page.locator(selector);
  await element.waitFor({ state: 'visible' });
  await expect(element).toContainText(expectedText);
}

/**
 * Take screenshot with timestamp for debugging
 */
export async function takeDebugScreenshot(page: Page, name: string) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  await page.screenshot({ 
    path: `test-results/debug-${name}-${timestamp}.png`,
    fullPage: true 
  });
}

/**
 * Check responsive behavior
 */
export async function testResponsive(page: Page, action: () => Promise<void>) {
  const viewports = [
    { width: 375, height: 667, name: 'mobile' },
    { width: 768, height: 1024, name: 'tablet' },
    { width: 1920, height: 1080, name: 'desktop' }
  ];
  
  for (const viewport of viewports) {
    await page.setViewportSize(viewport);
    await page.waitForTimeout(500); // Allow for responsive adjustments
    
    try {
      await action();
    } catch (error) {
      throw new Error(`Test failed on ${viewport.name} viewport: ${error}`);
    }
  }
}

/**
 * Verify page accessibility basics
 */
export async function checkBasicAccessibility(page: Page) {
  // Check for basic accessibility features
  const checks = [
    // Page has title
    async () => {
      const title = await page.title();
      expect(title.length).toBeGreaterThan(0);
    },
    
    // Main heading exists
    async () => {
      const h1 = page.locator('h1').first();
      await expect(h1).toBeVisible();
    },
    
    // Interactive elements are focusable
    async () => {
      const interactiveElements = page.locator('button, a, input, select, textarea');
      const count = await interactiveElements.count();
      
      if (count > 0) {
        // Check first few interactive elements
        const elementsToCheck = Math.min(count, 3);
        for (let i = 0; i < elementsToCheck; i++) {
          const element = interactiveElements.nth(i);
          await element.focus();
          await expect(element).toBeFocused();
        }
      }
    }
  ];
  
  for (const check of checks) {
    await check();
  }
}