# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-platform TapTap application built as a Yarn monorepo with **Independent Platforms Architecture**. Web and Zalo Mini App platforms operate independently while sharing pure UI components. The project uses React 18.3.1, TypeScript 5.5.0, and Vite for building.

### Architecture Approach: Independent Platforms (2025)
The codebase follows an **Independent Platforms** pattern where:
- Each platform (Web/Zalo) has its own services, hooks, and state management
- Shared package contains only pure, presentation components
- No platform detection in shared components
- Apps can be easily separated into standalone projects

**Benefits:**
- ✅ Complete platform independence - can split into separate repos anytime
- ✅ No cross-platform dependencies to remove
- ✅ Each team can work independently
- ✅ Clear separation of concerns
- ✅ Easier testing and maintenance

**Key Principles:**
1. **Shared = Pure**: Only presentation components, no business logic
2. **Props over Platform Detection**: Pass callbacks and data via props
3. **Container Pattern**: Each app wraps shared components with platform logic
4. **Independent Services**: Each platform has its own service layer

### Legacy Mobile Project
**NOTE**: The directory `./taptap-mobile` contains the old mobile project that needs to be migrated to this current monorepo structure. This legacy project should be referenced for:
- Existing mobile-specific features and components
- Business logic that needs to be ported to the shared package
- Mobile UX patterns and design implementations
- API integrations and data models

When working on new features, always check if similar functionality exists in the legacy mobile project before implementing from scratch.

## Test Account

For testing the application, use this test account:
- **Phone**: **********
- **Password**: 939393
- **OTP**: 999999 (if required)

## Development Commands

### Core Development
```bash
# Install dependencies
yarn install

# Start development servers
yarn dev:web              # Web app on Vite dev server (http://localhost:9070/)
yarn dev:zalo             # Zalo Mini App development

# Build applications  
yarn build:web            # Build web app for production
yarn build:zalo           # Build Zalo Mini App
yarn build:all            # Build both platforms

# Component development
yarn storybook            # Start Storybook on port 6006
yarn build-storybook     # Build static Storybook
```

### Code Quality
```bash
# Linting and type checking
yarn lint                 # ESLint for all packages
yarn lint:fix             # Fix auto-fixable linting issues
yarn type-check           # TypeScript type checking across monorepo

# Testing
yarn test                 # Run Jest tests
yarn test:watch           # Run tests in watch mode
yarn test:coverage        # Generate coverage report
```

### Workspace Management
```bash
# Clean and reset
yarn run-clean            # Remove all node_modules
yarn install:all         # Reinstall with frozen lockfile
```

## Architecture

### Monorepo Structure
- **Root**: Yarn workspace configuration with Turborepo for build optimization
- **shared/**: 90% of codebase - platform-agnostic components, hooks, services, and utilities
- **apps/web/**: React SPA with Vite, React Router, Tailwind CSS, and Storybook
- **apps/taptap-zalo/**: Zalo Mini App using zmp-sdk and zmp-ui

### Shared Package Architecture
```
shared/
├── components/ui/        # Pure presentation components (no platform logic)
├── hooks/               # Shared hooks (data fetching, utilities)
├── services/            # Common services and API clients
├── utils/               # Utility functions and helpers
├── types/               # TypeScript definitions
├── constants/           # Configuration constants
└── styles/              # Design tokens and shared styles
```

### Web App Architecture
```
apps/web/
├── services/            # Web-specific services (auth, storage, navigation)
├── hooks/               # Web-specific hooks
├── store/               # Zustand stores
├── providers/           # React context providers
├── pages/               # Page components
└── components/          # Web-only components
```

### Zalo App Architecture
```
apps/taptap-zalo/
├── services/            # Zalo-specific services (ZMP SDK wrappers)
├── hooks/               # Zalo-specific hooks
├── store/               # Jotai atoms
├── pages/               # Page components
└── components/native/   # Zalo-only components
```

### Platform Independence
**IMPORTANT**: Platform detection is now ONLY used in platform-specific apps, NOT in shared components:
- Platform detection exists in `shared/utils/platform.ts` for backward compatibility
- Shared components are pure and receive all data/callbacks via props
- Each app handles its own platform-specific logic

### Component System
Shared components are **pure presentation components**:
- No platform detection or conditional logic
- All data and callbacks passed via props
- Use Tailwind CSS classes with design system tokens
- Export from `shared/components/ui/index.ts`
- Platform-specific behavior handled by parent containers

### Platform-Specific Components (2025)
**IMPORTANT**: Platform-specific components live in their respective apps:

```
apps/web/src/components/           # Web-only components
apps/taptap-zalo/src/components/   # Zalo-only components
shared/components/ui/               # Pure shared components
```

**Component Rules:**
- ✅ **Shared components**: Pure, no platform logic
- ✅ **Web components**: In `apps/web`, can use Web APIs
- ✅ **Zalo components**: In `apps/taptap-zalo`, can use ZMP SDK
- ❌ **Never**: Platform detection in shared components

## Refactoring Components to Pure (Independent Platforms)

When refactoring shared components to be pure:

### 1. Remove Platform Detection
```typescript
// ❌ BEFORE
import { isZaloMiniApp } from '../../utils/platform';
if (isZaloMiniApp()) { /* ... */ }

// ✅ AFTER
// Remove all platform checks - handle in parent container
```

### 2. Convert Navigation to Props
```typescript
// ❌ BEFORE
import { useNavigate } from 'react-router-dom';
const navigate = useNavigate();
onClick={() => navigate('/path')}

// ✅ AFTER
interface Props {
  onNavigate?: (path: string) => void;
}
onClick={() => onNavigate?.('/path')}
```

### 3. Move API Calls to Container
```typescript
// ❌ BEFORE (in shared component)
const [data, setData] = useState([]);
useEffect(() => {
  fetchData().then(setData);
}, []);

// ✅ AFTER (receive via props)
interface Props {
  data: Item[];
  loading?: boolean;
  error?: string | null;
}
```

### 4. Extract State Management
```typescript
// ❌ BEFORE
const { user, points } = useAuthStore();

// ✅ AFTER
interface Props {
  user?: User;
  points?: number;
}
```

## Development Patterns

### Platform-Specific Logic
```typescript
// ❌ OLD PATTERN (Don't use in shared)
import { isZaloMiniApp } from '@taptap/shared';
if (isZaloMiniApp()) { /* ... */ }

// ✅ NEW PATTERN (Use props)
interface Props {
  onNavigate: (path: string) => void;
  onShare?: (data: any) => void;
}
```

### Shared Components Usage
```typescript
// In Web app
import { Button, Card } from '@taptap/shared';
import { webNavigationService } from '../services/webNavigation';

<Card onClick={() => webNavigationService.to('/details')} />

// In Zalo app
import { Button, Card } from '@taptap/shared';
import { useZaloNavigation } from '../hooks/useZaloNavigation';

const { navigate } = useZaloNavigation();
<Card onClick={() => navigate('/details')} />
```

### Authentication Pattern
```typescript
// Web app
import { useWebAuth } from '../hooks/useWebAuth';
const { login, user, loading } = useWebAuth();

// Zalo app
import { useZaloAuth } from '../hooks/useZaloAuth';
const { login, user, loading } = useZaloAuth();
```

### Storage Pattern
```typescript
// Web app
import { webStorageService } from '../services/webStorage';
webStorageService.setLocal('key', value);

// Zalo app
import { zaloStorageService } from '../services/zaloStorage';
zaloStorageService.set('key', value);
```

## Testing

- **Framework**: Jest with jsdom environment
- **Setup**: `jest.setup.ts` with Testing Library configuration
- **Coverage**: Focused on `shared/` package
- **Test files**: `*.test.ts`, `*.test.tsx`, or `__tests__/` directories
- **Mocking**: Module name mapping for `@/` alias to `shared/`

## TypeScript Configuration

- **Target**: ES2020 with modern module resolution
- **Paths**: 
  - `@shared/*` → `./shared/*`
  - `@taptap/shared` → `./shared/index`
  - `@taptap/web` → `./apps/web/src`
- **Strict mode**: Enabled with unused locals/parameters checking

## Build System

- **Turborepo**: Optimizes builds with dependency caching
- **Vite**: Build tool for both web and Zalo apps
- **ESLint**: Configured for React, TypeScript, and React Hooks
- **TypeScript**: Strict type checking across workspace

## Platform Constraints

### Zalo Mini App
- **Bundle size**: <3MB total limit  
- **SDK**: zmp-sdk latest with zmp-ui latest
- **State**: Jotai for state management
- **Commands**: `zmp start`, `zmp deploy`

### Web App  
- **Bundle size**: ~500KB initial target
- **Router**: React Router 6.26.0
- **State**: Zustand 4.5.0
- **Commands**: Standard Vite dev/build

## API Services and HTTP Client

### Creating API Services
**IMPORTANT**: When creating new API service files in `shared/services/api/`:

#### Import Rules for getHttpClient:
```typescript
// ✅ CORRECT: Files directly in /api/ folder
import { getHttpClient } from '../http';

// ✅ CORRECT: Files in subfolders like /api/auth/, /api/merchant/
import { getHttpClient } from '../../http';

// ❌ WRONG: Never import directly from httpClient.ts
import { getHttpClient } from '../http/httpClient';
```

**Rule**: Count the directory levels from your file to `shared/services/` folder to determine the correct import path.

## API Response Structure

**IMPORTANT**: The HTTP client handles both new and legacy API response formats automatically:

### Current API Format (2025)
APIs now return responses in this structure:
```json
{
  "status": {
    "message": "Success",
    "code": 200,
    "success": true
  },
  "data": [...]
}
```

### Response Handling
The HTTP client (`shared/services/http/httpClient.ts`) automatically:
1. **Detects the response format** (new vs legacy)
2. **Extracts the actual data** from `response.data` (new format) or uses response directly (legacy)
3. **Handles status checking** via `status.success` and `status.code` fields
4. **Returns clean data** to API consumers

**CRITICAL HTTP CLIENT RULE**: All `httpClient.get()`, `httpClient.post()`, `httpClient.put()`, `httpClient.delete()`, `httpClient.patch()` methods now return the FULL API response structure, NOT just the data field:

```json
{
  "status": {
    "message": "Success", 
    "code": 200,
    "success": true
  },
  "data": {
    // Actual API data here
    "topKeyword": [...],
    "history": [...], 
    "popular": [...]
  },
  "meta": {
    "currentPage": 1,
    "pageSize": 10,
    "totalPages": 0,
    "totalRows": 0
  }
}
```

### For API Consumers (UPDATED 2025)
- ✅ **CORRECT**: `response.data.sections` - Access nested data properties
- ❌ **WRONG**: `response.sections` - Missing .data level
- ✅ **DEFENSIVE**: `response?.data?.sections || []` - Always use defensive checks
- ✅ **STATUS CHECK**: Use `response.status.success` to verify API success
- ✅ **META ACCESS**: Use `response.meta.totalPages` to get pagination info
- 🔄 **BREAKING CHANGE**: HTTP client methods NO LONGER auto-unwrap response.data
- 🚨 **MUST UPDATE**: All API consumers must now access `.data` property explicitly

### HTTP Client Method Changes (2025)
```typescript
// ❌ OLD BEHAVIOR (before 2025)
const userData = await httpClient.get('/users/123'); // returned just the data

// ✅ NEW BEHAVIOR (2025+)
const response = await httpClient.get('/users/123'); // returns full response
const userData = response.data; // must extract .data explicitly
const isSuccess = response.status.success; // can check status
const pagination = response.meta; // can access pagination
```

The HTTP client now returns the COMPLETE response structure for full API transparency.

## Key Files to Understand

- `shared/utils/platform.ts`: Platform detection logic
- `shared/components/ui/`: Core UI component implementations
- `shared/services/`: Authentication and storage service adapters
- `shared/hooks/`: Custom hooks for platform features
- `turbo.json`: Build pipeline configuration
- `package.json`: Workspace scripts and dependency management
- `docs/z-index-system.md`: Z-index layer system and guidelines

## Development Workflow

1. Always test changes on both platforms: `yarn dev:web` and `yarn dev:zalo`
2. Run type checking before commits: `yarn type-check`
3. Use Storybook for component development: `yarn storybook`
4. Follow the platform-aware component pattern for new UI components
5. Add new shared functionality to the `shared/` package first
6. Use the unified hooks and services for platform-specific features

## Z-Index Layer Management

**CRITICAL**: Always follow the z-index layer system defined in `docs/z-index-system.md`

### Z-Index Scale (MUST follow):
- `z-navbar` (40): Bottom navigation bar
- `z-backdrop` (50): Modal backdrops  
- `z-drawer` (60): BottomSheet, side drawers
- `z-modal` (70): Modals, dialogs
- `z-notification` (90): Toast notifications

### Rules:
1. **NEVER use hardcoded z-index values** like `z-50` or `z-[999]`
2. **ALWAYS use Tailwind z-index classes** from our defined scale
3. **BottomSheet MUST use `z-drawer`** to appear above navbar
4. **Modal/Dialog MUST use `z-modal`** for proper layering
5. **Toast MUST use `z-notification`** to stay on top

### Common Issues:
- If BottomSheet appears behind navbar → Use `z-drawer` not `z-50`
- If Modal backdrop doesn't cover navbar → Use `z-backdrop` minimum
- If Toast is hidden → Use `z-notification` not `z-50`

## Component Management Rules

### CRITICAL: Avoid Component Duplication
**ALWAYS follow this workflow when implementing new designs:**

1. **Check Component Inventory First**
   - Review `shared/COMPONENTS_INVENTORY.md` before starting
   - Check existing components in `shared/components/ui/`
   - Look for similar functionality, layout patterns, or design elements

2. **Analyze & Reuse Strategy**
   ```typescript
   // ✅ GOOD: Check what exists first
   import { SectionHeader, Card } from '@taptap/shared';
   
   // ❌ BAD: Creating duplicate SectionHeader without checking
   const MyCustomHeader = () => { /* duplicate logic */ };
   ```

3. **Smart Component Decisions**
   - **Reuse**: If 80%+ similar to existing component
   - **Extend**: If 60-80% similar, add props/variants to existing
   - **Compose**: If can be built with existing components
   - **Create New**: Only if <60% similar and serves distinct purpose

4. **Documentation Requirements**
   - **Always update** `COMPONENTS_INVENTORY.md` when adding new components
   - Include component size, purpose, and composition patterns
   - Document reusability guidelines and avoid duplicate creation

### Platform-Specific Component Guidelines

**CRITICAL**: For Zalo Mini App-only components to avoid confusion:

1. **Naming Convention**: 
   - Prefix component names with `Zalo` (e.g., `ZaloEntrance`, `ZaloOnboarding`)
   - Use descriptive names that clearly indicate Zalo-specific functionality

2. **Platform Detection**: 
   ```typescript
   // ✅ GOOD: Always check platform in Zalo-only components
   import { isZaloMiniApp } from '../../../utils/platform';
   
   export const ZaloEntrance: React.FC<Props> = (props) => {
     // Only render in Zalo Mini App environment
     if (!isZaloMiniApp()) {
       return null;
     }
     return <div>...</div>;
   };
   ```

3. **File Structure**:
   - Store in `shared/components/ui/ZaloComponentName/`
   - Include proper TypeScript interfaces with `Props` suffix
   - Add Storybook stories for documentation
   - Export from main `shared/components/ui/index.ts`

4. **Asset Management for Zalo Components**:
   - Store Zalo-specific assets in `shared/assets/images/zalo/`
   - Use proper ES module imports: `import imageSrc from '../../../assets/images/zalo/filename.png'`
   - Download real Figma assets, avoid placeholders

### Figma Implementation Workflow

1. **Extract Design**: Use `mcp__figma2__get_figma_data` for accurate specs
2. **Download Assets**: Use `mcp__figma2__download_figma_images` for images/icons
3. **Inventory Check**: Cross-reference with existing components
4. **Implementation**: 
   - Reuse existing components where possible
   - Create targeted new components only when necessary
   - Follow mobile-first approach (414px max-width)
   - **IMPORTANT**: Ignore UI bars, status bars, and system-level elements (e.g., "UI Bars / Status Bars / Black Base") from Figma designs
5. **Integration**: Add to web app with proper asset imports
6. **Documentation**: Update inventory and add Storybook stories

### Mobile-First Design Rules

- **Container Width**: 414px max-width, centered on larger screens
- **Navigation**: Bottom navigation always visible
- **Layout**: Vertical stacking, horizontal scroll for card collections
- **Cards**: Fixed widths (278px NewsCard, 132px FlashSaleCard)
- **Typography**: Mobile-optimized sizes (10px-16px range)
- **Images**: Standard heights (120px NewsCard, 132px FlashSaleCard)

### Asset Management

- **Images**: Store in `shared/assets/images/` and `apps/web/src/assets/images/`
- **Icons**: Store in `shared/assets/icons/` as SVG files
- **Import Pattern**: Use Vite asset imports in web app for proper bundling
- **Figma Assets**: Download real assets, avoid placeholders

#### SVG Icon Import Rules
**IMPORTANT**: Always import SVG icons as ES modules, never use direct path strings in src attributes:

```typescript
// ✅ CORRECT: Import SVG as module
import websiteIcon from '../../../assets/icons/website-icon.svg';
<img src={websiteIcon} alt="Website" />

// ❌ WRONG: Direct path string
<img src="/shared/assets/icons/website-icon.svg" alt="Website" />
```

This ensures proper bundling, caching, and path resolution in both development and production builds.

### Component Composition Patterns

```typescript
// ✅ GOOD: Composition over duplication
const FlashSaleSection = () => (
  <div>
    <SectionHeader title="Flash Sale" actionText="Tất cả" />
    <div className="flex gap-3 overflow-x-auto">
      {items.map(item => <FlashSaleCard key={item.id} {...item} />)}
    </div>
  </div>
);

// ❌ BAD: Recreating header functionality
const FlashSaleSection = () => (
  <div>
    <div className="flex justify-between">
      <h2>Flash Sale</h2>
      <button>Tất cả</button>
    </div>
    {/* ... rest of component */}
  </div>
);
```

# MCP Servers
  - The Figma Dev Mode MCP Server provides an assets endpoint which can serve image and SVG assets
  - IMPORTANT: If the Figma Dev Mode MCP Server returns a localhost source for an image or an SVG, use that image or SVG source directly
  - IMPORTANT: DO NOT import/add new icon packages, all the assets should be in the Figma payload
  - IMPORTANT: do NOT use or create placeholders if a localhost source is provided

## figma

## figma2
- get_figma_data function with the link you provided. Using the information returned from this function, your agent will generate a design.
- download_figma_images : Download SVG and PNG images used in a Figma file based on the IDs of image or icon

## chrome-mcp-stdio
- **Debugging Commands**:
  - `chrome_navigate`: Navigate to URLs or refresh current tab
  - `chrome_get_web_content`: Get page content and metadata
  - `chrome_console`: Capture console logs and errors
  - `chrome_screenshot`: Take screenshots (use `storeBase64: true` for viewing)
  - `chrome_network_debugger_start/stop`: Monitor network requests with response bodies
  - `chrome_click_element`, `chrome_fill_or_select`: Interact with page elements
  
- **Error Debugging Workflow**:
  1. Navigate to the problematic URL
  2. Get console logs to identify JavaScript errors
  3. Check network requests for failed API calls or 500 errors
  4. Take screenshots to see visual issues
  5. Inspect page content for missing elements

- **Common Issues**:
  - **500 Internal Server Error**: Usually module resolution conflicts or missing files
  - **Component Export Conflicts**: Duplicate exports or missing component files
  - **Vite Build Errors**: Check for duplicate files, incorrect import paths, or circular dependencies

<!-- BACKLOG.MD GUIDELINES START -->
# Instructions for the usage of Backlog.md CLI Tool

## 1. Source of Truth

- Tasks live under **`backlog/tasks/`** (drafts under **`backlog/drafts/`**).
- Every implementation decision starts with reading the corresponding Markdown task file.
- Project documentation is in **`backlog/docs/`**.
- Project decisions are in **`backlog/decisions/`**.

## 2. Defining Tasks

### **Title**

Use a clear brief title that summarizes the task.

### **Description**: (The **"why"**)

Provide a concise summary of the task purpose and its goal. Do not add implementation details here. It
should explain the purpose and context of the task. Code snippets should be avoided.

### **Acceptance Criteria**: (The **"what"**)

List specific, measurable outcomes that define what means to reach the goal from the description. Use checkboxes (`- [ ]`) for tracking.
When defining `## Acceptance Criteria` for a task, focus on **outcomes, behaviors, and verifiable requirements** rather
than step-by-step implementation details.
Acceptance Criteria (AC) define *what* conditions must be met for the task to be considered complete.
They should be testable and confirm that the core purpose of the task is achieved.
**Key Principles for Good ACs:**

- **Outcome-Oriented:** Focus on the result, not the method.
- **Testable/Verifiable:** Each criterion should be something that can be objectively tested or verified.
- **Clear and Concise:** Unambiguous language.
- **Complete:** Collectively, ACs should cover the scope of the task.
- **User-Focused (where applicable):** Frame ACs from the perspective of the end-user or the system's external behavior.

    - *Good Example:* "- [ ] User can successfully log in with valid credentials."
    - *Good Example:* "- [ ] System processes 1000 requests per second without errors."
    - *Bad Example (Implementation Step):* "- [ ] Add a new function `handleLogin()` in `auth.ts`."

### Task file

Once a task is created it will be stored in `backlog/tasks/` directory as a Markdown file with the format
`task-<id> - <title>.md` (e.g. `task-42 - Add GraphQL resolver.md`).

### Additional task requirements

- Tasks must be **atomic** and **testable**. If a task is too large, break it down into smaller subtasks.
  Each task should represent a single unit of work that can be completed in a single PR.

- **Never** reference tasks that are to be done in the future or that are not yet created. You can only reference
  previous
  tasks (id < current task id).

- When creating multiple tasks, ensure they are **independent** and they do not depend on future tasks.   
  Example of wrong tasks splitting: task 1: "Add API endpoint for user data", task 2: "Define the user model and DB
  schema".  
  Example of correct tasks splitting: task 1: "Add system for handling API requests", task 2: "Add user model and DB
  schema", task 3: "Add API endpoint for user data".

## 3. Recommended Task Anatomy

```markdown
# task‑42 - Add GraphQL resolver

## Description (the why)

Short, imperative explanation of the goal of the task and why it is needed.

## Acceptance Criteria (the what)

- [ ] Resolver returns correct data for happy path
- [ ] Error response matches REST
- [ ] P95 latency ≤ 50 ms under 100 RPS

## Implementation Plan (the how) (added after starting work on a task)

1. Research existing GraphQL resolver patterns
2. Implement basic resolver with error handling
3. Add performance monitoring
4. Write unit and integration tests
5. Benchmark performance under load

## Implementation Notes (only added after finishing work on a task)

- Approach taken
- Features implemented or modified
- Technical decisions and trade-offs
- Modified or added files
```

## 6. Implementing Tasks

Mandatory sections for every task:

- **Implementation Plan**: (The **"how"**) Outline the steps to achieve the task. Because the implementation details may
  change after the task is created, **the implementation plan must be added only after putting the task in progress**
  and before starting working on the task.
- **Implementation Notes**: Document your approach, decisions, challenges, and any deviations from the plan. This
  section is added after you are done working on the task. It should summarize what you did and why you did it. Keep it
  concise but informative.

**IMPORTANT**: Do not implement anything else that deviates from the **Acceptance Criteria**. If you need to
implement something that is not in the AC, update the AC first and then implement it or create a new task for it.

## 2. Typical Workflow

```bash
# 1 Identify work
backlog task list -s "To Do" --plain

# 2 Read details & documentation
backlog task 42 --plain
# Read also all documentation files in `backlog/docs/` directory.
# Read also all decision files in `backlog/decisions/` directory.

# 3 Start work: assign yourself & move column
backlog task edit 42 -a @{yourself} -s "In Progress"

# 4 Add implementation plan before starting
backlog task edit 42 --plan "1. Analyze current implementation\n2. Identify bottlenecks\n3. Refactor in phases"

# 5 Break work down if needed by creating subtasks or additional tasks
backlog task create "Refactor DB layer" -p 42 -a @{yourself} -d "Description" --ac "Tests pass,Performance improved"

# 6 Complete and mark Done
backlog task edit 42 -s Done --notes "Implemented GraphQL resolver with error handling and performance monitoring"
```

### 7. Final Steps Before Marking a Task as Done

Always ensure you have:

1. ✅ Marked all acceptance criteria as completed (change `- [ ]` to `- [x]`)
2. ✅ Added an `## Implementation Notes` section documenting your approach
3. ✅ Run all tests and linting checks
4. ✅ Updated relevant documentation

## 8. Definition of Done (DoD)

A task is **Done** only when **ALL** of the following are complete:

1. **Acceptance criteria** checklist in the task file is fully checked (all `- [ ]` changed to `- [x]`).
2. **Implementation plan** was followed or deviations were documented in Implementation Notes.
3. **Automated tests** (unit + integration) cover new logic.
4. **Static analysis**: linter & formatter succeed.
5. **Documentation**:
    - All relevant docs updated (any relevant README file, backlog/docs, backlog/decisions, etc.).
    - Task file **MUST** have an `## Implementation Notes` section added summarising:
        - Approach taken
        - Features implemented or modified
        - Technical decisions and trade-offs
        - Modified or added files
6. **Review**: self review code.
7. **Task hygiene**: status set to **Done** via CLI (`backlog task edit <id> -s Done`).
8. **No regressions**: performance, security and licence checks green.

⚠️ **IMPORTANT**: Never mark a task as Done without completing ALL items above.

## 9. Handy CLI Commands

| Purpose          | Command                                                                |
|------------------|------------------------------------------------------------------------|
| Create task      | `backlog task create "Add OAuth"`                                      |
| Create with desc | `backlog task create "Feature" -d "Enables users to use this feature"` |
| Create with AC   | `backlog task create "Feature" --ac "Must work,Must be tested"`        |
| Create with deps | `backlog task create "Feature" --dep task-1,task-2`                    |
| Create sub task  | `backlog task create -p 14 "Add Google auth"`                          |
| List tasks       | `backlog task list --plain`                                            |
| View detail      | `backlog task 7 --plain`                                               |
| Edit             | `backlog task edit 7 -a @{yourself} -l auth,backend`                   |
| Add plan         | `backlog task edit 7 --plan "Implementation approach"`                 |
| Add AC           | `backlog task edit 7 --ac "New criterion,Another one"`                 |
| Add deps         | `backlog task edit 7 --dep task-1,task-2`                              |
| Add notes        | `backlog task edit 7 --notes "We added this and that feature because"` |
| Mark as done     | `backlog task edit 7 -s "Done"`                                        |
| Archive          | `backlog task archive 7`                                               |
| Draft flow       | `backlog draft create "Spike GraphQL"` → `backlog draft promote 3.1`   |
| Demote to draft  | `backlog task demote <task-id>`                                        |

## 10. Tips for AI Agents

- **Always use `--plain` flag** when listing or viewing tasks for AI-friendly text output instead of using Backlog.md
  interactive UI.
- When users mention to create a task, they mean to create a task using Backlog.md CLI tool.

<!-- BACKLOG.MD GUIDELINES END -->

## Serena MCP Rules

## Development Server Management
**CRITICAL: Do NOT use `execute_shell_command` to start development servers when using Serena MCP.**

- ❌ **Never run**: `yarn dev:web`, `yarn dev:zalo`, `yarn storybook` via `execute_shell_command`, `list_dir`
- ❌ **Never run**: Any long-running development processes via shell commands
- ✅ **Instead**: Inform user to manually start servers in separate terminal windows
- ✅ **Use shell commands for**: Quick operations like `yarn lint`, `yarn type-check`, `yarn build`

**Reasoning**: Development servers are long-running processes that should remain active in user's control. Serena should focus on code analysis and modifications, not process management.

**Correct approach**:
```
User: "Start the web server and test the camera page"
Assistant: "Please start the web server manually with `yarn dev:web` in a separate terminal, then navigate to /camera to test the changes I've implemented."
```

## console-ninja mcp rule
- Cần kiêm tra các response API trả về thì dùng console-ninja mcp
- Chỉ console.log khi cần, console.log đúng biến cần, Xoá toàn bộ console.log khi debug xong

## context7 mcp rule
- Luôn kiểm tra docs bằng context7 mcp trước khi implement

## Component
- LUÔN viết component vào shared/components/ui, luôn kiểm tra các component đã có trước khi viết

