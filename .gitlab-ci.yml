variables:
    APP_NAME: "enabler-webapp"
    REPOSITORY_NAME: "$AWS_ID/$APP_NAME"
    REPLICAS: "1"

include:
  - project: 'whitelable/infrastructure/sharedlibrary'
    ref: prod
    file: 
      - 'templates/build.yml'
      - 'templates/commit.yml'
      - 'templates/merge.yml'
      - 'templates/deploy.yml'
      - 'templates/test.yml'
      - 'vars/global.yml'
      - 'shared/share-before-script.yml'

stages:
  - build
  - test
  - commit
  - merge
  - deploy

## Build and Push Image to ECR
build_and_push_image:
  stage: build
  before_script:
    - !reference [.share_before_script, before_script]
    - !reference [.share_before_script_build, before_script]
    - sed -i '/ENV[ ]TZ='"'"'Asia*/r '"$FOLDER_DEPLOY/$BRANCH_DEPLOY/$APP_NAME/.env.$BRANCH_DEPLOY"'' Dockerfile
  extends:
    - .build_and_push_image_nodejs_to_ECR

## Test 
test_image:
  stage: test
  before_script:
    - !reference [.share_before_script, before_script]
    - !reference [.share_before_script_test, before_script]
  extends: .docker_image_heath_check

## Change Image tag repository
change_image_tag_repository:
  stage: commit
  before_script:
    - !reference [.share_before_script, before_script]
    - !reference [.share_before_script_git, before_script]
    - !reference [.share_before_script_commit, before_script]
  extends: .change_image_tag_repository

# Merge git repository
merge_git_repository:
  stage: merge
  before_script:
    - !reference [.share_before_script, before_script]
    - !reference [.share_before_script_git, before_script]
    - !reference [.share_before_script_merge, before_script]
  extends: .merge_git_repository

## Deploy service to K8s cluster
deploy_service_k8scluster:
  stage: deploy
  before_script:
    - !reference [.share_before_script, before_script]
    - !reference [.share_before_script_git, before_script]
    - !reference [.share_before_script_deploy, before_script]
  extends: .deploy_service_k8scluster
