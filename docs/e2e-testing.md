# E2E Testing with <PERSON><PERSON>

## Overview

This project uses [Playwright](https://playwright.dev/) for end-to-end testing across both Web and Zalo Mini App platforms.

## Setup

### Installation

Dependencies are already installed. To install browsers:

```bash
yarn playwright:install
```

## Test Structure

```
e2e/
├── web/                    # Web app E2E tests
│   ├── homepage.spec.ts
│   └── my-rewards.spec.ts
├── zalo/                   # Zalo Mini App E2E tests
│   └── zalo-homepage.spec.ts
└── global-setup.ts         # Global test setup
```

## Running Tests

### All Tests
```bash
yarn test:e2e
```

### Platform-Specific Tests
```bash
# Web app only (all browsers)
yarn test:e2e:web

# Zalo Mini App only
yarn test:e2e:zalo

# Mobile viewports only
yarn test:e2e:mobile
```

### Development & Debugging
```bash
# Interactive UI mode
yarn test:e2e:ui

# Headed mode (see browser)
yarn test:e2e:headed

# Debug mode with DevTools
yarn test:e2e:debug
```

## Test Projects

The configuration includes multiple test projects:

### Web App Projects
- **web-chromium**: Desktop Chrome testing
- **web-firefox**: Desktop Firefox testing  
- **web-webkit**: Desktop Safari testing
- **web-mobile-chrome**: Mobile Chrome (Pixel 5)
- **web-mobile-safari**: Mobile Safari (iPhone 12)

### Zalo Mini App Projects
- **zalo-chromium**: Desktop Chrome testing
- **zalo-mobile-chrome**: Mobile Chrome testing

## Development Servers

Tests automatically start development servers:
- **Web**: `http://localhost:9070` (via `yarn dev:web`)
- **Zalo**: `http://localhost:3000` (via `yarn dev:zalo`)

## Writing Tests

### Basic Test Structure
```typescript
import { test, expect } from '@playwright/test';

test.describe('Feature Name', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/path');
  });

  test('should do something', async ({ page }) => {
    // Test implementation
    await expect(page.locator('.element')).toBeVisible();
  });
});
```

### Best Practices

1. **Mobile-First**: Test mobile viewports (414px width)
2. **Wait for Network**: Use `networkidle` for API calls
3. **Robust Selectors**: Prefer `data-testid` attributes
4. **Loading States**: Test loading and empty states
5. **Error Handling**: Test error scenarios

### Platform-Specific Tests

#### Web App Tests
- Focus on React Router navigation
- Test responsive design breakpoints
- Verify Web API features

#### Zalo Mini App Tests  
- Test within Zalo environment constraints
- Verify mobile-only interactions
- Check ZMP SDK integration

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Install dependencies
  run: yarn install --frozen-lockfile

- name: Install Playwright browsers
  run: yarn playwright:install

- name: Run E2E tests
  run: yarn test:e2e
```

### Test Artifacts
- Screenshots on failure
- Videos on failure  
- HTML reports
- JUnit XML reports

## Debugging Failed Tests

### Local Debugging
```bash
# Debug specific test
yarn test:e2e:debug e2e/web/homepage.spec.ts

# Run with headed browser
yarn test:e2e:headed

# Generate trace files
PWDEBUG=1 yarn test:e2e
```

### Trace Viewer
```bash
# View trace after test failure
npx playwright show-trace test-results/*/trace.zip
```

## Configuration

Key configuration in `playwright.config.ts`:

- **Test Directory**: `./e2e`
- **Parallel Execution**: Enabled
- **Retries**: 2 on CI, 0 locally  
- **Reporters**: HTML + JUnit
- **Screenshots**: On failure only
- **Videos**: On failure only

## Maintenance

### Updating Tests
1. Keep tests in sync with UI changes
2. Update selectors when components change
3. Maintain mobile-first testing approach
4. Test both platforms when making shared changes

### Performance
- Tests run in parallel for speed
- Reuse existing dev servers when possible
- Optimize for CI/CD pipeline efficiency