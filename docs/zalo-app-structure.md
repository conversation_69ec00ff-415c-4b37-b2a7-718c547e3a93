# Zalo Mini App Structure Documentation

## Overview
The Zalo Mini App is currently a basic implementation that needs to be expanded to match the Web app's functionality while using Zalo-specific features from the ZMP SDK.

## Current Implementation Status

### ✅ Completed Features

#### 1. Basic Routing (✅ Partial)
- **Router**: ZMP Router (Zalo Mini Program Router)
- **Routes**: 14 basic routes defined
- **Current Pages**:
  - HomePage (`/`)
  - LoginPage (`/login`)
  - EarnPage (`/earn`)
  - RewardsPage (`/rewards`)
  - GamesPage (`/games`)
  - ProfilePage (`/profile`)
  - CameraPage (`/camera`)
  - QRPaymentPage (`/qr-payment`)
  - BillsPage (`/bills`)
  - ExchangePage (`/exchange`)
  - SearchPage (`/search`)
  - SearchResultsPage (`/search/results`)
  - ComponentDemoPage (`/demo`)

#### 2. Shared Integration (✅ Complete)
- Uses components from `@taptap/shared`
- Uses stores from `@taptap/shared`
- HTTP service initialization for Zalo platform

#### 3. UI Framework (✅ Complete)
- ZMP UI components (`zmp-ui`)
- Tailwind CSS for styling
- ZMP SDK for native features

### 🚧 Missing Features (Need Implementation)

#### 1. Service Layer
- No Zalo-specific services created yet
- Need to wrap ZMP SDK features:
  - `zaloAuthService`: Authentication with Zalo OAuth
  - `zaloStorageService`: Using ZMP storage
  - `zaloPaymentService`: Zalo payment integration
  - `zaloCameraService`: Native camera/scanner
  - `zaloLocationService`: Location services
  - `zaloShareService`: Social sharing

#### 2. State Management
- Currently using shared stores
- Need Jotai setup for Zalo-specific state:
  - User preferences atom
  - Navigation state atom
  - Cache atoms
  - UI state atoms

#### 3. Hooks
- No Zalo-specific hooks created
- Need to implement:
  - `useZaloAuth`: Zalo authentication
  - `useZaloUser`: User profile from Zalo
  - `useZaloPayment`: Payment handling
  - `useZaloShare`: Social sharing
  - `useZaloLocation`: Location services
  - `useZaloCamera`: Camera/scanner

#### 4. Missing Pages
Many pages from Web app not yet implemented:
- Game detail pages
- Reward detail pages
- Merchant pages
- Membership pages
- News pages
- Inbox pages
- My rewards pages
- Point history pages
- Transaction history
- Contact page
- Deals pages

#### 5. Native Components
- No native Zalo components created
- Need platform-specific implementations:
  - ZaloBottomNavigation
  - ZaloHeader
  - ZaloScanner
  - ZaloPaymentButton
  - ZaloShareButton

## Directory Structure

```
apps/taptap-zalo/
├── src/
│   ├── app.ts                     # App entry point
│   ├── components/
│   │   ├── layout.tsx             # Main layout with routing
│   │   ├── logo.tsx               # Logo component
│   │   ├── clock.tsx              # Clock component
│   │   └── native/
│   │       └── ZaloEntrance.tsx   # Zalo-specific entrance
│   ├── pages/                     # Page components
│   │   ├── index.tsx              # HomePage
│   │   ├── LoginPage.tsx
│   │   ├── ComponentDemoPage.tsx
│   │   ├── SearchPage.tsx
│   │   ├── SearchResultsPage.tsx
│   │   ├── EarnPage.tsx
│   │   ├── RewardsPage.tsx
│   │   ├── GamesPage.tsx
│   │   ├── ProfilePage.tsx
│   │   ├── CameraPage.tsx
│   │   ├── QRPaymentPage.tsx
│   │   ├── BillsPage.tsx
│   │   └── ExchangePage.tsx
│   ├── services/                  # (To be created)
│   ├── store/                     # (To be created)
│   ├── hooks/                     # (To be created)
│   ├── utils/                     # (To be created)
│   └── css/
│       ├── app.scss
│       └── tailwind.scss
├── public/
├── app-config.json
├── package.json
├── tsconfig.json
├── vite.config.js
├── tailwind.config.js
└── postcss.config.js
```

## Dependencies

### Production
- `@taptap/shared`: Workspace dependency
- `react`: 18.3.1
- `react-dom`: 18.3.1
- `jotai`: 2.12.1 (for state management)
- `zmp-sdk`: Latest (Zalo Mini Program SDK)
- `zmp-ui`: Latest (Zalo UI components)

### Development
- `vite`: 5.2.13
- `zmp-vite-plugin`: Latest
- `tailwindcss`: 3.4.3
- TypeScript and related tools

## Implementation Plan for Phase 3

### 3.2 Setup Zalo Routing with ZMP Router
1. Expand routing to match Web app routes
2. Add route guards for authentication
3. Implement navigation state management
4. Add deep linking support

### 3.3 Create Zalo Services with ZMP SDK
1. **zaloAuthService**:
   - OAuth login with Zalo
   - Get user profile
   - Token management
   
2. **zaloStorageService**:
   - Wrapper for ZMP storage API
   - Sync with cloud storage
   
3. **zaloPaymentService**:
   - Zalo payment integration
   - Transaction handling
   
4. **zaloCameraService**:
   - QR/barcode scanning
   - Image capture
   
5. **zaloLocationService**:
   - Get current location
   - Location permissions

### 3.4 Setup Jotai Stores for Zalo
1. Create atoms for:
   - User state
   - Navigation state
   - UI preferences
   - Cache management
   
2. Setup providers and persistence

### 3.5 Implement Zalo-specific Features
1. **Native Components**:
   - Bottom navigation with Zalo style
   - Native headers
   - Payment buttons
   - Share buttons
   
2. **Zalo Hooks**:
   - Authentication hooks
   - Payment hooks
   - Location hooks
   - Share hooks
   
3. **Missing Pages**:
   - Implement all pages from Web app
   - Adapt UI for Zalo design guidelines
   - Use ZMP UI components

## ZMP SDK Features to Utilize

1. **Authentication**:
   - `getAccessToken()`
   - `getUserInfo()`
   - `getPhoneNumber()`

2. **Storage**:
   - `setStorage()`
   - `getStorage()`
   - `removeStorage()`

3. **Navigation**:
   - `navigateTo()`
   - `navigateBack()`
   - `openWebView()`

4. **UI Components**:
   - `Page`
   - `Button`
   - `List`
   - `Input`
   - `Modal`
   - `Sheet`
   - `Tabs`

5. **Native Features**:
   - `openCamera()`
   - `scanQRCode()`
   - `getLocation()`
   - `share()`
   - `payment()`

## Platform-Specific Considerations

### Zalo Mini App Constraints
- Bundle size limit: <3MB
- Must use ZMP UI components for native feel
- Follow Zalo design guidelines
- Use Zalo OAuth for authentication
- Payment through Zalo Pay

### Performance Optimizations
- Code splitting with React.lazy
- Image optimization
- Minimize bundle size
- Use ZMP SDK caching

## Next Steps

1. **Immediate Priority**:
   - Create Zalo service layer
   - Setup Jotai state management
   - Implement missing pages

2. **Secondary Priority**:
   - Create Zalo-specific hooks
   - Implement native components
   - Add payment integration

3. **Final Steps**:
   - Testing on Zalo app
   - Performance optimization
   - Deploy to Zalo Mini App platform