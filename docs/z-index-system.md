# Z-Index Layer System

## Overview
This document defines the z-index layer system for the TapTap application to ensure proper stacking order of UI elements and prevent z-index conflicts.

## Z-Index Scale

We use a standardized scale with clear separation between layers:

```css
/* Base layers */
--z-index-base: 0;          /* Default content */
--z-index-below: -1;        /* Elements behind content */
--z-index-above: 1;         /* Slightly elevated content */

/* Interactive layers */
--z-index-dropdown: 10;     /* Dropdowns, select menus */
--z-index-sticky: 20;       /* Sticky headers/footers */
--z-index-fixed: 30;        /* Fixed positioned elements */
--z-index-navbar: 40;       /* Navigation bars (bottom/top) */

/* Overlay layers */
--z-index-backdrop: 50;     /* Modal backdrops */
--z-index-drawer: 60;       /* Side drawers, sheets */
--z-index-modal: 70;        /* Modals, dialogs */
--z-index-popover: 80;      /* Popovers, tooltips */

/* Critical layers */
--z-index-notification: 90; /* Toast notifications */
--z-index-critical: 100;    /* Critical alerts, system messages */
--z-index-max: 9999;        /* Development/debug only */
```

## Component Z-Index Assignments

### Navigation Components
- **BottomNavBar**: `z-40` (navbar layer)
- **TopHeader**: `z-40` (navbar layer)
- **StickyTabs**: `z-20` (sticky layer)

### Overlay Components
- **BottomSheet**: `z-60` (drawer layer)
- **Modal/Dialog**: `z-70` (modal layer)
- **ExchangePointsDialog**: `z-70` (modal layer)
- **WarningSheet**: `z-60` (drawer layer)

### Feedback Components
- **Toast**: `z-90` (notification layer)
- **Loading Spinner Overlay**: `z-50` (backdrop layer)
- **Tooltips**: `z-80` (popover layer)

### Interactive Components
- **Dropdown Menu**: `z-10` (dropdown layer)
- **Autocomplete**: `z-10` (dropdown layer)
- **Date Picker**: `z-10` (dropdown layer)

## Implementation Guidelines

### 1. Use Tailwind Z-Index Classes
```tsx
// ❌ Bad - hardcoded z-index
<div className="z-[999]">

// ✅ Good - use defined scale
<div className="z-60"> // drawer layer
```

### 2. Tailwind Config Extension
Add to `tailwind.config.js`:
```js
module.exports = {
  theme: {
    extend: {
      zIndex: {
        'dropdown': '10',
        'sticky': '20',
        'fixed': '30',
        'navbar': '40',
        'backdrop': '50',
        'drawer': '60',
        'modal': '70',
        'popover': '80',
        'notification': '90',
        'critical': '100',
      }
    }
  }
}
```

### 3. Component Examples

#### BottomSheet (Above NavBar)
```tsx
<div className="fixed inset-0 z-drawer"> // z-60
  <div className="bg-black/50">...</div>
  <div className="fixed bottom-0 ...">
    {/* Sheet content */}
  </div>
</div>
```

#### NavBar (Below Overlays)
```tsx
<nav className="fixed bottom-0 z-navbar"> // z-40
  {/* Navigation items */}
</nav>
```

#### Toast Notification (Top Layer)
```tsx
<div className="fixed top-4 right-4 z-notification"> // z-90
  {/* Toast content */}
</div>
```

## Debugging Z-Index Issues

### Common Issues and Solutions

1. **BottomSheet hidden behind NavBar**
   - Ensure BottomSheet uses `z-drawer` (60) which is higher than `z-navbar` (40)

2. **Modal backdrop not covering NavBar**
   - Backdrop should use `z-backdrop` (50) minimum
   - Modal content should use `z-modal` (70)

3. **Dropdown cut off by container**
   - Check parent containers don't have `overflow: hidden`
   - Ensure dropdown uses `z-dropdown` (10) minimum

### Debug Helper
Use this CSS to visualize z-index layers:
```css
[class*="z-"] {
  position: relative;
}
[class*="z-"]::after {
  content: attr(class);
  position: absolute;
  top: 0;
  right: 0;
  background: red;
  color: white;
  padding: 2px 4px;
  font-size: 10px;
  pointer-events: none;
}
```

## Migration Checklist

When updating components to use the new z-index system:

- [ ] Replace hardcoded z-index values with Tailwind classes
- [ ] Verify component layer assignment matches its purpose
- [ ] Test component stacking with overlapping elements
- [ ] Update component documentation with z-index layer
- [ ] Check mobile and desktop views for z-index issues

## Best Practices

1. **Never use z-index > 100** except for debugging
2. **Always use the predefined scale** - don't create arbitrary values
3. **Document any exceptions** in component comments
4. **Test overlay interactions** - ensure proper stacking when multiple overlays are open
5. **Consider accessibility** - ensure focus management works with z-index layers

## Component Specific Notes

### BottomSheet/Drawer Components
- Must use `z-drawer` (60) to appear above navigation
- Backdrop should use same or lower z-index
- Animation should not affect z-index

### Modal/Dialog Components  
- Must use `z-modal` (70) for highest overlay priority
- Should trap focus within modal
- Backdrop prevents interaction with lower layers

### Navigation Components
- Use `z-navbar` (40) for consistent navigation layer
- Should remain accessible but below overlays
- Mobile navigation may need special handling

## Testing Z-Index

### Manual Testing
1. Open each overlay type (sheet, modal, toast)
2. Verify correct stacking order
3. Test with navigation visible
4. Check edge cases (multiple overlays)

### Automated Testing
```tsx
// Example test for z-index
expect(bottomSheet).toHaveStyle({ zIndex: 60 });
expect(navBar).toHaveStyle({ zIndex: 40 });
```

## Troubleshooting

If a component appears behind/above incorrectly:

1. Check its z-index assignment
2. Verify parent containers don't override z-index
3. Ensure proper positioning (fixed/absolute/relative)
4. Check for transform properties that create new stacking contexts
5. Use browser DevTools to inspect computed z-index

## References

- [MDN: Understanding z-index](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index)
- [Tailwind CSS: Z-Index](https://tailwindcss.com/docs/z-index)
- [CSS Stacking Context](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context)