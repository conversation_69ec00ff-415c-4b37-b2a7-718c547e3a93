# 📱 Phase 3: Zalo Standalone Development (Tuần 4-5)

## 🎯 Mục Tiêu
Xây dựng complete Zalo Mini App hoàn toàn độc lập, tận dụng tối đa native features của Zalo.

## 📅 Timeline: 10 ngày (2 tuần)

### Week 1: Core Setup & Pages
| Ngày | Focus | Deliverables |
|------|-------|--------------|
| Ngày 1 | Project Setup | ZMP setup, routing, state |
| Ngày 2 | Zalo Auth & API | Zalo SDK integration, API setup |
| Ngày 3 | HomePage | Zalo-optimized home page |
| Ngày 4 | Native Features | Camera, QR, Payment integration |
| Ngày 5 | Profile & Bills | User profile, OCR integration |

### Week 2: Features & Polish
| Ngày | Focus | Deliverables |
|------|-------|--------------|
| Ngày 6 | Rewards & Exchange | Reward pages with Zalo Pay |
| Ngày 7 | Zalo-Specific | OA integration, share, shortcuts |
| Ngày 8 | State Management | Jotai setup complete |
| Ngày 9 | Testing | Zalo simulator testing |
| Ngày 10 | Optimization | Bundle size, performance |

## 📁 Zalo App Structure

```
apps/taptap-zalo/
├── src/
│   ├── pages/                 # All Zalo pages
│   │   ├── index.tsx          # HomePage
│   │   ├── ProfilePage.tsx
│   │   ├── CameraPage.tsx
│   │   ├── QRPaymentPage.tsx
│   │   ├── BillsPage.tsx
│   │   ├── SearchPage.tsx
│   │   ├── MerchantPage.tsx
│   │   ├── RewardsPage.tsx
│   │   └── ExchangePage.tsx
│   │
│   ├── components/            # Zalo-specific components
│   │   ├── layout/
│   │   │   ├── ZaloHeader.tsx
│   │   │   ├── ZaloTabBar.tsx
│   │   │   └── ZaloPage.tsx
│   │   ├── native/
│   │   │   ├── ZaloCamera.tsx
│   │   │   ├── ZaloQRScanner.tsx
│   │   │   ├── ZaloPayment.tsx
│   │   │   └── ZaloShare.tsx
│   │   └── oa/
│   │       ├── OAWidget.tsx
│   │       └── OAFollowButton.tsx
│   │
│   ├── services/              # Zalo API services
│   │   ├── api/
│   │   │   ├── zaloAuthService.ts
│   │   │   ├── userService.ts
│   │   │   ├── merchantService.ts
│   │   │   ├── rewardService.ts
│   │   │   └── ocrService.ts
│   │   ├── zalo/
│   │   │   ├── zaloSDK.ts
│   │   │   ├── zaloStorage.ts
│   │   │   └── zaloPermissions.ts
│   │   └── http/
│   │       └── zaloHttpClient.ts
│   │
│   ├── stores/                # Jotai atoms
│   │   ├── atoms/
│   │   │   ├── authAtom.ts
│   │   │   ├── userAtom.ts
│   │   │   ├── merchantAtom.ts
│   │   │   └── rewardAtom.ts
│   │   └── hooks/
│   │       ├── useZaloAuth.ts
│   │       └── useZaloUser.ts
│   │
│   ├── hooks/                 # Zalo-specific hooks
│   │   ├── useZaloCamera.ts
│   │   ├── useZaloQR.ts
│   │   ├── useZaloPayment.ts
│   │   ├── useZaloShare.ts
│   │   └── useZaloLocation.ts
│   │
│   ├── utils/                 # Zalo utilities
│   │   ├── zaloHelpers.ts
│   │   ├── zaloConfig.ts
│   │   └── zaloConstants.ts
│   │
│   ├── components/
│   │   └── layout.tsx         # Main layout
│   └── app.tsx               # App entry
│
├── zmp.config.js             # ZMP configuration
└── package.json              # Zalo dependencies only
```

## 📋 DETAILED IMPLEMENTATION

### Day 1: Zalo Project Setup

#### 1.1 ZMP Configuration
**File:** `apps/taptap-zalo/zmp.config.js`

```javascript
module.exports = {
  app: {
    id: 'com.taptap.zalo',
    name: 'TapTap',
    version: '1.0.0',
    description: 'TapTap Loyalty Program',
  },
  build: {
    output: 'dist',
    publicPath: '/',
  },
  window: {
    defaultTitle: 'TapTap',
    titleBarColor: '#F65D79',
    navigationBarColor: '#F65D79',
    navigationBarTextStyle: 'white',
  },
  permissions: [
    'getUserInfo',
    'getLocation',
    'openCamera',
    'scanQRCode',
    'payment',
    'sendNotification',
    'createShortcut',
  ],
};
```

#### 1.2 Routing Setup
**File:** `apps/taptap-zalo/src/components/layout.tsx`

```typescript
import { App, ZMPRouter, AnimationRoutes, Route, Page } from 'zmp-ui';
import { getSystemInfo } from 'zmp-sdk';
import HomePage from '../pages/index';
import ProfilePage from '../pages/ProfilePage';
import CameraPage from '../pages/CameraPage';
import QRPaymentPage from '../pages/QRPaymentPage';
import BillsPage from '../pages/BillsPage';
import SearchPage from '../pages/SearchPage';
import MerchantPage from '../pages/MerchantPage';
import RewardsPage from '../pages/RewardsPage';
import ExchangePage from '../pages/ExchangePage';

const Layout = () => {
  return (
    <App theme={getSystemInfo().zaloTheme}>
      <ZMPRouter>
        <AnimationRoutes>
          <Route path="/" element={<HomePage />} />
          <Route path="/profile" element={<ProfilePage />} />
          <Route path="/camera" element={<CameraPage />} />
          <Route path="/qr-payment" element={<QRPaymentPage />} />
          <Route path="/bills" element={<BillsPage />} />
          <Route path="/search" element={<SearchPage />} />
          <Route path="/merchant/:id" element={<MerchantPage />} />
          <Route path="/rewards" element={<RewardsPage />} />
          <Route path="/exchange" element={<ExchangePage />} />
        </AnimationRoutes>
      </ZMPRouter>
    </App>
  );
};

export default Layout;
```

#### 1.3 Jotai State Setup
**File:** `apps/taptap-zalo/src/stores/atoms/authAtom.ts`

```typescript
import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { getUserInfo } from 'zmp-sdk';

export interface ZaloUser {
  id: string;
  name: string;
  avatar: string;
  phone?: string;
  loyaltyPoints: number;
  membershipTier: string;
}

// Persist user data using Zalo storage
export const userAtom = atomWithStorage<ZaloUser | null>(
  'user',
  null,
  {
    getItem: async (key) => {
      const { getItem } = await import('zmp-sdk');
      const value = await getItem({ key });
      return value ? JSON.parse(value) : null;
    },
    setItem: async (key, value) => {
      const { setItem } = await import('zmp-sdk');
      await setItem({ key, value: JSON.stringify(value) });
    },
    removeItem: async (key) => {
      const { removeItem } = await import('zmp-sdk');
      await removeItem({ key });
    },
  }
);

export const isAuthenticatedAtom = atom(
  (get) => get(userAtom) !== null
);

export const loadingAtom = atom(false);

// Auth actions
export const loginAtom = atom(
  null,
  async (get, set) => {
    set(loadingAtom, true);
    try {
      // Get Zalo user info
      const zaloUser = await getUserInfo();
      
      // Call backend to create/update user
      const response = await zaloAuthService.login({
        zaloId: zaloUser.id,
        name: zaloUser.name,
        avatar: zaloUser.avatar,
      });
      
      set(userAtom, response.user);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      set(loadingAtom, false);
    }
  }
);
```

---

### Day 2: Zalo SDK Integration

#### 2.1 Zalo SDK Service
**File:** `apps/taptap-zalo/src/services/zalo/zaloSDK.ts`

```typescript
import {
  getUserInfo,
  getAccessToken,
  getLocation,
  openCamera,
  scanQRCode,
  payment,
  openShareSheet,
  createShortcut,
  setNavigationBarTitle,
  setNavigationBarColor,
  showToast,
  showLoading,
  closeLoading,
  openMiniApp,
  openWebview,
  followOA,
  unfollowOA,
  checkFollowedOA,
} from 'zmp-sdk';

class ZaloSDKService {
  // User & Auth
  async getUserInfo() {
    try {
      const user = await getUserInfo();
      return {
        id: user.id,
        name: user.name,
        avatar: user.avatar.startsWith('http') 
          ? user.avatar 
          : `https://zalo.me${user.avatar}`,
      };
    } catch (error) {
      console.error('Get user info failed:', error);
      throw error;
    }
  }
  
  async getAccessToken() {
    const token = await getAccessToken();
    return token;
  }
  
  // Location
  async getCurrentLocation() {
    try {
      const location = await getLocation();
      return {
        latitude: location.latitude,
        longitude: location.longitude,
      };
    } catch (error) {
      showToast({ message: 'Không thể lấy vị trí' });
      throw error;
    }
  }
  
  // Camera & QR
  async openCamera(options = {}) {
    const result = await openCamera({
      type: 'photo',
      cameraType: 'back',
      ...options,
    });
    return result;
  }
  
  async scanQRCode() {
    const result = await scanQRCode();
    return result.data;
  }
  
  // Payment
  async makePayment(amount: number, description: string) {
    try {
      const result = await payment({
        amount,
        description,
        method: 'zalopay',
      });
      return result;
    } catch (error) {
      showToast({ message: 'Thanh toán thất bại' });
      throw error;
    }
  }
  
  // Share
  async shareContent(title: string, description: string, url?: string) {
    await openShareSheet({
      type: 'link',
      data: {
        title,
        description,
        url: url || window.location.href,
      },
    });
  }
  
  // UI Controls
  setNavigationTitle(title: string) {
    setNavigationBarTitle({ title });
  }
  
  setNavigationColor(color: string) {
    setNavigationBarColor({ 
      statusBarColor: color,
      navigationBarColor: color,
    });
  }
  
  showLoading(message = 'Đang tải...') {
    showLoading({ title: message });
  }
  
  hideLoading() {
    closeLoading();
  }
  
  showToast(message: string, icon = 'success') {
    showToast({ 
      message,
      icon,
      duration: 2000,
    });
  }
  
  // OA Integration
  async followOfficialAccount(oaId: string) {
    await followOA({ id: oaId });
  }
  
  async checkOAFollowStatus(oaId: string) {
    const result = await checkFollowedOA({ id: oaId });
    return result.isFollowing;
  }
  
  // App Shortcuts
  async createAppShortcut() {
    try {
      await createShortcut();
      this.showToast('Đã thêm vào màn hình chính');
    } catch (error) {
      console.error('Create shortcut failed:', error);
    }
  }
}

export const zaloSDK = new ZaloSDKService();
```

#### 2.2 Zalo HTTP Client
**File:** `apps/taptap-zalo/src/services/http/zaloHttpClient.ts`

```typescript
import axios from 'axios';
import { getAccessToken } from 'zmp-sdk';

const API_URL = import.meta.env.VITE_API_URL || 'https://api.taptap.com';

export const zaloHttpClient = axios.create({
  baseURL: API_URL,
  timeout: 30000,
});

// Request interceptor for Zalo auth
zaloHttpClient.interceptors.request.use(
  async (config) => {
    try {
      const token = await getAccessToken();
      if (token) {
        config.headers['X-Zalo-Token'] = token;
      }
    } catch (error) {
      console.error('Get access token failed:', error);
    }
    
    // Add platform header
    config.headers['X-Platform'] = 'zalo';
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
zaloHttpClient.interceptors.response.use(
  (response) => {
    // Handle API response format
    if (response.data?.status && response.data?.data) {
      return response.data;
    }
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle auth error
      zaloSDK.showToast('Phiên đăng nhập hết hạn', 'error');
    }
    return Promise.reject(error);
  }
);
```

---

### Day 3: Zalo HomePage

**File:** `apps/taptap-zalo/src/pages/index.tsx`

```typescript
import React, { useEffect } from 'react';
import { Page, Box, Text } from 'zmp-ui';
import { useNavigate } from 'react-router-dom';
import { Button, Card } from '@taptap/shared/components';
import { formatters } from '@taptap/shared/utils';
import { useAtom } from 'jotai';
import { userAtom, loginAtom } from '../stores/atoms/authAtom';
import { homeDataAtom, fetchHomeDataAtom } from '../stores/atoms/homeAtom';
import { zaloSDK } from '../services/zalo/zaloSDK';

const HomePage = () => {
  const navigate = useNavigate();
  const [user] = useAtom(userAtom);
  const [homeData] = useAtom(homeDataAtom);
  const [, fetchHomeData] = useAtom(fetchHomeDataAtom);
  const [, login] = useAtom(loginAtom);
  
  useEffect(() => {
    // Auto login with Zalo
    if (!user) {
      login();
    }
    
    // Fetch home data
    fetchHomeData();
    
    // Set navigation bar
    zaloSDK.setNavigationTitle('TapTap');
    zaloSDK.setNavigationColor('#F65D79');
  }, []);
  
  const handleScanBill = () => {
    navigate('/camera');
  };
  
  const handleQRPayment = () => {
    navigate('/qr-payment');
  };
  
  const handleViewRewards = () => {
    navigate('/rewards');
  };
  
  const handleShare = async () => {
    await zaloSDK.shareContent(
      'TapTap - Tích điểm thông minh',
      `${user?.name} đang dùng TapTap để tích điểm. Tham gia ngay!`
    );
  };
  
  return (
    <Page className="bg-gray-50">
      {/* Header Section */}
      <Box className="bg-primary text-white p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <img
              src={user?.avatar}
              alt={user?.name}
              className="w-12 h-12 rounded-full mr-3"
            />
            <div>
              <Text className="font-bold text-lg">
                Xin chào, {user?.name}!
              </Text>
              <Text className="text-sm opacity-90">
                {user?.membershipTier} Member
              </Text>
            </div>
          </div>
          <Button
            onClick={handleShare}
            variant="ghost"
            className="text-white"
          >
            <ShareIcon />
          </Button>
        </div>
        
        {/* Points Display */}
        <Card className="bg-white/10 backdrop-blur p-4">
          <Text className="text-sm text-white/80">Điểm VUI</Text>
          <Text className="text-2xl font-bold text-white">
            {formatters.points(user?.loyaltyPoints || 0)}
          </Text>
        </Card>
      </Box>
      
      {/* Quick Actions */}
      <Box className="p-4">
        <div className="grid grid-cols-4 gap-3">
          <ActionButton
            icon={<CameraIcon />}
            label="Quét bill"
            onClick={handleScanBill}
          />
          <ActionButton
            icon={<QRIcon />}
            label="QR Pay"
            onClick={handleQRPayment}
          />
          <ActionButton
            icon={<GiftIcon />}
            label="Đổi quà"
            onClick={handleViewRewards}
          />
          <ActionButton
            icon={<HistoryIcon />}
            label="Lịch sử"
            onClick={() => navigate('/bills')}
          />
        </div>
      </Box>
      
      {/* Banners */}
      {homeData?.banners && (
        <Box className="px-4 mb-4">
          <ZaloBannerSwiper banners={homeData.banners} />
        </Box>
      )}
      
      {/* Flash Sales */}
      {homeData?.flashSales && (
        <Box className="p-4">
          <SectionHeader title="Flash Sale" />
          <div className="flex gap-3 overflow-x-auto">
            {homeData.flashSales.map((item) => (
              <FlashSaleCard
                key={item.id}
                item={item}
                onClick={() => navigate(`/reward/${item.id}`)}
              />
            ))}
          </div>
        </Box>
      )}
      
      {/* Popular Merchants */}
      {homeData?.merchants && (
        <Box className="p-4">
          <SectionHeader 
            title="Thương hiệu nổi bật"
            onViewAll={() => navigate('/merchants')}
          />
          <div className="grid grid-cols-3 gap-3">
            {homeData.merchants.slice(0, 6).map((merchant) => (
              <MerchantCard
                key={merchant.id}
                merchant={merchant}
                onClick={() => navigate(`/merchant/${merchant.id}`)}
              />
            ))}
          </div>
        </Box>
      )}
      
      {/* My Rewards */}
      {homeData?.myRewards && homeData.myRewards.length > 0 && (
        <Box className="p-4">
          <SectionHeader 
            title="Ưu đãi của tôi"
            onViewAll={() => navigate('/profile/rewards')}
          />
          <div className="space-y-3">
            {homeData.myRewards.map((reward) => (
              <MyRewardCard
                key={reward.id}
                reward={reward}
                onClick={() => navigate(`/reward/use/${reward.id}`)}
              />
            ))}
          </div>
        </Box>
      )}
      
      {/* OA Widget */}
      <Box className="p-4">
        <OAFollowWidget oaId="taptap_official" />
      </Box>
      
      {/* Create Shortcut Prompt */}
      {!homeData?.hasShortcut && (
        <Box className="p-4">
          <Card className="p-4 bg-blue-50">
            <Text className="font-semibold mb-2">
              Thêm vào màn hình chính
            </Text>
            <Text className="text-sm text-gray-600 mb-3">
              Truy cập nhanh TapTap từ màn hình chính
            </Text>
            <Button
              onClick={() => zaloSDK.createAppShortcut()}
              variant="primary"
              size="sm"
            >
              Thêm ngay
            </Button>
          </Card>
        </Box>
      )}
    </Page>
  );
};

export default HomePage;
```

---

### Day 4: Native Features Integration

#### 4.1 Zalo Camera Component
**File:** `apps/taptap-zalo/src/components/native/ZaloCamera.tsx`

```typescript
import React from 'react';
import { Button } from '@taptap/shared/components';
import { zaloSDK } from '../../services/zalo/zaloSDK';

export const ZaloCamera: React.FC<ZaloCameraProps> = ({
  onCapture,
  onError,
  type = 'bill',
}) => {
  const handleOpenCamera = async () => {
    try {
      zaloSDK.showLoading('Đang mở camera...');
      
      const result = await zaloSDK.openCamera({
        type: 'photo',
        cameraType: 'back',
      });
      
      if (result.filePath) {
        // Upload to server
        const file = await uploadZaloImage(result.filePath);
        onCapture(file);
      }
    } catch (error) {
      onError?.(error as Error);
      zaloSDK.showToast('Không thể mở camera', 'error');
    } finally {
      zaloSDK.hideLoading();
    }
  };
  
  const handleScanQR = async () => {
    try {
      const qrData = await zaloSDK.scanQRCode();
      onCapture({ type: 'qr', data: qrData });
    } catch (error) {
      onError?.(error as Error);
      zaloSDK.showToast('Không thể quét QR', 'error');
    }
  };
  
  return (
    <div className="flex flex-col items-center justify-center h-full p-4">
      <div className="text-center mb-8">
        <CameraIllustration className="w-48 h-48 mx-auto mb-4" />
        <h2 className="text-xl font-bold mb-2">
          {type === 'bill' ? 'Chụp hóa đơn' : 'Quét mã'}
        </h2>
        <p className="text-gray-600">
          {type === 'bill' 
            ? 'Chụp rõ hóa đơn để tích điểm'
            : 'Quét mã QR để thanh toán'}
        </p>
      </div>
      
      <div className="w-full space-y-3">
        {type === 'bill' ? (
          <Button
            onClick={handleOpenCamera}
            variant="primary"
            fullWidth
            size="lg"
          >
            <CameraIcon className="mr-2" />
            Mở Camera
          </Button>
        ) : (
          <Button
            onClick={handleScanQR}
            variant="primary"
            fullWidth
            size="lg"
          >
            <QRIcon className="mr-2" />
            Quét mã QR
          </Button>
        )}
      </div>
    </div>
  );
};
```

#### 4.2 Zalo Payment Integration
**File:** `apps/taptap-zalo/src/hooks/useZaloPayment.ts`

```typescript
import { useState } from 'react';
import { zaloSDK } from '../services/zalo/zaloSDK';
import { paymentService } from '../services/api/paymentService';

export const useZaloPayment = () => {
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const createPayment = async (
    amount: number,
    description: string,
    orderId: string
  ) => {
    setProcessing(true);
    setError(null);
    
    try {
      // Create payment order on backend
      const order = await paymentService.createOrder({
        amount,
        description,
        orderId,
        method: 'zalopay',
      });
      
      // Process payment via Zalo Pay
      const result = await zaloSDK.makePayment(
        amount,
        description
      );
      
      if (result.success) {
        // Confirm payment on backend
        await paymentService.confirmPayment(order.id, result.transactionId);
        
        zaloSDK.showToast('Thanh toán thành công');
        return result;
      } else {
        throw new Error('Payment failed');
      }
    } catch (error) {
      setError('Thanh toán thất bại');
      zaloSDK.showToast('Thanh toán thất bại', 'error');
      throw error;
    } finally {
      setProcessing(false);
    }
  };
  
  return {
    createPayment,
    processing,
    error,
  };
};
```

---

## ✅ Phase 3 Complete Checklist

### Pages Implementation
- [ ] HomePage với Zalo features
- [ ] ProfilePage với Zalo user info
- [ ] CameraPage với native camera
- [ ] QRPaymentPage với Zalo Pay
- [ ] BillsPage với OCR
- [ ] SearchPage optimized cho mobile
- [ ] MerchantPage với location
- [ ] RewardsPage với categories
- [ ] ExchangePage với payment

### Zalo Native Features
- [ ] ZMP SDK integration
- [ ] getUserInfo implementation
- [ ] Camera/Photo access
- [ ] QR Scanner
- [ ] Zalo Pay integration
- [ ] Location services
- [ ] Share functionality
- [ ] OA integration
- [ ] Shortcuts creation
- [ ] Push notifications

### State Management (Jotai)
- [ ] Auth atoms
- [ ] User atoms
- [ ] Merchant atoms
- [ ] Reward atoms
- [ ] Cart atoms
- [ ] UI atoms
- [ ] Persistent storage với Zalo

### Zalo-Specific Services
- [ ] Zalo HTTP client
- [ ] Zalo auth service
- [ ] Zalo storage service
- [ ] Zalo permission handling
- [ ] Error handling
- [ ] Analytics integration

### UI Optimization
- [ ] Mobile-first design
- [ ] Touch-optimized interactions
- [ ] Native navigation bar
- [ ] Bottom sheet modals
- [ ] Pull-to-refresh
- [ ] Infinite scroll
- [ ] Skeleton loading

## 🎯 Success Metrics

- ✅ Fully functional Zalo Mini App
- ✅ All native features integrated
- ✅ Bundle size <3MB
- ✅ Can be extracted as standalone
- ✅ No dependencies on Web code
- ✅ Optimized for mobile experience
- ✅ Native-like performance

## 🚀 Deployment Ready

By end of Week 5, the Zalo app should be:
1. Fully functional Mini App
2. Ready to deploy via ZMP CLI
3. Can be extracted to separate repository
4. Utilizes all Zalo native features
5. Optimized for mobile performance

---

**Next:** [Phase 4 - Testing](./phase-4-testing.md)

**Status:** 🟡 IN PROGRESS
**Last Updated:** ${new Date().toISOString()}