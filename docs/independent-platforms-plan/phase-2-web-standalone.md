# 🌐 Phase 2: Web Standalone Development (Tuần 2-3)

## 🎯 Mục Tiêu
Xây dựng complete web application hoàn toàn độc lập, có thể tách ra thành dự án riêng bất cứ lúc nào.

## 📅 Timeline: 10 ngày (2 tuần)

### Week 1: Core Setup & Pages
| Ngày | Focus | Deliverables |
|------|-------|--------------|
| Ngày 1 | Project Setup | Routing, state, services structure |
| Ngày 2 | Auth & API | Complete auth flow, API client |
| Ngày 3 | HomePage | Complete home page with all sections |
| Ngày 4 | Profile & Bills | User profile, bill scanning pages |
| Ngày 5 | Search & Merchant | Search, merchant detail pages |

### Week 2: Features & Polish
| Ngày | Focus | Deliverables |
|------|-------|--------------|
| Ngày 6 | Rewards & Exchange | Reward pages, redemption flow |
| Ngày 7 | Web-Specific Features | PWA, notifications, SEO |
| Ngày 8 | State Management | Complete Zustand setup |
| Ngày 9 | Testing | Unit & integration tests |
| Ngày 10 | Optimization | Performance, bundle size |

## 📁 Web App Structure

```
apps/web/
├── src/
│   ├── pages/                 # All web pages
│   │   ├── HomePage.tsx
│   │   ├── ProfilePage.tsx
│   │   ├── BillScanPage.tsx
│   │   ├── SearchPage.tsx
│   │   ├── MerchantDetailPage.tsx
│   │   ├── RewardsPage.tsx
│   │   └── ...
│   │
│   ├── components/            # Web-specific components
│   │   ├── layout/
│   │   │   ├── WebHeader.tsx
│   │   │   ├── WebNavigation.tsx
│   │   │   └── WebFooter.tsx
│   │   ├── features/
│   │   │   ├── WebCamera.tsx
│   │   │   ├── WebQRScanner.tsx
│   │   │   └── WebFileUpload.tsx
│   │   └── seo/
│   │       ├── MetaTags.tsx
│   │       └── StructuredData.tsx
│   │
│   ├── services/              # Web API services
│   │   ├── api/
│   │   │   ├── authService.ts
│   │   │   ├── userService.ts
│   │   │   ├── merchantService.ts
│   │   │   ├── rewardService.ts
│   │   │   └── ocrService.ts
│   │   ├── http/
│   │   │   ├── httpClient.ts
│   │   │   └── interceptors.ts
│   │   └── storage/
│   │       ├── localStorage.ts
│   │       └── sessionStorage.ts
│   │
│   ├── stores/                # Zustand stores
│   │   ├── authStore.ts
│   │   ├── userStore.ts
│   │   ├── merchantStore.ts
│   │   ├── rewardStore.ts
│   │   └── uiStore.ts
│   │
│   ├── hooks/                 # Web-specific hooks
│   │   ├── useWebAuth.ts
│   │   ├── useWebCamera.ts
│   │   ├── useWebStorage.ts
│   │   ├── useWebNotification.ts
│   │   └── useWebShare.ts
│   │
│   ├── router/                # React Router setup
│   │   ├── routes.tsx
│   │   ├── PrivateRoute.tsx
│   │   └── RouterProvider.tsx
│   │
│   ├── utils/                 # Web-specific utilities
│   │   ├── seo.ts
│   │   ├── pwa.ts
│   │   ├── analytics.ts
│   │   └── webHelpers.ts
│   │
│   └── App.tsx               # Main app component
│
├── public/
│   ├── manifest.json          # PWA manifest
│   ├── service-worker.js      # Service worker
│   └── assets/
│
└── package.json              # Web dependencies only
```

## 📋 DETAILED IMPLEMENTATION

### Day 1: Project Setup

#### 1.1 Router Setup
**File:** `apps/web/src/router/routes.tsx`

```typescript
import { createBrowserRouter } from 'react-router-dom';
import { lazy, Suspense } from 'react';

// Lazy load pages for code splitting
const HomePage = lazy(() => import('../pages/HomePage'));
const ProfilePage = lazy(() => import('../pages/ProfilePage'));
const BillScanPage = lazy(() => import('../pages/BillScanPage'));
const SearchPage = lazy(() => import('../pages/SearchPage'));
const MerchantDetailPage = lazy(() => import('../pages/MerchantDetailPage'));
const RewardsPage = lazy(() => import('../pages/RewardsPage'));
const ExchangePage = lazy(() => import('../pages/ExchangePage'));

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<PageLoader />}>
            <HomePage />
          </Suspense>
        ),
      },
      {
        path: 'profile',
        element: (
          <PrivateRoute>
            <ProfilePage />
          </PrivateRoute>
        ),
      },
      {
        path: 'bills',
        element: (
          <PrivateRoute>
            <BillScanPage />
          </PrivateRoute>
        ),
      },
      {
        path: 'search',
        element: <SearchPage />,
      },
      {
        path: 'merchant/:id',
        element: <MerchantDetailPage />,
      },
      {
        path: 'rewards',
        element: <RewardsPage />,
      },
      {
        path: 'exchange',
        element: (
          <PrivateRoute>
            <ExchangePage />
          </PrivateRoute>
        ),
      },
    ],
  },
  {
    path: '*',
    element: <NotFoundPage />,
  },
]);
```

#### 1.2 State Management Setup
**File:** `apps/web/src/stores/authStore.ts`

```typescript
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authService } from '../services/api/authService';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      loading: false,
      
      login: async (credentials) => {
        set({ loading: true });
        try {
          const response = await authService.login(credentials);
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            loading: false,
          });
          
          // Store token in localStorage
          localStorage.setItem('token', response.token);
        } catch (error) {
          set({ loading: false });
          throw error;
        }
      },
      
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        });
        localStorage.removeItem('token');
        window.location.href = '/';
      },
      
      refreshToken: async () => {
        const token = get().token;
        if (!token) return;
        
        try {
          const newToken = await authService.refreshToken(token);
          set({ token: newToken });
          localStorage.setItem('token', newToken);
        } catch (error) {
          get().logout();
        }
      },
      
      updateProfile: async (data) => {
        const user = get().user;
        if (!user) return;
        
        const updated = await authService.updateProfile(user.id, data);
        set({ user: updated });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
```

---

### Day 2: Auth & API Services

#### 2.1 HTTP Client
**File:** `apps/web/src/services/http/httpClient.ts`

```typescript
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'https://api.taptap.com';

export const httpClient = axios.create({
  baseURL: API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth
httpClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
httpClient.interceptors.response.use(
  (response) => {
    // Handle new API format
    if (response.data?.status && response.data?.data) {
      return response.data;
    }
    return response;
  },
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired, try refresh
      const authStore = useAuthStore.getState();
      await authStore.refreshToken();
      
      // Retry original request
      return httpClient(error.config);
    }
    
    return Promise.reject(error);
  }
);
```

#### 2.2 Auth Service
**File:** `apps/web/src/services/api/authService.ts`

```typescript
import { httpClient } from '../http/httpClient';

export const authService = {
  login: async (credentials: LoginCredentials) => {
    const response = await httpClient.post('/auth/login', credentials);
    return response.data;
  },
  
  register: async (data: RegisterData) => {
    const response = await httpClient.post('/auth/register', data);
    return response.data;
  },
  
  logout: async () => {
    await httpClient.post('/auth/logout');
  },
  
  refreshToken: async (token: string) => {
    const response = await httpClient.post('/auth/refresh', { token });
    return response.data.token;
  },
  
  verifyOTP: async (phone: string, otp: string) => {
    const response = await httpClient.post('/auth/verify-otp', { phone, otp });
    return response.data;
  },
  
  updateProfile: async (userId: string, data: Partial<User>) => {
    const response = await httpClient.put(`/users/${userId}`, data);
    return response.data;
  },
};
```

---

### Day 3: HomePage Implementation

**File:** `apps/web/src/pages/HomePage.tsx`

```typescript
import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Button, Card } from '@taptap/shared/components';
import { formatters } from '@taptap/shared/utils';
import { WebHeader } from '../components/layout/WebHeader';
import { WebNavigation } from '../components/layout/WebNavigation';
import { useAuthStore } from '../stores/authStore';
import { useHomeStore } from '../stores/homeStore';

export const HomePage: React.FC = () => {
  const { user } = useAuthStore();
  const { banners, merchants, rewards, news, fetchHomeData } = useHomeStore();
  
  useEffect(() => {
    fetchHomeData();
  }, []);
  
  return (
    <div className="min-h-screen bg-gray-50">
      <WebHeader user={user} />
      
      {/* Hero Section */}
      <section className="bg-primary text-white py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold mb-2">
            Xin chào, {user?.firstName || 'Bạn'}!
          </h1>
          <p className="text-lg">
            Bạn có {formatters.points(user?.loyaltyPoints || 0)}
          </p>
        </div>
      </section>
      
      {/* Quick Actions */}
      <section className="max-w-6xl mx-auto px-4 py-6">
        <div className="grid grid-cols-4 gap-4">
          <Link to="/bills" className="text-center">
            <div className="bg-white p-4 rounded-lg shadow hover:shadow-md">
              <CameraIcon className="w-8 h-8 mx-auto mb-2" />
              <span className="text-sm">Quét hóa đơn</span>
            </div>
          </Link>
          <Link to="/rewards" className="text-center">
            <div className="bg-white p-4 rounded-lg shadow hover:shadow-md">
              <GiftIcon className="w-8 h-8 mx-auto mb-2" />
              <span className="text-sm">Đổi thưởng</span>
            </div>
          </Link>
          <Link to="/merchants" className="text-center">
            <div className="bg-white p-4 rounded-lg shadow hover:shadow-md">
              <StoreIcon className="w-8 h-8 mx-auto mb-2" />
              <span className="text-sm">Cửa hàng</span>
            </div>
          </Link>
          <Link to="/profile" className="text-center">
            <div className="bg-white p-4 rounded-lg shadow hover:shadow-md">
              <UserIcon className="w-8 h-8 mx-auto mb-2" />
              <span className="text-sm">Tài khoản</span>
            </div>
          </Link>
        </div>
      </section>
      
      {/* Banner Carousel */}
      <section className="max-w-6xl mx-auto px-4 py-6">
        <WebBannerCarousel banners={banners} />
      </section>
      
      {/* Popular Merchants */}
      <section className="max-w-6xl mx-auto px-4 py-6">
        <h2 className="text-xl font-bold mb-4">Thương hiệu nổi bật</h2>
        <div className="grid grid-cols-4 gap-4">
          {merchants.map((merchant) => (
            <Link key={merchant.id} to={`/merchant/${merchant.id}`}>
              <Card className="hover:shadow-lg transition-shadow">
                <img
                  src={merchant.logo}
                  alt={merchant.name}
                  className="w-full h-32 object-cover rounded-t"
                />
                <div className="p-3">
                  <h3 className="font-semibold">{merchant.name}</h3>
                  <p className="text-sm text-gray-600">{merchant.category}</p>
                </div>
              </Card>
            </Link>
          ))}
        </div>
      </section>
      
      {/* Featured Rewards */}
      <section className="max-w-6xl mx-auto px-4 py-6">
        <h2 className="text-xl font-bold mb-4">Ưu đãi hot</h2>
        <div className="grid grid-cols-3 gap-4">
          {rewards.map((reward) => (
            <WebRewardCard key={reward.id} reward={reward} />
          ))}
        </div>
      </section>
      
      {/* News Section */}
      <section className="max-w-6xl mx-auto px-4 py-6">
        <h2 className="text-xl font-bold mb-4">Tin tức</h2>
        <div className="space-y-4">
          {news.map((article) => (
            <WebNewsCard key={article.id} article={article} />
          ))}
        </div>
      </section>
      
      <WebNavigation />
    </div>
  );
};

export default HomePage;
```

---

### Day 4: Web-Specific Features

#### 4.1 Web Camera Component
**File:** `apps/web/src/components/features/WebCamera.tsx`

```typescript
import React, { useRef, useState } from 'react';
import { Button } from '@taptap/shared/components';

export const WebCamera: React.FC<WebCameraProps> = ({
  onCapture,
  onError,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isCapturing, setIsCapturing] = useState(false);
  
  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' },
        audio: false,
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      setStream(mediaStream);
      setIsCapturing(true);
    } catch (error) {
      onError?.(error as Error);
    }
  };
  
  const capturePhoto = () => {
    if (!videoRef.current) return;
    
    const canvas = document.createElement('canvas');
    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;
    
    const context = canvas.getContext('2d');
    context?.drawImage(videoRef.current, 0, 0);
    
    canvas.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], 'photo.jpg', { type: 'image/jpeg' });
        onCapture(file);
        stopCamera();
      }
    }, 'image/jpeg', 0.95);
  };
  
  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
      setIsCapturing(false);
    }
  };
  
  return (
    <div className="relative w-full h-full">
      {!isCapturing ? (
        <div className="flex flex-col items-center justify-center h-full">
          <CameraIcon className="w-24 h-24 text-gray-400 mb-4" />
          <Button onClick={startCamera} variant="primary" size="lg">
            Mở Camera
          </Button>
        </div>
      ) : (
        <>
          <video
            ref={videoRef}
            autoPlay
            playsInline
            className="w-full h-full object-cover"
          />
          <div className="absolute bottom-8 left-0 right-0 flex justify-center gap-4">
            <Button onClick={capturePhoto} variant="primary" size="lg">
              Chụp ảnh
            </Button>
            <Button onClick={stopCamera} variant="outline" size="lg">
              Hủy
            </Button>
          </div>
        </>
      )}
    </div>
  );
};
```

#### 4.2 PWA Setup
**File:** `apps/web/public/manifest.json`

```json
{
  "name": "TapTap Web",
  "short_name": "TapTap",
  "description": "TapTap Loyalty Program",
  "theme_color": "#F65D79",
  "background_color": "#ffffff",
  "display": "standalone",
  "scope": "/",
  "start_url": "/",
  "icons": [
    {
      "src": "/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

**Service Worker:** `apps/web/public/service-worker.js`
```javascript
// Simple service worker for offline support
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open('v1').then((cache) => {
      return cache.addAll([
        '/',
        '/index.html',
        '/manifest.json',
      ]);
    })
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request).then((response) => {
      return response || fetch(event.request);
    })
  );
});
```

---

## ✅ Phase 2 Complete Checklist

### Pages Implementation
- [ ] HomePage with all sections
- [ ] ProfilePage with settings
- [ ] BillScanPage with camera
- [ ] SearchPage with filters
- [ ] MerchantDetailPage
- [ ] RewardsPage with categories
- [ ] ExchangePage with history
- [ ] LoginPage with OTP
- [ ] RegisterPage
- [ ] NotFoundPage

### Web-Specific Features
- [ ] React Router setup
- [ ] Browser camera integration
- [ ] File upload handling
- [ ] PWA configuration
- [ ] Push notifications
- [ ] Web Share API
- [ ] SEO optimization
- [ ] Google Analytics
- [ ] Social login (Google, Facebook)
- [ ] Browser storage management

### State Management (Zustand)
- [ ] Auth store
- [ ] User store
- [ ] Merchant store
- [ ] Reward store
- [ ] Cart store
- [ ] UI store
- [ ] Persist middleware

### API Services
- [ ] HTTP client with interceptors
- [ ] Auth service
- [ ] User service
- [ ] Merchant service
- [ ] Reward service
- [ ] OCR service
- [ ] Transaction service
- [ ] Error handling

### Testing
- [ ] Unit tests for services
- [ ] Unit tests for stores
- [ ] Component tests
- [ ] Integration tests
- [ ] E2E tests (Cypress)

## 🎯 Success Metrics

- ✅ Fully functional web app
- ✅ All features working independently
- ✅ Can be extracted as standalone project
- ✅ No dependencies on Zalo code
- ✅ Optimized for web (SEO, performance)
- ✅ PWA ready
- ✅ <500KB initial bundle

## 🚀 Deployment Ready

By end of Week 3, the web app should be:
1. Fully functional standalone application
2. Ready to deploy to any hosting service
3. Can be extracted to separate repository
4. No cleanup needed if splitting from monorepo
5. Complete with all web-specific optimizations

---

**Next:** [Phase 3 - Zalo Standalone](./phase-3-zalo-standalone.md)

**Status:** 🟡 IN PROGRESS
**Last Updated:** ${new Date().toISOString()}