# 📦 Phase 1: Shared Foundation (Tuần 1)

## 🎯 Mục Tiêu
Xây dựng thư viện shared components và utilities thuần túy, không chứa bất kỳ platform-specific code nào.

## ⚠️ Nguyên Tắc Vàng
**"If it needs platform detection, it doesn't belong in shared"**

## 📅 Timeline: 5 ngày

| Ngày | Focus | Deliverables |
|------|-------|--------------|
| Ngày 1 | Component Audit | Identify truly shareable components |
| Ngày 2 | Base Components | Button, Input, Card, etc. |
| Ngày 3 | Complex Components | Lists, Forms, Modals |
| Ngày 4 | Utilities & Types | Formatters, validators, types |
| Ngày 5 | Documentation | Storybook, usage guides |

## 📋 DETAILED TASKS

### Day 1: Component Audit & Planning

#### 1.1 Analyze Current Components
**Task:** Review all existing components và classify

```markdown
## Component Classification

### ✅ PURE (Can Share)
- Button: No platform code
- Input: Pure HTML input
- Card: Just styling
- Badge: Pure presentational
- Avatar: Image display only

### ⚠️ MIXED (Need Refactor)
- NavigationHeader: Has platform detection → Split
- Modal: Different on mobile → Create 2 versions
- SearchBar: Has navigation logic → Extract logic

### ❌ PLATFORM-SPECIFIC (Don't Share)
- BottomNavigation: Web only
- ZaloEntrance: Zalo only
- WebFileUpload: Web only
```

**Checklist:**
- [ ] List all components in shared/
- [ ] Identify platform-specific code
- [ ] Create refactor plan
- [ ] Define component API standards
- [ ] Setup folder structure

#### 1.2 Setup Shared Structure
```
shared/
├── components/
│   ├── ui/           # Pure UI components
│   │   ├── Button/
│   │   ├── Input/
│   │   ├── Card/
│   │   └── index.ts
│   └── composite/    # Composed components
│       ├── Form/
│       ├── List/
│       └── index.ts
├── utils/
│   ├── formatters/
│   ├── validators/
│   └── helpers/
├── types/
│   ├── models/
│   ├── common/
│   └── index.ts
├── styles/           # Design tokens
│   ├── colors.ts
│   ├── spacing.ts
│   └── typography.ts
└── index.ts         # Main export
```

---

### Day 2: Base Components

#### 2.1 Button Component
**File:** `shared/components/ui/Button/Button.tsx`

```typescript
// PURE Component - No platform code
import React from 'react';
import { cn } from '../../utils/cn';

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  loading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  loading = false,
  disabled = false,
  onClick,
  children,
  className,
  type = 'button',
}) => {
  const baseStyles = 'inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2';
  
  const variants = {
    primary: 'bg-primary text-white hover:bg-primary-dark',
    secondary: 'bg-secondary text-white hover:bg-secondary-dark',
    outline: 'border-2 border-primary text-primary hover:bg-primary/10',
    ghost: 'text-primary hover:bg-primary/10',
  };
  
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };
  
  return (
    <button
      type={type}
      className={cn(
        baseStyles,
        variants[variant],
        sizes[size],
        fullWidth && 'w-full',
        (disabled || loading) && 'opacity-50 cursor-not-allowed',
        className
      )}
      onClick={onClick}
      disabled={disabled || loading}
    >
      {loading && <LoadingSpinner className="mr-2" />}
      {children}
    </button>
  );
};
```

**Checklist:**
- [ ] No platform imports
- [ ] No navigation logic
- [ ] Pure presentation
- [ ] Fully typed
- [ ] Documented

#### 2.2 Input Component
**File:** `shared/components/ui/Input/Input.tsx`

```typescript
// PURE Component
export interface InputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  error?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
}

export const Input: React.FC<InputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  label,
  required,
  disabled,
  className,
}) => {
  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        onBlur={onBlur}
        disabled={disabled}
        className={cn(
          'w-full px-3 py-2 border rounded-lg',
          error && 'border-red-500',
          disabled && 'bg-gray-100',
          className
        )}
      />
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};
```

#### 2.3 Card Component
```typescript
// Pure presentational component
export const Card: React.FC<CardProps> = ({
  children,
  padding = 'md',
  shadow = 'sm',
  className,
  onClick,
}) => {
  const Component = onClick ? 'button' : 'div';
  
  return (
    <Component
      className={cn(
        'bg-white rounded-lg',
        paddings[padding],
        shadows[shadow],
        onClick && 'cursor-pointer hover:shadow-md transition-shadow',
        className
      )}
      onClick={onClick}
    >
      {children}
    </Component>
  );
};
```

---

### Day 3: Complex Components

#### 3.1 Form Components
**Note:** Forms are pure presentation, validation logic stays in apps

```typescript
// shared/components/composite/Form/Form.tsx
export interface FormProps {
  onSubmit: (data: any) => void;
  children: React.ReactNode;
  className?: string;
}

export const Form: React.FC<FormProps> = ({
  onSubmit,
  children,
  className,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Collect form data
    const formData = new FormData(e.target as HTMLFormElement);
    const data = Object.fromEntries(formData);
    onSubmit(data);
  };
  
  return (
    <form onSubmit={handleSubmit} className={className}>
      {children}
    </form>
  );
};

// Form fields are just styled wrappers
export const FormField: React.FC<FormFieldProps> = ({
  children,
  error,
}) => {
  return (
    <div className="mb-4">
      {children}
      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </div>
  );
};
```

#### 3.2 List Components
```typescript
// Pure list component - no infinite scroll logic
export const List: React.FC<ListProps> = ({
  items,
  renderItem,
  emptyMessage = 'No items',
  loading,
  className,
}) => {
  if (loading) {
    return <ListSkeleton />;
  }
  
  if (items.length === 0) {
    return <EmptyState message={emptyMessage} />;
  }
  
  return (
    <div className={cn('space-y-2', className)}>
      {items.map((item, index) => (
        <div key={item.id || index}>
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  );
};
```

---

### Day 4: Utilities & Types

#### 4.1 Pure Formatters
**File:** `shared/utils/formatters.ts`

```typescript
// Pure functions - no platform code
export const formatters = {
  currency: (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  },
  
  phone: (phone: string): string => {
    // Format Vietnamese phone
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return cleaned.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
    }
    return phone;
  },
  
  date: (date: Date | string): string => {
    const d = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('vi-VN').format(d);
  },
  
  points: (points: number): string => {
    return points.toLocaleString('vi-VN') + ' VUI';
  },
  
  truncate: (text: string, length: number = 100): string => {
    if (text.length <= length) return text;
    return text.slice(0, length) + '...';
  },
};
```

#### 4.2 Pure Validators
**File:** `shared/utils/validators.ts`

```typescript
// Pure validation functions
export const validators = {
  email: (email: string): boolean => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  },
  
  phone: (phone: string): boolean => {
    const cleaned = phone.replace(/\D/g, '');
    return cleaned.length === 10 && cleaned.startsWith('0');
  },
  
  required: (value: any): boolean => {
    return value !== null && value !== undefined && value !== '';
  },
  
  minLength: (value: string, min: number): boolean => {
    return value.length >= min;
  },
  
  maxLength: (value: string, max: number): boolean => {
    return value.length <= max;
  },
};
```

#### 4.3 Type Definitions
**File:** `shared/types/models.ts`

```typescript
// Pure type definitions - no platform-specific fields
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone: string;
  avatar?: string;
  loyaltyPoints: number;
  membershipTier: 'bronze' | 'silver' | 'gold' | 'platinum';
}

export interface Merchant {
  id: string;
  name: string;
  logo: string;
  category: string;
  rating: number;
  distance?: number;
  address?: string;
}

export interface Reward {
  id: string;
  title: string;
  description: string;
  points: number;
  image: string;
  expiryDate?: string;
  quantity?: number;
}

export interface Transaction {
  id: string;
  type: 'earn' | 'redeem';
  amount: number;
  points: number;
  merchantId: string;
  date: string;
  status: 'pending' | 'completed' | 'failed';
}
```

---

### Day 5: Documentation & Testing

#### 5.1 Storybook Setup
```typescript
// .storybook/main.js
module.exports = {
  stories: ['../shared/components/**/*.stories.tsx'],
  addons: [
    '@storybook/addon-essentials',
    '@storybook/addon-a11y',
  ],
};

// Example story
// shared/components/ui/Button/Button.stories.tsx
export default {
  title: 'UI/Button',
  component: Button,
};

export const Primary = {
  args: {
    variant: 'primary',
    children: 'Click me',
  },
};

export const Loading = {
  args: {
    loading: true,
    children: 'Loading...',
  },
};
```

#### 5.2 Usage Documentation
**File:** `shared/README.md`

```markdown
# Shared Components Library

## Installation
```bash
# From web app
import { Button, Input } from '@taptap/shared';

# From Zalo app
import { Button, Input } from '@taptap/shared';
```

## Components

### Button
Pure button component with variants and sizes.

**Usage:**
```tsx
<Button 
  variant="primary"
  size="lg"
  onClick={() => console.log('clicked')}
>
  Click Me
</Button>
```

### Input
Pure input component with validation display.

**Usage:**
```tsx
<Input
  type="email"
  label="Email"
  value={email}
  onChange={setEmail}
  error={emailError}
/>
```

## Utilities

### Formatters
```tsx
import { formatters } from '@taptap/shared/utils';

formatters.currency(50000); // "50.000 ₫"
formatters.phone('0901234567'); // "0901 234 567"
```

### Validators
```tsx
import { validators } from '@taptap/shared/utils';

validators.email('<EMAIL>'); // true
validators.phone('0901234567'); // true
```
```

---

## ✅ Phase 1 Checklist

### Components
- [ ] Button (pure)
- [ ] Input (pure)
- [ ] Card (pure)
- [ ] Badge (pure)
- [ ] Avatar (pure)
- [ ] List (pure)
- [ ] Form (pure)
- [ ] Modal (pure)
- [ ] Select (pure)
- [ ] Checkbox (pure)

### Utilities
- [ ] Formatters (pure functions)
- [ ] Validators (pure functions)
- [ ] Helpers (pure functions)
- [ ] Constants (pure data)

### Types
- [ ] Model interfaces
- [ ] Common types
- [ ] Prop types
- [ ] Utility types

### Documentation
- [ ] Storybook setup
- [ ] Component stories
- [ ] Usage guides
- [ ] API documentation

### Quality
- [ ] No platform imports
- [ ] No navigation logic
- [ ] No API calls
- [ ] Full TypeScript
- [ ] Unit tests

## 🎯 Success Criteria

✅ **Pure Components:** Zero platform-specific code
✅ **Reusability:** Can copy-paste to any React project
✅ **Documentation:** Every component has Storybook story
✅ **Type Safety:** 100% TypeScript coverage
✅ **Independence:** No dependencies on app code

## ⚠️ Anti-Patterns to Avoid

```typescript
// ❌ BAD: Platform detection in shared
if (isZaloMiniApp()) {
  // Don't do this in shared!
}

// ❌ BAD: Navigation in shared
const Button = ({ onClick }) => {
  const navigate = useNavigate(); // Don't import routing!
  // ...
};

// ❌ BAD: API calls in shared
const UserCard = () => {
  useEffect(() => {
    fetchUser(); // Don't make API calls!
  }, []);
};

// ✅ GOOD: Pure component
const Button = ({ onClick, children }) => {
  return <button onClick={onClick}>{children}</button>;
};
```

## 🎉 Phase 1 Deliverables

By end of Week 1:
1. ✅ Pure component library (~20 components)
2. ✅ Pure utility functions
3. ✅ Type definitions
4. ✅ Storybook documentation
5. ✅ Zero platform coupling

---

**Next:** [Phase 2 - Web Standalone](./phase-2-web-standalone.md)

**Status:** 🟡 IN PROGRESS
**Last Updated:** ${new Date().toISOString()}