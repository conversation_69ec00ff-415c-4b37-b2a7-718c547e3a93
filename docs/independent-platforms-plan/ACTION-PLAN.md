# 🚀 ACTION PLAN - INDEPENDENT PLATFORMS IMPLEMENTATION

## 📌 QUYẾT ĐỊNH: Independent Platforms Approach

**Lý do lựa chọn:**
- ✅ <PERSON><PERSON> tách thành 2 dự án riêng trong tương lai
- ✅ Teams có thể làm việc độc lập hoàn toàn
- ✅ Optimize tối đa cho từng platform
- ✅ Không có complexity của adapters/wrappers
- ✅ Clear separation of concerns

## 📅 IMMEDIATE NEXT STEPS (Tuần này)

### Day 1-2: Component Audit & Planning
**Owner:** Lead Developer

```bash
# 1. Tạo branch mới cho refactor
git checkout -b feature/independent-platforms

# 2. Backup current state
git tag pre-refactor-backup

# 3. Create tracking document
touch shared/COMPONENTS_AUDIT.md
```

**Tasks:**
1. [ ] Review tất cả components trong `shared/components/ui/`
2. [ ] Identify platform-specific code cần remove
3. [ ] List components cần split thành 2 versions
4. [ ] Document decision cho mỗi component
5. [ ] Estimate effort cho refactor

### Day 3-5: Create Pure Shared Foundation
**Owner:** Frontend Team

**Priority Components to Refactor:**
```typescript
// shared/components/ui/ - Keep ONLY pure components

✅ KEEP AS-IS (Already pure):
- Button
- Input  
- Card
- Badge
- Avatar

⚠️ REFACTOR (Remove platform code):
- NavigationHeader → Move to apps/*/components/
- Modal → Split into WebModal và ZaloModal
- SearchBar → Remove navigation logic

❌ REMOVE (Platform-specific):
- BottomNavigation → Move to apps/web/
- ZaloEntrance → Move to apps/taptap-zalo/
```

---

## 📋 WEEK 1: SHARED FOUNDATION

### Folder Structure to Create:
```
shared/
├── components/
│   └── ui/              # ONLY pure UI components
│       ├── Button/
│       ├── Input/
│       ├── Card/
│       ├── Badge/
│       ├── Avatar/
│       └── index.ts
├── utils/
│   ├── formatters.ts    # Pure functions only
│   ├── validators.ts    # Pure validators
│   └── constants.ts     # Shared constants
├── types/
│   ├── models.ts        # Data models
│   └── common.ts        # Common types
└── styles/
    ├── colors.ts        # Design tokens
    └── typography.ts    # Typography system
```

### Component Refactor Example:
```typescript
// ❌ BEFORE (có platform detection)
export const NavigationHeader = () => {
  if (isZaloMiniApp()) {
    setNavigationBarTitle(title);
    return null;
  }
  return <WebHeader />;
};

// ✅ AFTER (2 separate components)
// apps/web/src/components/WebNavigationHeader.tsx
export const WebNavigationHeader = () => {
  return <header>...</header>;
};

// apps/taptap-zalo/src/components/ZaloNavigationHeader.tsx
export const ZaloNavigationHeader = () => {
  useEffect(() => {
    setNavigationBarTitle(title);
  }, [title]);
  return null;
};
```

---

## 📋 WEEK 2-3: WEB STANDALONE

### Setup Tasks:
```bash
# Create web app structure
mkdir -p apps/web/src/{pages,components,services,stores,hooks,utils}

# Install web-specific dependencies
cd apps/web
yarn add react-router-dom zustand axios
yarn add -D @types/react-router-dom
```

### File Creation Order:
1. **Router Setup** (`apps/web/src/router/`)
2. **HTTP Client** (`apps/web/src/services/http/`)
3. **Auth Service** (`apps/web/src/services/api/`)
4. **Auth Store** (`apps/web/src/stores/`)
5. **HomePage** (`apps/web/src/pages/`)

### Web-Specific Features to Implement:
- [ ] Browser camera with getUserMedia
- [ ] File upload with input[type=file]
- [ ] PWA with service worker
- [ ] Push notifications
- [ ] localStorage/sessionStorage
- [ ] SEO meta tags
- [ ] Google Analytics

---

## 📋 WEEK 4-5: ZALO STANDALONE

### Setup Tasks:
```bash
# Create Zalo app structure
mkdir -p apps/taptap-zalo/src/{pages,components,services,stores,hooks,utils}

# Install Zalo-specific dependencies
cd apps/taptap-zalo
yarn add jotai zmp-sdk zmp-ui
```

### Zalo SDK Integration Priority:
1. **getUserInfo** - Auth flow
2. **Camera/QR** - Bill scanning
3. **Payment** - Zalo Pay
4. **Storage** - Data persistence
5. **Share** - Social features

### Zalo-Specific Components:
```typescript
// apps/taptap-zalo/src/components/native/
├── ZaloCamera.tsx       // Uses openCamera SDK
├── ZaloQRScanner.tsx    // Uses scanQRCode SDK
├── ZaloPayment.tsx      // Uses payment SDK
├── ZaloShare.tsx        // Uses openShareSheet SDK
└── ZaloOAWidget.tsx     // OA integration
```

---

## 🔧 TECHNICAL DECISIONS

### State Management
| Platform | Library | Reason |
|----------|---------|---------|
| Web | Zustand | Simple, lightweight, good DX |
| Zalo | Jotai | Atomic state, React Suspense support |

### Routing
| Platform | Library | Config Location |
|----------|---------|-----------------|
| Web | React Router v6 | `apps/web/src/router/` |
| Zalo | ZMP Router | `apps/taptap-zalo/src/components/layout.tsx` |

### HTTP Client
| Platform | Base | Auth Method |
|----------|------|-------------|
| Web | Axios | JWT Bearer token |
| Zalo | Axios | Zalo Access Token |

### Storage
| Platform | Primary | Fallback |
|----------|---------|----------|
| Web | localStorage | sessionStorage |
| Zalo | Zalo SDK storage | In-memory |

---

## 📊 TRACKING METRICS

### Week 1 Goals
- [ ] 100% pure components identified
- [ ] 0 platform imports in shared/
- [ ] Storybook setup complete
- [ ] Component audit document complete

### Week 2-3 Goals (Web)
- [ ] All pages created
- [ ] Web routing working
- [ ] Web auth flow complete
- [ ] Camera/upload working
- [ ] PWA configured

### Week 4-5 Goals (Zalo)
- [ ] All pages created
- [ ] Zalo SDK integrated
- [ ] Native features working
- [ ] Zalo Pay integrated
- [ ] OA widget working

### Week 6 Goals (Testing)
- [ ] >80% test coverage both apps
- [ ] E2E tests passing
- [ ] Performance targets met
- [ ] Bundle sizes within limits

### Week 7 Goals (Deployment)
- [ ] Web deployed to staging
- [ ] Zalo deployed to test
- [ ] CI/CD pipelines ready
- [ ] Monitoring configured

---

## 👥 TEAM ASSIGNMENTS

### Week 1 (Shared Foundation)
- **Lead Dev:** Architecture, component audit
- **Dev 1:** Pure component refactoring
- **Dev 2:** Utilities and types

### Week 2-3 (Web Development)
- **Lead Dev:** Web architecture, routing
- **Dev 1:** Web pages implementation
- **Dev 2:** Web services and state

### Week 4-5 (Zalo Development)
- **Lead Dev:** Zalo architecture, SDK
- **Dev 1:** Zalo pages implementation
- **Dev 2:** Zalo native features

### Week 6-7 (Testing & Deploy)
- **All:** Testing and bug fixes
- **DevOps:** CI/CD setup
- **QA:** Test execution

---

## 🚨 RISK MITIGATION

| Risk | Mitigation |
|------|------------|
| Code duplication | Accept as tradeoff for independence |
| Maintenance overhead | Strong conventions, good documentation |
| Feature disparity | Regular sync meetings |
| Knowledge silos | Pair programming, code reviews |

---

## ✅ WEEK 1 CHECKLIST (Start immediately)

### Monday
- [ ] Team kickoff meeting
- [ ] Create feature branch
- [ ] Start component audit
- [ ] Setup project boards

### Tuesday
- [ ] Complete component audit
- [ ] Identify refactor targets
- [ ] Start refactoring pure components
- [ ] Remove platform detection

### Wednesday
- [ ] Continue component refactoring
- [ ] Create utility functions
- [ ] Setup type definitions
- [ ] Update imports

### Thursday
- [ ] Complete component refactoring
- [ ] Setup Storybook
- [ ] Write component stories
- [ ] Test all components

### Friday
- [ ] Code review
- [ ] Documentation update
- [ ] Plan Week 2
- [ ] Team retrospective

---

## 📞 COMMUNICATION PLAN

### Daily Standups
- Time: 9:00 AM
- Duration: 15 minutes
- Focus: Blockers and progress

### Weekly Reviews
- Friday 4:00 PM
- Show completed work
- Plan next week

### Slack Channels
- `#refactor-general` - General discussion
- `#refactor-web` - Web team
- `#refactor-zalo` - Zalo team
- `#refactor-blockers` - Urgent issues

---

## 🎯 DEFINITION OF DONE

### Component is "Done" when:
- ✅ No platform-specific code
- ✅ Full TypeScript types
- ✅ Storybook story created
- ✅ Unit tests passing
- ✅ Code reviewed

### Page is "Done" when:
- ✅ Fully functional
- ✅ Connected to API
- ✅ State management working
- ✅ Responsive design
- ✅ Tested on target platform

### Platform is "Done" when:
- ✅ All features working
- ✅ Can be extracted to separate repo
- ✅ No dependencies on other platform
- ✅ Tests passing
- ✅ Deployed to staging

---

## 🚀 LET'S START!

**First Action:** Create branch and start component audit

```bash
git checkout -b feature/independent-platforms
echo "# Component Audit Report" > shared/COMPONENTS_AUDIT.md
echo "Date: $(date)" >> shared/COMPONENTS_AUDIT.md
```

**Success Criteria:** In 7 weeks, we have 2 completely independent, fully functional apps that can be split into separate repositories at any time.

---

**Document Status:** 🟢 APPROVED & READY TO EXECUTE
**Last Updated:** ${new Date().toISOString()}
**Version:** 1.0.0