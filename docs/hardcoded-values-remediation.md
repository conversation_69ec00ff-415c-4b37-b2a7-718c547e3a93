# Hardcoded Values Remediation Guide

## Overview

This document identifies hardcoded values throughout the TapTap web application that should be made configurable for better maintainability, internationalization, and deployment flexibility.

## Categories of Hardcoded Values

### 1. Hardcoded Text Strings (Internationalization Issues)

#### **Critical Priority - Vietnamese UI Labels**

**Location:** `apps/web/src/components/navigation/BottomNavigation/BottomNavigation.tsx`
```typescript
// Lines 38-59 - Bottom navigation labels
'Trang chủ', 'Ưu đãi', 'Đổi thưởng', 'V<PERSON> chơi', 'Tài khoản'
```
**Impact:** Prevents internationalization, difficult to rebrand
**Solution:** Extract to i18n configuration file

**Location:** `apps/web/src/pages/home/<USER>
```typescript
// Lines 449, 464-465, 502 - Pull-to-refresh messages
'Đang làm mới...', 'Th<PERSON> để làm mới', 'Kéo để làm mới', 'Ưu đãi của tôi'
```
**Impact:** Cannot support multiple languages
**Solution:** Move to internationalization constants

#### **Error Messages**

**Location:** `shared/utils/giftCodeValidation.ts`
```typescript
// Lines 11, 18, 25, 36, 64-101 - Validation error messages
'Vui lòng nhập mã đổi quà'
'Mã đã được sử dụng'  
'Mã không hợp lệ'
```
**Impact:** User-facing errors cannot be localized
**Solution:** Centralized error message configuration

**Location:** `shared/hooks/useRewardDetail.ts`
```typescript
// Lines 93, 113-166 - API error messages
'ID phần thưởng không hợp lệ'
'Không thể tải thông tin phần thưởng'
'Đổi thành công! Mã voucher:'
```
**Impact:** Backend integration messages are not configurable
**Solution:** Extract to message configuration file

### 2. Hardcoded URLs and API Endpoints

#### **CDN Configuration**

**Location:** `shared/constants/config.ts`
```typescript
// Lines 71, 73 - CDN base URLs
'https://vui-cdn.taptap.vn/cdn-cgi/image'
'https://vui-cdn.taptap.vn'
```
**Impact:** Cannot switch CDN providers or environments easily
**Solution:** Environment-specific configuration

#### **Service URLs**

**Location:** `shared/services/ocr/ocrService.ts`
```typescript
// Lines 7-9 - OCR service endpoints
'https://ocr-engine-servicetest.taptap.com.vn'
'https://ocr-engine-servicestag.taptap.com.vn'  
'https://ocr-engine-service.taptap.com.vn'
```
**Impact:** Service endpoints hardcoded for different environments
**Solution:** Environment-based service configuration

#### **API Paths**

**Location:** `shared/services/api/membership.ts`
```typescript
// Lines 116-124 - API endpoint paths
'/api/v3.1/tier'
'/b2c/v3/mobile/voucher/detail'
```
**Impact:** API versioning and path changes require code updates
**Solution:** Centralize in API configuration object

### 3. Hardcoded Configuration Values

#### **Timeouts and Limits**

**Location:** `shared/constants/config.ts`
```typescript
// Lines 11-12, 56-58, 62-64, 75-76
timeout: 10000
retryAttempts: 3
maxFileSize: 3 * 1024 * 1024  // Zalo
maxFileSize: 10 * 1024 * 1024 // Web  
defaultQuality: 80
```
**Impact:** Cannot adjust performance parameters per environment
**Solution:** Environment-specific performance configuration

#### **Business Rules**

**Location:** `shared/utils/giftCodeValidation.ts`
```typescript
// Lines 1-2 - Validation constants
MIN_GIFT_CODE_LENGTH = 6
MAX_GIFT_CODE_LENGTH = 20
```
**Impact:** Business rule changes require code deployment
**Solution:** Move to business rules configuration

#### **UI Configuration**

**Location:** `apps/web/src/pages/home/<USER>
```typescript
// Lines 244, 441-442 - Pull-to-refresh configuration
maximumPullLength: 200
refreshThreshold: 60
```
**Impact:** UI behavior cannot be adjusted without code changes
**Solution:** Move to component configuration

### 4. Hardcoded Styling Values

#### **Colors**

**Location:** `apps/web/src/components/navigation/BottomNavigation/BottomNavigation.tsx`
```typescript
// Lines 23-25 - Icon colors
"#1A1818", "#9A9A9A", "#F7CC15"
```
**Impact:** Theme changes require code updates
**Solution:** Use design system variables

**Location:** `apps/web/src/styles/theme.ts`
```typescript  
// Lines 3-12 - Theme colors
'#1976d2', '#dc004e', '#ffffff', '#f5f5f5'
```
**Status:** Already centralized but could use CSS custom properties
**Solution:** Consider CSS custom properties for runtime theme switching

### 5. Hardcoded Asset References

#### **Placeholder Images**
Multiple files contain placeholder URLs:
```typescript
'https://picsum.photos/400/300?random=1'
'https://via.placeholder.com/150'
```
**Impact:** External dependencies, inconsistent placeholders
**Solution:** Create centralized placeholder configuration

## Recommended Solutions

### 1. Internationalization System

Create a comprehensive i18n system:

```typescript
// config/i18n/index.ts
export const MESSAGES = {
  vi: {
    navigation: {
      home: 'Trang chủ',
      offers: 'Ưu đãi',
      rewards: 'Đổi thưởng',
      games: 'VUI chơi',
      account: 'Tài khoản'
    },
    pullToRefresh: {
      refreshing: 'Đang làm mới...',
      releaseToRefresh: 'Thả để làm mới', 
      pullToRefresh: 'Kéo để làm mới'
    },
    errors: {
      giftCode: {
        empty: 'Vui lòng nhập mã đổi quà',
        invalid: 'Mã không hợp lệ',
        used: 'Mã đã được sử dụng'
      },
      reward: {
        invalidId: 'ID phần thưởng không hợp lệ',
        loadError: 'Không thể tải thông tin phần thưởng'
      }
    },
    success: {
      rewardExchanged: 'Đổi thành công! Mã voucher:'
    }
  },
  en: {
    // English translations
    navigation: {
      home: 'Home',
      offers: 'Offers', 
      rewards: 'Rewards',
      games: 'Games',
      account: 'Account'
    }
    // ... rest of translations
  }
}
```

Usage example:
```typescript
// Hook for accessing messages
import { useTranslation } from './hooks/useTranslation';

const BottomNavigation = () => {
  const { t } = useTranslation();
  
  return (
    <nav>
      <span>{t('navigation.home')}</span>
      <span>{t('navigation.offers')}</span>
    </nav>
  );
};
```

### 2. Environment Configuration

Create environment-specific configuration:

```typescript
// config/env-config.ts
interface EnvironmentConfig {
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
  };
  services: {
    ocrUrl: string;
  };
  cdn: {
    imageBaseUrl: string;
    assetsBaseUrl: string;
  };
  features: {
    maxFileSize: number;
    defaultQuality: number;
  };
}

const getEnvConfig = (): EnvironmentConfig => ({
  api: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || 'https://apistag.taptap.com.vn',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
    retryAttempts: parseInt(import.meta.env.VITE_RETRY_ATTEMPTS || '3')
  },
  services: {
    ocrUrl: import.meta.env.VITE_OCR_SERVICE_URL || 'https://ocr-engine-servicetest.taptap.com.vn'
  },
  cdn: {
    imageBaseUrl: import.meta.env.VITE_CDN_IMAGE_BASE_URL || 'https://vui-cdn.taptap.vn/cdn-cgi/image',
    assetsBaseUrl: import.meta.env.VITE_CDN_ASSETS_BASE_URL || 'https://vui-cdn.taptap.vn'
  },
  features: {
    maxFileSize: parseInt(import.meta.env.VITE_MAX_FILE_SIZE || '10485760'), // 10MB
    defaultQuality: parseInt(import.meta.env.VITE_DEFAULT_QUALITY || '80')
  }
});

export const ENV_CONFIG = getEnvConfig();
```

Environment files:
```bash
# .env.development
VITE_API_BASE_URL=https://apistag.taptap.com.vn
VITE_OCR_SERVICE_URL=https://ocr-engine-servicetest.taptap.com.vn
VITE_API_TIMEOUT=10000

# .env.staging  
VITE_API_BASE_URL=https://apistag.taptap.com.vn
VITE_OCR_SERVICE_URL=https://ocr-engine-servicestag.taptap.com.vn
VITE_API_TIMEOUT=15000

# .env.production
VITE_API_BASE_URL=https://api.taptap.com.vn
VITE_OCR_SERVICE_URL=https://ocr-engine-service.taptap.com.vn
VITE_API_TIMEOUT=8000
```

### 3. Business Rules Configuration

```typescript
// config/business-rules.ts
export const BUSINESS_RULES = {
  validation: {
    giftCode: {
      minLength: parseInt(import.meta.env.VITE_GIFT_CODE_MIN_LENGTH || '6'),
      maxLength: parseInt(import.meta.env.VITE_GIFT_CODE_MAX_LENGTH || '20')
    }
  },
  ui: {
    pullToRefresh: {
      maximumLength: parseInt(import.meta.env.VITE_PULL_REFRESH_MAX_LENGTH || '200'),
      threshold: parseInt(import.meta.env.VITE_PULL_REFRESH_THRESHOLD || '60')
    }
  }
} as const;
```

### 4. API Configuration

```typescript
// config/api-config.ts
export const API_ENDPOINTS = {
  membership: {
    tier: '/api/v3.1/tier',
    voucherDetail: '/b2c/v3/mobile/voucher/detail'
  },
  rewards: {
    list: '/api/v3/rewards',
    detail: '/api/v3/rewards/:id',
    exchange: '/api/v3/rewards/:id/exchange'
  }
} as const;

// Usage
const membershipService = {
  getTierInfo: () => httpClient.get(API_ENDPOINTS.membership.tier),
  getVoucherDetail: (id: string) => 
    httpClient.get(API_ENDPOINTS.membership.voucherDetail.replace(':id', id))
};
```

### 5. Design System Enhancement

```typescript
// styles/design-tokens.ts
export const DESIGN_TOKENS = {
  colors: {
    primary: 'var(--color-primary, #1976d2)',
    secondary: 'var(--color-secondary, #dc004e)',
    background: 'var(--color-background, #ffffff)',
    surface: 'var(--color-surface, #f5f5f5)',
    // Navigation specific
    navIcon: 'var(--nav-icon-color, #1A1818)',
    navIconInactive: 'var(--nav-icon-inactive, #9A9A9A)', 
    navIconActive: 'var(--nav-icon-active, #F7CC15)'
  },
  spacing: {
    pullRefreshThreshold: 'var(--pull-refresh-threshold, 60px)',
    pullRefreshMax: 'var(--pull-refresh-max, 200px)'
  }
} as const;
```

CSS custom properties:
```css
/* styles/theme.css */
:root {
  --color-primary: #1976d2;
  --color-secondary: #dc004e;
  --nav-icon-color: #1A1818;
  --nav-icon-inactive: #9A9A9A;
  --nav-icon-active: #F7CC15;
}

[data-theme="dark"] {
  --color-primary: #42a5f5;
  --color-secondary: #f06292;
  --nav-icon-color: #ffffff;
}
```

### 6. Centralized Assets Configuration

```typescript
// config/assets-config.ts
export const ASSETS_CONFIG = {
  placeholders: {
    image: import.meta.env.VITE_PLACEHOLDER_IMAGE || '/placeholder-image.png',
    avatar: import.meta.env.VITE_PLACEHOLDER_AVATAR || '/placeholder-avatar.png',
    brand: import.meta.env.VITE_PLACEHOLDER_BRAND || '/placeholder-brand.png'
  },
  external: {
    picsum: 'https://picsum.photos',
    placeholder: 'https://via.placeholder.com'
  }
} as const;
```

## Implementation Priority

### **Phase 1 (High Priority)**
1. ✅ Internationalization system for user-facing text
2. ✅ Environment configuration for APIs and services  
3. ✅ Error message centralization

### **Phase 2 (Medium Priority)**  
4. ✅ Business rules configuration
5. ✅ UI component configuration (pull-to-refresh, etc.)
6. ✅ API endpoints centralization

### **Phase 3 (Low Priority)**
7. ✅ Enhanced design system with CSS custom properties
8. ✅ Assets configuration centralization
9. ✅ Theme switching capabilities

## Migration Strategy

### Step 1: Create Configuration Infrastructure
- Set up i18n system with initial Vietnamese translations
- Create environment configuration structure
- Add TypeScript types for all configuration

### Step 2: Migrate High-Impact Areas
- Replace hardcoded navigation labels with i18n
- Convert API URLs to environment variables
- Centralize error messages

### Step 3: Gradual Migration
- Update components one by one to use configuration
- Add environment variables for each configuration type
- Test thoroughly in all environments

### Step 4: Validation & Documentation
- Ensure all hardcoded values are eliminated
- Document new configuration system
- Create deployment guides for different environments

## Benefits After Implementation

1. **Internationalization Ready**: Easy addition of new languages
2. **Environment Flexibility**: Simple deployment to different environments
3. **Business Agility**: Quick changes to business rules without deployments
4. **Theme Capabilities**: Runtime theme switching support
5. **Maintenance Efficiency**: Centralized configuration management
6. **Testing Improvements**: Configurable values for different test scenarios

## Conclusion

This remediation addresses 50+ hardcoded values across the application, significantly improving maintainability, internationalization capabilities, and deployment flexibility. The phased approach ensures minimal disruption while maximizing benefits.