# RewardDetail Business Rules & Flow Documentation

## Overview
The RewardDetail screen displays comprehensive information about a reward before users claim/purchase it. It handles complex scenarios including flash sales, age verification, point balances, stock management, and special merchant-specific flows. This is the primary conversion point where users decide to exchange points for rewards.

## Component Location
- **Main Container**: `taptap-mobile/src/scenes/rewardV3/screens/RewardDetail/index.tsx`
- **UI Component**: `taptap-mobile/src/scenes/rewardV3/screens/RewardDetail/RewardDetail.ui.tsx`
- **Purchase Flow**: `taptap-mobile/src/scenes/rewardV3/components/BottomSheetReward.tsx`
- **APIs**: `taptap-mobile/src/scenes/rewardV3/redux/api.ts`

## Architecture Pattern
The component uses **Container-Presenter Pattern**:
- **Container** (`index.tsx`): Handles data fetching, business logic, navigation
- **Presenter** (`RewardDetail.ui.tsx`): Pure UI rendering and user interactions

## Core Data Types

### RewardItemType
```typescript
interface RewardItemType {
  id: string,
  name: string,
  isVuiPoint: boolean,
  isBrandCurrencyPoint: boolean,
  issueVUIPoint: number,
  issueBrandCurrencyPoint: number,
  issueVUIPointFlsOrPP: number,  // Flash sale/special price
  issueBCPointFlsOrPP: number,   // Flash sale brand currency
  remainCount: number,           // Stock available
  stateMaxPerUser: number,       // Max per user (0 = unlimited)
  canPurchase: boolean,
  status: 'active' | 'disable',
  consentType: number,           // 1 = requires 18+ age verification
  startTime: string,
  endTime: string,
  campaign: FlashSaleCampaign,
  merchant: MerchantType,
  source: 'INTERNAL' | 'THIRD_PARTY',
  termsConditions: string
}
```

### FlashSaleCampaign
```typescript
interface FlashSaleCampaign {
  campaignName: string,
  campaignType: 'flashSale' | 'theme',
  campaignStartTime: string,
  campaignEndTime: string,
  campaignCountdownTime: string,  // When countdown starts
  displayOnPrice: boolean
}
```

### IRewardRemainCount
```typescript
interface IRewardRemainCount {
  issueRemainPerUser: number,    // How many left for this user
  message: string                // Display message for limits
}
```

## Main Application Flow

```mermaid
flowchart TD
    Start([User enters RewardDetail]) --> LoadData[Load reward detail and remaining count]
    
    LoadData --> CheckLoading{Loading complete?}
    CheckLoading -->|No| ShowLoading[Show loading view]
    CheckLoading -->|Yes| CheckStatus{Status code = 200?}
    
    CheckStatus -->|No| ShowError[Show error/not found view]
    CheckStatus -->|Yes| CheckExpired{Reward expired or disabled?}
    
    CheckExpired -->|Yes| ShowError
    CheckExpired -->|No| CheckBrandCurrency{Uses brand currency?}
    
    CheckBrandCurrency -->|Yes| FetchBalance[Fetch brand currency balance]
    CheckBrandCurrency -->|No| RenderUI[Render main UI]
    
    FetchBalance --> RenderUI
    RenderUI --> HandleAutoRedeem{autoRedeemReward param?}
    
    HandleAutoRedeem -->|Yes| TriggerPurchase[Auto trigger purchase flow]
    HandleAutoRedeem -->|No| WaitUserAction[Wait for user interaction]
    
    TriggerPurchase --> PurchaseFlow[Purchase Flow]
    WaitUserAction --> UserClicks{User clicks redeem?}
    
    UserClicks -->|Yes| CheckNestle{Is Nestle merchant?}
    UserClicks -->|No| ContinueFlow[Continue normal flow]
    
    CheckNestle -->|Yes| CheckNestleInfo{Need to collect Nestle info?}
    CheckNestle -->|No| CheckAge[Check age verification]
    
    CheckNestleInfo -->|Yes| ShowInfoForm[Show Nestle info collection form]
    CheckNestleInfo -->|No| CheckAge
    
    ShowInfoForm --> FormComplete[Form completed]
    FormComplete --> CheckAge
    
    CheckAge --> AgeCheck{consentType = 1?}
    AgeCheck -->|Yes| CheckUserAge{User age >= 18?}
    AgeCheck -->|No| ShowPurchaseSheet[Show purchase confirmation sheet]
    
    CheckUserAge -->|No| ShowAgeError[Show age restriction dialog]
    CheckUserAge -->|Yes| ShowPurchaseSheet
    
    ShowAgeError --> EndFlow[End flow]
    ShowPurchaseSheet --> PurchaseFlow
    
    PurchaseFlow --> PurchaseComplete[Purchase complete]
    PurchaseComplete --> UpdateStock[Update stock counts]
    UpdateStock --> NavigateMyRewards[Navigate to My Rewards]
```

## Flash Sale Flow

```mermaid
flowchart TD
    FlashSale[Flash Sale Campaign] --> CheckTime{Current time vs campaign?}
    
    CheckTime -->|Before countdown| ShowNormal[Show normal reward view]
    CheckTime -->|In countdown| ShowCountdown[Show flash sale countdown]
    CheckTime -->|In campaign| ShowActive[Show active flash sale]
    CheckTime -->|After campaign| ShowNormal
    
    ShowCountdown --> UserSetsReminder{User sets reminder?}
    UserSetsReminder -->|Yes| SetReminder[Add calendar reminder]
    UserSetsReminder -->|No| WaitCampaign[Wait for campaign start]
    
    SetReminder --> WaitCampaign
    WaitCampaign --> CampaignStarts[Campaign starts automatically]
    
    ShowActive --> ShowFlashPrice[Display flash sale pricing]
    ShowFlashPrice --> CountdownActive[Show countdown to end]
    CountdownActive --> CampaignEnds{Campaign ends?}
    
    CampaignEnds -->|Yes| RefreshData[Auto refresh reward data]
    CampaignEnds -->|No| ContinueCountdown[Continue countdown]
    
    CampaignStarts --> RefreshData
    RefreshData --> ShowNormal
```

## Stock Management & Availability

```mermaid
flowchart TD
    StockCheck[Check Stock Availability] --> CheckGlobalStock{remainCount > 0?}
    
    CheckGlobalStock -->|No| ShowOutStock[Show "Out of Stock"]
    CheckGlobalStock -->|Yes| CheckUserLimit{stateMaxPerUser != 0?}
    
    CheckUserLimit -->|No| ShowAvailable[Show available (unlimited per user)]
    CheckUserLimit -->|Yes| CheckUserRemain{issueRemainPerUser > 0?}
    
    CheckUserRemain -->|No| ShowUserLimit[Show user limit reached]
    CheckUserRemain -->|Yes| ShowStockWarning{remainCount < 20?}
    
    ShowStockWarning -->|Yes| ShowLimitedStock[Show "X left in stock" warning]
    ShowStockWarning -->|No| ShowAvailable
    
    ShowOutStock --> DisableButton[Disable purchase button]
    ShowUserLimit --> DisableButton
    ShowLimitedStock --> EnableButton[Enable purchase button]
    ShowAvailable --> EnableButton
```

## Point Balance & Purchase Eligibility

```mermaid
flowchart TD
    PointCheck[Check Purchase Eligibility] --> CheckPoints{Enough points?}
    
    CheckPoints -->|VUI Points| CheckVUI{VUI balance >= required?}
    CheckPoints -->|Brand Currency| CheckBC{Brand currency >= required?}
    CheckPoints -->|Both| CheckBoth[Check both balances]
    
    CheckVUI -->|No| CalcShortfall[Calculate VUI shortfall]
    CheckVUI -->|Yes| EnablePurchase[Enable purchase button]
    
    CheckBC -->|No| CalcBCShortfall[Calculate BC shortfall]
    CheckBC -->|Yes| EnablePurchase
    
    CheckBoth --> CheckVUIFirst{VUI sufficient?}
    CheckVUIFirst -->|No| CalcShortfall
    CheckVUIFirst -->|Yes| CheckBCSecond{BC sufficient?}
    
    CheckBCSecond -->|No| CalcBCShortfall
    CheckBCSecond -->|Yes| EnablePurchase
    
    CalcShortfall --> ShowEarnMore[Show "Earn X more VUI" button]
    CalcBCShortfall --> ShowEarnMoreBC[Show "Earn X more BC" button]
    
    ShowEarnMore --> NavigateEarn[Navigate to Earn section]
    ShowEarnMoreBC --> NavigateEarn
    EnablePurchase --> AllowPurchase[Allow purchase action]
```

## API Endpoints and Variables

### Primary APIs

#### 1. Get Reward Detail
- **Endpoint**: `GET /reward/detail/{rewardId}`
- **Parameters**:
  - `rewardId: string` - Unique reward identifier
- **Response**: `RewardItemType`
- **Key Fields**:
  ```typescript
  {
    id: string,
    name: string,
    isVuiPoint: boolean,
    isBrandCurrencyPoint: boolean,
    issueVUIPoint: number,
    issueBrandCurrencyPoint: number,
    issueVUIPointFlsOrPP: number,
    issueBCPointFlsOrPP: number,
    remainCount: number,
    stateMaxPerUser: number,
    canPurchase: boolean,
    consentType: number,
    startTime: string,
    endTime: string,
    campaign: FlashSaleCampaign,
    merchant: MerchantType,
    source: 'INTERNAL' | 'THIRD_PARTY'
  }
  ```

#### 2. Get Remaining Count Per User
- **Endpoint**: `GET /reward/count-remain?rewardId={rewardId}`
- **Parameters**:
  - `rewardId: string`
- **Response**: `IRewardRemainCount`
- **Purpose**: Check how many of this reward the user can still claim

#### 3. Purchase Reward
- **Endpoint**: `POST /protected/purchase-reward`
- **Headers**: Encrypted security headers
- **Payload**: 
  ```typescript
  {
    id: string,         // rewardId
    campaignId?: string // For flash sale campaigns
  }
  ```
- **Response**: `IResponseRewardRedeem`

#### 4. Get Brand Currency Balance
- **Endpoint**: `GET /currency/balance`
- **Parameters**:
  ```typescript
  {
    currencyCode: string,
    action: 'GET_BALANCE_DETAIL'
  }
  ```
- **Purpose**: Check user's brand-specific currency balance

#### 5. Confirm Age (18+ verification)
- **Endpoint**: `PUT /user/consent/confirm-age`
- **Payload**:
  ```typescript
  {
    action: 'consent',
    data: { id: string }
  }
  ```

## Conditional Rendering Rules

### 1. Stock Availability Warning
**Shows when**:
- `remainCount < 20 && remainCount > 0`

**Content**:
- "X left in stock" with fire icon
- Orange background for low stock
- Red background for out of stock

### 2. Age Restriction Notice
**Shows when**:
- `consentType === 1`

**Content**:
- Warning text: "This reward is for users 18 and above"
- Blocks purchase if user age < 18

### 3. Flash Sale Information
**Shows when**:
- `campaign` object exists
- Current time is in countdown, active, or pre-sale period

**States**:
- **Before Sale**: Shows start time
- **Countdown**: Shows countdown with reminder option
- **Active**: Shows active countdown to end
- **Ended**: Hides flash sale info

### 4. User Purchase Limits
**Shows when**:
- `stateMaxPerUser !== 0` (not unlimited)
- `myRemainingRewards.message` exists

**Content**:
- Shows remaining quantity user can purchase
- Disables purchase when limit reached

### 5. Third-Party Vendor Info
**Shows when**:
- `source === 'THIRD_PARTY'`

**Content**:
- Additional vendor information section
- Different styling and information display

### 6. Tier/Membership Info
**Shows when**:
- `tierName` exists

**Content**:
- Shows user's tier level
- Colored badge with tier name

### 7. Nestle Special Flow
**Shows when**:
- `merchant.code === 'NESTLE'`
- Requires additional information collection

**Flow**:
- Shows form collection bottom sheet
- Must complete before purchase

## Special Business Logic

### Price Display Logic
The component handles multiple pricing scenarios:

1. **Normal Price**: `issueVUIPoint` or `issueBrandCurrencyPoint`
2. **Flash Sale Price**: `issueVUIPointFlsOrPP` or `issueBCPointFlsOrPP`
3. **Theme Campaign Price**: Similar to flash sale but different display rules

### Stock Management
- **Global Stock**: `remainCount` - total available
- **Per User Stock**: `stateMaxPerUser` & `issueRemainPerUser`
- **Stock Warning Threshold**: 20 items remaining

### Age Verification Flow
For rewards with `consentType === 1`:
1. Check user's date of birth
2. Calculate age using `calculateUserAge()`
3. If < 18, show restriction dialog
4. If >= 18, proceed with purchase

### Flash Sale Timing
- **Countdown Phase**: Between `campaignCountdownTime` and `campaignStartTime`
- **Active Phase**: Between `campaignStartTime` and `campaignEndTime`
- **Reminder System**: Users can set calendar reminders during countdown

### Point Sufficiency Check
```typescript
const enoughPoint = userVUIPoints - requiredVUIPoints;
const enoughCurrency = userBrandCurrency - requiredBrandCurrency;

const isNotEnoughPoints = 
  (enoughPoint < 0 && isVuiPoint) || 
  (enoughCurrency < 0 && isBrandCurrencyPoint);
```

## User Actions & Navigation

### Primary Actions
1. **Purchase/Redeem**: Main conversion action
2. **Earn More**: When insufficient points
3. **Set Reminder**: For flash sale countdowns
4. **View Images**: Merchant gallery
5. **View Stores**: Nearby locations
6. **Favorite Merchant**: Heart button

### Navigation Flows
- **Successful Purchase** → My Rewards List
- **Insufficient Points** → Earn Section
- **View Images** → Image Gallery
- **View Stores** → Store Locations
- **Back** → Previous screen

### Error Handling

#### Loading States
- Shows skeleton loading during initial fetch
- Loading indicators on purchase actions
- Refresh capability on data fetch failure

#### Error States
- **404/Not Found**: Shows error view with retry
- **Network Errors**: Toast notifications
- **Purchase Failures**: Error dialogs with retry options
- **Age Restriction**: Informational dialog

#### Stock Management Errors
- **Out of Stock**: Disables purchase, shows warning
- **User Limit Reached**: Shows limit message, disables purchase
- **Expired Rewards**: Shows error view

## Analytics/Tracking Integration

### Key Events
- `rdGoToEarn` - Navigate to earn more points
- `flashSaleRemindMe` - Set/cancel flash sale reminders
- `rewardDetailFavBrandSelect` - Favorite merchant actions
- `purchase` - Purchase button clicked
- `rpDialogConfirmView` - Purchase dialog viewed

### Tracking Parameters
```typescript
const paramsTracking = {
  status: string,
  merchant_code: string,
  reward_name: string,
  reward_point: number,
  reward_brandCurrency_point: number,
  brandCurrency_code: string
};
```

## Testing Considerations

### Key Test Scenarios
1. **Loading states** - Success, error, timeout scenarios
2. **Stock variations** - In stock, low stock, out of stock
3. **Point balance** - Sufficient, insufficient VUI/BC points
4. **Age verification** - Under 18, over 18 scenarios
5. **Flash sale timing** - Before, countdown, active, ended
6. **Purchase flow** - Success, failure, network issues
7. **Nestle special flow** - Form collection scenarios
8. **Third-party vendors** - Different display rules
9. **Tier restrictions** - Different membership levels
10. **Campaign types** - Flash sale vs theme campaigns

### Edge Cases
- Flash sale ending during user session
- Stock running out during purchase flow
- Age verification failures
- Network connectivity issues during purchase
- Invalid reward IDs or expired rewards
- Concurrent purchase attempts

### Performance Considerations
- Image loading optimization
- Auto-refresh triggers for campaign updates
- Efficient re-renders on stock/price changes

---

*This documentation covers the complete business logic and flow of the RewardDetail component as implemented in the TapTap mobile application.*