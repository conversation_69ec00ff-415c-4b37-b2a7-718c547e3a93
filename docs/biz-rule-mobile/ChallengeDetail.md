# ChallengeDetail Business Rules & Flow Documentation

## Overview
The ChallengeDetail screen displays comprehensive information about entertainment challenges in the TapTap mobile application. It handles challenge participation, progress tracking, gift receiving, and various challenge states including completion and expiration. This is part of the gamification system that encourages user engagement.

## Component Location
- **Main Component**: `taptap-mobile/src/scenes/entertainment/screens/Challenges/ChallengeDetail/index.tsx`
- **Data Hook**: `taptap-mobile/src/scenes/entertainment/helpers/useFetchChallengeV3Detail.ts`
- **Gift Reception**: `taptap-mobile/src/scenes/entertainment/helpers/useReceivePopup.tsx`
- **APIs**: `taptap-mobile/src/scenes/entertainment/redux/api.ts`

## Core Data Types

### IChallengeV3
```typescript
interface IChallengeV3 {
  challengeId: string,
  displayChallengeName: string,
  challengeImage: string,
  hexBackgroundColor: string,
  challengeNameColor: string,
  description: string,
  note: string,
  startDate: string,
  endDate: string,
  conditionChallengeType: string,
  conditions: ChallengeCondition[],
  userProgress: UserProgress,
  packageGift: PackageGift,
  canUserJoinChallenge: boolean,
  isDisplayRemainingTime: boolean,
  joinChallengeType: 'MANUAL' | 'AUTO'
}
```

### UserProgress
```typescript
interface UserProgress {
  hadJoinChallenge: boolean,
  currentProgress: number,
  maxProgress: number,
  percentProgress: number,
  state: 'SUCCESSFULLY' | 'NOT_RECEIVED_YET' | 'RELEASING' | 'FAILED' | 'IN_PROGRESS',
  progress: BrandProgress[]
}
```

### ChallengeCondition
```typescript
interface ChallengeCondition {
  conditionType: 'BRAND' | 'OPEN_LINK',
  conditionValue: string,
  conditionName: string
}
```

## Main Application Flow

```mermaid
flowchart TD
    Start([User enters ChallengeDetail]) --> LoadChallenge[Load challenge detail]
    
    LoadChallenge --> CheckLoading{Loading complete?}
    CheckLoading -->|No| ShowLoading[Show loading view]
    CheckLoading -->|Yes| CheckError{Has error?}
    
    CheckError -->|Yes| ShowError[Show error message]
    CheckError -->|No| CheckExpired{Challenge expired?}
    
    CheckExpired --> ExpiredCheck{remainingDays < 1 OR packageGift.remain = 0?}
    ExpiredCheck -->|Yes| ShowExpiredView[Show expired challenge - grayscale]
    ExpiredCheck -->|No| CheckJoinType{joinChallengeType?}
    
    CheckJoinType -->|AUTO| AutoJoined[User automatically joined]
    CheckJoinType -->|MANUAL| CheckManualJoin{hadJoinChallenge?}
    
    CheckManualJoin -->|Yes| ManualJoined[User manually joined]
    CheckManualJoin -->|No| ShowJoinButton[Show join challenge button]
    
    AutoJoined --> CheckProgress[Check user progress]
    ManualJoined --> CheckProgress
    ShowJoinButton --> UserJoins{User clicks join?}
    
    UserJoins -->|Yes| JoinChallenge[Join challenge API call]
    UserJoins -->|No| WaitJoin[Wait for user action]
    
    JoinChallenge --> JoinSuccess{Join successful?}
    JoinSuccess -->|Yes| RefreshData[Refresh challenge data]
    JoinSuccess -->|No| ShowJoinError[Show join error]
    
    RefreshData --> CheckProgress
    CheckProgress --> CheckState{userProgress.state?}
    
    CheckState -->|IN_PROGRESS| ShowProgress[Show progress tracking]
    CheckState -->|NOT_RECEIVED_YET| ShowReceiveButton[Show 'Receive Gift' button]
    CheckState -->|SUCCESSFULLY| ShowCompleted[Show completed state]
    CheckState -->|FAILED| ShowFailed[Show failed state]
    CheckState -->|RELEASING| ShowReleasing[Show releasing state]
    
    ShowReceiveButton --> UserReceives{User clicks receive?}
    UserReceives -->|Yes| ReceiveGift[Receive gift API call]
    UserReceives -->|No| WaitReceive[Wait for user action]
    
    ReceiveGift --> ReceiveSuccess{Receive successful?}
    ReceiveSuccess -->|Yes| ShowSuccessPopup[Show success popup]
    ReceiveSuccess -->|No| ShowReceiveError[Show receive error]
    
    ShowSuccessPopup --> RefreshAfterReceive[Refresh challenge data]
    RefreshAfterReceive --> ShowCompleted
    
    ShowExpiredView --> EndFlow[End flow]
    ShowCompleted --> EndFlow
    ShowFailed --> EndFlow
    ShowProgress --> EndFlow
```

## Challenge State Management

```mermaid
flowchart TD
    StateCheck[Check Challenge State] --> JoinCheck{Join Type & Status}
    
    JoinCheck -->|AUTO + Any Progress| AutoJoinedState[Automatically Joined]
    JoinCheck -->|MANUAL + hadJoinChallenge=true| ManualJoinedState[Manually Joined]
    JoinCheck -->|MANUAL + hadJoinChallenge=false| NotJoinedState[Not Joined Yet]
    
    AutoJoinedState --> ProgressCheck[Check Progress State]
    ManualJoinedState --> ProgressCheck
    NotJoinedState --> ShowJoinUI[Show Join Button]
    
    ProgressCheck --> StateSwitch{userProgress.state}
    
    StateSwitch -->|IN_PROGRESS| ActiveChallenge[Active Challenge]
    StateSwitch -->|NOT_RECEIVED_YET| ReadyToReceive[Ready to Receive Gift]
    StateSwitch -->|SUCCESSFULLY| CompletedChallenge[Challenge Completed]
    StateSwitch -->|FAILED| FailedChallenge[Challenge Failed]
    StateSwitch -->|RELEASING| ReleasingGift[Gift Being Released]
    
    ActiveChallenge --> ShowProgressBar[Show progress: currentProgress/maxProgress]
    ActiveChallenge --> ShowTimeRemaining[Show remaining time]
    ActiveChallenge --> ShowBrandConditions[Show brand completion status]
    
    ReadyToReceive --> ShowReceiveButton[Show floating 'Receive Gift' button]
    ReadyToReceive --> ShowCompletedTag[Show completed tag]
    
    CompletedChallenge --> ShowSuccessTag[Show success tag]
    CompletedChallenge --> GrayOutButton[Disable all action buttons]
    
    FailedChallenge --> ShowFailedTag[Show failed tag]
    FailedChallenge --> GrayOutButton
    
    ReleasingGift --> ShowReleasingTag[Show releasing tag]
    ReleasingGift --> ShowReceiveButton
```

## Expiration Logic

```mermaid
flowchart TD
    ExpirationCheck[Check Challenge Expiration] --> TimeCheck{calculateRemainingDays(endDate) < 1?}
    
    TimeCheck -->|Yes| SetExpired[Mark as expired]
    TimeCheck -->|No| GiftCheck{packageGift.remain === 0?}
    
    GiftCheck -->|Yes| CheckReceived{User received gift?}
    GiftCheck -->|No| NotExpired[Not expired]
    
    CheckReceived -->|state=NOT_RECEIVED_YET & joined| AllowReceive[Still allow gift receive]
    CheckReceived -->|Other states| SetExpired
    
    SetExpired --> ApplyExpiredStyles[Apply expired visual styles]
    ApplyExpiredStyles --> GrayscaleImage[Convert challenge image to grayscale]
    ApplyExpiredStyles --> ChangeColors[Change background to gray]
    ApplyExpiredStyles --> ChangeTextColor[Change text color to black]
    ApplyExpiredStyles --> DisableActions[Disable all action buttons]
    
    NotExpired --> NormalDisplay[Show normal challenge colors and actions]
    AllowReceive --> NormalDisplay
```

## Conditional Rendering Rules

### 1. Challenge Image Display
- **Normal**: Full color challenge image
- **Expired**: Grayscale filter applied (`<Grayscale amount={1} />`)

### 2. Background Colors
- **Normal**: `challenge.hexBackgroundColor`
- **Expired**: `COLORS.grey3` (gray)

### 3. Text Colors  
- **Normal**: `challenge.challengeNameColor`
- **Expired**: `COLORS.primaryBlack` (black)

### 4. Progress Display
**Shows when**:
- User has joined challenge (`isJoinedChallenge = true`)
- Challenge is not completed (`state !== 'SUCCESSFULLY'`)

**Content**:
- Progress bar: `currentProgress/maxProgress`
- Percentage: `percentProgress`
- Time remaining if `isDisplayRemainingTime = true`

### 5. Completion Tags
**CompleteTag shows when**:
- `state` is one of: `'SUCCESSFULLY'`, `'NOT_RECEIVED_YET'`, `'RELEASING'`, `'FAILED'`
- User has joined challenge

**TimeAgoTag shows when**:
- User has joined challenge
- Challenge is not completed
- Shows progress and time remaining

### 6. Action Button (Float Button)
**Receive Gift Button shows when**:
- `state === 'NOT_RECEIVED_YET'`
- User has joined challenge
- Challenge is not expired

**Join Challenge Button shows when**:
- `joinChallengeType === 'MANUAL'`
- `hadJoinChallenge === false`
- `canUserJoinChallenge === true`
- Challenge is not expired

## API Endpoints and Variables

### Primary APIs

#### 1. Get Challenge Detail
- **Endpoint**: `GET /entertainment/challenge/{challengeId}`
- **Response**: `IChallengeV3`
- **Key Fields**:
  ```typescript
  {
    challengeId: string,
    displayChallengeName: string,
    challengeImage: string,
    hexBackgroundColor: string,
    description: string, // HTML content
    note: string, // HTML content
    endDate: string,
    conditions: ChallengeCondition[],
    userProgress: UserProgress,
    packageGift: PackageGift,
    joinChallengeType: 'MANUAL' | 'AUTO'
  }
  ```

#### 2. Join Challenge
- **Endpoint**: `POST /entertainment/challenge/join`
- **Payload**: `{ challengeId: string }`
- **Purpose**: Manually join a challenge (for MANUAL type challenges)

#### 3. Receive Gift
- **Endpoint**: `POST /entertainment/challenge/receive-gift`
- **Payload**: `{ challengeId: string }`
- **Purpose**: Claim completed challenge rewards

#### 4. Get Challenge Progress
- **Endpoint**: `GET /entertainment/challenge/{challengeId}/progress`
- **Response**: Updated `UserProgress`
- **Purpose**: Refresh user's progress data

## Special Business Logic

### Auto-Refresh Triggers
The component automatically refreshes challenge data when:
1. **Focus Change**: When screen comes into focus (`useIsFocused`)
2. **Challenge Completion**: When Redux store indicates challenge was completed
3. **Gift Reception**: When Redux store indicates gift was received
4. **Manual Refresh**: After successful join or receive actions

### Progress Calculation
```typescript
const isCompleted = [
  'SUCCESSFULLY', 
  'NOT_RECEIVED_YET', 
  'RELEASING', 
  'FAILED'
].includes(userProgress?.state || '') && isJoinedChallenge;
```

### Join Status Logic
```typescript
const isJoinedChallenge = 
  (joinChallengeType === 'MANUAL' && userProgress?.hadJoinChallenge) ||
  joinChallengeType === 'AUTO';
```

### Brand Condition Handling
- Finds first condition with type `'BRAND'` or `'OPEN_LINK'`
- Displays brand completion progress
- Shows associated merchant information

### Animated Scroll Effects
- **Background Animation**: Parallax effect on challenge image
- **Header Opacity**: Fades in header background on scroll
- **Interpolation Values**: 
  - Start: `0` (top of scroll)
  - Middle: `TOP_HEIGHT - HEADER_HEIGHT` (319px)
  - End: `TOP_HEIGHT` (411px)

## User Actions & Navigation

### Primary Actions
1. **Join Challenge**: For manual-join challenges
2. **Receive Gift**: When challenge is completed
3. **View Brand Progress**: Navigate to brand-specific progress
4. **External Links**: Open challenge-related websites

### Navigation Flows
- **Successful Join** → Refresh challenge data
- **Successful Receive** → Show success popup → Refresh data
- **Brand Progress** → Brand detail screens
- **External Links** → In-app browser or external app

## Error Handling

### Loading States
- Shows skeleton loading during initial fetch
- Loading indicators on action buttons
- Refresh capability on data fetch failure

### Error States
- **Network Errors**: Shows error message with retry option
- **Join Failures**: Error dialog with retry capability
- **Receive Failures**: Error toast with retry option
- **Expired Challenges**: Visual indicators and disabled actions

### Challenge State Errors
- **Already Joined**: Prevents duplicate join attempts
- **Gift Already Received**: Shows completed state
- **Insufficient Progress**: Disables receive button
- **Expired Challenges**: All actions disabled

## Analytics/Tracking Integration

### Key Events (via TrackingManagerV3)
- Challenge detail view
- Join challenge action
- Receive gift action
- Brand condition interactions
- External link clicks

### Tracking Parameters
```typescript
const trackingParams = {
  challengeId: string,
  challengeName: string,
  userProgress: number,
  challengeState: string
};
```

## Testing Considerations

### Key Test Scenarios
1. **Loading states** - Success, error, timeout scenarios
2. **Join types** - AUTO vs MANUAL join challenges
3. **Progress states** - All 5 possible states
4. **Expiration logic** - Time-based and gift-based expiration
5. **Gift receiving** - Success, failure, already received
6. **Visual states** - Expired (grayscale) vs normal display
7. **Auto-refresh** - Focus changes, Redux updates
8. **Brand conditions** - Single/multiple brand requirements
9. **HTML content** - Description and note rendering
10. **Animated interactions** - Scroll effects, button states

### Edge Cases
- Challenge expiring during user session
- Gift stock running out during completion
- Network failures during join/receive actions
- Invalid challenge IDs
- User losing eligibility mid-session
- Concurrent completion attempts

### Performance Considerations
- Lazy loading of challenge images
- Efficient re-renders on state changes
- Animated scroll performance optimization
- HTML content rendering efficiency

---

*This documentation covers the complete business logic and flow of the ChallengeDetail component in the TapTap mobile entertainment system.*