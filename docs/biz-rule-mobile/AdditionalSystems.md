# Additional Mobile Systems Business Rules & Flow Documentation

## Overview
This document covers additional mobile systems in the TapTap application including Account Management, Home Dashboard, OCR Bill Scanning, Avatar System, Burn VUI System, and Entertainment Games.

## Systems Architecture

### 1. Account Management System (`/scenes/account`)
**Purpose**: User profile management and account-related features
**Key Components**:
- User profile management (MyInfo)
- Password management (ChangePassword)
- Customer support (ContactUs, FAQs)
- Account deletion and linking
- Tier membership system
- Terms and policies management

### 2. Home Dashboard System (`/scenes/homeV3`)
**Purpose**: Main application dashboard with dynamic content sections
**Key Components**:
- Dynamic section rendering based on configuration
- Banner management and display
- News and content sections
- Popular merchant highlights
- Game integrations
- Reward showcases
- Challenge promotions
- Flash sale displays
- User-specific content personalization

### 3. OCR Bill Scanning System (`/scenes/ocr`)
**Purpose**: Receipt scanning for point earning through image recognition
**Key Components**:
- Camera-based bill scanning
- Image cropping and processing
- Bill validation and verification
- Point calculation from scanned receipts
- Scan history and tracking
- Campaign-based scanning rewards
- User barcode management

### 4. Avatar System (`/scenes/avatar`)
**Purpose**: User avatar customization and personalization
**Key Components**:
- Avatar selection interface
- Customization options
- Avatar board management

### 5. Burn VUI System (`/scenes/burn`)
**Purpose**: Points redemption and reward claiming
**Key Components**:
- VUI point spending interface
- Reward collection panels
- Merchant reward listings
- Top merchant highlights

## Account Management Flow

```mermaid
flowchart TD
    AccountEntry[User enters Account section] --> LoadProfile[Load user profile data]
    
    LoadProfile --> ShowAccountMenu[Show account menu options]
    ShowAccountMenu --> MenuSelection{User selects option}
    
    MenuSelection -->|My Info| ShowProfileEdit[Show profile editing]
    MenuSelection -->|Change Password| ShowPasswordChange[Show password change]
    MenuSelection -->|Tier Member| ShowTierInfo[Show tier membership info]
    MenuSelection -->|Contact Us| ShowContactSupport[Show contact support]
    MenuSelection -->|FAQ| ShowFAQList[Show FAQ list]
    MenuSelection -->|Delete Account| ShowDeleteConfirm[Show delete confirmation]
    MenuSelection -->|Link Partner| ShowPartnerLink[Show partner linking]
    MenuSelection -->|Terms/Policy| ShowLegalInfo[Show terms and policies]
    
    ShowProfileEdit --> ProfileFields{Field to update}
    ProfileFields -->|Personal Info| UpdatePersonalInfo[Update personal information]
    ProfileFields -->|Date of Birth| UpdateDOB[Update date of birth]
    ProfileFields -->|Gender| UpdateGender[Update gender selection]
    ProfileFields -->|Province| UpdateLocation[Update location info]
    
    UpdatePersonalInfo --> ValidateInfo{Valid information?}
    ValidateInfo -->|No| ShowValidationError[Show validation errors]
    ValidateInfo -->|Yes| SaveProfile[Save profile updates]
    
    ShowValidationError --> ShowProfileEdit
    SaveProfile --> ProfileUpdateResult[Show update result]
    
    ShowPasswordChange --> CurrentPasswordInput[Enter current password]
    CurrentPasswordInput --> NewPasswordInput[Enter new password]
    NewPasswordInput --> ConfirmPasswordInput[Confirm new password]
    ConfirmPasswordInput --> ValidatePasswords{Passwords valid?}
    
    ValidatePasswords -->|No| ShowPasswordError[Show password errors]
    ValidatePasswords -->|Yes| UpdatePassword[Update password]
    
    ShowPasswordError --> NewPasswordInput
    UpdatePassword --> PasswordUpdateResult[Show password update result]
    
    ShowTierInfo --> LoadTierData[Load tier membership data]
    LoadTierData --> DisplayTierStatus[Display current tier status]
    DisplayTierStatus --> ShowTierBenefits[Show tier benefits]
    ShowTierBenefits --> ShowProgressToNext[Show progress to next tier]
    
    ShowDeleteConfirm --> DeleteConfirmation{User confirms deletion?}
    DeleteConfirmation -->|No| ShowAccountMenu
    DeleteConfirmation -->|Yes| ProcessAccountDeletion[Process account deletion]
    
    ProcessAccountDeletion --> DeletionResult{Deletion successful?}
    DeletionResult -->|No| ShowDeletionError[Show deletion error]
    DeletionResult -->|Yes| LogoutUser[Logout user]
    
    ShowDeletionError --> ShowAccountMenu
    LogoutUser --> ReturnToAuth[Return to authentication]
```

## Home Dashboard Flow

```mermaid
flowchart TD
    HomeEntry[User enters Home screen] --> LoadHomeConfig[Load home configuration]
    
    LoadHomeConfig --> LoadSections[Load all home sections]
    LoadSections --> RenderSections[Render dynamic sections]
    
    RenderSections --> SectionTypes{Section types}
    
    SectionTypes -->|Banner| LoadBanners[Load banner content]
    SectionTypes -->|News| LoadNews[Load news articles]
    SectionTypes -->|Rewards| LoadRewards[Load reward highlights]
    SectionTypes -->|Merchants| LoadMerchants[Load popular merchants]
    SectionTypes -->|Games| LoadGames[Load available games]
    SectionTypes -->|Challenges| LoadChallenges[Load active challenges]
    SectionTypes -->|Flash Sale| LoadFlashSale[Load flash sale items]
    SectionTypes -->|Affiliate| LoadAffiliate[Load affiliate opportunities]
    
    LoadBanners --> DisplayBanners[Display banner carousel]
    DisplayBanners --> BannerInteraction{User interacts with banner?}
    BannerInteraction -->|Yes| NavigateBannerTarget[Navigate to banner target]
    BannerInteraction -->|No| ContinueHome[Continue home browsing]
    
    LoadNews --> DisplayNews[Display news section]
    DisplayNews --> NewsInteraction{User selects news?}
    NewsInteraction -->|Yes| OpenNewsDetail[Open news detail]
    NewsInteraction -->|No| ContinueHome
    
    LoadRewards --> DisplayRewards[Display reward section]
    DisplayRewards --> RewardInteraction{User selects reward?}
    RewardInteraction -->|Yes| OpenRewardDetail[Open reward detail]
    RewardInteraction -->|No| ContinueHome
    
    LoadMerchants --> DisplayMerchants[Display merchant section]
    DisplayMerchants --> MerchantInteraction{User selects merchant?}
    MerchantInteraction -->|Yes| OpenMerchantDetail[Open merchant detail]
    MerchantInteraction -->|No| ContinueHome
    
    LoadGames --> DisplayGames[Display games section]
    DisplayGames --> GameInteraction{User selects game?}
    GameInteraction -->|Yes| OpenGame[Open game interface]
    GameInteraction -->|No| ContinueHome
    
    LoadChallenges --> DisplayChallenges[Display challenges section]
    DisplayChallenges --> ChallengeInteraction{User selects challenge?}
    ChallengeInteraction -->|Yes| OpenChallengeDetail[Open challenge detail]
    ChallengeInteraction -->|No| ContinueHome
    
    LoadFlashSale --> DisplayFlashSale[Display flash sale section]
    DisplayFlashSale --> FlashSaleInteraction{User selects flash sale item?}
    FlashSaleInteraction -->|Yes| OpenFlashSaleDetail[Open flash sale detail]
    FlashSaleInteraction -->|No| ContinueHome
    
    ContinueHome --> RefreshCheck{Refresh triggered?}
    RefreshCheck -->|Yes| LoadHomeConfig
    RefreshCheck -->|No| HandleScrolling[Handle section scrolling]
    
    HandleScrolling --> LoadMoreContent{Load more content needed?}
    LoadMoreContent -->|Yes| LoadAdditionalItems[Load additional section items]
    LoadMoreContent -->|No| ContinueHome
    
    LoadAdditionalItems --> UpdateSections[Update section content]
    UpdateSections --> ContinueHome
```

## OCR Bill Scanning Flow

```mermaid
flowchart TD
    OCREntry[User enters OCR scanning] --> CheckPermissions[Check camera permissions]
    
    CheckPermissions --> PermissionStatus{Camera permission granted?}
    PermissionStatus -->|No| RequestPermission[Request camera permission]
    PermissionStatus -->|Yes| CheckUserStatus[Check user scanning status]
    
    RequestPermission --> PermissionResult{Permission granted?}
    PermissionResult -->|No| ShowPermissionError[Show permission error]
    PermissionResult -->|Yes| CheckUserStatus
    
    ShowPermissionError --> ExitScanning[Exit scanning flow]
    
    CheckUserStatus --> StatusCheck{Scanning allowed?}
    StatusCheck -->|Daily limit reached| ShowDailyLimitError[Show daily limit message]
    StatusCheck -->|Account blocked| ShowBlockedMessage[Show blocked account message]
    StatusCheck -->|Allowed| StartScanningFlow[Start scanning workflow]
    
    ShowDailyLimitError --> ExitScanning
    ShowBlockedMessage --> ExitScanning
    
    StartScanningFlow --> ShowScanInterface[Show camera scan interface]
    ShowScanInterface --> ScanSteps[Show progress steps]
    ScanSteps --> Page1Snap[Page 1: Snap Bill]
    
    Page1Snap --> CameraReady[Camera interface ready]
    CameraReady --> UserAction{User action}
    
    UserAction -->|Take Photo| CaptureImage[Capture bill image]
    UserAction -->|Flash Toggle| ToggleFlash[Toggle camera flash]
    UserAction -->|Cancel| ExitScanning
    
    ToggleFlash --> CameraReady
    
    CaptureImage --> ProcessImage[Process captured image]
    ProcessImage --> Page2Processing[Page 2: Image Processing]
    
    Page2Processing --> ShowProcessingUI[Show processing animation]
    ShowProcessingUI --> CropImage[Auto-crop bill image]
    CropImage --> ValidateImage{Image quality valid?}
    
    ValidateImage -->|No| ShowRetakeOption[Show retake option]
    ValidateImage -->|Yes| Page3Confirmation[Page 3: Confirmation]
    
    ShowRetakeOption --> RetakeChoice{User wants to retake?}
    RetakeChoice -->|Yes| Page1Snap
    RetakeChoice -->|No| ExitScanning
    
    Page3Confirmation --> ShowBillPreview[Show cropped bill preview]
    ShowBillPreview --> BillInfoInput[Bill information input form]
    BillInfoInput --> UserConfirmation{User confirms bill info?}
    
    UserConfirmation -->|Edit| EditBillInfo[Edit bill information]
    UserConfirmation -->|Confirm| SubmitBill[Submit bill for processing]
    UserConfirmation -->|Cancel| ExitScanning
    
    EditBillInfo --> BillInfoInput
    
    SubmitBill --> APISubmission[API call to submit bill]
    APISubmission --> SubmissionResult{Submission successful?}
    
    SubmissionResult -->|No| ShowSubmissionError[Show submission error]
    SubmissionResult -->|Yes| CalculateReward[Calculate earning points]
    
    ShowSubmissionError --> Page3Confirmation
    
    CalculateReward --> ShowRewardResult[Show earned points]
    ShowRewardResult --> UpdateUserBalance[Update user balance]
    UpdateUserBalance --> SaveBillHistory[Save to bill history]
    SaveBillHistory --> ShowSuccessMessage[Show success message]
    ShowSuccessMessage --> OfferAnotherScan{Offer another scan?}
    
    OfferAnotherScan -->|Yes| CheckUserStatus
    OfferAnotherScan -->|No| ExitScanning
```

## API Endpoints and Business Logic

### Account Management APIs

#### 1. Get User Profile
- **Endpoint**: `GET /account/profile`
- **Response**: Complete user profile information
- **Purpose**: Display user information in account section

#### 2. Update User Profile
- **Endpoint**: `PUT /account/profile`
- **Payload**:
  ```typescript
  {
    fullName?: string,
    dateOfBirth?: string,
    gender?: 'male' | 'female' | 'other',
    province?: string,
    district?: string,
    address?: string
  }
  ```

#### 3. Change Password
- **Endpoint**: `POST /account/change-password`
- **Payload**:
  ```typescript
  {
    currentPassword: string,
    newPassword: string,
    confirmPassword: string
  }
  ```

#### 4. Delete Account
- **Endpoint**: `DELETE /account/delete`
- **Purpose**: Permanently delete user account

#### 5. Get Tier Membership Info
- **Endpoint**: `GET /account/tier-membership`
- **Response**: Current tier status and benefits

### Home Dashboard APIs

#### 1. Get Home Configuration
- **Endpoint**: `GET /home/<USER>
- **Response**: Dynamic home section configuration
- **Purpose**: Determine which sections to display

#### 2. Get Section Content
- **Endpoint**: `GET /home/<USER>/{sectionCode}`
- **Parameters**: `{ page?: number, size?: number }`
- **Response**: Section-specific content data

### OCR Scanning APIs

#### 1. Get User Scanning Status
- **Endpoint**: `GET /ocr/user-status`
- **Response**: Daily scan limits and current status
- **Purpose**: Check if user can scan more bills

#### 2. Submit Bill Scan
- **Endpoint**: `POST /ocr/submit-bill`
- **Payload**:
  ```typescript
  {
    image: string, // Base64 encoded image
    billInfo: {
      merchantCode?: string,
      amount?: number,
      date?: string,
      billNumber?: string
    },
    location?: { lat: number, lng: number }
  }
  ```

#### 3. Get Bill History
- **Endpoint**: `GET /ocr/bill-history`
- **Parameters**: `{ page: number, size: number, status?: string }`
- **Response**: User's bill scanning history

## Business Rules and Validations

### Account Management Rules

#### Profile Update Validation
```typescript
interface ProfileValidation {
  fullName: {
    minLength: number,    // Minimum name length
    maxLength: number,    // Maximum name length
    allowedChars: RegExp  // Allowed character pattern
  },
  dateOfBirth: {
    minAge: number,       // Minimum age requirement
    maxAge: number,       // Maximum age limit
    format: string        // Expected date format
  },
  phoneNumber: {
    immutable: boolean    // Cannot be changed after registration
  }
}
```

#### Password Security Rules
- **Minimum Length**: 8 characters
- **Complexity**: Must contain letters and numbers
- **History**: Cannot reuse last 3 passwords
- **Expiry**: Optional password expiry policy

### OCR Scanning Rules

#### Daily Scanning Limits
```typescript
interface ScanningLimits {
  dailyLimit: number,           // Maximum scans per day
  minimumAmount: number,        // Minimum bill amount
  maximumAmount: number,        // Maximum bill amount
  allowedMerchants: string[],   // Participating merchant codes
  pointsPerDollar: number       // Point earning rate
}
```

#### Image Quality Requirements
- **Resolution**: Minimum image resolution
- **File Size**: Maximum file size limits
- **Format**: Supported image formats (JPEG, PNG)
- **Quality Score**: Minimum OCR confidence threshold

### Home Dashboard Rules

#### Section Display Logic
```typescript
interface SectionRules {
  userTierRequired?: string,      // Minimum tier requirement
  locationBased?: boolean,        // Show based on user location
  timeRestricted?: {              // Time-based display rules
    startTime: string,
    endTime: string
  },
  personalizedContent?: boolean,   // Use user preferences
  maxItems?: number               // Maximum items per section
}
```

## Error Handling and User Experience

### Account Management Errors
- **Profile Update Failures**: Clear field-level validation messages
- **Password Change Issues**: Security-focused error messages
- **Account Deletion**: Confirmation flows and reversal options
- **Network Issues**: Retry mechanisms and offline handling

### OCR Scanning Errors
- **Camera Issues**: Permission guidance and troubleshooting
- **Image Quality**: Guidance for better photo capture
- **Processing Failures**: Retry options with improved instructions
- **Daily Limits**: Clear explanation of reset times and alternatives

### Home Dashboard Errors
- **Content Loading**: Graceful fallbacks and retry options
- **Network Issues**: Cached content display when offline
- **Section Failures**: Individual section error handling
- **Performance**: Lazy loading and progressive enhancement

## Analytics and Tracking

### Account Management Metrics
- **Profile Update Rate**: How often users update profiles
- **Feature Usage**: Which account features are most used
- **Support Requests**: FAQ effectiveness and contact patterns
- **Account Deletion**: Reasons and prevention opportunities

### OCR Scanning Metrics
- **Scan Success Rate**: Percentage of successful scans
- **Image Quality Distribution**: Quality scores of submitted images
- **Daily Usage Patterns**: Peak scanning times and behaviors
- **Point Earning Effectiveness**: Average points per scan

### Home Dashboard Metrics
- **Section Engagement**: Click-through rates for each section
- **Content Preferences**: Which content types perform best
- **User Flow Patterns**: Navigation paths through home sections
- **Personalization Effectiveness**: Impact of personalized content

---

*This documentation covers the additional mobile systems that support user engagement, account management, and earning opportunities in the TapTap mobile application.*