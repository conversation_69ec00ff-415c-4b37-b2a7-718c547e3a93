# Currency & Rating Systems Business Rules & Flow Documentation

## Overview
This document covers the Currency Management and Brand Rating systems in the TapTap mobile application. These systems handle multi-currency point management, brand feedback collection, and survey/data collection campaigns.

## Systems Architecture

### 1. Currency System (`/scenes/currencies`)
**Purpose**: Multi-currency point management beyond basic VUI points
**Key Components**:
- VUI (Virtual User Interest) points - primary currency
- Brand-specific currency management
- Point history and transaction tracking
- Currency conversion and exchange
- Balance display and management

### 2. Brand Rating System (`/scenes/brandrating`)
**Purpose**: Brand feedback, surveys, and data collection
**Key Components**:
- Brand rating and review interface
- Survey campaign management
- Data collection workflows
- Rating history and completion tracking
- Feedback submission and validation

## Currency System Flow

```mermaid
flowchart TD
    CurrencyEntry[User enters Currency section] --> LoadBalances[Load currency balances]
    
    LoadBalances --> DisplayCurrencies[Display available currencies]
    DisplayCurrencies --> CurrencyChoice{User selects currency}
    
    CurrencyChoice -->|VUI Points| ShowVUIDetail[Show VUI point details]
    CurrencyChoice -->|Brand Currency| ShowBrandDetail[Show brand currency details]
    CurrencyChoice -->|History| ShowPointHistory[Show point history]
    
    ShowVUIDetail --> VUIActions{VUI point actions}
    VUIActions -->|View History| ShowVUIHistory[Show VUI transaction history]
    VUIActions -->|Exchange| ShowExchangeOptions[Show exchange options]
    VUIActions -->|Spend| ShowSpendingOptions[Show spending options]
    
    ShowBrandDetail --> BrandActions{Brand currency actions}
    BrandActions -->|View Balance| ShowBrandBalance[Show detailed brand balance]
    BrandActions -->|Transaction History| ShowBrandHistory[Show brand transaction history]
    BrandActions -->|Exchange| ShowBrandExchange[Show brand exchange options]
    
    ShowPointHistory --> FilterOptions[Show history filter options]
    FilterOptions --> ApplyFilters{Apply filters}
    ApplyFilters -->|Date Range| FilterByDate[Filter by date range]
    ApplyFilters -->|Currency Type| FilterByCurrency[Filter by currency type]
    ApplyFilters -->|Transaction Type| FilterByType[Filter by transaction type]
    
    FilterByDate --> DisplayFilteredHistory[Display filtered history]
    FilterByCurrency --> DisplayFilteredHistory
    FilterByType --> DisplayFilteredHistory
    
    DisplayFilteredHistory --> HistoryActions{History actions}
    HistoryActions -->|View Details| ShowTransactionDetail[Show transaction details]
    HistoryActions -->|Export| ExportHistory[Export transaction history]
    HistoryActions -->|Dispute| InitiateDispute[Initiate transaction dispute]
    
    ShowExchangeOptions --> ExchangeFlow[Currency exchange flow]
    ShowSpendingOptions --> SpendingFlow[Point spending flow]
    ShowBrandExchange --> BrandExchangeFlow[Brand currency exchange flow]
    
    ExchangeFlow --> ConfirmExchange[Confirm exchange transaction]
    SpendingFlow --> ConfirmSpending[Confirm point spending]
    BrandExchangeFlow --> ConfirmBrandExchange[Confirm brand exchange]
    
    ConfirmExchange --> ProcessExchange[Process exchange]
    ConfirmSpending --> ProcessSpending[Process spending]
    ConfirmBrandExchange --> ProcessBrandExchange[Process brand exchange]
    
    ProcessExchange --> UpdateBalances[Update currency balances]
    ProcessSpending --> UpdateBalances
    ProcessBrandExchange --> UpdateBalances
    
    UpdateBalances --> ShowTransactionResult[Show transaction result]
    ShowTransactionResult --> RefreshDashboard[Refresh currency dashboard]
```

## Brand Rating System Flow

```mermaid
flowchart TD
    RatingEntry[User enters Brand Rating] --> CheckAvailability[Check available surveys/ratings]
    
    CheckAvailability --> ShowOptions{Available options?}
    ShowOptions -->|Surveys| ShowSurveyList[Show survey campaigns]
    ShowOptions -->|Ratings| ShowRatingList[Show brand rating opportunities]
    ShowOptions -->|History| ShowRatingHistory[Show rating history]
    ShowOptions -->|None| ShowEmptyState[Show no available surveys]
    
    ShowSurveyList --> SurveySelection{User selects survey}
    SurveySelection --> CheckEligibility[Check survey eligibility]
    
    CheckEligibility --> EligibilityCheck{User eligible?}
    EligibilityCheck -->|No| ShowIneligible[Show ineligibility message]
    EligibilityCheck -->|Yes| ShowSurveyIntro[Show survey introduction]
    
    ShowIneligible --> ShowSurveyList
    ShowSurveyIntro --> IntroActions{User action}
    IntroActions -->|Start Survey| BeginSurvey[Begin survey questions]
    IntroActions -->|Cancel| ShowSurveyList
    
    BeginSurvey --> ShowQuestion[Show current question]
    ShowQuestion --> QuestionType{Question type?}
    
    QuestionType -->|Multiple Choice| ShowMultipleChoice[Show multiple choice options]
    QuestionType -->|Text Input| ShowTextInput[Show text input field]
    QuestionType -->|Rating Scale| ShowRatingScale[Show rating scale]
    QuestionType -->|Image Upload| ShowImageUpload[Show image upload]
    
    ShowMultipleChoice --> ValidateChoice{Valid choice?}
    ShowTextInput --> ValidateText{Valid text input?}
    ShowRatingScale --> ValidateRating{Valid rating?}
    ShowImageUpload --> ValidateImage{Valid image?}
    
    ValidateChoice -->|No| ShowChoiceError[Show choice error]
    ValidateChoice -->|Yes| NextQuestion[Move to next question]
    
    ValidateText -->|No| ShowTextError[Show text validation error]
    ValidateText -->|Yes| NextQuestion
    
    ValidateRating -->|No| ShowRatingError[Show rating error]
    ValidateRating -->|Yes| NextQuestion
    
    ValidateImage -->|No| ShowImageError[Show image error]
    ValidateImage -->|Yes| NextQuestion
    
    ShowChoiceError --> ShowMultipleChoice
    ShowTextError --> ShowTextInput
    ShowRatingError --> ShowRatingScale
    ShowImageError --> ShowImageUpload
    
    NextQuestion --> CheckProgress{More questions?}
    CheckProgress -->|Yes| ShowQuestion
    CheckProgress -->|No| ShowSurveyReview[Show survey review]
    
    ShowSurveyReview --> ReviewActions{User action}
    ReviewActions -->|Submit| SubmitSurvey[Submit survey responses]
    ReviewActions -->|Edit| EditResponses[Edit previous responses]
    ReviewActions -->|Cancel| CancelSurvey[Cancel survey]
    
    EditResponses --> ShowQuestion
    CancelSurvey --> ShowSurveyList
    
    SubmitSurvey --> ProcessSubmission[Process survey submission]
    ProcessSubmission --> SubmissionResult{Submission successful?}
    
    SubmissionResult -->|No| ShowSubmissionError[Show submission error]
    SubmissionResult -->|Yes| ShowThankYou[Show thank you message]
    
    ShowSubmissionError --> ShowSurveyReview
    ShowThankYou --> ShowReward[Show survey reward]
    ShowReward --> UpdateProgress[Update completion status]
    UpdateProgress --> RefreshSurveyList[Refresh available surveys]
    
    ShowRatingList --> RatingSelection{User selects brand to rate}
    RatingSelection --> LoadBrandInfo[Load brand information]
    LoadBrandInfo --> ShowRatingForm[Show brand rating form]
    
    ShowRatingForm --> RatingComponents{Rating components}
    RatingComponents --> OverallRating[Overall satisfaction rating]
    RatingComponents --> CategoryRatings[Category-specific ratings]
    RatingComponents --> CommentInput[Comment/feedback input]
    RatingComponents --> TagSuggestions[Suggestion tags]
    
    OverallRating --> ValidateOverall{Valid overall rating?}
    CategoryRatings --> ValidateCategories{Valid category ratings?}
    CommentInput --> ValidateComment{Valid comment?}
    TagSuggestions --> SelectTags[Select relevant tags]
    
    ValidateOverall -->|No| ShowOverallError[Show overall rating error]
    ValidateOverall -->|Yes| CheckRatingComplete[Check rating completeness]
    
    ValidateCategories -->|No| ShowCategoryError[Show category error]
    ValidateCategories -->|Yes| CheckRatingComplete
    
    ValidateComment -->|No| ShowCommentError[Show comment error]
    ValidateComment -->|Yes| CheckRatingComplete
    
    SelectTags --> CheckRatingComplete
    
    CheckRatingComplete --> MinimumRequirements{Minimum requirements met?}
    MinimumRequirements -->|No| ShowRequirementError[Show requirement error]
    MinimumRequirements -->|Yes| SubmitRating[Submit brand rating]
    
    SubmitRating --> ProcessRating[Process rating submission]
    ProcessRating --> RatingResult{Rating successful?}
    
    RatingResult -->|No| ShowRatingError[Show rating error]
    RatingResult -->|Yes| ShowRatingSuccess[Show rating success]
    
    ShowRatingError --> ShowRatingForm
    ShowRatingSuccess --> ShowRatingReward[Show rating reward]
    ShowRatingReward --> UpdateRatingHistory[Update rating history]
```

## API Endpoints and Business Logic

### Currency System APIs

#### 1. Get User Balances
- **Endpoint**: `GET /currencies/balances`
- **Response**: All currency balances for user
- **Purpose**: Display currency dashboard

#### 2. Get Currency Details
- **Endpoint**: `GET /currencies/{currencyCode}`
- **Response**: Detailed currency information and balance
- **Purpose**: Show specific currency details

#### 3. Get Point History
- **Endpoint**: `GET /currencies/history`
- **Parameters**:
  ```typescript
  {
    currencyCode?: string,
    startDate?: string,
    endDate?: string,
    transactionType?: 'EARN' | 'SPEND' | 'TRANSFER' | 'EXCHANGE',
    page: number,
    size: number
  }
  ```
- **Response**: Paginated transaction history

#### 4. Exchange Currency
- **Endpoint**: `POST /currencies/exchange`
- **Payload**:
  ```typescript
  {
    fromCurrency: string,
    toCurrency: string,
    amount: number,
    exchangeRate?: number
  }
  ```

### Brand Rating System APIs

#### 1. Get Available Surveys
- **Endpoint**: `GET /brand-rating/surveys/available`
- **Response**: List of available survey campaigns
- **Purpose**: Show survey opportunities

#### 2. Get Survey Details
- **Endpoint**: `GET /brand-rating/surveys/{surveyId}`
- **Response**: Survey questions, structure, and rewards
- **Purpose**: Load survey content

#### 3. Submit Survey Response
- **Endpoint**: `POST /brand-rating/surveys/{surveyId}/submit`
- **Payload**:
  ```typescript
  {
    responses: {
      questionId: string,
      answer: any,
      timeSpent: number
    }[],
    deviceInfo?: any,
    location?: { lat: number, lng: number }
  }
  ```

#### 4. Get Brand Rating Opportunities
- **Endpoint**: `GET /brand-rating/brands/available`
- **Response**: Brands available for rating
- **Purpose**: Show rating opportunities

#### 5. Submit Brand Rating
- **Endpoint**: `POST /brand-rating/brands/{brandCode}/rate`
- **Payload**:
  ```typescript
  {
    overallRating: number, // 1-5 scale
    categoryRatings: {
      category: string,
      rating: number
    }[],
    comment?: string,
    tags?: string[],
    anonymous: boolean
  }
  ```

#### 6. Get Rating History
- **Endpoint**: `GET /brand-rating/history`
- **Parameters**: `{ status?: 'COMPLETE' | 'INCOMPLETE', page: number, size: number }`
- **Response**: User's rating and survey history

## Business Rules and Validations

### Currency System Rules

#### Balance Management
```typescript
interface CurrencyBalance {
  currencyCode: string,
  currencyName: string,
  balance: number,
  pendingBalance: number,    // Points being processed
  lockedBalance: number,     // Points locked for transactions
  expiryDate?: string,       // When points expire
  lastUpdated: string
}
```

#### Exchange Rules
```typescript
interface ExchangeRules {
  minExchangeAmount: number,
  maxExchangeAmount: number,
  exchangeRate: number,
  exchangeFee: number,
  dailyExchangeLimit: number,
  allowedExchangePairs: {
    from: string,
    to: string,
    rate: number
  }[]
}
```

#### Transaction Limits
- **Daily Spending Limit**: Maximum points spendable per day
- **Monthly Transfer Limit**: Maximum transferable amount per month
- **Minimum Balance**: Required minimum balance to maintain
- **Expiry Rules**: Point expiration policies

### Brand Rating System Rules

#### Survey Eligibility
```typescript
interface SurveyEligibility {
  ageRange: { min: number, max: number },
  gender?: 'male' | 'female' | 'any',
  location?: string[],
  previousParticipation: boolean,
  brandAffinity?: string[],
  completionRate?: number // Minimum completion rate required
}
```

#### Rating Validation
```typescript
interface RatingRules {
  minimumRatings: number,        // Minimum category ratings required
  commentMinLength: number,      // Minimum comment length
  commentMaxLength: number,      // Maximum comment length
  allowedRatingScale: number[],  // Valid rating values (e.g., 1-5)
  mandatoryFields: string[],     // Required rating fields
  cooldownPeriod: number        // Hours between ratings for same brand
}
```

#### Survey Completion Rules
- **Minimum Questions**: Must answer required questions
- **Time Limits**: Maximum time to complete survey
- **Progress Saving**: Allow saving partial progress
- **Quality Checks**: Detect inconsistent or rapid responses

## Reward and Incentive Systems

### Currency Earning
```typescript
interface EarningRewards {
  surveyCompletion: {
    baseReward: number,
    bonusReward: number,     // For high-quality responses
    currencyType: string
  },
  brandRating: {
    pointsPerRating: number,
    qualityBonus: number,    // For detailed ratings
    firstRatingBonus: number // Extra points for first rating
  }
}
```

### Quality Incentives
- **Detailed Comments**: Extra points for comprehensive feedback
- **Photo Uploads**: Bonus for visual feedback
- **Consistency Checks**: Rewards for consistent rating patterns
- **Speed Bonuses**: Points for quick but quality responses

## Data Collection and Privacy

### Survey Data Handling
```typescript
interface SurveyData {
  anonymizationLevel: 'FULL' | 'PARTIAL' | 'NONE',
  dataRetentionPeriod: number,    // Days to retain data
  sharingPermissions: {
    withBrand: boolean,
    forAnalytics: boolean,
    forMarketing: boolean
  },
  userConsent: {
    dataCollection: boolean,
    dataSharing: boolean,
    marketing: boolean
  }
}
```

### Privacy Controls
- **Anonymization Options**: Users can choose anonymity level
- **Data Opt-out**: Users can request data deletion
- **Sharing Controls**: Granular control over data sharing
- **Consent Management**: Clear consent for different data uses

## Analytics and Insights

### Currency Usage Metrics
- **Balance Trends**: How currency balances change over time
- **Exchange Patterns**: Popular currency exchange pairs
- **Spending Categories**: Where users spend their points
- **Earning Sources**: How users primarily earn points

### Rating System Metrics
- **Completion Rates**: Survey completion statistics
- **Rating Distribution**: How users rate different brands
- **Quality Scores**: Assessment of response quality
- **Engagement Patterns**: When and how often users participate

### Business Intelligence
```typescript
interface AnalyticsData {
  userSegmentation: {
    highValueUsers: number,
    activeRaters: number,
    surveyCompletors: number
  },
  brandInsights: {
    averageRatings: Record<string, number>,
    ratingTrends: any[],
    feedbackCategories: any[]
  },
  currencyMetrics: {
    totalBalance: number,
    dailyTransactions: number,
    exchangeVolume: number
  }
}
```

## Error Handling and Validation

### Currency System Errors
- **Insufficient Balance**: Clear messaging and top-up options
- **Exchange Rate Changes**: Handle rate fluctuations during transactions
- **Transaction Failures**: Rollback mechanisms and retry logic
- **System Maintenance**: Graceful degradation during maintenance

### Rating System Errors
- **Survey Timeouts**: Save progress and allow resumption
- **Validation Errors**: Specific field-level error messages
- **Submission Failures**: Retry mechanisms and data preservation
- **Network Issues**: Offline capability and sync when reconnected

## Testing Considerations

### Currency System Testing
1. **Balance Accuracy**: Verify balance calculations are correct
2. **Exchange Rate Testing**: Test rate changes and edge cases
3. **Transaction Integrity**: Ensure no double-spending or loss
4. **Performance Testing**: High-volume transaction handling

### Rating System Testing
1. **Survey Flow Testing**: Complete survey flows with various responses
2. **Validation Testing**: Test all validation rules and error cases
3. **Data Integrity**: Ensure response data is accurately stored
4. **Reward Processing**: Verify rewards are correctly awarded

### Integration Testing
1. **Cross-System**: Currency rewards for rating activities
2. **API Reliability**: All API endpoints under various conditions
3. **Data Consistency**: Consistency across different data sources
4. **User Experience**: End-to-end user journey testing

---

*This documentation covers the currency management and brand rating systems that enable multi-currency point management and valuable user feedback collection in the TapTap mobile application.*