# TapTap Mobile Business Rules & Flow Documentation

## Overview
This documentation repository contains comprehensive business rules, flow diagrams, and system architecture analysis for all major components of the TapTap mobile application. The analysis is based on the legacy mobile codebase (`taptap-mobile/`) and serves as a reference for understanding existing business logic, data flows, and system interactions.

## Purpose
- **Migration Reference**: Guide for migrating features from legacy mobile to current web/Zalo monorepo
- **Business Logic Documentation**: Preserve critical business rules and conditional logic
- **System Understanding**: Comprehensive overview of all mobile application systems
- **API Documentation**: Complete API endpoint specifications and data structures
- **Flow Visualization**: Mermaid diagrams showing user journeys and system interactions

## Documentation Structure

### Core Business Systems

#### 1. [Reward and Voucher Management](./MyRewardDetail.md)
- **MyRewardDetail**: Voucher detail management with QR codes, transfers, and status tracking
- **RewardDetail**: Reward claiming system with flash sales and point validation
- **API Endpoints**: 5 comprehensive endpoints for voucher operations
- **Business Rules**: Voucher status workflows, transfer eligibility, QR generation
- **Key Features**: Auto-redeem, peer-to-peer transfers, expiry management

#### 2. [Challenge and Entertainment](./ChallengeDetail.md) 
- **ChallengeDetail**: Gamified challenge system with milestone progression
- **API Endpoints**: Challenge participation and gift claiming
- **Business Rules**: Milestone validation, gift distribution, progress tracking
- **Key Features**: Animated interactions, social sharing, achievement systems

#### 3. [Messaging and Communication](./InboxDetail.md)
- **InboxDetail**: Template-based message rendering with 7 message types
- **InboxList**: Two-tab inbox management with mark-all-as-read functionality
- **API Endpoints**: Message management and read status tracking
- **Business Rules**: Template processing, CTA button systems, automatic read tracking
- **Key Features**: Dynamic content rendering, batch operations, notification integration

#### 4. [Referral and Social Systems](./ReferralChallenge.md)
- **ReferralChallenge**: Complex 7-screen referral system with Firebase integration
- **API Endpoints**: Referral tracking, friend management, reward distribution
- **Business Rules**: Anti-fraud detection, milestone achievements, social sharing
- **Key Features**: Real-time leaderboards, contact integration, dynamic rewards

### Earning and Engagement Systems

#### 5. [Points and Daily Engagement](./EarningSystems.md)
- **Earn System**: Points earning through various methods and merchant interactions
- **Daily Check-in**: Calendar-based engagement with streak tracking
- **Favorite Brands**: Personalization system affecting recommendations
- **API Endpoints**: Earning methods, check-in processing, brand preferences
- **Business Rules**: Point calculations, streak bonuses, personalization logic
- **Key Features**: Multi-method earning, streak protection, brand-based recommendations

#### 6. [Authentication and Transactions](./AuthAndTransactionSystems.md)
- **Authentication**: Phone-based registration with OTP validation
- **QR Scanning**: Multi-purpose QR processing for receipts, gifts, payments
- **Voucher Transfer**: Peer-to-peer voucher sharing with contact management
- **API Endpoints**: Auth flows, QR processing, transfer operations
- **Business Rules**: Security patterns, QR validation, transfer limits
- **Key Features**: Multi-factor authentication, fraud detection, contact integration

#### 7. [Currency and Rating Systems](./CurrencyAndRatingSystems.md)
- **Currency Management**: Multi-currency point system beyond VUI points
- **Brand Rating**: Survey and feedback collection with rewards
- **API Endpoints**: Currency operations, survey management, rating submission
- **Business Rules**: Exchange rates, survey eligibility, rating validation
- **Key Features**: Multi-currency support, survey campaigns, quality incentives

### Extended Application Features

#### 8. [Additional Core Systems](./AdditionalSystems.md)
- **Account Management**: Profile management, tier systems, customer support
- **Home Dashboard**: Dynamic content sections with personalized displays
- **OCR Bill Scanning**: Receipt scanning for point earning through image recognition
- **Avatar System**: User personalization and customization
- **Burn VUI System**: Points redemption and reward claiming
- **API Endpoints**: Profile management, content delivery, OCR processing
- **Business Rules**: Profile validation, content personalization, scanning limits
- **Key Features**: Tier progression, dynamic sections, image processing

#### 9. [Merchant and Search](./MerchantAndSearchSystems.md)
- **Merchant Management**: Comprehensive merchant details with tier-based rewards
- **Search System**: Universal search across merchants, rewards, and content
- **API Endpoints**: Merchant data, tier information, search operations
- **Business Rules**: Tier progression, search relevance, result filtering
- **Key Features**: Multi-tab interfaces, advanced filtering, location services

#### 10. [Affiliate and Supporting Systems](./AffiliateAndSupportingSystems.md)
- **Affiliate Marketing**: Third-party partnerships with commission tracking
- **E-commerce**: Direct shopping with cart and checkout flows
- **Online Orders**: Order history and tracking management
- **Entertainment Games**: Gamification features and branded games
- **API Endpoints**: Affiliate tracking, e-commerce operations, order management
- **Business Rules**: Commission calculations, inventory management, order workflows
- **Key Features**: External partnerships, shopping experiences, order tracking

## System Architecture Overview

### Key Architectural Patterns
1. **Container-Presenter Pattern**: Separation of business logic and UI components
2. **Redux Integration**: Centralized state management with slices and selectors
3. **Custom Hooks**: Reusable data fetching and state management logic
4. **Template-Based Rendering**: Dynamic content rendering based on configurations
5. **Firebase Integration**: Real-time data synchronization and analytics
6. **Multi-platform Support**: Shared business logic across platforms

### Common Technical Features
- **React Native 0.68+** with TypeScript 4.8+
- **Redux Toolkit** for state management
- **React Navigation 6** for routing
- **Firebase** for real-time features and analytics
- **REST API** integration with comprehensive error handling
- **Biometric authentication** and security features
- **Push notifications** and background processing
- **Offline capabilities** with data synchronization

## API Architecture Summary

### Response Structure Pattern (2025)
All APIs follow a consistent response format:
```json
{
  "status": {
    "message": "Success",
    "code": 200,
    "success": true
  },
  "data": {
    // Actual API data
  },
  "meta": {
    "currentPage": 1,
    "pageSize": 10,
    "totalPages": 5,
    "totalRows": 50
  }
}
```

### Common API Patterns
- **Pagination**: Consistent page/size parameters across list endpoints
- **Filtering**: Standardized filter parameters for data queries
- **Error Handling**: Uniform error response structure with actionable messages
- **Authentication**: JWT-based authentication with refresh token support
- **Rate Limiting**: API throttling to prevent abuse
- **Caching**: Strategic caching for frequently accessed data

## Business Rules Categories

### Data Validation Rules
- **User Input Validation**: Phone numbers, emails, passwords, personal information
- **Business Logic Validation**: Point calculations, tier requirements, transfer limits
- **Security Validation**: Authentication tokens, permission checks, fraud detection
- **Data Integrity**: Cross-system data consistency and referential integrity

### Workflow Rules
- **State Transitions**: Valid state changes for vouchers, orders, challenges
- **Conditional Logic**: Feature availability based on user attributes
- **Time-Based Rules**: Expiry handling, campaign periods, daily limits
- **Geographic Rules**: Location-based features and restrictions

### Personalization Rules  
- **Content Personalization**: Recommendations based on user preferences
- **Tier-Based Features**: Progressive feature unlocking based on user tier
- **Behavioral Targeting**: Content and offers based on user behavior
- **A/B Testing**: Feature flag management and experiment tracking

## Migration Guidelines

### Priority Systems for Migration
1. **High Priority**: Authentication, Rewards, Vouchers, Points (core user value)
2. **Medium Priority**: Challenges, Inbox, Search, Merchant Detail (engagement)
3. **Low Priority**: Affiliate, E-commerce, OCR, Games (extended features)

### Technical Migration Considerations
- **API Compatibility**: Ensure new implementations maintain API contract compatibility
- **State Management**: Adapt Redux patterns to current state management solutions
- **Component Architecture**: Transform container-presenter patterns to current component patterns
- **Business Logic**: Preserve critical business rules and conditional logic
- **Data Models**: Maintain data structure compatibility where possible

### Business Logic Preservation
- **Validation Rules**: Maintain all validation logic for data integrity
- **Calculation Logic**: Preserve point calculations, tier progression, and reward logic  
- **Workflow Logic**: Keep state transition rules and approval workflows
- **Security Patterns**: Maintain authentication and authorization patterns

## Usage Guide

### For Developers
1. **Feature Implementation**: Reference relevant documentation before implementing new features
2. **API Integration**: Use documented endpoints and data structures as specifications
3. **Business Logic**: Implement documented business rules and validation logic
4. **Flow Understanding**: Use Mermaid diagrams to understand complete user journeys

### For Product Managers
1. **Feature Requirements**: Use documentation to understand existing feature scope
2. **Business Rules**: Reference documented rules when defining new requirements
3. **User Flows**: Use flow diagrams to understand user experience patterns
4. **System Dependencies**: Understand how systems interconnect and affect each other

### for QA Engineers
1. **Test Scenarios**: Use business rules to create comprehensive test cases
2. **Edge Cases**: Reference validation rules for boundary testing
3. **Integration Testing**: Use system flows for end-to-end test scenarios
4. **Data Validation**: Use API specifications for data integrity testing

## Maintenance and Updates

### Documentation Updates
- **Business Rule Changes**: Update documentation when business logic changes
- **API Changes**: Keep API specifications current with backend changes  
- **Flow Updates**: Update Mermaid diagrams when user flows change
- **Feature Additions**: Document new features following established patterns

### Validation Process
- **Code Review**: Ensure implementations match documented business rules
- **API Testing**: Validate API endpoints against documented specifications
- **User Testing**: Verify user flows match documented diagrams
- **Business Validation**: Confirm business rules align with product requirements

---

**Last Updated**: August 2025  
**Version**: 1.0  
**Total Systems Documented**: 10 major systems with 50+ subsystems  
**Total API Endpoints**: 100+ documented endpoints  
**Flow Diagrams**: 25+ comprehensive Mermaid diagrams  

*This documentation represents a comprehensive analysis of the TapTap mobile application's business logic and serves as the definitive reference for understanding system behavior, data flows, and business rules across all application features.*