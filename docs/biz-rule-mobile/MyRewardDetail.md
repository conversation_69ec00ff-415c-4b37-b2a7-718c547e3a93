# MyRewardDetail Business Rules & Flow Documentation

## Overview
The MyRewardDetail screen is the comprehensive view for individual reward/voucher details in the TapTap mobile application. It handles various voucher states, ownership scenarios, and special features like auto-redeem, 7-Eleven linking, and voucher transfers.

## Component Location
- **File**: `taptap-mobile/src/scenes/myrewardV3/screens/MyRewardDetail/index.tsx`
- **Hook**: `taptap-mobile/src/scenes/myrewardV3/helpers/useFetchMyRewardDetail.ts`
- **API**: `taptap-mobile/src/scenes/myrewardV3/redux/api.ts`

## Core Data Types

### EVoucherStatus Enum
```typescript
enum EVoucherStatus {
  DONE = 3,      // Redeemed
  ACTIVE = 1,    // Active/usable
  EXPIRED = 4,   // Expired
}
```

### OwnershipStatus Enum
```typescript
enum OwnershipStatus {
  OWNED = 'OWNED',           // User owns the voucher
  TRANSFERRED = 'TRANSFERRED', // User gave voucher away
  RECEIVED = 'RECEIVED',      // User received voucher from someone
}
```

### ESurveyType Enum
```typescript
enum ESurveyType {
  MERCHANDISE = 'merch',              // Merchandise survey
  FORM_ACCESS_TRADE = 'formAccesstrade' // Access trade form
}
```

## Main Flow Diagram

```mermaid
flowchart TD
    Start([User enters MyRewardDetail]) --> LoadDetail[Load reward detail via API]
    
    LoadDetail --> CheckLoading{Loading?}
    CheckLoading -->|Yes| ShowLoading[Show loading screen with back button]
    CheckLoading -->|No| CheckDetail{Detail exists?}
    
    CheckDetail -->|No| ShowError[Show error screen with back button]
    CheckDetail -->|Yes| CheckMerchant{Is merchant = TAPTAP?}
    
    CheckMerchant -->|No| FetchMerchant[Fetch merchant info via API]
    CheckMerchant -->|Yes| SkipMerchant[Skip merchant fetch]
    
    FetchMerchant --> CheckTransfer{allowTransfer && status = ACTIVE?}
    SkipMerchant --> CheckTransfer
    
    CheckTransfer -->|Yes| ShowBottomPanel[Show transfer bottom panel]
    CheckTransfer -->|No| RenderMain[Render main content]
    
    ShowBottomPanel --> RenderMain
    RenderMain --> RenderHeader[Render header with navigation & member code]
    RenderHeader --> RenderDetailCard[Render detail card with voucher info]
    RenderDetailCard --> RenderStoreNearby[Render nearby stores]
    RenderStoreNearby --> CheckVendorInfo{source = THIRD_PARTY?}
    
    CheckVendorInfo -->|Yes| ShowVendorInfo[Show vendor information section]
    CheckVendorInfo -->|No| RenderMarkUsed
    ShowVendorInfo --> RenderMarkUsed[Render mark as used section]
    
    RenderMarkUsed --> RenderUsageGuide[Render usage guide from T&C]
    RenderUsageGuide --> CheckMerchantInfo{Has merchant info?}
    
    CheckMerchantInfo -->|Yes| ShowMerchantContact[Show merchant contact info]
    CheckMerchantInfo -->|No| CheckImageGallery{Has images?}
    
    ShowMerchantContact --> CheckImageGallery
    CheckImageGallery -->|Yes| ShowImageGallery[Show image gallery]
    CheckImageGallery -->|No| CheckOwnership{Check ownership status}
    
    ShowImageGallery --> CheckOwnership
    CheckOwnership --> CheckTransferInfo{ownershipStatus != OWNED?}
    
    CheckTransferInfo -->|Yes| ShowTransferInfo[Show transfer information section]
    CheckTransferInfo -->|No| CheckAllowTransfer{allowTransfer?}
    
    ShowTransferInfo --> CheckAllowTransfer
    CheckAllowTransfer -->|Yes| ShowTransferButton[Show transfer button]
    CheckAllowTransfer -->|No| End([End])
    
    ShowTransferButton --> End
```

## Middle Section Conditional Logic

The middle section of the detail card renders different components based on voucher properties:

```mermaid
flowchart TD
    MiddleSection[Determine middle section content] --> CheckAutoRedeem{autoRedeem && activeAutoRedeemDate?}
    
    CheckAutoRedeem -->|Yes| SkipTimer[Skip timer section]
    CheckAutoRedeem -->|No| ShowTimer[Show SectionTimerReward]
    
    SkipTimer --> CheckBottomSection[Determine bottom section]
    ShowTimer --> CheckBottomSection
    
    CheckBottomSection --> CheckSurvey{posIdentifier type?}
    CheckSurvey -->|MERCHANDISE| ShowMerchSurvey[Show SectionSurveyForm - Merchandise]
    CheckSurvey -->|FORM_ACCESS_TRADE| ShowFormSurvey[Show SectionSurveyForm - Access Trade]
    CheckSurvey -->|Other| Check711{is 7-Eleven only && not linked?}
    
    ShowMerchSurvey --> SurveyFlow[Survey form flow]
    ShowFormSurvey --> SurveyFlow
    
    Check711 -->|Yes| Show711Link[Show SectionLink711]
    Check711 -->|No| CheckAutoRedeemSection{autoRedeem?}
    
    Show711Link --> End711[End]
    CheckAutoRedeemSection -->|Yes| ShowAutoRedeem[Show SectionAutoRedeem]
    CheckAutoRedeemSection -->|No| ShowNothing[Show nothing]
    
    ShowAutoRedeem --> AutoRedeemFlow[Auto redeem flow]
    ShowNothing --> EndNothing[End]
    
    SurveyFlow --> EndSurvey[End survey]
    AutoRedeemFlow --> EndAuto[End auto redeem]
```

## API Endpoints and Variables

### Primary APIs

#### 1. Get Reward Detail
- **Endpoint**: `GET /voucher/detail/{codeId}`
- **Parameters**:
  - `codeId: string` - Voucher code identifier
  - `source?: SourceRewardType` - Source of the reward
- **Response**: `MyRewardType` object
- **Key Fields**:
  ```typescript
  {
    merchantCode: string,
    merchantName: string,
    merchantLogo: string,
    name: string,
    code: string,
    tnc: string,
    startTime: string,
    endTime: string,
    redeemBrands: string[],
    posIdentifier: string,
    status: EVoucherStatus,
    markAsUsed: boolean,
    enableMarkAsUsed: boolean,
    autoRedeem: boolean,
    timeRedeemAuto: number,
    activeAutoRedeemDate: string,
    executeAutoRedeemDate: string,
    ownershipStatus: OwnershipStatus,
    allowTransfer: boolean,
    settingRedeemData: string
  }
  ```

#### 2. Get Merchant Info
- **Endpoint**: `GET /merchant/{merchantCode}`
- **Response**: `MerchantType` with images array

#### 3. Mark As Used
- **Endpoint**: `PUT /voucher/markAsUsed/{codeId}`
- **Payload**: `{ markAsUsed: boolean }`
- **Behavior**: Optimistic update with rollback on failure

#### 4. Auto Redeem
- **Endpoint**: `PUT /auto-redeem/{codeId}`
- **Purpose**: Manually trigger auto-redeem before scheduled time

#### 5. Survey Form Link
- **Endpoint**: `GET /get-link-credential`
- **Parameters**:
  ```typescript
  {
    gameType: 'merchandise',
    userId: string,
    voucherCode: string,
    mobile: string
  }
  ```

## Conditional Rendering Rules

### 1. Mark As Used Section
**Shows when**:
- `enableMarkAsUsed = true`
- `status !== DONE`
- `status !== EXPIRED`
- Current time is before `endTime`
- `ownershipStatus !== TRANSFERRED`

**Behavior**:
- Toggle with confirmation dialog
- Optimistic UI update
- Rollback on API failure
- Tracking events for analytics

### 2. 7-Eleven Link Section (SectionLink711)
**Shows when**:
- `redeemBrands.length === 1`
- `redeemBrands[0] === "7ELEVEN"`
- User has not linked 7-Eleven account (`qrCode711.length === 0`)

**Behavior**:
- Shows blurred barcode image
- Opens linking bottom sheet modal
- Fetches user consent after linking

### 3. Auto Redeem Section (SectionAutoRedeem)
**Shows when**:
- `autoRedeem = true`

**Two sub-components**:
- **SectionCountDownReward**: When `executeAutoRedeemDate` exists (countdown active)
- **SectionWarningUseVoucher**: When no `executeAutoRedeemDate` (can activate)

### 4. Survey Form Section (SectionSurveyForm)
**Shows when**:
- `posIdentifier` contains survey type (`merch` or `formAccesstrade`)

**States**:
- **Not Submitted**: Shows form button, can fill info
- **Submitted**: Shows checkmark, "view info" button for merchandise
- **Expired**: Form button disabled

### 5. Voucher Transfer Section
**Shows when**:
- `ownershipStatus !== OWNED`

**Display Info**:
- **RECEIVED**: Shows giver information
- **TRANSFERRED**: Shows receiver information

### 6. Transfer Bottom Panel
**Shows when**:
- `allowTransfer = true`
- `status = ACTIVE`

**Transfer Limits**:
- Checks `remainTransferDailyPerUser`
- Disables if limit reached
- Shows warning message

## Special Business Logic

### 7-Eleven Integration
```mermaid
flowchart LR
    Check711[Check if 7-Eleven voucher] --> IsLinked{User linked account?}
    IsLinked -->|No| ShowLink[Show link section]
    IsLinked -->|Yes| ShowCode[Show voucher code normally]
    
    ShowLink --> UserLinks[User completes linking]
    UserLinks --> FetchConsent[Fetch user consent]
    FetchConsent --> UpdateUI[Update UI to show code]
```

### Auto Redeem Flow
```mermaid
flowchart TD
    AutoRedeemVoucher[Auto redeem voucher] --> HasExecuteDate{Has executeAutoRedeemDate?}
    
    HasExecuteDate -->|Yes| ShowCountdown[Show countdown timer]
    HasExecuteDate -->|No| ShowActivate[Show activation option]
    
    ShowCountdown --> CountdownComplete{Countdown reaches 0?}
    CountdownComplete -->|Yes| AutoExecute[Automatically redeem]
    
    ShowActivate --> UserActivates[User manually activates]
    UserActivates --> StartCountdown[Start countdown]
    StartCountdown --> ShowCountdown
    
    AutoExecute --> UpdateStatus[Update voucher status to DONE]
    UpdateStatus --> RefreshData[Refresh reward data]
```

### Survey Form Integration
```mermaid
flowchart TD
    SurveyForm[Survey form voucher] --> CheckType{Survey type?}
    
    CheckType -->|MERCHANDISE| GetLinkAPI[Call get link API]
    CheckType -->|FORM_ACCESS_TRADE| UseDirectLink[Use provided link with UTM params]
    
    GetLinkAPI --> OpenBrowser[Open in-app browser]
    UseDirectLink --> AddUTM[Add UTM parameters]
    AddUTM --> OpenBrowser
    
    OpenBrowser --> UserCompletes[User completes form]
    UserCompletes --> BrowserClose[Browser closes]
    BrowserClose --> RefreshData[Refresh reward data]
    RefreshData --> UpdateSubmittedState[Update to submitted state]
```

### Mark As Used Flow
```mermaid
flowchart TD
    MarkAsUsed[User toggles mark as used] --> ShowDialog[Show confirmation dialog]
    ShowDialog --> UserConfirms{User confirms?}
    
    UserConfirms -->|Yes| OptimisticUpdate[Update UI immediately]
    UserConfirms -->|No| CancelAction[Cancel action]
    
    OptimisticUpdate --> APICall[Call mark as used API]
    APICall --> APISuccess{API success?}
    
    APISuccess -->|Yes| KeepUpdate[Keep UI update]
    APISuccess -->|No| RollbackUpdate[Rollback UI]
    
    RollbackUpdate --> ShowError[Show error toast]
    KeepUpdate --> UpdateCount[Update voucher count]
```

## Error Handling

### Loading States
- Shows loading indicator during initial fetch
- Shows loading on mark as used toggle
- Shows loading on survey form actions

### Error States
- No detail found: Shows error screen with retry option
- API failures: Shows toast messages with specific error text
- Network issues: Handled by axios with proper error responses

### Optimistic UI Updates
- Mark as used immediately updates UI, rolls back on failure
- Voucher transfer updates immediately reflect in UI
- Count updates happen after successful API calls

## Analytics/Tracking Integration

The component includes comprehensive tracking via `TrackingManagerV3`:

### Key Events
- `mrDetailBarcode` - Member code button press
- `mrDetailImageViewAll` - View all images
- `mrDetailImageThumbnail` - View single image
- `mrDetailFillForm` - Survey form interaction
- `mrDetailMarkAsUsedTurnOn/Off` - Mark as used actions
- `mrDetailLinkAccount` - 7-Eleven linking
- `mrDetailDialogView/Btn` - Dialog interactions

### Tracking Parameters
```typescript
const paramsTracking = {
  merchant_code: string,
  reward_name: string,
  // Additional context-specific params
};
```

## Testing Considerations

### Key Test Scenarios
1. **Loading states** - Initial load, error handling
2. **Status variations** - ACTIVE, DONE, EXPIRED vouchers
3. **Ownership states** - OWNED, TRANSFERRED, RECEIVED
4. **7-Eleven integration** - Linked/unlinked scenarios
5. **Auto redeem** - With/without execute date
6. **Survey forms** - Both types, submitted/unsubmitted
7. **Transfer functionality** - Limits, permissions
8. **Mark as used** - Toggle, confirmation, rollback
9. **Image gallery** - With/without images
10. **Merchant info** - TapTap vs external merchants

### Edge Cases
- Expired vouchers with mark as used enabled
- Transfer limits reached
- Auto redeem past end time
- Survey forms with invalid links
- 7-Eleven vouchers with linking failures
- Network failures during critical actions

---

*This documentation covers the complete business logic and flow of the MyRewardDetail component as implemented in the TapTap mobile application.*