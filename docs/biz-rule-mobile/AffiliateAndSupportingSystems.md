# Affiliate & Supporting Systems Business Rules & Flow Documentation

## Overview
This document covers the Affiliate Marketing, E-commerce, Online Order Management, and other supporting systems in the TapTap mobile application. These systems enable third-party partnerships, online shopping experiences, and additional earning opportunities.

## Systems Architecture

### 1. Affiliate Marketing System (`/scenes/affiliateV2`, `/scenes/affliate`)
**Purpose**: Third-party affiliate partnerships and commission-based earning
**Key Components**:
- Affiliate product detail pages with earning information
- Commission rate displays and calculations  
- External link management and tracking
- Deal hot section and promotional content
- Partnership terms and conditions
- Earnings tracking and history

### 2. E-commerce System (`/scenes/ecommerce`)
**Purpose**: Direct e-commerce functionality and QSR (Quick Service Restaurant) integration
**Key Components**:
- E-commerce index and product browsing
- QSR-specific ordering interfaces
- Product catalog management
- Shopping cart and checkout flows
- Order tracking and management

### 3. Online Order Management (`/scenes/onlineOrder`)
**Purpose**: Order history and tracking for online purchases
**Key Components**:
- Online order history display
- Order detail views with status tracking
- Smart notification system
- Order analytics and insights
- Refund and support request handling

### 4. Entertainment Games (`/scenes/entertainment`)
**Purpose**: Gamification features and branded games
**Key Components**:
- Challenge participation and tracking
- Wheel games and spin rewards
- Web-based game integration
- Achievement systems and leaderboards
- Branded entertainment content

### 5. Supporting Systems
- **Avatar System** (`/scenes/avatar`): User personalization
- **Burn VUI System** (`/scenes/burn`): Point redemption management
- **Store System** (`/scenes/store`): Store location services
- **E-wallet Integration** (`/scenes/ewallet`): Payment processing

## Affiliate Marketing Flow

```mermaid
flowchart TD
    AffiliateEntry[User enters Affiliate section] --> LoadAffiliateOffers[Load affiliate offers]
    
    LoadAffiliateOffers --> DisplayOffersList[Display affiliate offers list]
    DisplayOffersList --> OfferSelection{User selects offer}
    
    OfferSelection -->|Select Offer| ShowAffiliateDetail[Show affiliate detail]
    OfferSelection -->|Browse More| LoadMoreOffers[Load more offers]
    OfferSelection -->|Filter| ShowOfferFilters[Show offer filters]
    
    ShowAffiliateDetail --> LoadPartnerInfo[Load partner information]
    LoadPartnerInfo --> DisplayAffiliateInfo[Display affiliate information]
    
    DisplayAffiliateInfo --> AffiliateComponents{Affiliate detail components}
    
    AffiliateComponents -->|Earning Info| ShowEarningDetails[Show earning calculation]
    AffiliateComponents -->|Product Info| ShowProductDetails[Show product information]
    AffiliateComponents -->|Terms| ShowPartnerTerms[Show partnership terms]
    AffiliateComponents -->|Reviews| ShowProductReviews[Show product reviews]
    
    ShowEarningDetails --> EarningCalculation[Display commission calculation]
    EarningCalculation --> ShowEarningExamples[Show earning examples]
    
    DisplayAffiliateInfo --> UserAction{User action}
    UserAction -->|Buy Now| InitiatePurchase[Initiate affiliate purchase]
    UserAction -->|Save Offer| SaveToFavorites[Save offer to favorites]
    UserAction -->|Share| ShareAffiliateOffer[Share affiliate offer]
    
    InitiatePurchase --> ValidateUser{User eligible?}
    ValidateUser -->|No| ShowEligibilityError[Show eligibility requirements]
    ValidateUser -->|Yes| RedirectToPartner[Redirect to partner site]
    
    ShowEligibilityError --> DisplayAffiliateInfo
    
    RedirectToPartner --> TrackClick[Track affiliate click]
    TrackClick --> OpenExternalBrowser[Open external browser]
    OpenExternalBrowser --> UserPurchases[User completes purchase on partner site]
    
    UserPurchases --> PartnerCallback[Partner sends purchase confirmation]
    PartnerCallback --> ValidatePurchase[Validate purchase details]
    ValidatePurchase --> CalculateCommission[Calculate affiliate commission]
    CalculateCommission --> AwardPoints[Award VUI points to user]
    AwardPoints --> UpdateEarningHistory[Update earning history]
    UpdateEarningHistory --> NotifyUser[Send earning notification]
    
    SaveToFavorites --> UpdateFavorites[Update user favorites list]
    ShareAffiliateOffer --> ShowSharingOptions[Show sharing options]
    ShowSharingOptions --> ExecuteShare[Execute share action]
```

## E-commerce and Online Order Flow

```mermaid
flowchart TD
    EcommerceEntry[User enters E-commerce] --> LoadEcommerceHome[Load e-commerce home]
    
    LoadEcommerceHome --> ShowEcommerceOptions[Show e-commerce options]
    ShowEcommerceOptions --> EcommerceType{E-commerce type}
    
    EcommerceType -->|General Store| ShowGeneralStore[Show general store]
    EcommerceType -->|QSR| ShowQSRInterface[Show QSR interface]
    
    ShowGeneralStore --> BrowseProducts[Browse product catalog]
    BrowseProducts --> ProductActions{Product actions}
    
    ProductActions -->|View Product| ShowProductDetail[Show product detail]
    ProductActions -->|Add to Cart| AddToCart[Add product to cart]
    ProductActions -->|Search| ExecuteProductSearch[Execute product search]
    ProductActions -->|Filter| ApplyProductFilters[Apply product filters]
    
    ShowProductDetail --> ProductDetailActions{Product detail actions}
    ProductDetailActions -->|Add to Cart| AddToCart
    ProductDetailActions -->|Buy Now| InitiateCheckout[Initiate direct checkout]
    ProductDetailActions -->|Share| ShareProduct[Share product]
    
    AddToCart --> UpdateCart[Update shopping cart]
    UpdateCart --> CartActions{Cart actions}
    
    CartActions -->|Continue Shopping| BrowseProducts
    CartActions -->|View Cart| ShowShoppingCart[Show shopping cart]
    CartActions -->|Checkout| InitiateCheckout
    
    ShowShoppingCart --> CartManagement{Cart management}
    CartManagement -->|Update Quantity| UpdateQuantity[Update item quantity]
    CartManagement -->|Remove Item| RemoveFromCart[Remove item from cart]
    CartManagement -->|Apply Coupon| ApplyCoupon[Apply discount coupon]
    CartManagement -->|Checkout| InitiateCheckout
    
    UpdateQuantity --> RecalculateTotal[Recalculate cart total]
    RemoveFromCart --> RecalculateTotal
    ApplyCoupon --> ValidateCoupon{Coupon valid?}
    
    ValidateCoupon -->|No| ShowCouponError[Show coupon error]
    ValidateCoupon -->|Yes| RecalculateTotal
    
    RecalculateTotal --> UpdateCart
    
    InitiateCheckout --> CheckoutFlow[Checkout process]
    CheckoutFlow --> CollectShippingInfo[Collect shipping information]
    CollectShippingInfo --> SelectPaymentMethod[Select payment method]
    SelectPaymentMethod --> ReviewOrder[Review order details]
    ReviewOrder --> ProcessPayment[Process payment]
    ProcessPayment --> PaymentResult{Payment successful?}
    
    PaymentResult -->|No| ShowPaymentError[Show payment error]
    PaymentResult -->|Yes| CreateOrder[Create order]
    
    ShowPaymentError --> SelectPaymentMethod
    
    CreateOrder --> GenerateOrderID[Generate order ID]
    GenerateOrderID --> SendOrderConfirmation[Send order confirmation]
    SendOrderConfirmation --> UpdateInventory[Update product inventory]
    UpdateInventory --> NotifyMerchant[Notify merchant of order]
    NotifyMerchant --> ShowOrderSuccess[Show order success]
    
    ShowOrderSuccess --> OfferTracking[Offer order tracking]
    OfferTracking --> OrderTrackingFlow[Order tracking flow]
    
    OrderTrackingFlow --> ShowOrderHistory[Show order history]
    ShowOrderHistory --> LoadOrderList[Load user's order list]
    LoadOrderList --> DisplayOrders[Display order list]
    
    DisplayOrders --> OrderSelection{User selects order}
    OrderSelection -->|View Details| ShowOrderDetail[Show order detail]
    OrderSelection -->|Track Order| ShowTrackingInfo[Show tracking information]
    OrderSelection -->|Reorder| InitiateReorder[Initiate reorder]
    OrderSelection -->|Support| ContactSupport[Contact customer support]
    
    ShowOrderDetail --> OrderDetailInfo[Show detailed order information]
    OrderDetailInfo --> OrderActions{Order actions available}
    
    OrderActions -->|Cancel Order| CancelOrder[Cancel order if eligible]
    OrderActions -->|Request Refund| RequestRefund[Request order refund]
    OrderActions -->|Leave Review| LeaveProductReview[Leave product review]
    OrderActions -->|Reorder| InitiateReorder
    
    CancelOrder --> ProcessCancellation[Process order cancellation]
    RequestRefund --> ProcessRefundRequest[Process refund request]
    InitiateReorder --> BrowseProducts
```

## API Endpoints and Business Logic

### Affiliate Marketing APIs

#### 1. Get Affiliate Offers
- **Endpoint**: `GET /affiliate/offers`
- **Parameters**: `{ page: number, size: number, category?: string }`
- **Response**: List of available affiliate partnerships
- **Purpose**: Display affiliate earning opportunities

#### 2. Get Affiliate Detail
- **Endpoint**: `GET /affiliate/offers/{offerId}`
- **Response**: Detailed affiliate information including earning rates
- **Purpose**: Show comprehensive affiliate offer details

#### 3. Track Affiliate Click
- **Endpoint**: `POST /affiliate/clicks`
- **Payload**: 
  ```typescript
  {
    offerId: string,
    userId: string,
    clickSource: string,
    timestamp: string
  }
  ```
- **Purpose**: Track user clicks to partner sites

#### 4. Process Affiliate Earning
- **Endpoint**: `POST /affiliate/earnings`
- **Payload**:
  ```typescript
  {
    offerId: string,
    userId: string,
    purchaseAmount: number,
    commissionRate: number,
    partnerOrderId: string,
    purchaseDate: string
  }
  ```

### E-commerce APIs

#### 1. Get Product Catalog
- **Endpoint**: `GET /ecommerce/products`
- **Parameters**: `{ category?: string, search?: string, page: number, size: number }`
- **Response**: Product listings with pricing and availability

#### 2. Manage Shopping Cart
- **Endpoint**: `POST /ecommerce/cart`
- **Payload**:
  ```typescript
  {
    action: 'add' | 'update' | 'remove',
    productId: string,
    quantity?: number,
    selectedOptions?: any
  }
  ```

#### 3. Process Order
- **Endpoint**: `POST /ecommerce/orders`
- **Payload**:
  ```typescript
  {
    items: {
      productId: string,
      quantity: number,
      unitPrice: number
    }[],
    shippingAddress: any,
    paymentMethod: string,
    couponCode?: string
  }
  ```

### Online Order APIs

#### 1. Get Order History
- **Endpoint**: `GET /orders/history`
- **Parameters**: `{ status?: string, startDate?: string, endDate?: string, page: number, size: number }`
- **Response**: User's order history with status information

#### 2. Get Order Detail
- **Endpoint**: `GET /orders/{orderId}`
- **Response**: Detailed order information and tracking status

#### 3. Cancel Order
- **Endpoint**: `POST /orders/{orderId}/cancel`
- **Purpose**: Cancel order if within cancellation window

## Business Rules and Validations

### Affiliate Marketing Rules

#### Commission Structure
```typescript
interface AffiliateRules {
  commissionTypes: {
    percentage: number,      // Percentage of purchase amount
    fixed: number,          // Fixed amount per purchase
    tiered: {               // Tiered commission structure
      minAmount: number,
      maxAmount: number,
      rate: number
    }[]
  },
  eligibilityRules: {
    minUserTier?: string,   // Minimum user tier required
    maxEarningsPerDay: number,
    maxEarningsPerMonth: number,
    excludedCategories: string[]
  },
  trackingRules: {
    cookieDuration: number,  // Days to track conversions
    attributionWindow: number,
    deduplicationLogic: string
  }
}
```

#### Earning Validation
- **Purchase Verification**: Validate purchases through partner callbacks
- **Fraud Detection**: Monitor for suspicious affiliate activity
- **Earning Limits**: Daily and monthly earning caps
- **Quality Requirements**: Minimum purchase amounts for earning

### E-commerce Rules

#### Product Management
```typescript
interface EcommerceRules {
  productRules: {
    minPrice: number,
    maxPrice: number,
    stockValidation: boolean,
    categoryRestrictions: string[]
  },
  cartRules: {
    maxItemsPerCart: number,
    maxTotalAmount: number,
    expirationTime: number    // Cart expiration in minutes
  },
  orderRules: {
    minOrderAmount: number,
    maxOrderAmount: number,
    cancellationWindow: number, // Hours after order
    refundWindow: number       // Days for refunds
  }
}
```

#### Payment and Shipping
- **Payment Validation**: Support multiple payment methods
- **Shipping Calculations**: Dynamic shipping cost calculations
- **Tax Handling**: Automatic tax calculations by location
- **Inventory Management**: Real-time stock validation

### Online Order Management Rules

#### Order Status Workflow
```typescript
interface OrderStatusRules {
  statusProgression: [
    'pending',
    'confirmed', 
    'processing',
    'shipped',
    'delivered',
    'completed'
  ],
  cancellationRules: {
    allowedStatuses: ['pending', 'confirmed'],
    timeLimit: number,        // Hours after order placement
    refundPolicy: 'full' | 'partial' | 'none'
  },
  refundRules: {
    eligibleStatuses: string[],
    timeLimit: number,        // Days after delivery
    refundMethods: string[],
    processingTime: number    // Business days for refund
  }
}
```

## User Experience Patterns

### Affiliate Engagement
1. **Clear Earning Display**: Prominent earning potential information
2. **Trust Indicators**: Partner credibility and review display
3. **Seamless Redirection**: Smooth transition to partner sites
4. **Earning Tracking**: Clear visibility of pending and earned commissions
5. **Personalized Offers**: Recommendations based on user interests

### E-commerce Experience
1. **Product Discovery**: Intuitive browsing and search capabilities
2. **Smart Recommendations**: AI-powered product suggestions
3. **Streamlined Checkout**: Minimal friction purchase process
4. **Multiple Payment Options**: Support for various payment methods
5. **Order Transparency**: Clear order status and tracking information

### Order Management
1. **Status Clarity**: Clear order status progression
2. **Proactive Updates**: Push notifications for status changes
3. **Easy Modifications**: Simple order changes when possible
4. **Support Access**: Quick access to customer support
5. **Reorder Convenience**: One-click reordering of previous purchases

## Analytics and Tracking

### Affiliate Performance Metrics
- **Click-through Rates**: Affiliate offer engagement rates
- **Conversion Rates**: Purchase completion rates from affiliate clicks
- **Average Order Value**: Value of purchases through affiliate links
- **Partner Performance**: Success rates by affiliate partner
- **User Earning Patterns**: How users engage with affiliate offers

### E-commerce Analytics
- **Product Performance**: Sales data by product and category
- **Cart Abandonment**: Analysis of checkout drop-off points
- **Payment Method Usage**: Preferred payment methods by users
- **Customer Lifetime Value**: Long-term value of e-commerce customers
- **Inventory Turnover**: Product inventory movement analysis

### Order Management Insights
```typescript
interface OrderAnalytics {
  fulfillmentMetrics: {
    avgProcessingTime: number,
    onTimeDeliveryRate: number,
    orderAccuracy: number
  },
  customerSatisfaction: {
    cancelationRate: number,
    refundRate: number,
    reorderRate: number,
    supportTicketVolume: number
  },
  operationalEfficiency: {
    peakOrderTimes: any[],
    resourceUtilization: number,
    costPerOrder: number
  }
}
```

## Error Handling and Recovery

### Affiliate System Errors
- **Partner Site Issues**: Fallback messaging and retry options
- **Tracking Failures**: Alternative attribution methods
- **Commission Disputes**: Clear resolution processes
- **Link Expiration**: Automatic link validation and updates

### E-commerce Error Handling
- **Payment Failures**: Multiple payment retry options
- **Inventory Issues**: Real-time stock validation and alternatives
- **Shipping Problems**: Alternative delivery options
- **System Downtime**: Graceful degradation and offline capabilities

### Order Management Errors
- **Order Processing Issues**: Clear error messaging and resolution
- **Delivery Problems**: Proactive communication and solutions
- **Customer Service**: Multiple support channels and escalation
- **System Integration**: Robust API error handling and fallbacks

---

*This documentation covers the affiliate marketing, e-commerce, and supporting systems that expand earning opportunities and enhance user engagement in the TapTap mobile application.*