# ReferralChallenge Business Rules & Flow Documentation

## Overview
The ReferralChallenge system is a comprehensive multi-screen referral program with milestone-based progression, real-time Firebase integration, social sharing capabilities, and anti-fraud detection. It encourages user engagement through friend invitations and reward mechanisms with sophisticated tracking and analytics.

## Component Architecture

### Main Screens
- **ReferralChallengeBoard**: Main dashboard with milestone progression
- **MilestoneDetail**: Detailed view of individual milestones
- **ReferralCode**: Referral code creation and management
- **ReferralCodeConfirm**: Code confirmation flow
- **SharingSocial**: Social media sharing interface
- **FriendList**: Friend invitation and progress tracking
- **ReferralChallengeIntro**: Onboarding and terms

### Component Locations
```
taptap-mobile/src/scenes/referralchallenge/screens/
├── ReferralChallengeBoard/ - Main progression dashboard
├── MilestoneDetail/ - Individual milestone details
├── ReferralCode/ - Code creation interface
├── ReferralCodeConfirm/ - Code confirmation
├── SharingSocial/ - Social sharing tools
├── FriendList/ - Friend management
└── ReferralChallengeIntro/ - Onboarding flow
```

## Core Data Types

### Milestone Structure
```typescript
interface MilestoneType {
  id: string,
  milestoneId: string,
  name: string,
  description: string,
  targetValue: number,
  currentProgress: number,
  percentProgress: number,
  status: 'COMPLETED' | 'IN_PROGRESS' | 'LOCKED',
  rewards: RewardItem[],
  conditions: MilestoneCondition[]
}
```

### Firebase Real-time Data
```typescript
interface IFirebaseMilestoneType {
  inviteFriendList: FriendInvite[],
  refereeTransactionList: RefereeTransaction[],
  currentProgress: number,
  maxProgress: number,
  status: string
}
```

### Referral Campaign
```typescript
interface ReferralCampaign {
  referralId: string,
  referralName: string,
  referralTnc: string,
  mobile: string,
  ended: boolean,
  status: 'RUNNING' | 'ENDED' | 'BANNED',
  currentMilestoneId: string,
  listMilestones: MilestoneType[]
}
```

## Main Application Flow

```mermaid
flowchart TD
    Start([User enters Referral Challenge]) --> CheckEntry{Entry point?}
    
    CheckEntry -->|New User| ShowIntro[Show ReferralChallengeIntro]
    CheckEntry -->|Returning User| LoadBoard[Load ReferralChallengeBoard]
    CheckEntry -->|Code Entry| ShowCodeInput[Show ReferralCode screen]
    
    ShowIntro --> ReadTerms[User reads terms and conditions]
    ReadTerms --> AcceptTerms{User accepts?}
    AcceptTerms -->|Yes| CreateCode[Navigate to ReferralCode creation]
    AcceptTerms -->|No| ExitFlow[Exit referral flow]
    
    CreateCode --> InputRefCode[User inputs custom referral code]
    InputRefCode --> ValidateCode{Code validation}
    ValidateCode -->|Invalid| ShowError[Show validation error]
    ValidateCode -->|Valid| ConfirmCode[Show ReferralCodeConfirm]
    
    ShowError --> InputRefCode
    ConfirmCode --> CreateAPICall[Call createRefCode API]
    CreateAPICall --> APIResponse{API Success?}
    
    APIResponse -->|Success| LoadBoard
    APIResponse -->|Error| ShowAPIError[Show API error]
    ShowAPIError --> InputRefCode
    
    LoadBoard --> InitializeFirebase[Initialize Firebase listeners]
    InitializeFirebase --> LoadMilestones[Load milestone data]
    LoadMilestones --> CheckAnimation{Skip animation?}
    
    CheckAnimation -->|No| PlayAnimation[Play milestone progression animation]
    CheckAnimation -->|Yes| ShowBoard[Show board directly]
    
    PlayAnimation --> ShowBoard
    ShowBoard --> UserInteraction{User interaction?}
    
    UserInteraction -->|View Milestone| OpenMilestoneDetail[Navigate to MilestoneDetail]
    UserInteraction -->|Share| OpenSharingSocial[Open SharingSocial]
    UserInteraction -->|Friends| OpenFriendList[Navigate to FriendList]
    UserInteraction -->|Terms| ShowTermsModal[Show T&C modal]
    
    OpenMilestoneDetail --> MilestoneFlow[Milestone Detail Flow]
    OpenSharingSocial --> SharingFlow[Social Sharing Flow]
    OpenFriendList --> FriendFlow[Friend Management Flow]
    ShowTermsModal --> ShowBoard
    
    MilestoneFlow --> BackToBoard[Return to board]
    SharingFlow --> BackToBoard
    FriendFlow --> BackToBoard
    BackToBoard --> ShowBoard
```

## Firebase Real-time Integration

```mermaid
flowchart TD
    FirebaseInit[Initialize Firebase Listeners] --> SetupPaths[Setup Firebase database paths]
    
    SetupPaths --> MilestoneListener[Milestone Progress Listener]
    SetupPaths --> StatusListener[Referral Status Listener]
    SetupPaths --> PackageListener[Package Remaining Listener]
    SetupPaths --> BanListener[User Ban Status Listener]
    SetupPaths --> IdListener[Current Milestone ID Listener]
    
    MilestoneListener --> MilestoneUpdate{Milestone data changed?}
    MilestoneUpdate -->|Yes| UpdateProgress[Update milestone progress]
    MilestoneUpdate -->|No| ContinueListening[Continue listening]
    
    StatusListener --> StatusUpdate{Status changed?}
    StatusUpdate -->|ENDED/BANNED| ShowErrorModal[Show error modal]
    StatusUpdate -->|RUNNING| ContinueListening
    
    PackageListener --> PackageUpdate{Package count changed?}
    PackageUpdate -->|Yes| UpdatePackageUI[Update package remaining UI]
    PackageUpdate -->|No| ContinueListening
    
    BanListener --> BanUpdate{User banned?}
    BanUpdate -->|Yes| ShowBanModal[Show ban modal with CS contact]
    BanUpdate -->|No| ContinueListening
    
    IdListener --> IdUpdate{Milestone ID changed?}
    IdUpdate -->|Yes| UpdateCurrentMilestone[Update current milestone]
    IdUpdate -->|No| ContinueListening
    
    UpdateProgress --> RefreshUI[Refresh board UI]
    UpdatePackageUI --> RefreshUI
    UpdateCurrentMilestone --> RefreshUI
    RefreshUI --> ContinueListening
    
    ShowErrorModal --> NavigateHome[Navigate to home]
    ShowBanModal --> NavigateHome
    ContinueListening --> FirebaseInit
```

## Milestone Detail Flow

```mermaid
flowchart TD
    MilestoneDetail[Open Milestone Detail] --> LoadDetail[Load milestone data]
    
    LoadDetail --> CheckStatus{Milestone status?}
    CheckStatus -->|COMPLETED| ShowClaimButton[Show 'Claim Reward' button]
    CheckStatus -->|IN_PROGRESS| ShowProgress[Show progress tracking]
    CheckStatus -->|LOCKED| ShowLocked[Show locked state]
    
    ShowClaimButton --> UserClaims{User clicks claim?}
    UserClaims -->|Yes| ClaimAPI[Call claimRewards API]
    UserClaims -->|No| ShowDetailContent[Show milestone content]
    
    ClaimAPI --> ClaimResult{Claim successful?}
    ClaimResult -->|Yes| PlayClaimAnimation[Play claim success animation]
    ClaimResult -->|No| ShowClaimError[Show claim error]
    
    PlayClaimAnimation --> PlaySound[Play success sound]
    PlaySound --> ShowSuccessModal[Show success modal]
    ShowSuccessModal --> UpdateMilestone[Update milestone status]
    UpdateMilestone --> RefreshBoard[Refresh board data]
    
    ShowProgress --> ShowConditions[Show milestone conditions]
    ShowConditions --> ShowRewards[Show milestone rewards]
    ShowRewards --> ShowFriendProgress[Show friend invitation progress]
    
    ShowLocked --> ShowRequirements[Show unlock requirements]
    ShowRequirements --> ShowDetailContent
    
    ShowDetailContent --> ShowMyRefCode[Show user's referral code]
    ShowMyRefCode --> ShowSharingOptions[Show sharing options]
    ShowSharingOptions --> UserActions{User actions?}
    
    UserActions -->|Share| TriggerShare[Trigger social sharing]
    UserActions -->|Copy Code| CopyToClipboard[Copy referral code]
    UserActions -->|Back| ReturnToBoard[Return to board]
    
    TriggerShare --> SharingFlow[Social sharing flow]
    CopyToClipboard --> ShowCopyToast[Show copy success toast]
    ShowCopyToast --> ShowDetailContent
    
    SharingFlow --> ShowDetailContent
    RefreshBoard --> ReturnToBoard
    ShowClaimError --> ShowDetailContent
```

## Social Sharing System

```mermaid
flowchart TD
    SharingSystem[Social Sharing System] --> LoadContent[Load sharing content]
    
    LoadContent --> CheckContent{Content available?}
    CheckContent -->|No| GenerateContent[Generate default sharing content]
    CheckContent -->|Yes| ShowSharingOptions[Show sharing platform options]
    
    GenerateContent --> FormatMessage[Format referral message]
    FormatMessage --> AddReferralLink[Add referral link]
    AddReferralLink --> ShowSharingOptions
    
    ShowSharingOptions --> PlatformChoice{User selects platform}
    
    PlatformChoice -->|Facebook| FacebookShare[Share to Facebook]
    PlatformChoice -->|Zalo| ZaloShare[Share to Zalo]
    PlatformChoice -->|SMS| SMSShare[Share via SMS]
    PlatformChoice -->|WhatsApp| WhatsAppShare[Share to WhatsApp]
    PlatformChoice -->|Copy Link| CopyLink[Copy to clipboard]
    PlatformChoice -->|More Options| SystemShare[Open system share sheet]
    
    FacebookShare --> TrackShare[Track Facebook share event]
    ZaloShare --> TrackShare[Track Zalo share event]
    SMSShare --> TrackShare[Track SMS share event]
    WhatsAppShare --> TrackShare[Track WhatsApp share event]
    CopyLink --> ShowCopySuccess[Show copy success message]
    SystemShare --> TrackShare[Track system share event]
    
    TrackShare --> ShareSuccess{Share successful?}
    ShareSuccess -->|Yes| ShowShareToast[Show share success toast]
    ShareSuccess -->|No| ShowShareError[Show share error]
    
    ShowCopySuccess --> TrackCopy[Track copy event]
    TrackCopy --> UpdateAnalytics[Update sharing analytics]
    
    ShowShareToast --> UpdateAnalytics
    ShowShareError --> ShowSharingOptions
    UpdateAnalytics --> EndSharingFlow[End sharing flow]
```

## Friend Management System

```mermaid
flowchart TD
    FriendManagement[Friend Management] --> LoadFriendData[Load friend list data]
    
    LoadFriendData --> ShowTabs[Show History/Transaction tabs]
    ShowTabs --> TabSelection{User selects tab}
    
    TabSelection -->|Friends| ShowFriendList[Show registered friends list]
    TabSelection -->|Transactions| ShowTransactionList[Show referee transactions]
    
    ShowFriendList --> LoadRegistered[Load registered friends]
    LoadRegistered --> DisplayFriends[Display friend cards]
    DisplayFriends --> FriendActions{Friend actions available?}
    
    FriendActions -->|View Details| ShowFriendDetail[Show friend progress detail]
    FriendActions -->|Invite More| OpenSharingFlow[Open sharing interface]
    
    ShowTransactionList --> LoadTransactions[Load referee transaction history]
    LoadTransactions --> DisplayTransactions[Display transaction list]
    DisplayTransactions --> TransactionDetails{Transaction details}
    
    TransactionDetails --> ShowTransactionInfo[Show transaction details]
    ShowTransactionInfo --> ShowCommissionInfo[Show referral commission]
    
    ShowFriendDetail --> FriendProgress[Show friend's progress]
    FriendProgress --> FriendRewards[Show rewards earned from friend]
    FriendRewards --> BackToFriendList[Back to friend list]
    
    OpenSharingFlow --> SharingInterface[Social sharing interface]
    SharingInterface --> BackToFriendList
    
    BackToFriendList --> ShowFriendList
    ShowCommissionInfo --> ShowTransactionList
```

## API Endpoints and Variables

### Primary APIs

#### 1. Get Running Referral
- **Endpoint**: `GET /referral/running`
- **Parameters**:
  ```typescript
  {
    mobile: string,
    ignoreFraud: string // IGNORE_LIMIT_FEATURE
  }
  ```
- **Response**: `ResponseRunningReferralDTO`
- **Purpose**: Load active referral campaign data

#### 2. Create Referral Code
- **Endpoint**: `POST /referral/code`
- **Payload**:
  ```typescript
  {
    mobile: string,
    referralCode: string, // Custom code (max 14 chars, alphanumeric)
    deviceId: string
  }
  ```
- **Response**: `ResponseCheckCode`
- **Validation**: Alphanumeric only, 14 character limit

#### 3. Get Milestone Detail
- **Endpoint**: `GET /referral/milestone/{milestoneId}`
- **Parameters**:
  ```typescript
  {
    mobile: string,
    ignoreFraud: string
  }
  ```
- **Response**: `ResponseMilestoneDetailDTO`

#### 4. Claim Rewards
- **Endpoint**: `POST /referral/claim`
- **Payload**:
  ```typescript
  {
    mobile: string,
    milestoneId: string,
    deviceId: string
  }
  ```
- **Response**: `ResponseClaimRewardDTO`
- **Purpose**: Claim milestone rewards

#### 5. Get Friend List
- **Endpoint**: `GET /referral/friends`
- **Parameters**:
  ```typescript
  {
    mobile: string,
    page: number,
    size: number
  }
  ```
- **Response**: `ResponseListFriend`

#### 6. Get Referee Transactions
- **Endpoint**: `GET /referral/transactions`
- **Parameters**:
  ```typescript
  {
    mobile: string,
    milestoneId: string,
    page: number,
    size: number
  }
  ```
- **Response**: Transaction history data

### Firebase Database Paths

#### Real-time Listeners
```typescript
// Customer-specific paths
const fbPathMileStoneId = `Customer/${mobile}/Referral/${referralId}/currentMilestoneId`;
const fbPathProgressMilestone = `Customer/${mobile}/Referral/${referralId}/MilestoneDtos/${currentMilestoneId}`;
const fbPathReferralRefCodeStatus = `Customer/${mobile}/Referral/refCodeState`;

// Global referral paths  
const fbPathPackageMilestone = `Referral/${referralId}/MilestoneDtos/${milestoneId}`;
const fbPathReferralStatus = `Referral/${referralId}/Status`;
```

## Anti-Fraud & Security Features

### Status Monitoring
- **RUNNING**: Active referral campaign
- **ENDED**: Campaign has ended
- **BANNED**: User banned from referral program

### Fraud Detection
- Real-time monitoring via Firebase
- Automatic ban detection and user notification
- Customer service contact integration
- Device ID tracking for security

### Error Handling
```typescript
enum REFERRAL_STATUS {
  RUNNING = 'RUNNING',
  ENDED = 'ENDED', 
  BANNED = 'BANNED'
}
```

## Animation & UX Features

### Progression Animations
- **Two-phase animation system**: Initial reveal + progress animation
- **Bezier easing curves**: Smooth milestone progression
- **Sound effects**: Success sounds on claim
- **Lottie animations**: Rich visual feedback
- **Auto-scroll**: Focus on current milestone

### Visual States
- **Completed milestones**: Check marks, completed styling
- **Current milestone**: Highlighted, animated progress
- **Locked milestones**: Grayed out, disabled state
- **Expired campaign**: Grayscale filter applied

## Conditional Rendering Rules

### 1. Board Display
**Normal State**: Full color, interactive
**Ended State**: Grayscale filter, disabled interactions

### 2. Milestone Status
- **COMPLETED**: Show claim button or completed checkmark
- **IN_PROGRESS**: Show progress bar and requirements
- **LOCKED**: Show lock icon and unlock requirements

### 3. Social Sharing
- **Active Campaign**: Full sharing functionality
- **Ended/Banned**: Disabled sharing buttons

### 4. Friend List Access
**Available when**: Campaign is RUNNING
**Hidden when**: Campaign ended or user banned

## Analytics/Tracking Integration

### Key Events (via TrackingManager)
```typescript
// Main board interactions
trackReferral().mainTnc() // Terms clicked
trackReferral().mainFriend() // Friend list accessed

// Milestone interactions  
trackReferral().milestoneDetail() // Milestone viewed
trackReferral().claimReward() // Reward claimed

// Sharing events
trackReferral().shareGeneral() // General sharing
trackReferral().shareFacebook() // Facebook specific
trackReferral().shareZalo() // Zalo specific
```

### Tracking Parameters
```typescript
const trackingParams = {
  referralId: string,
  milestoneId: string,
  mobile: string,
  platform: string,
  action: string
};
```

## Testing Considerations

### Key Test Scenarios
1. **Referral Code Creation** - Validation, uniqueness, API integration
2. **Firebase Real-time Updates** - Listener setup, data synchronization
3. **Milestone Progression** - Status changes, animations, rewards
4. **Social Sharing** - Platform integration, content generation
5. **Anti-fraud Detection** - Ban scenarios, status monitoring
6. **Friend Management** - List loading, progress tracking
7. **Reward Claiming** - API calls, success/error handling
8. **Animation System** - Timing, performance, skip functionality

### Edge Cases
- Network connectivity during Firebase updates
- Rapid milestone progression
- Campaign ending during user session
- Invalid referral codes
- Sharing failures
- Firebase connection issues
- Concurrent reward claims

### Performance Considerations
- Firebase listener management and cleanup
- Animation performance optimization
- Image loading and caching
- Memory management for large friend lists

---

*This documentation covers the complete business logic and flow of the ReferralChallenge system with its multi-screen architecture, real-time updates, and comprehensive tracking features.*