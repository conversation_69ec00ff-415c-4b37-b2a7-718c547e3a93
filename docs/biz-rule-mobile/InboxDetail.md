# InboxDetail Business Rules & Flow Documentation

## Overview
The InboxDetail screen displays detailed inbox message content with support for multiple template types including vouchers, points, mixed content, and surveys. It provides a flexible template-based rendering system that adapts to different message types and automatically marks messages as read when opened.

## Component Location
- **Main Component**: `taptap-mobile/src/scenes/inbox/screens/InboxDetail/index.tsx`
- **Template Components**: 
  - `TemplatePoint.tsx` - VUI points and brand currency
  - `TemplateVoucher.tsx` - Voucher rewards
  - `TemplateMixed.tsx` - Combined points and vouchers
  - `TemplateContent.tsx` - Text content and HTML
- **APIs**: `taptap-mobile/src/scenes/inbox/redux/api.ts`

## Core Data Types

### InboxHistoryType
```typescript
interface InboxHistoryType {
  createdAt: string,
  isSeen: boolean,
  mobile: string,
  id: string,
  storeName: string,
  templateBasicData: {
    directLink?: string,
    body: string,
    title: string,
    subTitle: string,
    type: InboxTypeEnum,
    ctaButton?: string,
    ctaLink?: string
  },
  templateCode?: string,
  templateId: null,
  templateMetaData: {
    voucherCodeId?: string,
    voucherCodeIdList?: string[],
    vuiPoint?: string,
    brandCurrencyCode?: string,
    brandCurrencyPoint?: string,
    brandCurrencyLogo?: string,
    brandCurrencyName?: string,
    storeCode?: string
  },
  templateParams?: any,
  userId: string
}
```

### InboxTypeEnum
```typescript
enum InboxTypeEnum {
  VOUCHER = 'VOUCHER',
  VOUCHER_INFO = 'VOUCHER_INFO', 
  VUI = 'VUI',
  BRAND_CURRENCY = 'BRAND_CURRENCY',
  VUI_BRAND_CURRENCY = 'VUI_BRAND_CURRENCY',
  MIXED = 'MIXED',
  CONTENT = 'CONTENT'
}
```

## Main Application Flow

```mermaid
flowchart TD
    Start([User opens InboxDetail]) --> CheckSource{Message source?}
    
    CheckSource -->|Has ID| FetchDetail[Fetch inbox detail by ID]
    CheckSource -->|No ID| UseParams[Use route parameters directly]
    
    FetchDetail --> CheckFetch{Fetch successful?}
    CheckFetch -->|Yes| MarkRead[Mark message as read in Redux]
    CheckFetch -->|No| ShowError[Show loading error]
    
    MarkRead --> DetermineTemplate[Determine template type]
    UseParams --> DetermineTemplate
    
    DetermineTemplate --> TemplateSwitch{templateBasicData.type}
    
    TemplateSwitch -->|VUI| RenderPointTemplate[Render TemplatePoint - VUI]
    TemplateSwitch -->|BRAND_CURRENCY| RenderPointTemplate[Render TemplatePoint - Brand Currency]
    TemplateSwitch -->|VUI_BRAND_CURRENCY| RenderPointTemplate[Render TemplatePoint - Both]
    
    TemplateSwitch -->|VOUCHER| RenderVoucherTemplate[Render TemplateVoucher - Full Info]
    TemplateSwitch -->|VOUCHER_INFO| RenderVoucherInfoTemplate[Render TemplateVoucher - Info Only]
    
    TemplateSwitch -->|MIXED| CheckMixed{templateCode = SURVEY?}
    CheckMixed -->|Yes| RenderSurveyTemplate[Render TemplateMixed - Survey Mode]
    CheckMixed -->|No| RenderMixedTemplate[Render TemplateMixed - Standard]
    
    TemplateSwitch -->|CONTENT| RenderContentTemplate[Render TemplateContent]
    TemplateSwitch -->|Default| RenderContentTemplate
    
    RenderPointTemplate --> CheckCTA{Has CTA link?}
    RenderVoucherTemplate --> CheckCTA
    RenderVoucherInfoTemplate --> CheckCTA
    RenderSurveyTemplate --> CheckCTA
    RenderMixedTemplate --> CheckCTA
    RenderContentTemplate --> CheckCTA
    
    CheckCTA -->|Yes| ShowCTAButton[Show CTA button in bottom panel]
    CheckCTA -->|No| ShowContent[Show content only]
    
    ShowCTAButton --> UserClicksCTA{User clicks CTA?}
    UserClicksCTA -->|Yes| TrackAction[Track 'direct2Link' action]
    TrackAction --> OpenLink[Open external website/webview]
    
    ShowContent --> EndFlow[End flow]
    OpenLink --> EndFlow
```

## Template Rendering Logic

```mermaid
flowchart TD
    TemplateRender[Template Rendering] --> TypeCheck{Message Type}
    
    TypeCheck -->|VUI| PointTemplate[TemplatePoint]
    TypeCheck -->|BRAND_CURRENCY| PointTemplate
    TypeCheck -->|VUI_BRAND_CURRENCY| PointTemplate
    
    TypeCheck -->|VOUCHER| VoucherTemplate[TemplateVoucher - Show Quantity]
    TypeCheck -->|VOUCHER_INFO| VoucherInfoTemplate[TemplateVoucher - Hide Quantity]
    
    TypeCheck -->|MIXED| MixedCheck{Survey or Standard?}
    TypeCheck -->|CONTENT| ContentTemplate[TemplateContent]
    
    MixedCheck -->|templateCode=SURVEY| SurveyMixed[TemplateMixed - Survey Mode]
    MixedCheck -->|Standard| StandardMixed[TemplateMixed - Standard Mode]
    
    PointTemplate --> ShowPointRewards[Display VUI/Brand Currency Rewards]
    ShowPointRewards --> PointLogo[Show currency logos and amounts]
    
    VoucherTemplate --> ShowVoucherList[Display voucher list with quantities]
    VoucherInfoTemplate --> ShowVoucherInfo[Display voucher info without quantities]
    
    SurveyMixed --> ParseSurveyData[Parse surveyGifts JSON data]
    ParseSurveyData --> ShowSurveyRewards[Display survey completion rewards]
    ShowSurveyRewards --> ShowFormData[Display form submission data]
    
    StandardMixed --> CombineRewards[Combine points and vouchers display]
    
    ContentTemplate --> RenderHTML{Has HTML content?}
    RenderHTML -->|Yes| ShowHTMLContent[Render HTML body]
    RenderHTML -->|No| ShowPlainText[Show plain text content]
    
    ShowHTMLContent --> CheckWebview{Has webview data?}
    ShowPlainText --> CheckWebview
    CheckWebview -->|Yes| EmbedWebview[Embed webview content]
    CheckWebview -->|No| TextOnly[Text content only]
```

## CTA (Call-to-Action) Logic

```mermaid
flowchart TD
    CTACheck[Check CTA Availability] --> HasCTALink{ctaLink exists?}
    
    HasCTALink -->|No| NoCTA[No CTA button shown]
    HasCTALink -->|Yes| DetermineCTAText[Determine CTA button text]
    
    DetermineCTAText --> CTATextSource{CTA text source}
    CTATextSource -->|templateBasicData.ctaButton| UseCTAButton[Use ctaButton text]
    CTATextSource -->|route.params.ctaName| UseParamName[Use ctaName parameter]
    CTATextSource -->|Default| UseWatchNow[Use 'Watch Now' default]
    
    UseCTAButton --> ShowBottomPanel[Show bottom panel with CTA button]
    UseParamName --> ShowBottomPanel
    UseWatchNow --> ShowBottomPanel
    
    ShowBottomPanel --> UserClicksCTA{User clicks CTA?}
    UserClicksCTA -->|Yes| TrackClick[Track analytics: 'direct2Link']
    UserClicksCTA -->|No| WaitAction[Wait for user action]
    
    TrackClick --> OpenWebsite[Open website via openWebsite() utility]
    OpenWebsite --> HandleWebview{Handle webview or external browser}
    
    HandleWebview --> WebsiteOpened[Website/webview opened]
    WebsiteOpened --> EndFlow[End flow]
    
    NoCTA --> ContentOnly[Show content without CTA]
    ContentOnly --> EndFlow
```

## Template-Specific Business Logic

### TemplatePoint (VUI/Brand Currency)
**Used for types**: `VUI`, `BRAND_CURRENCY`, `VUI_BRAND_CURRENCY`

**Display Logic**:
- Shows VUI point amount with VUI logo
- Shows brand currency amount with brand-specific logo
- Displays both amounts for VUI_BRAND_CURRENCY type
- Includes currency name and formatted point values

### TemplateVoucher (Voucher Rewards)
**Used for types**: `VOUCHER`, `VOUCHER_INFO`

**Display Logic**:
- Fetches voucher details using `voucherCodeIdList`
- Shows voucher thumbnails and quantities
- `VOUCHER_INFO` hides quantity display (`hideQuantity={true}`)
- Displays voucher titles and merchant information

### TemplateMixed (Combined Content)
**Used for type**: `MIXED`

**Two modes**:
1. **Survey Mode** (`templateCode === 'SURVEY'`):
   - Parses `surveyGifts` JSON data
   - Shows survey completion rewards
   - Displays form submission parameters
   - Uses title from `templateBasicData.title`

2. **Standard Mode**:
   - Combines VUI points, brand currency, and vouchers
   - Shows all reward types in one template
   - Uses subtitle from `templateBasicData.subTitle`

### TemplateContent (Text/HTML)
**Used for type**: `CONTENT` and default

**Display Logic**:
- Renders HTML content if available
- Supports webview embedding
- Falls back to plain text for simple messages
- Handles external links and media

## API Endpoints and Variables

### Primary APIs

#### 1. Get Inbox Detail
- **Endpoint**: `GET /inbox/history`
- **Parameters**:
  ```typescript
  {
    id: string,
    action: 'SHOW_FOR_MOBILE'
  }
  ```
- **Response**: `InboxHistoryType`
- **Purpose**: Fetch detailed message content and metadata

#### 2. Mark Message as Read (Redux Action)
- **Action**: `inboxActions.readInbox(messageId)`
- **Purpose**: Update message read status in local state
- **Triggers**: Automatic when message detail is successfully loaded

### Message Data Structure
Each template receives specific data based on type:

#### Point Templates
```typescript
{
  vuiPoint: number,
  brandCurrencyCode: string,
  brandCurrencyLogo: string, 
  brandCurrencyName: string,
  brandCurrencyPoint: number
}
```

#### Voucher Templates
```typescript
{
  voucherCodeIdList: string[],
  hideQuantity: boolean // true for VOUCHER_INFO
}
```

#### Mixed Templates
```typescript
{
  vuiPoint: number,
  brandCurrencyData: BrandCurrencyInfo,
  voucherCodeIdList: string[],
  isSurvey: boolean,
  formData: any,
  surveyGifts: ParsedSurveyGift[]
}
```

## Conditional Rendering Rules

### 1. Loading State
**Shows when**:
- `id` parameter exists
- Detail data is empty (`isEmpty(detail)`)
- `loading = true`

### 2. Error State  
**Shows when**:
- `id` parameter exists
- Detail data is empty
- `loading = false`

### 3. Template Selection
**Priority order**:
1. Check if `id` exists → Use fetched detail
2. Use route parameters for direct content

### 4. CTA Button Display
**Shows when**:
- `ctaLink` exists in either detail data or route params
- Displayed in fixed bottom panel
- Button text priority: `ctaButton` → `ctaName` → "Watch Now"

### 5. Content Scrolling
**Behavior**:
- Non-content types: `bounces={false}` (fixed height)
- Content type: `bounces={true}` (scrollable)
- Special styling for template containers

## User Actions & Navigation

### Primary Actions
1. **Read Message**: Automatic when screen opens
2. **Click CTA**: Opens external links/webviews
3. **View Vouchers**: Navigate to voucher details (within templates)
4. **Scroll Content**: For long text/HTML messages

### External Integration
- **Website Opening**: Uses `openWebsite()` utility
- **Webview Handling**: Embedded webview support
- **Deep Linking**: Support for app-to-app navigation

## Error Handling

### Loading States
- Shows loading spinner during detail fetch
- Handles empty response gracefully
- Error state with retry capability

### Data Validation
- Handles missing template metadata gracefully
- Provides fallbacks for missing CTA text
- Validates JSON parsing for survey data

### Template Errors
- Falls back to TemplateContent for unknown types
- Handles missing voucher data in voucher templates
- Graceful degradation for malformed survey data

## Analytics/Tracking Integration

### Key Events (via TrackingManagerV3)
- **Detail View**: Automatic when message opens
- **CTA Click**: `trackInbox().detailAction({ action: 'direct2Link', name: ctaLink })`
- **Template Interactions**: Various template-specific events

### Tracking Parameters
```typescript
const trackingParams = {
  action: 'direct2Link',
  name: string, // CTA link URL
  messageId: string,
  templateType: InboxTypeEnum
};
```

## Testing Considerations

### Key Test Scenarios
1. **Template Rendering** - All 7 inbox types
2. **Data Fetching** - Success, failure, empty responses
3. **CTA Functionality** - With/without CTA links
4. **Survey Templates** - JSON parsing, form data display
5. **Webview Integration** - Embedded vs external browser
6. **Loading States** - Initial load, error recovery
7. **Mark as Read** - Automatic marking, Redux integration
8. **Content Types** - HTML, plain text, mixed media

### Edge Cases
- Invalid JSON in survey gifts
- Missing voucher data in voucher templates
- Malformed HTML content
- Network failures during detail fetch
- Missing template metadata
- Invalid CTA links

### Template-Specific Testing
- **PointTemplate**: Different currency combinations
- **VoucherTemplate**: Various voucher states and quantities
- **MixedTemplate**: Survey vs standard mode switching
- **ContentTemplate**: HTML rendering and webview embedding

---

*This documentation covers the complete business logic and flow of the InboxDetail component's template-based message rendering system.*