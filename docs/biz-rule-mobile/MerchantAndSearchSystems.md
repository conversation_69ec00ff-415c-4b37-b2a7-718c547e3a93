# Merchant & Search Systems Business Rules & Flow Documentation

## Overview
This document covers the Merchant Management and Search functionality in the TapTap mobile application. These systems handle merchant details, tier-based rewards, store management, and comprehensive search capabilities across merchants and rewards.

## Systems Architecture

### 1. Merchant Management System (`/scenes/merchant`)
**Purpose**: Comprehensive merchant information, earning opportunities, and tier-based reward systems
**Key Components**:
- Merchant detail pages with multi-tab interface
- Tier-based membership systems with progression tracking
- Store location services and nearby store discovery
- Reward catalogs and earning rate display
- Brand-specific features (wheel games, QR codes, special promotions)
- Online store integration and ordering

### 2. Search System (`/scenes/search`)
**Purpose**: Universal search across merchants, rewards, and content
**Key Components**:
- Multi-tab search interface (All, Merchants, Rewards)
- Search history and suggestions
- Real-time search results with filtering
- Trending and popular search terms
- Advanced search with filtering options

## Merchant Detail System Flow

```mermaid
flowchart TD
    MerchantEntry[User enters Merchant Detail] --> LoadMerchantData[Load merchant data]
    
    LoadMerchantData --> ShowMerchantDetail[Show merchant detail interface]
    ShowMerchantDetail --> TabNavigation[Show tab navigation]
    
    TabNavigation --> TabSelection{User selects tab}
    
    TabSelection -->|Rewards| ShowRewardsTab[Show available rewards]
    TabSelection -->|Info| ShowInfoTab[Show merchant information]
    TabSelection -->|Earn| ShowEarnTab[Show earning methods]
    TabSelection -->|Tier| ShowTierTab[Show tier membership]
    TabSelection -->|Online Store| ShowOnlineTab[Show online store]
    
    ShowRewardsTab --> LoadRewards[Load available rewards]
    LoadRewards --> DisplayRewardList[Display reward catalog]
    DisplayRewardList --> RewardInteraction{User interacts with reward?}
    
    RewardInteraction -->|Select Reward| NavigateToRewardDetail[Navigate to reward detail]
    RewardInteraction -->|Filter| ShowRewardFilters[Show filtering options]
    RewardInteraction -->|Sort| ShowSortOptions[Show sorting options]
    
    ShowInfoTab --> DisplayMerchantInfo[Display merchant information]
    DisplayMerchantInfo --> InfoSections{Information sections}
    
    InfoSections -->|Description| ShowDescription[Show merchant description]
    InfoSections -->|Contact| ShowContactInfo[Show contact information]
    InfoSections -->|Locations| ShowStoreLocations[Show store locations]
    InfoSections -->|Hours| ShowOperatingHours[Show operating hours]
    
    ShowStoreLocations --> StoreActions{Store location actions}
    StoreActions -->|View on Map| OpenMapView[Open map view]
    StoreActions -->|Get Directions| OpenDirections[Open navigation directions]
    StoreActions -->|Call Store| InitiateCall[Initiate phone call]
    
    ShowEarnTab --> DisplayEarnMethods[Display earning methods]
    DisplayEarnMethods --> EarnMethodTypes{Earning method types}
    
    EarnMethodTypes -->|Receipt Scan| ShowScanInstructions[Show scanning instructions]
    EarnMethodTypes -->|Store Visit| ShowVisitRewards[Show visit rewards]
    EarnMethodTypes -->|Purchase| ShowPurchaseRewards[Show purchase rewards]
    EarnMethodTypes -->|Special Offers| ShowSpecialOffers[Show special earning opportunities]
    
    ShowScanInstructions --> ScanAction{User wants to scan?}
    ScanAction -->|Yes| OpenScanInterface[Open scanning interface]
    ScanAction -->|No| DisplayEarnMethods
    
    ShowTierTab --> LoadTierInfo[Load tier membership information]
    LoadTierInfo --> DisplayTierStatus[Display current tier status]
    DisplayTierStatus --> TierComponents{Tier components}
    
    TierComponents -->|Current Status| ShowCurrentTier[Show current tier level]
    TierComponents -->|Progress| ShowTierProgress[Show progress to next tier]
    TierComponents -->|Benefits| ShowTierBenefits[Show tier benefits]
    TierComponents -->|History| ShowTierHistory[Show tier history]
    TierComponents -->|Requirements| ShowTierRequirements[Show tier requirements]
    
    ShowCurrentTier --> TierActions{Tier actions available?}
    TierActions -->|Upgrade Available| ShowUpgradeOption[Show upgrade option]
    TierActions -->|Special Rewards| ShowTierRewards[Show tier-specific rewards]
    
    ShowOnlineTab --> LoadOnlineStore[Load online store content]
    LoadOnlineStore --> OnlineStoreCheck{Online store available?}
    
    OnlineStoreCheck -->|Yes| DisplayOnlineStore[Display online store interface]
    OnlineStoreCheck -->|No| ShowOnlineUnavailable[Show online store unavailable]
    
    DisplayOnlineStore --> OnlineActions{Online store actions}
    OnlineActions -->|Browse Products| OpenProductCatalog[Open product catalog]
    OnlineActions -->|Place Order| InitiateOrderFlow[Initiate order flow]
    OnlineActions -->|Track Order| ShowOrderTracking[Show order tracking]
    
    NavigateToRewardDetail --> RewardDetailFlow[Continue in reward detail flow]
    OpenScanInterface --> ScanningFlow[Continue in scanning flow]
    InitiateOrderFlow --> OrderFlow[Continue in order flow]
```

## Search System Flow

```mermaid
flowchart TD
    SearchEntry[User enters Search] --> ShowSearchInterface[Show search interface]
    
    ShowSearchInterface --> SearchState{Current search state}
    
    SearchState -->|Empty| ShowSearchSuggestions[Show search suggestions]
    SearchState -->|Has Query| ShowSearchResults[Show search results]
    SearchState -->|Has History| ShowSearchHistory[Show search history]
    
    ShowSearchSuggestions --> SuggestionTypes{Suggestion types}
    SuggestionTypes -->|Popular| ShowPopularSearches[Show popular searches]
    SuggestionTypes -->|Trending| ShowTrendingSearches[Show trending searches]
    SuggestionTypes -->|Categories| ShowCategoryBrowse[Show category browsing]
    
    ShowSearchHistory --> HistoryActions{History actions}
    HistoryActions -->|Select Item| ExecuteHistorySearch[Execute previous search]
    HistoryActions -->|Clear History| ClearSearchHistory[Clear search history]
    
    ShowSearchInterface --> UserInput[User enters search query]
    UserInput --> QueryValidation{Valid search query?}
    
    QueryValidation -->|No| ShowInputValidation[Show input validation]
    QueryValidation -->|Yes| ExecuteSearch[Execute search]
    
    ShowInputValidation --> UserInput
    
    ExecuteSearch --> SearchAPI[Call search API]
    SearchAPI --> LoadSearchResults[Load search results]
    LoadSearchResults --> ShowSearchResults
    
    ShowSearchResults --> TabInterface[Show tabbed results]
    TabInterface --> ResultTabs{User selects tab}
    
    ResultTabs -->|All| ShowAllResults[Show all results]
    ResultTabs -->|Merchants| ShowMerchantResults[Show merchant results]
    ResultTabs -->|Rewards| ShowRewardResults[Show reward results]
    
    ShowAllResults --> AllResultSections{All results sections}
    AllResultSections -->|Top Merchants| DisplayTopMerchants[Display top merchant matches]
    AllResultSections -->|Top Rewards| DisplayTopRewards[Display top reward matches]
    AllResultSections -->|Suggestions| DisplaySearchSuggestions[Display search suggestions]
    
    DisplayTopMerchants --> MerchantResultActions{Merchant result actions}
    MerchantResultActions -->|View Merchant| NavigateToMerchant[Navigate to merchant detail]
    MerchantResultActions -->|View All| ShowAllMerchants[Show all merchant results]
    
    DisplayTopRewards --> RewardResultActions{Reward result actions}
    RewardResultActions -->|View Reward| NavigateToReward[Navigate to reward detail]
    RewardResultActions -->|View All| ShowAllRewards[Show all reward results]
    
    ShowMerchantResults --> LoadMerchantList[Load merchant search results]
    LoadMerchantList --> DisplayMerchantList[Display merchant list]
    DisplayMerchantList --> MerchantFiltering{Apply filters?}
    
    MerchantFiltering -->|Yes| ShowMerchantFilters[Show merchant filters]
    MerchantFiltering -->|No| MerchantSelection{User selects merchant?}
    
    ShowMerchantFilters --> ApplyMerchantFilters[Apply selected filters]
    ApplyMerchantFilters --> RefreshMerchantResults[Refresh merchant results]
    RefreshMerchantResults --> DisplayMerchantList
    
    MerchantSelection -->|Yes| NavigateToMerchant
    MerchantSelection -->|No| ContinueSearching[Continue searching]
    
    ShowRewardResults --> LoadRewardList[Load reward search results]
    LoadRewardList --> DisplayRewardList[Display reward list]
    DisplayRewardList --> RewardFiltering{Apply filters?}
    
    RewardFiltering -->|Yes| ShowRewardFilters[Show reward filters]
    RewardFiltering -->|No| RewardSelection{User selects reward?}
    
    ShowRewardFilters --> ApplyRewardFilters[Apply selected filters]
    ApplyRewardFilters --> RefreshRewardResults[Refresh reward results]
    RefreshRewardResults --> DisplayRewardList
    
    RewardSelection -->|Yes| NavigateToReward
    RewardSelection -->|No| ContinueSearching
    
    ContinueSearching --> UserInput
    NavigateToMerchant --> MerchantDetailFlow[Continue in merchant detail]
    NavigateToReward --> RewardDetailFlow[Continue in reward detail]
    
    ExecuteHistorySearch --> ExecuteSearch
    ShowPopularSearches --> PopularSelection{User selects popular search?}
    PopularSelection -->|Yes| ExecutePopularSearch[Execute popular search]
    PopularSelection -->|No| ShowSearchInterface
    
    ExecutePopularSearch --> ExecuteSearch
```

## API Endpoints and Business Logic

### Merchant Management APIs

#### 1. Get Merchant Detail
- **Endpoint**: `GET /merchants/{merchantCode}`
- **Response**: Complete merchant information including tabs, rewards, tier info
- **Purpose**: Display comprehensive merchant detail page

#### 2. Get Merchant Rewards
- **Endpoint**: `GET /merchants/{merchantCode}/rewards`
- **Parameters**: `{ page: number, size: number, category?: string, sortBy?: string }`
- **Response**: Paginated merchant reward catalog

#### 3. Get Merchant Tier Info
- **Endpoint**: `GET /merchants/{merchantCode}/tier/{userId}`
- **Response**: User's tier status and progress for specific merchant
- **Purpose**: Display tier-based benefits and requirements

#### 4. Get Store Locations
- **Endpoint**: `GET /merchants/{merchantCode}/stores`
- **Parameters**: `{ lat?: number, lng?: number, radius?: number }`
- **Response**: List of merchant store locations

#### 5. Get Earning Methods
- **Endpoint**: `GET /merchants/{merchantCode}/earning-methods`
- **Response**: Available earning methods and rates for merchant

### Search APIs

#### 1. Search All Content
- **Endpoint**: `GET /search`
- **Parameters**: 
  ```typescript
  {
    query: string,
    type?: 'all' | 'merchants' | 'rewards',
    page: number,
    size: number,
    filters?: {
      category?: string,
      priceRange?: { min: number, max: number },
      location?: string,
      availability?: boolean
    }
  }
  ```
- **Response**: Unified search results across all content types

#### 2. Get Search Suggestions
- **Endpoint**: `GET /search/suggestions`
- **Parameters**: `{ query?: string, type?: 'popular' | 'trending' }`
- **Response**: Search suggestions and auto-complete options

#### 3. Get Search History
- **Endpoint**: `GET /search/history`
- **Response**: User's search history
- **Purpose**: Enable quick re-search of previous queries

#### 4. Track Search Analytics
- **Endpoint**: `POST /search/analytics`
- **Payload**: 
  ```typescript
  {
    query: string,
    resultCount: number,
    selectedResult?: string,
    searchTime: number
  }
  ```

## Business Rules and Validations

### Merchant Tier System Rules

#### Tier Progression Logic
```typescript
interface TierRules {
  tierLevels: {
    name: string,
    requiredPoints: number,
    requiredTransactions?: number,
    requiredAmount?: number,
    benefits: string[],
    specialRewards?: any[]
  }[],
  progressTracking: {
    pointsEarned: number,
    transactionCount: number,
    totalSpent: number,
    currentTier: string,
    nextTier?: string,
    progressPercentage: number
  },
  tierMaintenance: {
    evaluationPeriod: number, // months
    degradationRules: boolean,
    gracePeriod: number // days
  }
}
```

#### Earning Rate Calculations
```typescript
interface EarningRules {
  baseRate: number,              // Base points per currency unit
  tierMultipliers: {             // Tier-based multipliers
    bronze: number,
    silver: number,
    gold: number,
    platinum: number
  },
  specialOffers: {               // Time-limited offers
    multiplier: number,
    startDate: string,
    endDate: string,
    conditions?: any
  }[],
  dailyLimits: {                 // Earning limits
    maxPoints: number,
    maxTransactions: number
  }
}
```

### Search System Rules

#### Search Query Processing
```typescript
interface SearchRules {
  queryValidation: {
    minLength: number,           // Minimum search query length
    maxLength: number,           // Maximum search query length
    allowedChars: RegExp,        // Allowed character pattern
    bannedWords: string[]        // Prohibited search terms
  },
  resultLimits: {
    maxResultsPerPage: number,
    maxTotalResults: number,
    timeoutDuration: number      // Search timeout in ms
  },
  relevanceScoring: {
    exactMatch: number,          // Score for exact matches
    partialMatch: number,        // Score for partial matches
    popularityBoost: number,     // Boost for popular items
    recencyBoost: number         // Boost for recent items
  }
}
```

#### Search Filtering Options
```typescript
interface SearchFilters {
  merchants: {
    categories: string[],        // Merchant categories
    location: {                  // Location-based filtering
      radius: number,
      coordinates: { lat: number, lng: number }
    },
    tierRequired?: string,       // Minimum tier requirement
    hasOnlineStore?: boolean     // Online store availability
  },
  rewards: {
    categories: string[],        // Reward categories
    priceRange: {               // Point price range
      min: number,
      max: number
    },
    availability: boolean,       // Currently available rewards
    expiryDate?: string         // Expiry date filtering
  }
}
```

## User Experience Patterns

### Merchant Detail Navigation
1. **Multi-tab Interface**: Organized information display
2. **Contextual Actions**: Tab-specific action buttons
3. **Progressive Loading**: Load tab content on demand
4. **Personalized Content**: User tier-based content display
5. **Quick Actions**: Direct access to common actions (scan, redeem, navigate)

### Search Experience Optimization
1. **Instant Search**: Real-time results as user types
2. **Smart Suggestions**: Context-aware auto-complete
3. **Visual Results**: Rich result display with images and ratings
4. **Filter Persistence**: Remember applied filters across sessions
5. **Search History**: Quick access to previous searches

### Tier System Engagement
1. **Progress Visualization**: Clear progress indicators
2. **Benefit Highlighting**: Emphasize tier benefits and rewards
3. **Upgrade Motivation**: Clear paths to next tier
4. **Achievement Recognition**: Celebrate tier upgrades
5. **Exclusive Content**: Tier-specific offers and rewards

## Analytics and Tracking

### Merchant Engagement Metrics
- **Tab Usage**: Which merchant detail tabs are most viewed
- **Action Completion**: Rates of reward redemptions, store visits
- **Tier Progression**: Speed and patterns of tier advancement
- **Store Locator Usage**: Geographic patterns of store searches
- **Online Store Conversion**: Online ordering from merchant pages

### Search Analytics
- **Query Performance**: Search success rates and result relevance
- **Popular Searches**: Trending and most frequent search terms
- **Filter Usage**: Which filters are most commonly applied
- **Result Interaction**: Click-through rates on search results
- **Search Abandonment**: Where users exit the search flow

### Business Intelligence
```typescript
interface MerchantAnalytics {
  engagement: {
    avgTimeOnMerchantPage: number,
    tabSwitchingPatterns: Record<string, number>,
    actionCompletionRates: Record<string, number>
  },
  tierSystemHealth: {
    tierDistribution: Record<string, number>,
    avgTimeToUpgrade: Record<string, number>,
    tierEngagementRates: Record<string, number>
  },
  rewardCatalogPerformance: {
    popularRewards: any[],
    conversionRatesByCategory: Record<string, number>,
    seasonalTrends: any[]
  }
}
```

## Error Handling and Performance

### Merchant Detail Errors
- **Loading Failures**: Graceful fallbacks with retry options
- **Tab Loading Issues**: Individual tab error handling
- **Store Location Errors**: GPS and mapping service fallbacks
- **Reward Unavailability**: Clear messaging and alternatives

### Search Performance Optimization
- **Query Debouncing**: Reduce API calls during typing
- **Result Caching**: Cache frequent search results
- **Progressive Loading**: Load results in batches
- **Offline Search**: Cached popular searches when offline

### Tier System Reliability
- **Progress Sync**: Ensure tier progress is accurately tracked
- **Benefit Availability**: Real-time validation of tier benefits
- **Data Consistency**: Consistent tier display across all screens
- **Rollback Protection**: Prevent accidental tier downgrades

---

*This documentation covers the merchant management and search systems that enable users to discover, engage with, and earn rewards from merchant partners in the TapTap mobile application.*