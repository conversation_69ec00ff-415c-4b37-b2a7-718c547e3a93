# Earning Systems Business Rules & Flow Documentation

## Overview
This document covers the core earning and engagement systems in the TapTap mobile application: Earn Points, Daily Check-in, Favorite Brands, and Currency Management. These systems work together to create a comprehensive user engagement and loyalty program.

## Systems Architecture

### 1. Earn System (`/scenes/earn`)
**Purpose**: Points earning through various methods and merchant interactions
**Key Components**:
- Main earn dashboard with merchant listings
- Earn method details and calculations
- VUI point earning mechanisms
- Merchant-specific earning opportunities

### 2. Daily Check-in System (`/scenes/dailyCheckin`)
**Purpose**: Daily engagement rewards and streak tracking
**Key Components**:
- Calendar-based check-in interface
- Streak tracking and bonus rewards
- Reminder system and notifications
- History and progress tracking

### 3. Favorite Brands System (`/scenes/favBrand`)
**Purpose**: User brand preferences and personalized experiences
**Key Components**:
- Brand selection during onboarding
- My favorite brands management
- Personalized recommendations

### 4. Currency System (`/scenes/currencies`)
**Purpose**: Brand-specific currency management beyond VUI points
**Key Components**:
- Multi-currency support
- Brand currency balances
- Currency exchange and management

## Earn System Flow

```mermaid
flowchart TD
    EarnEntry[User enters Earn section] --> LoadMethods[Load earning methods]
    
    LoadMethods --> ShowCategories[Show earning categories]
    ShowCategories --> CategoryChoice{User selects category}
    
    CategoryChoice -->|Merchants| ShowMerchants[Show merchant earning opportunities]
    CategoryChoice -->|Methods| ShowMethods[Show earning methods list]
    CategoryChoice -->|Scan| ShowScanInfo[Show scan earning info]
    
    ShowMerchants --> MerchantSelect{User selects merchant}
    MerchantSelect --> ShowMerchantDetail[Show merchant earning details]
    ShowMerchantDetail --> ShowEarnRates[Show point earning rates]
    ShowEarnRates --> UserAction{User action}
    
    UserAction -->|Visit Store| NavigateToMerchant[Navigate to merchant detail]
    UserAction -->|Scan Receipt| OpenScanner[Open receipt scanner]
    UserAction -->|View More| LoadMoreMerchants[Load more merchants]
    
    ShowMethods --> MethodSelect{User selects method}
    MethodSelect --> ShowMethodDetail[Show earning method details]
    ShowMethodDetail --> ShowCalculation[Show point calculation]
    ShowCalculation --> ShowInstructions[Show earning instructions]
    
    ShowScanInfo --> ScanInstructions[Show scanning instructions]
    ScanInstructions --> ScanTips[Show earning tips]
    
    NavigateToMerchant --> MerchantFlow[Continue in merchant flow]
    OpenScanner --> ScanFlow[Continue in scan flow]
    LoadMoreMerchants --> ShowMerchants
```

## Daily Check-in System Flow

```mermaid
flowchart TD
    CheckinEntry[User enters Daily Check-in] --> LoadProgress[Load user progress]
    
    LoadProgress --> CheckStatus{Check-in status today}
    CheckStatus -->|Already checked| ShowCompleted[Show completed state]
    CheckStatus -->|Available| ShowAvailable[Show check-in available]
    CheckStatus -->|Missed| ShowMissed[Show missed opportunity]
    
    ShowAvailable --> DisplayCalendar[Display calendar interface]
    DisplayCalendar --> ShowStreak[Show current streak]
    ShowStreak --> ShowRewards[Show today's reward]
    ShowRewards --> CheckinAction{User checks in}
    
    CheckinAction -->|Yes| ProcessCheckin[Process check-in]
    CheckinAction -->|No| ShowReminder[Show reminder options]
    
    ProcessCheckin --> APICall[Call check-in API]
    APICall --> CheckResult{Check-in successful}
    
    CheckResult -->|Success| ShowSuccess[Show success animation]
    CheckResult -->|Error| ShowError[Show error message]
    
    ShowSuccess --> UpdateStreak[Update streak counter]
    UpdateStreak --> CheckBonus{Streak bonus available}
    
    CheckBonus -->|Yes| ShowBonusReward[Show bonus reward popup]
    CheckBonus -->|No| UpdateCalendar[Update calendar display]
    
    ShowBonusReward --> UpdateCalendar
    UpdateCalendar --> ShowNewProgress[Show updated progress]
    
    ShowCompleted --> ShowHistory[Show check-in history]
    ShowHistory --> HistoryDetails[Show detailed history]
    
    ShowMissed --> ExplainMissed[Explain missed opportunity]
    ExplainMissed --> ShowReminder
    
    ShowReminder --> ReminderSetup{User sets reminder}
    ReminderSetup -->|Yes| SetReminder[Set notification reminder]
    ReminderSetup -->|No| EndFlow[End flow]
    
    SetReminder --> ConfirmReminder[Confirm reminder set]
    ConfirmReminder --> EndFlow
    
    ShowError --> RetryOption[Show retry option]
    RetryOption --> CheckinAction
```

## Favorite Brands System Flow

```mermaid
flowchart TD
    FavBrandEntry[Favorite Brands Entry] --> CheckContext{Entry context}
    
    CheckContext -->|Onboarding| ShowOnboarding[Show brand selection onboarding]
    CheckContext -->|Management| ShowMyFavBrands[Show my favorite brands]
    CheckContext -->|Update| ShowBrandUpdate[Show brand update interface]
    
    ShowOnboarding --> LoadBrandCollections[Load brand collections]
    LoadBrandCollections --> DisplayBrands[Display brand grid]
    DisplayBrands --> BrandSelection{User selects brands}
    
    BrandSelection --> ValidateSelection{Minimum brands selected}
    ValidateSelection -->|No| ShowMinRequirement[Show minimum selection requirement]
    ValidateSelection -->|Yes| SaveFavBrands[Save favorite brands]
    
    ShowMinRequirement --> DisplayBrands
    SaveFavBrands --> APICallSave[Call save favorite brands API]
    APICallSave --> SaveResult{Save successful}
    
    SaveResult -->|Success| ShowSuccessOnboard[Show success message]
    SaveResult -->|Error| ShowSaveError[Show save error]
    
    ShowSuccessOnboard --> CompleteOnboarding[Complete onboarding flow]
    ShowSaveError --> DisplayBrands
    
    ShowMyFavBrands --> LoadUserFavBrands[Load user's favorite brands]
    LoadUserFavBrands --> DisplayFavList[Display favorite brands list]
    DisplayFavList --> FavBrandActions{User actions}
    
    FavBrandActions -->|Remove Brand| RemoveFavBrand[Remove from favorites]
    FavBrandActions -->|Add More| ShowAddMore[Show add more brands]
    FavBrandActions -->|View Brand| NavigateToBrand[Navigate to brand detail]
    
    RemoveFavBrand --> ConfirmRemove{Confirm removal}
    ConfirmRemove -->|Yes| UpdateFavorites[Update favorites list]
    ConfirmRemove -->|No| DisplayFavList
    
    ShowAddMore --> DisplayBrands
    UpdateFavorites --> RefreshList[Refresh favorites list]
    RefreshList --> DisplayFavList
    
    NavigateToBrand --> BrandDetailFlow[Continue in brand detail]
```

## API Endpoints and Business Logic

### Earn System APIs

#### 1. Get Earning Methods
- **Endpoint**: `GET /earn/methods`
- **Response**: List of available earning methods
- **Purpose**: Display earning opportunities

#### 2. Get Merchant Earning Info
- **Endpoint**: `GET /earn/merchants`
- **Parameters**: `{ category?: string, page: number, size: number }`
- **Response**: Paginated merchant earning data

#### 3. Get Method Details
- **Endpoint**: `GET /earn/methods/{methodId}`
- **Response**: Detailed earning method information
- **Purpose**: Show earning calculations and instructions

### Daily Check-in APIs

#### 1. Get Check-in Progress
- **Endpoint**: `GET /daily-checkin/progress`
- **Response**: User's check-in history and current streak
- **Purpose**: Display calendar and progress

#### 2. Perform Check-in
- **Endpoint**: `POST /daily-checkin/checkin`
- **Payload**: `{ date: string }`
- **Response**: Check-in result and rewards

#### 3. Get Check-in History
- **Endpoint**: `GET /daily-checkin/history`
- **Parameters**: `{ month?: string, year?: string }`
- **Response**: Historical check-in data

### Favorite Brands APIs

#### 1. Get Brand Collections
- **Endpoint**: `GET /brands/collections`
- **Response**: Available brand categories and merchants
- **Purpose**: Brand selection interface

#### 2. Save Favorite Brands
- **Endpoint**: `POST /user/favorite-brands`
- **Payload**: `{ brandCodes: string[] }`
- **Purpose**: Save user's brand preferences

#### 3. Get User Favorite Brands
- **Endpoint**: `GET /user/favorite-brands`
- **Response**: User's current favorite brands
- **Purpose**: Display and manage favorites

## Business Rules and Conditional Logic

### Daily Check-in Rules

#### Streak Calculation
```typescript
interface CheckinStreak {
  currentStreak: number,      // Consecutive days
  maxStreak: number,         // Personal best
  lastCheckinDate: string,   // Last check-in date
  streakBonusThreshold: number // Days for bonus (e.g., 7, 30)
}
```

#### Check-in Availability
- **Available**: Current date, user hasn't checked in yet
- **Completed**: Current date, user already checked in
- **Missed**: Past date, user didn't check in
- **Future**: Future dates are disabled

#### Reward Structure
```typescript
interface CheckinReward {
  day: number,              // Day of streak
  vuiPoints: number,       // Base VUI points
  bonusPoints?: number,    // Bonus for milestones
  specialReward?: {        // Special rewards for long streaks
    type: 'VOUCHER' | 'BONUS_POINTS',
    value: any
  }
}
```

### Earning System Rules

#### Point Calculation
Different earning methods have different calculation rules:
- **Receipt Scanning**: Points per transaction amount
- **Store Visits**: Fixed points per visit
- **Brand Interactions**: Points per action
- **Referrals**: Points per successful referral

#### Earning Limits
```typescript
interface EarningLimits {
  dailyLimit: number,        // Max points per day
  monthlyLimit: number,      // Max points per month
  perTransactionLimit: number, // Max points per transaction
  cooldownPeriod: number     // Minutes between earnings
}
```

### Favorite Brands Rules

#### Selection Requirements
- **Minimum Selection**: Usually 3-5 brands during onboarding
- **Maximum Selection**: Usually 10-15 brands to maintain relevance
- **Update Frequency**: May have limits on how often users can change

#### Personalization Effects
Favorite brands affect:
- **Reward Recommendations**: Preferred brand rewards shown first
- **Earning Opportunities**: Brand-specific earning methods highlighted
- **Content Feed**: Relevant brand content prioritized
- **Notifications**: Brand-specific promotions and updates

## User Experience Patterns

### Onboarding Flow (New Users)
1. **Welcome to Earning**: Explain point system basics
2. **Choose Favorite Brands**: Select preferred brands
3. **Daily Check-in Introduction**: Explain streak benefits
4. **First Check-in**: Guide through first daily check-in
5. **Earning Method Tour**: Show different ways to earn

### Returning User Flow
1. **Check Daily Status**: Show check-in availability
2. **Display Earning Opportunities**: Show relevant merchants/methods
3. **Progress Updates**: Show streak status and achievements
4. **Recommendations**: Suggest relevant earning activities

### Engagement Strategies
- **Streak Protection**: Warnings before losing streaks
- **Bonus Notifications**: Special rewards for milestones
- **Personalized Offers**: Brand-specific earning opportunities
- **Social Elements**: Friend comparisons and achievements

## Analytics and Tracking

### Key Metrics Tracked
- **Daily Check-in Rate**: Percentage of users checking in daily
- **Streak Distribution**: Analysis of streak lengths
- **Earning Method Usage**: Which methods are most popular
- **Brand Preference Changes**: How favorite brands evolve
- **Point Earning Patterns**: When and how users earn points

### Tracking Events
```typescript
// Daily check-in events
trackDailyCheckin().checkinSuccess()
trackDailyCheckin().streakMilestone({ streak: number })
trackDailyCheckin().reminderSet()

// Earning events
trackEarn().methodSelected({ method: string })
trackEarn().merchantEarning({ merchantCode: string })
trackEarn().pointsEarned({ amount: number, method: string })

// Favorite brands events
trackFavBrands().brandSelected({ brandCode: string })
trackFavBrands().preferencesUpdated()
```

## Testing Considerations

### Key Test Scenarios
1. **Daily Check-in Edge Cases**:
   - Timezone changes during travel
   - Check-in at exactly midnight
   - Network failures during check-in
   - Streak recovery after missed days

2. **Earning System Validations**:
   - Point calculation accuracy
   - Daily/monthly limit enforcement
   - Duplicate earning prevention
   - Fraud detection for earning methods

3. **Favorite Brands Management**:
   - Minimum/maximum selection validation
   - Brand availability changes
   - Personalization accuracy
   - Cross-device synchronization

### Performance Considerations
- **Calendar Rendering**: Efficient rendering for large date ranges
- **Point Calculations**: Real-time calculation performance
- **Brand Loading**: Lazy loading for large brand catalogs
- **Analytics Batching**: Efficient event tracking

---

*This documentation covers the core earning and engagement systems that drive user loyalty and interaction in the TapTap mobile application.*