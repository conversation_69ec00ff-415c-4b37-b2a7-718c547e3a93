# Authentication & Transaction Systems Business Rules & Flow Documentation

## Overview
This document covers the Authentication, QR Scanning, and Voucher Transfer systems in the TapTap mobile application. These systems handle user identity, transaction processing, and peer-to-peer voucher sharing.

## Systems Architecture

### 1. Authentication System (`/scenes/auth`)
**Purpose**: User registration, sign-in, and identity verification
**Key Components**:
- Phone number verification
- OTP (One-Time Password) validation  
- Password setup and sign-in
- User registration with personal details
- Intro/onboarding flow
- Bonus code system

### 2. QR Code System (`/scenes/qr`)
**Purpose**: QR code scanning for transactions and rewards
**Key Components**:
- QR code scanner interface
- Transaction processing via QR
- Gift/reward claiming
- Receipt scanning for points

### 3. Voucher Transfer System (`/scenes/voucherTransfer`)
**Purpose**: Peer-to-peer voucher sharing and gifting
**Key Components**:
- Contact selection for transfers
- Transfer confirmation and processing
- Transaction result tracking
- Transfer history management

## Authentication Flow

```mermaid
flowchart TD
    AuthStart([User starts authentication]) --> CheckUserState{Existing user?}
    
    CheckUserState -->|New User| StartRegistration[Start registration flow]
    CheckUserState -->|Existing User| StartSignIn[Start sign-in flow]
    CheckUserState -->|Unknown| PhoneInput[Show phone input screen]
    
    PhoneInput --> ValidatePhone{Valid phone number?}
    ValidatePhone -->|No| ShowPhoneError[Show phone validation error]
    ValidatePhone -->|Yes| SendOTP[Send OTP to phone]
    
    ShowPhoneError --> PhoneInput
    SendOTP --> OTPScreen[Show OTP input screen]
    
    OTPScreen --> ValidateOTP{OTP validation}
    ValidateOTP -->|Invalid| ShowOTPError[Show OTP error]
    ValidateOTP -->|Valid| CheckAccount{Account exists?}
    
    ShowOTPError --> OTPScreen
    CheckAccount -->|No| StartRegistration
    CheckAccount -->|Yes| CheckPassword{Has password?}
    
    CheckPassword -->|No| SetupPassword[Show password setup]
    CheckPassword -->|Yes| PasswordSignIn[Show password sign-in]
    
    StartRegistration --> PersonalDetails[Collect personal details]
    PersonalDetails --> DatePicker[Date of birth selection]
    DatePicker --> CreatePassword[Create password]
    CreatePassword --> BonusCode[Optional bonus code entry]
    
    BonusCode --> ValidateBonus{Bonus code valid?}
    ValidateBonus -->|Invalid| ShowBonusError[Show bonus code error]
    ValidateBonus -->|Valid/Skip| CompleteRegistration[Complete registration]
    
    ShowBonusError --> BonusCode
    CompleteRegistration --> OnboardingFlow[Start onboarding]
    
    SetupPassword --> CompleteSetup[Complete password setup]
    PasswordSignIn --> ValidatePassword{Password correct?}
    
    ValidatePassword -->|No| ShowPasswordError[Show password error]
    ValidatePassword -->|Yes| SignInSuccess[Sign-in successful]
    
    ShowPasswordError --> PasswordSignIn
    CompleteSetup --> SignInSuccess
    SignInSuccess --> MainApp[Navigate to main app]
    
    OnboardingFlow --> IntroScreens[Show intro screens]
    IntroScreens --> FavBrandSelection[Select favorite brands]
    FavBrandSelection --> MainApp
```

## QR Code Scanning Flow

```mermaid
flowchart TD
    QRStart([User opens QR scanner]) --> RequestPermission[Request camera permission]
    
    RequestPermission --> CheckPermission{Permission granted?}
    CheckPermission -->|No| ShowPermissionError[Show permission error]
    CheckPermission -->|Yes| StartCamera[Start camera]
    
    ShowPermissionError --> RetryPermission[Retry permission request]
    RetryPermission --> RequestPermission
    
    StartCamera --> ShowScanner[Show QR scanner interface]
    ShowScanner --> ScanQR{QR code detected?}
    
    ScanQR -->|No| ContinueScanning[Continue scanning]
    ScanQR -->|Yes| ProcessQR[Process QR code]
    
    ProcessQR --> ValidateQR{Valid QR format?}
    ValidateQR -->|No| ShowInvalidQR[Show invalid QR error]
    ValidateQR -->|Yes| DetermineType{QR code type?}
    
    ShowInvalidQR --> ContinueScanning
    
    DetermineType -->|Receipt| ProcessReceipt[Process receipt scan]
    DetermineType -->|Gift| ProcessGift[Process gift claim]
    DetermineType -->|Payment| ProcessPayment[Process payment QR]
    DetermineType -->|Store| ProcessStore[Process store check-in]
    
    ProcessReceipt --> ValidateReceipt{Valid receipt?}
    ValidateReceipt -->|No| ShowReceiptError[Show receipt error]
    ValidateReceipt -->|Yes| CalculatePoints[Calculate earning points]
    
    CalculatePoints --> ShowPointsEarned[Show points earned]
    ShowPointsEarned --> UpdateBalance[Update user balance]
    
    ProcessGift --> ValidateGift{Valid gift code?}
    ValidateGift -->|No| ShowGiftError[Show gift error]
    ValidateGift -->|Yes| ClaimGift[Claim gift/reward]
    
    ClaimGift --> ShowGiftResult[Show claim result]
    ShowGiftResult --> UpdateInventory[Update user rewards]
    
    ProcessPayment --> ValidatePayment{Valid payment QR?}
    ValidatePayment -->|No| ShowPaymentError[Show payment error]
    ValidatePayment -->|Yes| ProcessTransaction[Process payment transaction]
    
    ProcessTransaction --> ShowTransactionResult[Show transaction result]
    
    ProcessStore --> ValidateStore{Valid store QR?}
    ValidateStore -->|No| ShowStoreError[Show store error]
    ValidateStore -->|Yes| CheckInStore[Check in to store]
    
    CheckInStore --> ShowCheckInResult[Show check-in result]
    ShowCheckInResult --> AwardVisitPoints[Award visit points]
    
    UpdateBalance --> ScanSuccess[Scan successful]
    UpdateInventory --> ScanSuccess
    ShowTransactionResult --> ScanSuccess
    AwardVisitPoints --> ScanSuccess
    
    ScanSuccess --> ContinueOrExit{Continue scanning?}
    ContinueOrExit -->|Yes| ContinueScanning
    ContinueOrExit -->|No| ExitScanner[Exit scanner]
    
    ShowReceiptError --> ContinueScanning
    ShowGiftError --> ContinueScanning
    ShowPaymentError --> ContinueScanning
    ShowStoreError --> ContinueScanning
```

## Voucher Transfer Flow

```mermaid
flowchart TD
    TransferStart([User initiates voucher transfer]) --> SelectVoucher[Select voucher to transfer]
    
    SelectVoucher --> ValidateTransfer{Transfer allowed?}
    ValidateTransfer -->|No| ShowTransferError[Show transfer restriction]
    ValidateTransfer -->|Yes| ContactSelection[Show contact selection]
    
    ShowTransferError --> EndTransfer[End transfer flow]
    
    ContactSelection --> ContactOptions{Contact source?}
    ContactOptions -->|Recent| ShowRecentContacts[Show recent transfer contacts]
    ContactOptions -->|Phone| ManualPhoneInput[Manual phone input]
    ContactOptions -->|Search| SearchContacts[Search contact by phone]
    
    ShowRecentContacts --> SelectRecent{Contact selected?}
    SelectRecent -->|No| ContactOptions
    SelectRecent -->|Yes| ValidateRecipient[Validate recipient]
    
    ManualPhoneInput --> ValidatePhone{Valid phone format?}
    ValidatePhone -->|No| ShowPhoneError[Show phone error]
    ValidatePhone -->|Yes| ValidateRecipient
    
    ShowPhoneError --> ManualPhoneInput
    
    SearchContacts --> SearchResult{Contact found?}
    SearchResult -->|No| ShowSearchError[Show not found error]
    SearchResult -->|Yes| ValidateRecipient
    
    ShowSearchError --> ContactSelection
    
    ValidateRecipient --> CheckRecipient{Recipient valid?}
    CheckRecipient -->|No| ShowRecipientError[Show recipient error]
    CheckRecipient -->|Self Transfer| ShowSelfError[Show self-transfer error]
    CheckRecipient -->|Valid| ShowTransferConfirm[Show transfer confirmation]
    
    ShowRecipientError --> ContactSelection
    ShowSelfError --> ContactSelection
    
    ShowTransferConfirm --> TransferDetails[Show transfer details]
    TransferDetails --> AddMessage[Optional message input]
    AddMessage --> ConfirmTransfer{User confirms?}
    
    ConfirmTransfer -->|No| ContactSelection
    ConfirmTransfer -->|Yes| ProcessTransfer[Process transfer]
    
    ProcessTransfer --> CheckLimits{Within transfer limits?}
    CheckLimits -->|No| ShowLimitError[Show transfer limit error]
    CheckLimits -->|Yes| ExecuteTransfer[Execute transfer API]
    
    ShowLimitError --> ShowTransferConfirm
    
    ExecuteTransfer --> TransferResult{Transfer successful?}
    TransferResult -->|No| ShowTransferAPIError[Show transfer error]
    TransferResult -->|Yes| TransferSuccess[Transfer completed]
    
    ShowTransferAPIError --> ShowTransferConfirm
    
    TransferSuccess --> UpdateVoucherStatus[Update voucher status]
    UpdateVoucherStatus --> SendNotification[Send notification to recipient]
    SendNotification --> ShowSuccessResult[Show success screen]
    ShowSuccessResult --> AddToRecentContacts[Add to recent contacts]
    AddToRecentContacts --> EndTransferSuccess[End successful transfer]
```

## API Endpoints and Business Logic

### Authentication APIs

#### 1. Send OTP
- **Endpoint**: `POST /auth/otp/send`
- **Payload**: `{ phoneNumber: string, countryCode: string }`
- **Purpose**: Send verification code to phone

#### 2. Verify OTP
- **Endpoint**: `POST /auth/otp/verify`
- **Payload**: `{ phoneNumber: string, otpCode: string }`
- **Purpose**: Validate OTP and check account status

#### 3. Register User
- **Endpoint**: `POST /auth/register`
- **Payload**: 
  ```typescript
  {
    phoneNumber: string,
    password: string,
    fullName: string,
    dateOfBirth: string,
    gender: 'male' | 'female' | 'other',
    bonusCode?: string
  }
  ```

#### 4. Sign In
- **Endpoint**: `POST /auth/signin`
- **Payload**: `{ phoneNumber: string, password: string }`
- **Response**: Authentication tokens and user profile

### QR Scanning APIs

#### 1. Process Receipt QR
- **Endpoint**: `POST /qr/receipt`
- **Payload**: `{ qrData: string, storeCode?: string }`
- **Response**: Points earned and transaction details

#### 2. Claim Gift QR
- **Endpoint**: `POST /qr/gift/claim`
- **Payload**: `{ giftCode: string }`
- **Response**: Gift details and claim result

#### 3. Store Check-in QR
- **Endpoint**: `POST /qr/store/checkin`
- **Payload**: `{ storeCode: string, location?: { lat: number, lng: number } }`
- **Response**: Check-in result and visit points

### Voucher Transfer APIs

#### 1. Validate Transfer
- **Endpoint**: `GET /voucher-transfer/validate`
- **Parameters**: `{ voucherCodeId: string, recipientPhone: string }`
- **Purpose**: Check transfer eligibility and limits

#### 2. Execute Transfer
- **Endpoint**: `POST /voucher-transfer/transfer`
- **Payload**:
  ```typescript
  {
    voucherCodeId: string,
    recipientPhone: string,
    message?: string,
    transferPin?: string
  }
  ```

#### 3. Get Transfer History
- **Endpoint**: `GET /voucher-transfer/history`
- **Parameters**: `{ page: number, size: number }`
- **Response**: Transfer history with sent/received vouchers

## Business Rules and Validations

### Authentication Rules

#### Phone Number Validation
```typescript
interface PhoneValidation {
  countryCode: string,    // +84, +1, etc.
  phoneNumber: string,    // Local format
  minLength: number,      // Minimum digits
  maxLength: number,      // Maximum digits
  allowedCountries: string[] // Supported country codes
}
```

#### Password Requirements
- **Minimum Length**: 8 characters
- **Character Requirements**: Letters and numbers
- **Complexity**: May require special characters
- **History**: Cannot reuse last N passwords

#### OTP Rules
- **Validity Period**: 5-10 minutes
- **Maximum Attempts**: 3-5 attempts before lockout
- **Rate Limiting**: Maximum requests per hour
- **Resend Cooldown**: 60-90 seconds between resends

### QR Code Processing Rules

#### Receipt Validation
```typescript
interface ReceiptRules {
  minAmount: number,        // Minimum transaction amount
  maxAmount: number,        // Maximum transaction amount
  allowedStores: string[],  // Valid store codes
  timeWindow: number,       // Hours after purchase valid
  duplicateCheck: boolean   // Prevent duplicate scans
}
```

#### Point Calculation
- **Base Rate**: Points per currency unit (e.g., 1 point per $1)
- **Multipliers**: Special rates for certain merchants
- **Bonuses**: Extra points for first-time visits
- **Caps**: Daily/monthly earning limits

### Voucher Transfer Rules

#### Transfer Eligibility
```typescript
interface TransferRules {
  allowedVoucherTypes: string[],     // Which vouchers can be transferred
  dailyTransferLimit: number,        // Max transfers per day
  recipientLimit: number,            // Max recipients per voucher
  coolingPeriod: number,            // Hours between transfers
  selfTransferAllowed: boolean       // Allow self-transfers
}
```

#### Recipient Validation
- **Account Status**: Recipient must have active account
- **Geographic Restrictions**: May be limited by region
- **Age Restrictions**: Some vouchers may have age limits
- **Previous Relationship**: May track transfer relationships

## Security Features

### Authentication Security
- **Device Fingerprinting**: Track device characteristics
- **Fraud Detection**: Monitor suspicious sign-in patterns
- **Session Management**: Secure token handling
- **Two-Factor Authentication**: Optional 2FA for enhanced security

### QR Security
- **Code Validation**: Cryptographic verification
- **Timestamp Checks**: Prevent replay attacks
- **Location Verification**: GPS validation for store check-ins
- **Rate Limiting**: Prevent abuse of scanning features

### Transfer Security
- **Transfer Pins**: Optional PIN for high-value transfers
- **Notification System**: Alerts for all transfer activities
- **Audit Trail**: Complete transfer history tracking
- **Fraud Monitoring**: Detect unusual transfer patterns

## Error Handling and Recovery

### Authentication Errors
- **Invalid Credentials**: Clear error messages and recovery options
- **Account Locked**: Temporary lockout with recovery instructions
- **Network Issues**: Retry mechanisms and offline handling
- **Server Errors**: Graceful degradation and retry logic

### QR Scanning Errors
- **Camera Issues**: Permission handling and troubleshooting
- **Invalid Codes**: Clear feedback on QR format issues
- **Processing Failures**: Retry options and alternative methods
- **Network Problems**: Queue scans for later processing

### Transfer Errors
- **Recipient Not Found**: Search suggestions and alternatives
- **Transfer Limits**: Clear explanation of limits and reset times
- **Payment Failures**: Retry mechanisms and alternative payment methods
- **System Errors**: Transaction rollback and notification systems

## Analytics and Tracking

### Authentication Metrics
- **Registration Completion Rate**: Percentage who complete full registration
- **Sign-in Success Rate**: Authentication success metrics
- **OTP Delivery Rate**: SMS/call delivery success
- **Onboarding Drop-off**: Where users leave the flow

### QR Usage Metrics
- **Scan Success Rate**: Successful scans vs. attempts
- **QR Type Distribution**: Which QR types are most used
- **Point Earning Patterns**: When and where users earn points
- **Error Rates**: Common scanning issues

### Transfer Metrics
- **Transfer Success Rate**: Completed vs. initiated transfers
- **Popular Recipients**: Transfer relationship patterns
- **Voucher Type Preferences**: Which vouchers are transferred most
- **Geographic Patterns**: Transfer patterns by location

---

*This documentation covers the authentication, scanning, and transfer systems that enable secure user access and transaction processing in the TapTap mobile application.*