# InboxList Business Rules & Flow Documentation

## Overview
The InboxList screen provides a two-tab interface for managing user inbox messages. It includes a History tab for personal messages with unread count tracking, and a Campaign tab for promotional content. The component features batch operations, analytics tracking, and lazy loading for optimal performance.

## Component Location
- **Main Component**: `taptap-mobile/src/scenes/inbox/screens/InboxList/index.tsx`
- **Tab Components**:
  - `TabHistory.tsx` - Personal message history
  - `TabCampaign.tsx` - Campaign/promotional messages
- **Supporting Components**:
  - `EmptyState.tsx` - Empty state display
- **APIs**: `taptap-mobile/src/scenes/inbox/redux/api.ts`

## Architecture

### Tab-Based Navigation System
- **React Native Tab View**: Swipeable tab interface
- **Lazy Loading**: Tabs load content only when first accessed
- **State Management**: Redux integration for unread counts and message states

## Core Data Types

### RouteTabTop
```typescript
interface RouteTabTop {
  key: 'history' | 'campaign',
  title: string,
  type?: 'NEW',
  counter?: number // Unread count for History tab
}
```

### InboxListParams
```typescript
interface InboxListParams {
  activeIndex?: number // 0 for History, 1 for Campaign
}
```

## Main Application Flow

```mermaid
flowchart TD
    Start([User enters InboxList]) --> CheckParams{activeIndex param?}
    
    CheckParams -->|Has activeIndex| SetInitialTab[Set initial tab to activeIndex]
    CheckParams -->|No activeIndex| DefaultHistory[Default to History tab (0)]
    
    SetInitialTab --> LoadTabs[Initialize tab structure]
    DefaultHistory --> LoadTabs
    
    LoadTabs --> LoadUnreadCount[Load total unread inbox count from Redux]
    LoadUnreadCount --> SetupTabs[Setup tab routes with unread counter]
    
    SetupTabs --> RenderTabView[Render TabView with lazy loading]
    RenderTabView --> UserInteraction{User interaction?}
    
    UserInteraction -->|Tab Switch| HandleTabChange[Handle tab index change]
    UserInteraction -->|Mark Read All| HandleMarkReadAll[Handle mark all as read]
    UserInteraction -->|Message Selection| HandleMessageAction[Handle message interaction]
    
    HandleTabChange --> TrackTabSwitch{Which tab?}
    TrackTabSwitch -->|History (0)| TrackHistory[Track: listTabHistory()]
    TrackTabSwitch -->|Campaign (1)| TrackCampaign[Track: listTabCampaign()]
    
    TrackHistory --> UpdateTabIndex[Update active tab index]
    TrackCampaign --> UpdateTabIndex
    
    HandleMarkReadAll --> CheckUnreadCount{totalUnreadInbox > 0?}
    CheckUnreadCount -->|No| DisableButton[Keep Mark All button disabled]
    CheckUnreadCount -->|Yes| ShowConfirmDialog[Show confirmation dialog]
    
    ShowConfirmDialog --> UserConfirms{User confirms?}
    UserConfirms -->|No| CancelMarkAll[Cancel mark all action]
    UserConfirms -->|Yes| DispatchMarkReadAll[Dispatch markReadAll Redux action]
    
    DispatchMarkReadAll --> APICall[Call mark read all API]
    APICall --> APISuccess{API success?}
    
    APISuccess -->|Yes| ShowSuccessToast[Show success toast]
    APISuccess -->|No| ShowErrorToast[Show error toast]
    
    ShowSuccessToast --> RefreshUnreadCount[Refresh unread count in Redux]
    RefreshUnreadCount --> UpdateTabBadges[Update tab badges/counters]
    
    UpdateTabIndex --> RenderTabView
    UpdateTabBadges --> RenderTabView
    DisableButton --> RenderTabView
    CancelMarkAll --> RenderTabView
```

## Tab Management System

```mermaid
flowchart TD
    TabSystem[Tab Management] --> TabSetup[Setup Tab Routes]
    
    TabSetup --> HistoryTab[History Tab Setup]
    TabSetup --> CampaignTab[Campaign Tab Setup]
    
    HistoryTab --> HistoryConfig[Configure History Tab]
    HistoryConfig --> AddUnreadBadge[Add unread counter badge]
    HistoryConfig --> SetTabType[Set type: 'NEW']
    
    CampaignTab --> CampaignConfig[Configure Campaign Tab]
    CampaignConfig --> NoCounterBadge[No counter badge]
    
    AddUnreadBadge --> TabViewRender[Render TabView]
    NoCounterBadge --> TabViewRender
    
    TabViewRender --> LazyLoading{Tab accessed for first time?}
    LazyLoading -->|Yes| LoadTabContent[Load tab content]
    LazyLoading -->|No| ShowPlaceholder[Show lazy placeholder]
    
    LoadTabContent --> TabHistory[Render TabHistory component]
    LoadTabContent --> TabCampaign[Render TabCampaign component]
    
    ShowPlaceholder --> EmptyPlaceholder[Show loading placeholder view]
    
    TabHistory --> HistoryFeatures[History Tab Features]
    HistoryFeatures --> MessageList[Display message list]
    HistoryFeatures --> UnreadTracking[Track unread status]
    HistoryFeatures --> MessageActions[Message actions (read, delete, etc.)]
    
    TabCampaign --> CampaignFeatures[Campaign Tab Features]
    CampaignFeatures --> PromotionalContent[Display promotional messages]
    CampaignFeatures --> CampaignActions[Campaign-specific actions]
```

## Mark All as Read Flow

```mermaid
flowchart TD
    MarkAllFlow[Mark All as Read Flow] --> CheckPrerequisites{Prerequisites check}
    
    CheckPrerequisites --> UnreadCountCheck{totalUnreadInbox > 0?}
    UnreadCountCheck -->|No| ButtonDisabled[Button disabled (gray icon)]
    UnreadCountCheck -->|Yes| ButtonEnabled[Button enabled (pink icon)]
    
    ButtonEnabled --> UserClicksMarkAll{User clicks mark all button?}
    UserClicksMarkAll -->|No| WaitUserAction[Wait for user action]
    UserClicksMarkAll -->|Yes| ShowConfirmationDialog[Show confirmation dialog]
    
    ShowConfirmationDialog --> DialogContent[Display confirmation message]
    DialogContent --> DialogActions[Show Cancel and OK buttons]
    
    DialogActions --> UserChoice{User choice?}
    UserChoice -->|Cancel| HideDialog[Hide dialog - no action]
    UserChoice -->|OK| ProceedMarkAll[Proceed with mark all]
    
    ProceedMarkAll --> ReduxDispatch[Dispatch inboxActions.markReadAll()]
    ReduxDispatch --> APICallMarkAll[API: PUT /inbox/mark-read-all]
    
    APICallMarkAll --> APIResult{API result?}
    APIResult -->|Success| SuccessFlow[Success flow]
    APIResult -->|Error| ErrorFlow[Error flow]
    
    SuccessFlow --> ShowSuccessToast[Show success toast: 'All messages marked as read']
    ShowSuccessToast --> UpdateReduxState[Update Redux: clear unread counts]
    UpdateReduxState --> RefreshTabBadges[Refresh tab badges (remove counter)]
    RefreshTabBadges --> DisableMarkAllButton[Disable mark all button]
    
    ErrorFlow --> ShowErrorToast[Show error toast]
    ShowErrorToast --> KeepCurrentState[Keep current state unchanged]
    
    HideDialog --> WaitUserAction
    ButtonDisabled --> WaitUserAction
    DisableMarkAllButton --> WaitUserAction
    KeepCurrentState --> WaitUserAction
```

## Redux Integration

### State Dependencies
```typescript
// Selectors used
const totalUnreadInbox = useAppSelector(inboxSelectors.selectTotalUnreadInbox);

// Actions dispatched  
dispatch(inboxActions.markReadAll({ callback }));
```

### Unread Count Management
- **Source**: Redux store via `inboxSelectors.selectTotalUnreadInbox`
- **Updates**: Automatically reflects in tab badge
- **Reset**: On successful mark all as read operation

## API Endpoints and Variables

### Primary APIs

#### 1. Mark All as Read
- **Endpoint**: `PUT /inbox/mark-read-all`
- **Purpose**: Mark all unread messages as read
- **Response**: Success/error status
- **Integration**: Called via Redux action

#### 2. Get Total Unread Count
- **Endpoint**: `GET /inbox/history?action=GET_UNSEEN_NUMBER`
- **Response**: `number` (total unread count)
- **Purpose**: Update tab badges and button states

#### 3. Tab Content APIs
Each tab component handles its own API calls:
- **History Tab**: Fetches personal message history
- **Campaign Tab**: Fetches promotional campaign messages

## UI Components & Styling

### Header Configuration
```typescript
<Header
  title={l10n.inbox.inbox}
  renderRight={index ? undefined : renderBtnRight} // Only show on History tab
/>
```

### Tab Configuration
```typescript
const routes: RouteTabTop[] = [
  {
    key: 'history',
    title: l10n.inbox.history,
    type: 'NEW',
    counter: totalUnreadInbox // Dynamic unread count
  },
  { 
    key: 'campaign', 
    title: l10n.inbox.campaign 
  }
];
```

### Mark All Button States
```typescript
// Button appearance based on unread count
<Icon24px.OutlineDoubleCheck
  fill={totalUnreadInbox ? COLORS.primaryPink : COLORS.grey1}
/>

// Button enabled state
disabled={totalUnreadInbox === 0}
```

## Conditional Rendering Rules

### 1. Mark All as Read Button
**Shows on**: History tab only (`index === 0`)
**Hidden on**: Campaign tab (`index === 1`)

**States**:
- **Enabled**: `totalUnreadInbox > 0` (pink icon)
- **Disabled**: `totalUnreadInbox === 0` (gray icon)

### 2. Tab Badges
**History tab**: Shows unread count if `totalUnreadInbox > 0`
**Campaign tab**: No badge/counter

### 3. Lazy Loading Placeholder
**Shows when**: Tab accessed for first time before content loads
**Content**: Empty placeholder view with loading state

## User Actions & Navigation

### Primary Actions
1. **Tab Switching**: Swipe or tap to change tabs
2. **Mark All as Read**: Bulk action for unread messages
3. **Message Interactions**: Delegated to individual tab components

### Navigation Patterns
- **Default Entry**: History tab (index 0)
- **Deep Link Entry**: Specified tab via `activeIndex` parameter
- **Cross-Tab**: Maintains state across tab switches

## Analytics/Tracking Integration

### Key Events (via TrackingManagerV3)
```typescript
// Tab switching events
TrackingManagerV3.getInstance()?.trackInbox().listTabHistory();
TrackingManagerV3.getInstance()?.trackInbox().listTabCampaign();
```

### Tracking Triggers
- **History Tab**: When user switches to index 0
- **Campaign Tab**: When user switches to index 1
- **Mark All Action**: Tracked within Redux action

## Performance Optimizations

### Lazy Loading
- **Initial Load**: Only active tab content loads
- **Tab Switch**: Content loads on first access
- **Memory**: Unused tabs remain lightweight

### State Management
- **Unread Count**: Cached in Redux, updated as needed
- **Tab Content**: Each tab manages its own loading states
- **Re-renders**: Optimized with React.memo and useCallback

## Error Handling

### API Failures
- **Mark All Error**: Shows error toast, maintains current state
- **Count Fetch Error**: Graceful degradation, button disabled
- **Network Issues**: Retry mechanisms in individual components

### State Recovery
- **Redux Hydration**: State persists across app restarts  
- **Error Boundaries**: Component-level error isolation
- **Fallback UI**: Empty states for failed data loads

## Testing Considerations

### Key Test Scenarios
1. **Tab Navigation** - Switch between tabs, lazy loading
2. **Unread Count** - Badge updates, button states
3. **Mark All Flow** - Confirmation dialog, success/error handling
4. **Deep Linking** - Entry with specific activeIndex
5. **Empty States** - No messages, no unread messages
6. **Analytics** - Event tracking on tab switches
7. **Performance** - Lazy loading, memory management

### Edge Cases
- Rapid tab switching during loading
- Network failure during mark all operation
- Unread count changes while dialog is open
- Component unmounting during API calls
- Invalid activeIndex parameters

### Integration Testing
- Redux state synchronization
- API call sequencing
- Cross-tab state persistence
- Analytics event ordering

---

*This documentation covers the complete business logic and flow of the InboxList component's tab-based inbox management system.*