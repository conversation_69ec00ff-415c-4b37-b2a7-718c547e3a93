# TapTap i18n Implementation Plan

## Executive Summary

This document outlines a comprehensive plan for implementing internationalization (i18n) across the TapTap web and Zalo Mini App platforms. After analyzing both react-i18next and next-intl solutions, we recommend **react-i18next** as the primary i18n library due to its flexibility across different platforms and better compatibility with our Independent Platforms Architecture.

## Current Architecture Analysis

### Platform Structure
- **Web App**: React SPA with Vite, React Router, and Tailwind CSS
- **Zalo Mini App**: Uses zmp-sdk and zmp-ui with Jotai for state management
- **Shared Package**: Pure presentation components with no platform detection

### Independent Platforms Benefits for i18n
- ✅ Each platform can have its own i18n configuration
- ✅ Shared components receive translated strings via props
- ✅ Platform-specific language detection and storage
- ✅ Easy to split into separate projects later

## Library Comparison: react-i18next vs next-intl

### react-i18next
**Pros:**
- ✅ Platform agnostic - works with any React setup
- ✅ Flexible configuration options
- ✅ Strong TypeScript support
- ✅ Namespace organization
- ✅ ICU message format support
- ✅ Extensive plugin ecosystem
- ✅ Works well with Vite and non-Next.js projects
- ✅ Mature and battle-tested

**Cons:**
- ❌ Requires more initial setup
- ❌ Bundle size slightly larger than next-intl

### next-intl
**Pros:**
- ✅ Excellent TypeScript integration
- ✅ Built specifically for Next.js
- ✅ Automatic type generation from JSON files
- ✅ Built-in routing support

**Cons:**
- ❌ Next.js specific - not suitable for Zalo Mini App
- ❌ Limited flexibility for custom setups
- ❌ Would require different solution for Zalo platform

## Recommended Solution: react-i18next

### Why react-i18next?
1. **Platform Compatibility**: Works seamlessly with both Vite (web) and Zalo Mini App environments
2. **Architecture Alignment**: Supports our Independent Platforms approach
3. **Flexibility**: Can adapt to different storage mechanisms per platform
4. **Shared Components**: Easy to pass translations as props to pure components

## Implementation Strategy

### Phase 1: Foundation Setup (Week 1-2)

#### 1.1 Install Dependencies
```bash
# In workspace root
yarn add react-i18next i18next i18next-browser-languagedetector i18next-http-backend

# TypeScript support
yarn add -D @types/i18next
```

#### 1.2 Create Shared i18n Configuration
```typescript
// shared/i18n/index.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

export const defaultI18nConfig = {
  fallbackLng: 'vi',
  supportedLngs: ['vi', 'en'],
  debug: process.env.NODE_ENV === 'development',
  
  interpolation: {
    escapeValue: false, // React already escapes
  },

  react: {
    useSuspense: false, // Disable suspense for better error handling
  },
};

// Export common types
export interface I18nNamespaces {
  common: string;
  navigation: string;
  forms: string;
  errors: string;
  rewards: string;
  merchants: string;
}

export type I18nKeys = keyof I18nNamespaces;
```

#### 1.3 Create Translation Structure
```
shared/
├── locales/
│   ├── vi/
│   │   ├── common.json
│   │   ├── navigation.json
│   │   ├── forms.json
│   │   ├── errors.json
│   │   ├── rewards.json
│   │   └── merchants.json
│   └── en/
│       ├── common.json
│       ├── navigation.json
│       ├── forms.json
│       ├── errors.json
│       ├── rewards.json
│       └── merchants.json
├── i18n/
│   ├── index.ts
│   ├── types.ts
│   └── hooks.ts
```

### Phase 2: Platform-Specific Implementation (Week 2-3)

#### 2.1 Web App Implementation
```typescript
// apps/web/src/i18n/index.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';
import { defaultI18nConfig } from '@taptap/shared/i18n';

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    ...defaultI18nConfig,
    
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'taptap-language',
    },
  });

export default i18n;
```

#### 2.2 Zalo Mini App Implementation
```typescript
// apps/taptap-zalo/src/i18n/index.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { defaultI18nConfig } from '@taptap/shared/i18n';
import { storage } from 'zmp-sdk';

// Import all translation files directly for Zalo
import viCommon from '@taptap/shared/locales/vi/common.json';
import viNavigation from '@taptap/shared/locales/vi/navigation.json';
// ... other Vietnamese translations

import enCommon from '@taptap/shared/locales/en/common.json';
import enNavigation from '@taptap/shared/locales/en/navigation.json';
// ... other English translations

const resources = {
  vi: {
    common: viCommon,
    navigation: viNavigation,
    // ... other namespaces
  },
  en: {
    common: enCommon,
    navigation: enNavigation,
    // ... other namespaces
  },
};

// Custom Zalo language detector
const zaloLanguageDetector = {
  name: 'zaloStorage',
  
  async lookup() {
    try {
      const savedLang = await storage.getItem('taptap-language');
      return savedLang || 'vi';
    } catch {
      return 'vi';
    }
  },

  async cacheUserLanguage(lng: string) {
    try {
      await storage.setItem('taptap-language', lng);
    } catch (error) {
      console.error('Failed to save language preference:', error);
    }
  }
};

i18n
  .use(zaloLanguageDetector)
  .use(initReactI18next)
  .init({
    ...defaultI18nConfig,
    resources,
  });

export default i18n;
```

### Phase 3: Shared Component Updates (Week 3-4)

#### 3.1 Create Translation Hook for Shared Components
```typescript
// shared/i18n/hooks.ts
import { useTranslation } from 'react-i18next';
import { I18nKeys } from './types';

export const useSharedTranslation = (namespace: I18nKeys) => {
  const { t, i18n } = useTranslation(namespace);
  
  return {
    t,
    currentLanguage: i18n.language,
    changeLanguage: (lng: string) => i18n.changeLanguage(lng),
    isLoading: false, // Can be enhanced based on needs
  };
};

// For TypeScript support
declare module 'react-i18next' {
  interface CustomTypeOptions {
    defaultNS: 'common';
    resources: {
      common: typeof import('../locales/vi/common.json');
      navigation: typeof import('../locales/vi/navigation.json');
      forms: typeof import('../locales/vi/forms.json');
      errors: typeof import('../locales/vi/errors.json');
      rewards: typeof import('../locales/vi/rewards.json');
      merchants: typeof import('../locales/vi/merchants.json');
    };
  }
}
```

#### 3.2 Update Pure Components Pattern
```typescript
// shared/components/ui/NavigationHeader/NavigationHeader.tsx
import React from 'react';

interface NavigationHeaderProps {
  title: string; // ✅ Receive translated string via props
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onLeftClick?: () => void;
  onRightClick?: () => void;
  className?: string;
  // Remove platform detection logic
}

export const NavigationHeader: React.FC<NavigationHeaderProps> = ({
  title, // ✅ Pure component receives translated title
  leftIcon,
  rightIcon,
  onLeftClick,
  onRightClick,
  className,
}) => {
  return (
    <header className={`flex items-center justify-between p-4 ${className}`}>
      {leftIcon && (
        <button onClick={onLeftClick} className="p-2">
          {leftIcon}
        </button>
      )}
      <h1 className="text-lg font-semibold">{title}</h1>
      {rightIcon && (
        <button onClick={onRightClick} className="p-2">
          {rightIcon}
        </button>
      )}
    </header>
  );
};
```

#### 3.3 Platform Container Pattern
```typescript
// apps/web/src/components/NavigationHeaderContainer.tsx
import { NavigationHeader } from '@taptap/shared';
import { useSharedTranslation } from '@taptap/shared/i18n/hooks';
import backIcon from '../assets/icons/back.svg';

interface NavigationHeaderContainerProps {
  titleKey: string;
  namespace?: string;
  onBack?: () => void;
}

export const NavigationHeaderContainer: React.FC<NavigationHeaderContainerProps> = ({
  titleKey,
  namespace = 'common',
  onBack,
}) => {
  const { t } = useSharedTranslation(namespace);
  
  return (
    <NavigationHeader
      title={t(titleKey)} // ✅ Pass translated string to pure component
      leftIcon={<img src={backIcon} alt="Back" />}
      onLeftClick={onBack}
    />
  );
};
```

### Phase 4: Translation Content Migration (Week 4-6)

#### 4.1 Initial Translation Files

**shared/locales/vi/common.json**
```json
{
  "actions": {
    "back": "Quay lại",
    "save": "Lưu",
    "cancel": "Hủy",
    "confirm": "Xác nhận",
    "close": "Đóng",
    "next": "Tiếp theo",
    "previous": "Trước",
    "submit": "Gửi"
  },
  "status": {
    "loading": "Đang tải...",
    "error": "Có lỗi xảy ra",
    "success": "Thành công",
    "empty": "Không có dữ liệu"
  },
  "time": {
    "now": "Bây giờ",
    "today": "Hôm nay",
    "yesterday": "Hôm qua",
    "tomorrow": "Ngày mai"
  }
}
```

**shared/locales/vi/navigation.json**
```json
{
  "tabs": {
    "home": "Trang chủ",
    "rewards": "Ưu đãi",
    "merchants": "Thương hiệu",
    "profile": "Cá nhân"
  },
  "pages": {
    "search": "Tìm kiếm",
    "myRewards": "Ưu đãi của tôi",
    "favorites": "Yêu thích",
    "settings": "Cài đặt"
  }
}
```

#### 4.2 Component Migration Priority
1. **High Priority**: Navigation components, common buttons, error messages
2. **Medium Priority**: Form labels, validation messages, search functionality
3. **Low Priority**: Static content, descriptions, help text

### Phase 5: Advanced Features (Week 6-8)

#### 5.1 Language Switching Component
```typescript
// shared/components/ui/LanguageSwitcher/LanguageSwitcher.tsx
interface LanguageSwitcherProps {
  currentLanguage: string;
  availableLanguages: { code: string; name: string; nativeName: string }[];
  onLanguageChange: (language: string) => void;
  className?: string;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  currentLanguage,
  availableLanguages,
  onLanguageChange,
  className,
}) => {
  return (
    <div className={`language-switcher ${className}`}>
      {availableLanguages.map((lang) => (
        <button
          key={lang.code}
          onClick={() => onLanguageChange(lang.code)}
          className={`lang-option ${currentLanguage === lang.code ? 'active' : ''}`}
        >
          {lang.nativeName}
        </button>
      ))}
    </div>
  );
};
```

#### 5.2 Formatted Messages for Dynamic Content
```typescript
// shared/i18n/formatters.ts
import { useSharedTranslation } from './hooks';

export const useFormattedMessages = (namespace: string) => {
  const { t } = useSharedTranslation(namespace);
  
  return {
    formatCount: (key: string, count: number) => 
      t(key, { count, defaultValue_plural: `${count} items` }),
    
    formatDate: (key: string, date: Date) => 
      t(key, { date: date.toLocaleDateString('vi-VN') }),
    
    formatCurrency: (key: string, amount: number) => 
      t(key, { amount: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(amount) }),
  };
};
```

#### 5.3 Error Boundary with i18n
```typescript
// shared/components/ui/ErrorBoundary/I18nErrorBoundary.tsx
import React, { Component, ReactNode } from 'react';
import { withTranslation, WithTranslation } from 'react-i18next';

interface Props extends WithTranslation {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
}

class I18nErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('I18n Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary">
          <h2>{this.props.t('errors.boundary.title')}</h2>
          <p>{this.props.t('errors.boundary.description')}</p>
          <button onClick={() => window.location.reload()}>
            {this.props.t('actions.reload')}
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default withTranslation('errors')(I18nErrorBoundary);
```

## Implementation Guidelines

### 1. Folder Structure
```
shared/
├── i18n/
│   ├── index.ts          # Common configuration
│   ├── types.ts          # TypeScript definitions
│   ├── hooks.ts          # Shared hooks
│   └── formatters.ts     # Formatting utilities
├── locales/
│   ├── vi/               # Vietnamese translations
│   └── en/               # English translations
└── components/ui/        # Pure components (receive translations via props)

apps/web/src/i18n/        # Web-specific i18n setup
apps/taptap-zalo/src/i18n/ # Zalo-specific i18n setup
```

### 2. Naming Conventions
- **Files**: kebab-case (e.g., `common.json`, `error-messages.json`)
- **Keys**: camelCase with dot notation (e.g., `user.profile.title`)
- **Namespaces**: Logical grouping (common, navigation, forms, errors)

### 3. Key Naming Strategy
```json
{
  "user": {
    "profile": {
      "title": "Thông tin cá nhân",
      "edit": "Chỉnh sửa",
      "save": "Lưu thay đổi"
    },
    "rewards": {
      "title": "Ưu đãi của tôi",
      "empty": "Bạn chưa có ưu đãi nào",
      "expired": "Đã hết hạn"
    }
  }
}
```

### 4. TypeScript Integration
```typescript
// shared/i18n/types.ts
export interface TranslationResources {
  common: typeof import('../locales/vi/common.json');
  navigation: typeof import('../locales/vi/navigation.json');
  forms: typeof import('../locales/vi/forms.json');
  errors: typeof import('../locales/vi/errors.json');
  rewards: typeof import('../locales/vi/rewards.json');
  merchants: typeof import('../locales/vi/merchants.json');
}

// Enhance react-i18next types
declare module 'react-i18next' {
  interface CustomTypeOptions {
    defaultNS: 'common';
    resources: TranslationResources;
    returnNull: false;
  }
}
```

## Testing Strategy

### 1. Unit Testing with i18n
```typescript
// shared/components/ui/__tests__/NavigationHeader.test.tsx
import React from 'react';
import { render } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../i18n/test-utils';
import { NavigationHeader } from '../NavigationHeader';

const renderWithI18n = (component: React.ReactElement) => {
  return render(
    <I18nextProvider i18n={i18n}>
      {component}
    </I18nextProvider>
  );
};

test('renders navigation header with translated title', () => {
  const { getByText } = renderWithI18n(
    <NavigationHeader title="Trang chủ" />
  );
  
  expect(getByText('Trang chủ')).toBeInTheDocument();
});
```

### 2. Test Utilities
```typescript
// shared/i18n/test-utils.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

i18n
  .use(initReactI18next)
  .init({
    lng: 'vi',
    fallbackLng: 'vi',
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    resources: {
      vi: {
        common: require('../locales/vi/common.json'),
        navigation: require('../locales/vi/navigation.json'),
      },
    },
  });

export default i18n;
```

## Performance Considerations

### 1. Bundle Optimization
- **Lazy Loading**: Load translations on-demand for non-critical sections
- **Tree Shaking**: Only import necessary translation namespaces
- **Code Splitting**: Separate language bundles per platform

### 2. Caching Strategy
```typescript
// apps/web/src/i18n/cache.ts
const translationCache = new Map();

export const getCachedTranslation = (key: string, lng: string) => {
  const cacheKey = `${lng}:${key}`;
  return translationCache.get(cacheKey);
};

export const setCachedTranslation = (key: string, lng: string, value: any) => {
  const cacheKey = `${lng}:${key}`;
  translationCache.set(cacheKey, value);
};
```

### 3. Bundle Size Analysis
- Web app: Estimated +50KB for react-i18next + translations
- Zalo app: +30KB (embedded translations, no HTTP backend)

## Migration Timeline

### Week 1-2: Foundation
- [ ] Install dependencies
- [ ] Create shared i18n configuration
- [ ] Set up basic translation structure
- [ ] Implement platform-specific configurations

### Week 3-4: Component Updates  
- [ ] Create translation hooks
- [ ] Update 10 high-priority shared components
- [ ] Implement container pattern for web and Zalo
- [ ] Create language switcher component

### Week 5-6: Content Migration
- [ ] Translate navigation elements
- [ ] Translate form labels and validation
- [ ] Translate error messages
- [ ] Translate search functionality

### Week 7-8: Advanced Features
- [ ] Implement formatted messages
- [ ] Add error boundary with i18n
- [ ] Performance optimization
- [ ] Testing and QA

## Risks and Mitigations

### Risk 1: Bundle Size Impact
**Mitigation**: 
- Use namespace-based lazy loading
- Implement tree shaking for unused translations
- Monitor bundle size with webpack-bundle-analyzer

### Risk 2: Platform Inconsistency
**Mitigation**:
- Shared translation files ensure consistency
- Container pattern maintains platform independence
- Automated testing across both platforms

### Risk 3: Developer Experience
**Mitigation**:
- Strong TypeScript support
- Clear documentation and examples
- ESLint rules for translation key validation

## Success Metrics

### Technical Metrics
- Bundle size increase < 10%
- Translation key coverage > 95%
- Type safety for all translation keys
- Zero console errors related to i18n

### User Experience Metrics
- Language switching response time < 200ms
- Translation accuracy verified by native speakers
- Consistent UI across both platforms
- No broken layouts due to text length differences

## Conclusion

This implementation plan provides a comprehensive roadmap for adding internationalization to the TapTap monorepo while maintaining the Independent Platforms Architecture. The choice of react-i18next ensures maximum compatibility and flexibility across both web and Zalo Mini App platforms.

The phased approach allows for incremental implementation, minimizing risks while ensuring thorough testing and quality assurance at each step. The emphasis on pure components and the container pattern maintains architectural integrity while providing a scalable i18n solution.

Next steps:
1. Get stakeholder approval for the plan
2. Begin Phase 1 implementation
3. Set up translation workflow with content team
4. Establish QA process for multilingual testing