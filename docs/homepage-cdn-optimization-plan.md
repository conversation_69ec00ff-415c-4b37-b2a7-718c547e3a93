# HomePage CDN Image Optimization Plan

## Overview
Apply CDN image optimization to all components and sections in the HomePage to improve performance and reduce bandwidth usage.

## Components Analysis

Based on the HomePage structure, the following components contain images that need CDN optimization:

### 🎯 **Priority 1: Direct HomePage Components**

1. **ProfileSearchHeader** (Line 333-341)
   - `avatarSrc` - User avatar images
   - **Impact**: High visibility header image
   - **Optimization**: Small avatar sizes (40px, 80px for retina)

2. **ActionCategories** (Line 346-351)
   - Panel icons from `panels.logo` (Line 124-128)
   - **Impact**: 4 action icons displayed prominently
   - **Optimization**: Icon sizes (36px, 72px for retina)

3. **MyRewardsSection** (Line 354-360)
   - `logoSrc` and `bgSrc` from reward items (Line 107-108)
   - **Impact**: Multiple reward cards with logos and backgrounds
   - **Optimization**: Logo (80px), Background (300px width)

### 🎯 **Priority 2: Dynamic Section Components**

4. **SectionBanner** (Line 182-187)
   - Banner images from section data
   - **Impact**: Large hero banners, high visibility
   - **Optimization**: Responsive banners (414px, 768px, 1200px)
   - **Status**: ✅ Already has BannerCarousel optimized

5. **SectionNews** (Line 191-199)
   - News article thumbnail images
   - **Impact**: Multiple news cards with images
   - **Optimization**: Thumbnails (150px, 300px for retina)

6. **SectionFlashSale** (Line 203-213)
   - Flash sale item images
   - **Impact**: Product images in carousel
   - **Optimization**: Product thumbnails (200px, 400px for retina)

7. **SectionPopularMerchant** (Line 217-225)
   - Merchant logo and banner images
   - **Impact**: Merchant branding visibility
   - **Optimization**: Logos (100px), Banners (300px)

8. **SectionReward** (Line 229-239)
   - Reward item images and logos
   - **Impact**: Multiple reward cards
   - **Optimization**: Similar to MyRewardsSection

9. **SectionEarnBy** (Line 255-262)
   - Earn by option icons/images
   - **Impact**: Action icons for earning methods
   - **Optimization**: Icons (64px, 128px for retina)

## Implementation Strategy

### Phase 1: Core Components (Highest Impact)
1. ✅ **BannerCarousel** - Already optimized
2. 🔄 **ProfileSearchHeader** - Avatar optimization
3. 🔄 **ActionCategories** - Panel icon optimization
4. 🔄 **MyRewardsSection** - Reward card optimization

### Phase 2: Section Components (Medium Impact)
5. 🔄 **SectionBanner** - Banner image optimization
6. 🔄 **SectionNews** - News thumbnail optimization
7. 🔄 **SectionFlashSale** - Product image optimization
8. 🔄 **SectionPopularMerchant** - Merchant image optimization

### Phase 3: Additional Sections (Lower Impact)
9. 🔄 **SectionReward** - Reward image optimization
10. 🔄 **SectionEarnBy** - Icon optimization

## Technical Approach

### 1. Component-Level Optimization
```tsx
// Import CDN utilities
import { resizeImage, resizeImageWithPreset, getAssetUrl } from '@taptap/shared';

// Apply optimization at component level
const optimizedImageUrl = resizeImage(originalUrl, {
  width: targetWidth,
  quality: 85,
  fit: 'cover',
  format: 'auto'
}, true);
```

### 2. Responsive Image Implementation
```tsx
// For components needing responsive images
const mobileUrl = resizeImage(imageUrl, { width: 300, quality: 80 });
const desktopUrl = resizeImage(imageUrl, { width: 600, quality: 85 });

<picture>
  <source media="(min-width: 768px)" srcSet={desktopUrl} />
  <img src={mobileUrl} alt="Optimized image" loading="lazy" />
</picture>
```

### 3. Preset Usage for Common Sizes
```tsx
// Use predefined presets where possible
const avatarUrl = resizeImageWithPreset(userAvatar, 'avatar');
const thumbnailUrl = resizeImageWithPreset(newsImage, 'thumbnail');
```

## Performance Targets

### Bandwidth Reduction
- **Avatars**: 90% reduction (50KB → 5KB)
- **Thumbnails**: 80% reduction (100KB → 20KB)  
- **Banners**: 70% reduction (500KB → 150KB)
- **Icons**: 85% reduction (20KB → 3KB)

### Loading Speed
- **First Contentful Paint**: Improve by 40%
- **Largest Contentful Paint**: Improve by 60%
- **Total Page Size**: Reduce by 50%

## Quality Matrix

| Component | Size | Quality | Format | Fit |
|-----------|------|---------|---------|-----|
| Avatar | 40px/80px | 85% | auto | cover |
| Panel Icons | 36px/72px | 90% | auto | contain |
| News Thumbnails | 150px/300px | 80% | auto | cover |
| Product Images | 200px/400px | 85% | auto | cover |
| Merchant Logos | 100px/200px | 90% | auto | contain |
| Banners | 414px/768px/1200px | 85% | auto | cover |

## Environment Configuration

```env
# Enable optimization for all components
VITE_ENABLE_IMAGE_RESIZE=true
VITE_CDN_IMAGE_BASE_URL=https://vui-cdn.taptap.vn/cdn-cgi/image
VITE_CDN_DEFAULT_QUALITY=85
VITE_CDN_DEFAULT_FORMAT=auto
```

## Testing Strategy

### 1. Component Testing
- Test each component individually
- Verify fallback behavior when CDN is disabled
- Check responsive image loading

### 2. Integration Testing  
- Test full HomePage loading performance
- Verify all images load correctly
- Check lazy loading behavior

### 3. Performance Testing
- Before/after bandwidth comparison
- Loading speed metrics
- Visual quality assessment

## Success Criteria

### Technical
- ✅ All components use CDN optimization
- ✅ No broken images or layout shifts
- ✅ Responsive images work across devices
- ✅ Fallback mechanism functions properly

### Performance
- ✅ 50% reduction in total image bandwidth
- ✅ 40% improvement in page load speed
- ✅ Maintained visual quality standards
- ✅ Improved Core Web Vitals scores

## Risk Mitigation

### Fallback Strategy
- Original URLs used when CDN is disabled
- Error handling for failed image loads
- Graceful degradation for unsupported formats

### Quality Assurance
- Visual regression testing
- Cross-browser compatibility
- Mobile device testing
- Different network conditions

## Next Steps

1. **Start with Phase 1** components (highest impact)
2. **Implement and test** each component individually  
3. **Monitor performance** improvements
4. **Gather user feedback** on image quality
5. **Optimize further** based on metrics

This plan ensures systematic optimization of all HomePage images while maintaining quality and user experience.