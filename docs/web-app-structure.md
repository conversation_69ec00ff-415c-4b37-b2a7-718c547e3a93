# Web App Structure Documentation

## Overview
The Web app is already well-structured as a standalone application within the monorepo. It uses React 18.3.1, TypeScript 5.5.0, Vite for building, and Zustand for state management.

## Current Implementation Status

### ✅ Completed Features

#### 1. Routing Structure (✅ Complete)
- **Router**: React Router v6.26.0
- **Routes**: 40+ routes defined in `/apps/web/src/App.tsx`
- **Lazy Loading**: All pages use React.lazy() for code splitting
- **Categories**:
  - Auth: Login
  - Games: List, Details, History, Wheel Game, Missions
  - Rewards: Exchange, Flash Sale, Gift Code, Reward Details
  - Membership: Overview, Benefits, Details, Tier List
  - Bill Scan: Camera, List, Details, Captured Receipts
  - User: Profile, Member Code, Transaction History
  - Points: My Points, VUI Points, Brand Currency
  - Content: News, Inbox, Contact
  - Search: Search page and results
  - QR: Scanner and payment

#### 2. HTTP Client (✅ Complete)
- **Location**: `/shared/services/http/httpClient.ts`
- **Features**:
  - Singleton pattern with `getHttpClient()`
  - Axios-based with interceptors
  - Token refresh mechanism
  - Platform detection (Web vs Zalo)
  - Device ID generation for Web
  - Error codes matching mobile project
  - Bearer token authentication
  - Response format handling (new vs legacy API)

#### 3. Authentication (✅ Complete)
- **AuthProvider**: `/apps/web/src/providers/AuthProvider.tsx`
  - React Context for auth state
  - Integration with `authService` from shared
  - Customer profile fetching
  - Token management
  - Auto-initialization on mount

#### 4. State Management (✅ Complete)
- **Zustand Stores**:
  1. **AppStore** (`/apps/web/src/store/index.ts`):
     - User state
     - Authentication state
     - UI state (loading, sidebar)
     - Game selection state
     - Search state
     - DevTools integration
  
  2. **BillScanStore** (`/apps/web/src/store/billScanStore.ts`):
     - Bill configuration
     - Earn method info
     - Merchants and steps
     - Caching mechanism
     - Auto-fetch hooks

#### 5. Component Structure (✅ Complete)
- **Layout**: `/apps/web/src/components/Layout.tsx`
- **Navigation**: `/apps/web/src/components/navigation/BottomNavigation/`
- **Error Handling**: `/apps/web/src/components/ErrorBoundary.tsx`
- **Pages**: 40+ page components in `/apps/web/src/pages/`
- **Shared Components**: Imported from `@taptap/shared`

### 🚧 In Progress Features

#### 1. Container Pattern Implementation
Some pages still need container components to connect to stores and handle business logic:
- Pages currently importing directly from shared
- Need to add Web-specific logic wrappers

#### 2. Service Layer
Need to create Web-specific service wrappers:
- `/apps/web/src/services/` (currently empty)
- Wrap shared services with Web-specific error handling
- Add Web-specific caching strategies

#### 3. Custom Hooks
Need to create Web-specific hooks:
- `/apps/web/src/hooks/` (currently empty)
- Navigation hooks
- Permission hooks
- Feature flag hooks

## Directory Structure

```
apps/web/
├── src/
│   ├── App.tsx                    # Main app with routing
│   ├── main.tsx                   # Entry point
│   ├── assets/                    # Web-specific assets
│   │   └── images/
│   ├── components/
│   │   ├── ErrorBoundary.tsx      # Error handling
│   │   ├── Layout.tsx             # App layout wrapper
│   │   └── navigation/
│   │       └── BottomNavigation/  # Web navigation
│   ├── pages/                     # 40+ page components
│   │   ├── auth/
│   │   ├── bill-scan/
│   │   ├── content/
│   │   ├── deals/
│   │   ├── errors/
│   │   ├── flash-sale/
│   │   ├── games/
│   │   ├── gift-code/
│   │   ├── home/
│   │   ├── inbox/
│   │   ├── membership/
│   │   ├── merchant/
│   │   ├── missions/
│   │   ├── my-rewards/
│   │   ├── news/
│   │   ├── point/
│   │   ├── reward-category/
│   │   ├── reward-detail/
│   │   ├── scan-qr/
│   │   ├── search/
│   │   ├── transaction-history/
│   │   └── user/
│   ├── providers/
│   │   └── AuthProvider.tsx       # Auth context provider
│   ├── services/                  # (To be implemented)
│   ├── hooks/                     # (To be implemented)
│   ├── store/
│   │   ├── index.ts              # Main Zustand store
│   │   └── billScanStore.ts      # Bill scan feature store
│   └── utils/                     # (To be implemented)
├── public/
├── index.html
├── package.json
├── tsconfig.json
├── vite.config.ts
├── tailwind.config.js
└── postcss.config.js
```

## Dependencies

### Production
- `@taptap/shared`: Workspace dependency
- `react`: 18.3.1
- `react-dom`: 18.3.1
- `react-router-dom`: 6.26.0
- `zustand`: 4.5.0

### Development
- `vite`: 5.4.0
- `typescript`: 5.5.0
- `tailwindcss`: 3.4.0
- `@storybook/*`: 8.3.0 - 9.0.17
- ESLint and related plugins

## Next Steps for Phase 2.5

1. **Create Web Services Layer**:
   - Wrap shared services with Web-specific logic
   - Add error handling and retry mechanisms
   - Implement caching strategies

2. **Create Web Hooks**:
   - `useWebNavigation`: Navigation utilities
   - `useWebAuth`: Auth utilities with Web-specific features
   - `useWebPermissions`: Permission checks
   - `useWebFeatureFlags`: Feature flag management

3. **Complete Container Pattern**:
   - Add container components for remaining pages
   - Connect pages to Zustand stores
   - Handle Web-specific business logic

4. **Add Web Utilities**:
   - Browser detection
   - Local storage utilities
   - Web-specific formatters
   - Analytics integration

5. **Enhance Error Handling**:
   - Global error boundaries
   - API error handling
   - User-friendly error messages
   - Error reporting

## Integration Points with Shared

The Web app successfully integrates with shared package:
- **Components**: All UI components from `@taptap/shared`
- **Services**: Auth, storage, API services from shared
- **Hooks**: Shared hooks for common functionality
- **Types**: TypeScript types from shared
- **Utils**: Platform detection and utilities from shared

## Platform-Specific Features

### Web-Only Features
- Browser-based authentication
- Local storage for persistence
- Full-screen modals and drawers
- Desktop-responsive layouts
- Browser notifications (planned)
- File uploads via browser (planned)
- Web analytics integration (planned)

### Excluded Features (Zalo-only)
- ZMP SDK features
- Native device APIs
- Zalo-specific UI components
- Mini app navigation
- Zalo payment integration