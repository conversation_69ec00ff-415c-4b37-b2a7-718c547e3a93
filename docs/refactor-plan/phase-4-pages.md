# 📱 Phase 4: Page Migration (Tuần 4-5)

## 🎯 <PERSON><PERSON><PERSON>i<PERSON> tất cả pages sang wrapper pattern, đả<PERSON> bảo mỗi page có thể chạy trên cả Web và Zalo với <100 lines platform-specific code.

## ⏰ Timeline: 10 ngày (2 tuần)

### Tuần 4: Priority Pages
| Ngày | Pages | Complexity |
|------|-------|------------|
| Ngày 1-2 | HomePage | High |
| Ngày 3-4 | BillListPage & CameraPage | High |
| Ngày 5 | ProfilePage | Medium |

### Tuần 5: Secondary Pages
| Ngày | Pages | Complexity |
|------|-------|------------|
| Ngày 6 | SearchPage & ResultsPage | Medium |
| Ngày 7 | MerchantDetailPage | Medium |
| Ngày 8 | RewardDetailPage | Low |
| Ngày 9 | ExchangePage & GamesPage | Low |
| Ngày 10 | Testing & Polish | - |

## 📋 DETAILED MIGRATION PLAN

### 🏠 HomePage Migration (Ngày 1-2)

#### Current Structure Analysis
```
Web: apps/web/src/pages/HomePage.tsx (~500 lines)
Zalo: apps/taptap-zalo/src/pages/index.tsx (~300 lines)
Shared logic potential: 90%
```

#### Step 1: Extract Business Logic
**File:** `shared/pages/home/<USER>

```typescript
export function useHomePage() {
  const navigate = useNavigation(); // Platform adapter
  const { profile, currentAvatar } = useAuthStore();
  const { sections, panels, loading, fetchHomeConfig } = useHomeStore();
  
  // All business logic here
  const handleSearch = (query: string) => {
    navigate(`/search?q=${encodeURIComponent(query)}`);
  };
  
  const handlePanelClick = (panel: Panel) => {
    // Platform-agnostic navigation
    const route = mapPanelToRoute(panel);
    navigate(route);
  };
  
  return {
    // Data
    profile,
    sections,
    panels,
    loading,
    
    // Actions
    handleSearch,
    handlePanelClick,
    fetchHomeConfig,
    // ...
  };
}
```

#### Step 2: Create Shared Page Component
**File:** `shared/pages/home/<USER>

```typescript
export const HomePageShared: React.FC = () => {
  const {
    profile,
    sections,
    panels,
    loading,
    handleSearch,
    handlePanelClick,
  } = useHomePage();
  
  if (loading) return <LoadingSkeleton />;
  
  return (
    <>
      <ProfileSearchHeader
        userName={profile.firstName}
        avatarSrc={profile.avatar}
        onSearch={handleSearch}
      />
      
      <ActionCategories
        points={profile.loyaltyPoints}
        actions={panels}
        onActionClick={handlePanelClick}
      />
      
      <DynamicSections sections={sections} />
    </>
  );
};
```

#### Step 3: Create Platform Wrappers

**Web Wrapper:** `apps/web/src/pages/HomePage.tsx`
```typescript
import { HomePageShared } from '@taptap/shared';

export const HomePage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <HomePageShared />
      <BottomNavigation /> {/* Web only */}
    </div>
  );
};
```

**Zalo Wrapper:** `apps/taptap-zalo/src/pages/index.tsx`
```typescript
import { Page } from 'zmp-ui';
import { HomePageShared } from '@taptap/shared';

export default function HomePage() {
  return (
    <Page className="bg-gray-50">
      <HomePageShared />
      {/* No BottomNavigation - Zalo native */}
    </Page>
  );
}
```

**Migration Checklist:**
- [ ] Extract all business logic to hook
- [ ] Create shared page component
- [ ] Create web wrapper (<50 lines)
- [ ] Create Zalo wrapper (<50 lines)
- [ ] Test navigation flows
- [ ] Test data fetching
- [ ] Test scroll behavior
- [ ] Verify performance

---

### 📸 BillListPage & CameraPage Migration (Ngày 3-4)

#### Special Considerations
- Camera API differences significant
- OCR integration complex
- File upload handling different

#### BillListPage Migration

**Step 1: Shared Hook**
**File:** `shared/pages/bills/useBillList.ts`

```typescript
export function useBillList() {
  const navigate = useNavigation();
  const {
    getTransactionsForStatus,
    fetchTransactions,
    statistics,
    // ...
  } = useOCRStore();
  
  const [activeTab, setActiveTab] = useState('waiting');
  
  // Tab configuration
  const tabs = [
    { id: 'waiting', label: 'Đang chờ', status: 'PROCESSING' },
    { id: 'completed', label: 'Thành công', status: 'COMPLETED' },
    { id: 'rejected', label: 'Từ chối', status: 'FAILED' },
  ];
  
  return {
    tabs,
    activeTab,
    setActiveTab,
    transactions: getTransactionsForStatus(activeTab),
    // ...
  };
}
```

**Step 2: Camera Abstraction**
**File:** `shared/services/camera/camera.service.ts`

```typescript
class CameraService {
  async scanBill(): Promise<File> {
    if (isZaloMiniApp()) {
      const { openCamera } = await import('zmp-sdk');
      const result = await openCamera({
        type: 'photo',
        // Zalo-specific options
      });
      return this.processZaloImage(result);
    } else {
      // Web camera implementation
      return this.openWebCamera();
    }
  }
  
  async scanQRCode(): Promise<string> {
    if (isZaloMiniApp()) {
      const { scanQRCode } = await import('zmp-sdk');
      const result = await scanQRCode();
      return result.data;
    } else {
      // Web QR scanner
      return this.openWebQRScanner();
    }
  }
}
```

**Migration Checklist:**
- [ ] Extract bill list logic
- [ ] Create camera abstraction
- [ ] Handle file upload differences
- [ ] Test OCR integration
- [ ] Verify image quality
- [ ] Test error scenarios

---

### 👤 ProfilePage Migration (Ngày 5)

#### Considerations
- Avatar selection different
- Settings management
- Logout flow differences

**Shared Profile Hook:**
```typescript
export function useProfile() {
  const { profile, logout, updateProfile } = useAuthStore();
  const navigate = useNavigation();
  const platform = usePlatform();
  
  const handleAvatarChange = async () => {
    if (platform === 'zalo') {
      // Use Zalo image picker
      const { openMediaPicker } = await import('zmp-sdk');
      const result = await openMediaPicker({ type: 'image' });
      // ...
    } else {
      // Web file input
    }
  };
  
  return {
    profile,
    handleAvatarChange,
    handleLogout: logout,
    // ...
  };
}
```

**Migration Checklist:**
- [ ] Extract profile logic
- [ ] Handle avatar selection
- [ ] Migrate settings
- [ ] Test logout flow
- [ ] Verify data persistence

---

### 🔍 SearchPage Migration (Ngày 6)

**Considerations:**
- Keyboard behavior differences
- Search suggestions
- Voice search (if applicable)

**Shared Search Logic:**
```typescript
export function useSearch() {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const { searchHistory, addToHistory } = useSearchStore();
  
  const debouncedQuery = useDebounce(query, 300);
  
  useEffect(() => {
    if (debouncedQuery) {
      fetchSuggestions(debouncedQuery);
    }
  }, [debouncedQuery]);
  
  return {
    query,
    setQuery,
    suggestions,
    searchHistory,
    handleSearch,
  };
}
```

---

## 📊 Page Migration Status

| Page | Shared Logic | Web Wrapper | Zalo Wrapper | Tests | Status |
|------|--------------|-------------|--------------|-------|--------|
| HomePage | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 In Progress |
| BillListPage | ❌ | ❌ | ❌ | ❌ | 📅 Planned |
| CameraPage | ❌ | ❌ | ❌ | ❌ | 📅 Planned |
| ProfilePage | ❌ | ❌ | ❌ | ❌ | 📅 Planned |
| SearchPage | ❌ | ❌ | ❌ | ❌ | 📅 Planned |
| MerchantDetailPage | ❌ | ❌ | ❌ | ❌ | 📅 Planned |
| RewardDetailPage | ❌ | ❌ | ❌ | ❌ | 📅 Planned |
| ExchangePage | ❌ | ❌ | ❌ | ❌ | 📅 Planned |

## 🎯 Migration Pattern Template

For each page migration, follow this template:

### 1. Analysis Phase
```markdown
## [PageName] Analysis
- Current LOC: Web ___ / Zalo ___
- Shared potential: ___%
- Complex features: [list]
- Platform-specific: [list]
```

### 2. Implementation Phase
```typescript
// Step 1: shared/pages/[page]/use[Page].ts
export function use[Page]() {
  // All business logic
}

// Step 2: shared/pages/[page]/[Page].shared.tsx
export const [Page]Shared = () => {
  const logic = use[Page]();
  // Shared UI
}

// Step 3: Platform wrappers (< 100 lines each)
```

### 3. Testing Phase
- [ ] Unit tests for hook
- [ ] Component tests
- [ ] Integration tests
- [ ] E2E tests on both platforms
- [ ] Performance tests

## 🚨 Common Migration Challenges

### Challenge: Navigation Differences
**Solution:** Always use navigation adapter

### Challenge: Native Features
**Solution:** Create service abstractions

### Challenge: Layout Differences
**Solution:** Use responsive design, platform classes

### Challenge: State Management
**Solution:** Keep all state in shared stores

### Challenge: Performance
**Solution:** Lazy load, code split, optimize bundles

## 📝 Migration Guidelines

### DO's ✅
- Extract ALL business logic to hooks
- Keep wrappers minimal (<100 lines)
- Use platform adapters consistently
- Test on real devices
- Document platform differences

### DON'Ts ❌
- Don't duplicate business logic
- Don't use platform checks in shared components
- Don't hardcode navigation paths
- Don't skip testing
- Don't ignore performance

## 🧪 Testing Strategy

### Test Structure
```
shared/pages/
├── home/
│   ├── __tests__/
│   │   ├── useHomePage.test.ts
│   │   ├── HomePage.shared.test.tsx
│   │   └── HomePage.integration.test.ts
│   ├── useHomePage.ts
│   └── HomePage.shared.tsx
```

### Test Coverage Requirements
- Hook logic: 100%
- Shared components: 95%
- Platform wrappers: 80%
- Integration: 90%

## ✅ Phase 4 Completion Checklist

### Week 4 Deliverables
- [ ] HomePage fully migrated
- [ ] BillListPage migrated
- [ ] CameraPage migrated
- [ ] ProfilePage migrated
- [ ] All priority pages working

### Week 5 Deliverables
- [ ] SearchPage migrated
- [ ] MerchantDetailPage migrated
- [ ] RewardDetailPage migrated
- [ ] All secondary pages migrated
- [ ] Full test coverage

### Quality Gates
- [ ] <100 lines per wrapper
- [ ] >95% shared code
- [ ] Zero business logic duplication
- [ ] All tests passing
- [ ] Performance benchmarks met

## 📈 Success Metrics

- **Shared Code:** >95%
- **Wrapper Size:** <100 lines
- **Load Time:** <2s
- **Bundle Size:** Within limits
- **Test Coverage:** >90%
- **Bug Rate:** <1%

## 🎉 Phase 4 Deliverables

By end of Week 5:
1. ✅ All pages migrated to wrapper pattern
2. ✅ >95% code sharing achieved
3. ✅ Platform wrappers <100 lines each
4. ✅ Full test coverage
5. ✅ Zero business logic duplication

---

**Next:** [Phase 5 - Testing & Optimization](./phase-5-testing.md)

**Status:** 🟡 IN PLANNING
**Last Updated:** ${new Date().toISOString()}