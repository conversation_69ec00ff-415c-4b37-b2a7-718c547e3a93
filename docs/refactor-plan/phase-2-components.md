# 🎨 Phase 2: Shared Components (Tuần 2)

## 🎯 <PERSON>ục Tiêu
Refactor toàn bộ UI components để có thể sử dụng trên cả Web và Zalo Mini App với zero duplication.

## ⏰ Timeline: 5 ngày

| Ngày | Focus Area | Deliverables |
|------|------------|--------------|
| Ngày 1 | Component Audit | Complete inventory & analysis |
| Ngày 2 | Base Components | Button, Input, Card refactored |
| Ngày 3 | Navigation Components | Headers, Tabs, Navigation |
| Ngày 4 | Complex Components | Lists, Forms, Modals |
| Ngày 5 | Documentation | Storybook, Tests, Docs |

## 📋 TASK BREAKDOWN

### 1️⃣ Component Audit & Analysis (Ngày 1)

#### 1.1 Component Inventory
**Task:** Kiểm tra toàn bộ components hiện có

**Checklist:**
- [ ] List tất cả components trong `shared/components/ui/`
- [ ] List components trong `apps/web/src/components/`
- [ ] List components trong `apps/taptap-zalo/src/components/`
- [ ] Identify duplicate components
- [ ] Categorize by complexity (simple/medium/complex)

**Output File:** `shared/COMPONENTS_AUDIT.md`
```markdown
# Component Audit Report

## Shared Components (42 total)
- ✅ Button (platform-ready)
- ⚠️ NavigationHeader (needs adaptation)
- ❌ BottomNavigation (web-only)
...

## Duplication Analysis
- BillCard: exists in both web and shared
- SearchBar: 3 different implementations
...

## Action Items
1. Merge duplicate components
2. Create platform adapters for:
   - NavigationHeader
   - BottomSheet
   - Modal
```

#### 1.2 Platform Compatibility Matrix
**Checklist:**
- [ ] Test each component on Zalo
- [ ] Document platform-specific issues
- [ ] Identify required adaptations
- [ ] Priority ranking for refactor

---

### 2️⃣ Base Component Refactoring (Ngày 2)

#### 2.1 Button Component
**File:** `shared/components/ui/Button/Button.tsx`

```typescript
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
  // Platform-specific
  hapticFeedback?: boolean; // Zalo only
}
```

**Checklist:**
- [ ] Unify all button variants
- [ ] Add platform-specific features (haptic for Zalo)
- [ ] Ensure consistent styling
- [ ] Add loading states
- [ ] Test accessibility

#### 2.2 Input Component
**File:** `shared/components/ui/Input/Input.tsx`

**Checklist:**
- [ ] Handle keyboard differences
- [ ] Support Zalo keyboard events
- [ ] Add validation states
- [ ] Support masks and formatters
- [ ] Test with various input types

#### 2.3 Card Component
**File:** `shared/components/ui/Card/Card.tsx`

**Checklist:**
- [ ] Standardize card layouts
- [ ] Support all card variants (News, Bill, Reward, etc.)
- [ ] Add touch feedback for mobile
- [ ] Optimize image loading
- [ ] Support skeleton states

---

### 3️⃣ Navigation Components (Ngày 3)

#### 3.1 NavigationHeader Adapter
**File:** `shared/components/ui/NavigationHeader/NavigationHeader.tsx`

```typescript
const NavigationHeader: React.FC<Props> = (props) => {
  const platform = usePlatform();
  
  useEffect(() => {
    if (platform === 'zalo') {
      // Use Zalo native navigation bar
      setNavigationBarTitle(props.title);
      if (props.leftButton) {
        setNavigationBarLeftButton(props.leftButton);
      }
    }
  }, [props, platform]);
  
  if (platform === 'zalo') {
    return null; // Zalo uses native header
  }
  
  // Web implementation
  return <WebNavigationHeader {...props} />;
};
```

**Checklist:**
- [ ] Create platform adapter pattern
- [ ] Map props to Zalo navigation API
- [ ] Handle back navigation
- [ ] Support action buttons
- [ ] Test on both platforms

#### 3.2 Tab Components
**File:** `shared/components/ui/SwiperTabContainer/`

**Checklist:**
- [ ] Unify tab implementations
- [ ] Support swipe gestures
- [ ] Add lazy loading for tab content
- [ ] Handle tab badges
- [ ] Optimize for performance

#### 3.3 BottomNavigation Handling
**Strategy:** Web uses component, Zalo uses native

**Checklist:**
- [ ] Create configuration for Zalo native tabs
- [ ] Ensure consistent navigation behavior
- [ ] Handle tab switching events
- [ ] Sync active states
- [ ] Document differences

---

### 4️⃣ Complex Components (Ngày 4)

#### 4.1 List Components
**Files:** Various list components

**Checklist:**
- [ ] Create base ListItem component
- [ ] Support virtual scrolling
- [ ] Add pull-to-refresh
- [ ] Handle empty states
- [ ] Optimize rendering performance

#### 4.2 Form Components
**Checklist:**
- [ ] Create FormProvider for validation
- [ ] Unify form controls
- [ ] Handle submission states
- [ ] Add error handling
- [ ] Support multi-step forms

#### 4.3 Modal/BottomSheet
**Strategy:** Adaptive component based on platform

```typescript
const Modal: React.FC<ModalProps> = (props) => {
  const platform = usePlatform();
  
  if (platform === 'mobile' || platform === 'zalo') {
    return <BottomSheet {...props} />;
  }
  
  return <DesktopModal {...props} />;
};
```

**Checklist:**
- [ ] Create adaptive Modal component
- [ ] Support different presentation styles
- [ ] Handle backdrop clicks
- [ ] Add animation support
- [ ] Test accessibility

---

### 5️⃣ Component System Setup (Ngày 5)

#### 5.1 Storybook Configuration
**Checklist:**
- [ ] Setup Storybook for shared components
- [ ] Add platform switcher addon
- [ ] Create stories for all components
- [ ] Add interaction tests
- [ ] Document props and usage

#### 5.2 Component Documentation
**File:** `shared/components/README.md`

**Checklist:**
- [ ] Document component API
- [ ] Add usage examples
- [ ] Platform differences notes
- [ ] Migration guide from old components
- [ ] Best practices

#### 5.3 Testing Setup
**Checklist:**
- [ ] Unit tests for all components
- [ ] Snapshot tests
- [ ] Accessibility tests
- [ ] Visual regression tests
- [ ] Performance tests

---

## 📊 Component Refactor Tracker

| Component | Status | Web | Zalo | Tests | Docs |
|-----------|--------|-----|------|-------|------|
| Button | ✅ Complete | ✅ | ✅ | ✅ | ✅ |
| Input | 🔄 In Progress | ✅ | ⚠️ | ✅ | 📝 |
| Card | 📅 Planned | - | - | - | - |
| NavigationHeader | 📅 Planned | - | - | - | - |
| SwiperTabContainer | 📅 Planned | - | - | - | - |
| Modal/BottomSheet | 📅 Planned | - | - | - | - |
| ... | ... | ... | ... | ... | ... |

## 🎨 Design System Alignment

### Color System
```typescript
// shared/styles/colors.ts
export const colors = {
  primary: '#F65D79',
  secondary: '#4A90E2',
  // ... platform-agnostic colors
};
```

### Typography
```typescript
// shared/styles/typography.ts
export const typography = {
  h1: 'text-2xl font-bold',
  h2: 'text-xl font-semibold',
  // ... using Tailwind classes
};
```

### Spacing & Layout
```typescript
// shared/styles/spacing.ts
export const spacing = {
  page: 'px-4 py-4',
  section: 'mb-4',
  // ... consistent spacing
};
```

## 🧪 Testing Requirements

### Component Testing Checklist
- [ ] Props validation
- [ ] Event handlers
- [ ] Accessibility (ARIA)
- [ ] Keyboard navigation
- [ ] Touch interactions
- [ ] Loading states
- [ ] Error states
- [ ] Empty states

### Platform Testing Matrix

| Test Case | Web Chrome | Web Safari | Zalo iOS | Zalo Android |
|-----------|------------|------------|----------|--------------|
| Render | ✅ | ✅ | ✅ | ✅ |
| Interaction | ✅ | ✅ | ✅ | ✅ |
| Performance | ✅ | ✅ | ✅ | ✅ |
| Accessibility | ✅ | ✅ | ⚠️ | ⚠️ |

## 📚 Documentation Structure

```
shared/components/
├── README.md (overview)
├── MIGRATION.md (from old components)
├── ui/
│   ├── Button/
│   │   ├── Button.tsx
│   │   ├── Button.stories.tsx
│   │   ├── Button.test.tsx
│   │   └── README.md
│   └── [other components...]
└── composite/
    └── [complex components...]
```

## 🚨 Common Pitfalls & Solutions

### Issue: Different default styles
**Solution:** Reset styles, use consistent base

### Issue: Touch vs Click events
**Solution:** Use unified event handler that works for both

### Issue: Keyboard handling differences
**Solution:** Abstract keyboard events into hooks

### Issue: Animation performance
**Solution:** Use CSS animations, avoid JS animations

### Issue: Image loading
**Solution:** Use lazy loading, optimize formats

## ✅ Phase 2 Completion Checklist

### Components Ready
- [ ] All base components refactored
- [ ] Navigation components adapted
- [ ] Complex components migrated
- [ ] Platform-specific handled
- [ ] Zero duplication achieved

### Quality Assurance
- [ ] All tests passing
- [ ] Storybook complete
- [ ] Documentation finished
- [ ] Accessibility verified
- [ ] Performance optimized

### Integration
- [ ] Integrated with Phase 1 adapters
- [ ] Used in at least 1 page
- [ ] No regressions
- [ ] Team trained
- [ ] Rollback plan ready

## 📈 Success Metrics

- **Component Reuse:** 100%
- **Code Duplication:** <1%
- **Test Coverage:** >95%
- **Storybook Coverage:** 100%
- **Load Time Impact:** <50ms
- **Bundle Size Impact:** <10KB

---

## 🎉 Phase 2 Deliverables

By end of Week 2:
1. ✅ 100% components platform-agnostic
2. ✅ Complete Storybook documentation
3. ✅ Full test coverage
4. ✅ Migration guide
5. ✅ Zero component duplication

---

**Next:** [Phase 3 - Business Logic](./phase-3-business-logic.md)

**Status:** 🟡 IN PLANNING
**Last Updated:** ${new Date().toISOString()}