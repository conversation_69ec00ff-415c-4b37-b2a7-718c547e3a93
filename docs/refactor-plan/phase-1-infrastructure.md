# 📦 Phase 1: Core Infrastructure (Tuần 1)

## 🎯 <PERSON>ục Tiêu
<PERSON>ây dựng lớp abstraction cơ bản để handle platform differences, tạo nền tảng vững chắc cho việc share code.

## ⏰ Timeline: 5 ngày

| Ngày | Focus Area | Deliverables |
|------|------------|--------------|
| Ngày 1 | Navigation System | Navigation Adapter hoàn chỉnh |
| Ngày 2 | Storage Layer | Storage Abstraction API |
| Ngày 3 | Platform Detection | Enhanced Detection System |
| Ngày 4 | API & Auth | Unified API Client |
| Ngày 5 | Testing & Documentation | Tests + Docs |

## 📋 TASK BREAKDOWN

### 1️⃣ Navigation Adapter System (Ngày 1)

#### 1.1 Tạo Navigation Interface
**File:** `shared/adapters/navigation/types.ts`

```typescript
export interface NavigationAdapter {
  navigate: (path: string | number, params?: any) => void;
  goBack: () => void;
  replace: (path: string, params?: any) => void;
  push: (path: string, params?: any) => void;
  getCurrentRoute: () => string;
  getParams: () => Record<string, any>;
}
```

**Checklist:**
- [ ] Define NavigationAdapter interface
- [ ] Create RouteMapping type
- [ ] Define NavigationParams type
- [ ] Add navigation events interface
- [ ] Document all types với JSDoc

#### 1.2 Web Navigation Implementation
**File:** `shared/adapters/navigation/web.adapter.ts`

**Checklist:**
- [ ] Implement NavigationAdapter for React Router
- [ ] Handle browser history API
- [ ] Support query params
- [ ] Add navigation guards
- [ ] Handle deep linking

#### 1.3 Zalo Navigation Implementation
**File:** `shared/adapters/navigation/zalo.adapter.ts`

**Checklist:**
- [ ] Implement NavigationAdapter for ZMP Router
- [ ] Map Web routes to Zalo routes
- [ ] Handle Zalo-specific navigation (openMiniApp, openWebview)
- [ ] Support route params via getRouteParams()
- [ ] Handle back navigation với native back button

#### 1.4 Navigation Hook
**File:** `shared/hooks/useNavigation.ts`

**Checklist:**
- [ ] Create useNavigation hook
- [ ] Auto-detect platform
- [ ] Return correct adapter
- [ ] Add navigation helpers (canGoBack, isActive, etc.)
- [ ] Memoize adapter instance

#### 1.5 Route Mapping Configuration
**File:** `shared/config/routes.ts`

**Checklist:**
- [ ] Define route constants
- [ ] Create route mapping table
- [ ] Add route parameter definitions
- [ ] Define route guards/permissions
- [ ] Create route builder utilities

**Testing Checklist:**
- [ ] Unit tests cho cả 2 adapters
- [ ] Integration test với actual routers
- [ ] Test route mapping accuracy
- [ ] Test parameter passing
- [ ] Test navigation events

---

### 2️⃣ Storage Abstraction Layer (Ngày 2)

#### 2.1 Storage Interface
**File:** `shared/adapters/storage/types.ts`

```typescript
export interface StorageAdapter {
  setItem: <T>(key: string, value: T) => Promise<void>;
  getItem: <T>(key: string) => Promise<T | null>;
  removeItem: (key: string) => Promise<void>;
  clear: () => Promise<void>;
  getKeys: () => Promise<string[]>;
  getStorageInfo: () => Promise<StorageInfo>;
}
```

**Checklist:**
- [ ] Define StorageAdapter interface
- [ ] Create StorageInfo type
- [ ] Add storage events interface
- [ ] Define storage key prefix strategy
- [ ] Add encryption interface (optional)

#### 2.2 Web Storage Implementation
**File:** `shared/adapters/storage/web.adapter.ts`

**Checklist:**
- [ ] Implement using localStorage/sessionStorage
- [ ] Add IndexedDB support for large data
- [ ] Handle storage quota exceeded
- [ ] Implement data serialization
- [ ] Add storage event listeners

#### 2.3 Zalo Storage Implementation
**File:** `shared/adapters/storage/zalo.adapter.ts`

**Checklist:**
- [ ] Implement using Zalo Storage API
- [ ] Handle async nature of Zalo storage
- [ ] Map errors appropriately
- [ ] Handle storage limits (check getNativeStorageInfo)
- [ ] Implement data serialization

#### 2.4 Storage Hook
**File:** `shared/hooks/useStorage.ts`

**Checklist:**
- [ ] Create useStorage hook với real-time updates
- [ ] Add useLocalStorage shorthand
- [ ] Add useSessionStorage for web
- [ ] Implement storage migration utilities
- [ ] Add storage debugging tools

**Testing Checklist:**
- [ ] Test CRUD operations
- [ ] Test data serialization
- [ ] Test storage limits
- [ ] Test error handling
- [ ] Test cross-tab sync (web)

---

### 3️⃣ Platform Detection Enhancement (Ngày 3)

#### 3.1 Enhanced Platform Detector
**File:** `shared/utils/platform.enhanced.ts`

**Checklist:**
- [ ] Improve Zalo detection accuracy
- [ ] Add version detection
- [ ] Detect device capabilities
- [ ] Add platform feature flags
- [ ] Cache detection results

#### 3.2 Platform Context Provider
**File:** `shared/contexts/PlatformContext.tsx`

**Checklist:**
- [ ] Create PlatformProvider component
- [ ] Provide platform info to entire app
- [ ] Add platform-specific config
- [ ] Handle platform changes (unlikely but safe)
- [ ] Add development mode overrides

#### 3.3 Platform-Specific Features
**File:** `shared/config/features.ts`

**Checklist:**
- [ ] Define feature availability matrix
- [ ] Create feature detection utilities
- [ ] Add fallback strategies
- [ ] Document platform limitations
- [ ] Create feature flags system

**Testing Checklist:**
- [ ] Test on actual Zalo app
- [ ] Test on various browsers
- [ ] Test with spoofed user agents
- [ ] Test feature detection
- [ ] Test fallback mechanisms

---

### 4️⃣ API Client Adaptation (Ngày 4)

#### 4.1 Unified Auth Provider
**File:** `shared/adapters/auth/index.ts`

**Checklist:**
- [ ] Abstract token retrieval
- [ ] Handle Zalo getAccessToken()
- [ ] Handle Web JWT tokens
- [ ] Implement token refresh logic
- [ ] Add auth state management

#### 4.2 HTTP Client Enhancement
**File:** `shared/services/http/httpClient.enhanced.ts`

**Checklist:**
- [ ] Auto-detect platform for auth
- [ ] Add request/response interceptors
- [ ] Handle platform-specific headers
- [ ] Implement retry logic
- [ ] Add request caching

#### 4.3 API Error Handler
**File:** `shared/services/http/errorHandler.ts`

**Checklist:**
- [ ] Unified error format
- [ ] Platform-specific error mapping
- [ ] User-friendly error messages
- [ ] Error reporting/logging
- [ ] Retry strategies

**Testing Checklist:**
- [ ] Test auth flow on both platforms
- [ ] Test API calls with mock server
- [ ] Test error scenarios
- [ ] Test token refresh
- [ ] Test offline handling

---

### 5️⃣ Environment Configuration (Ngày 5)

#### 5.1 Environment Manager
**File:** `shared/config/environment.ts`

**Checklist:**
- [ ] Unified env variable access
- [ ] Platform-specific configs
- [ ] Build-time vs runtime config
- [ ] Config validation
- [ ] Default values

#### 5.2 Build Configuration
**Files:** Update vite configs

**Checklist:**
- [ ] Configure path aliases
- [ ] Setup shared imports
- [ ] Configure build outputs
- [ ] Add platform-specific builds
- [ ] Optimize bundle sizes

#### 5.3 Development Tools
**Checklist:**
- [ ] Setup hot reload for both platforms
- [ ] Add platform switcher in dev mode
- [ ] Create mock adapters for testing
- [ ] Add debug panel
- [ ] Setup logging system

---

## 📊 Success Criteria

### Functional Requirements
- ✅ Navigation works identically on both platforms
- ✅ Storage API unified and consistent
- ✅ Platform detection 100% accurate
- ✅ API calls work seamlessly
- ✅ No platform-specific code in business logic

### Performance Requirements
- ✅ Platform detection < 1ms
- ✅ Navigation latency < 50ms
- ✅ Storage operations < 10ms
- ✅ Zero memory leaks
- ✅ Bundle size increase < 5KB

### Quality Requirements
- ✅ 100% test coverage for adapters
- ✅ TypeScript strict mode compliance
- ✅ No console errors/warnings
- ✅ Documentation complete
- ✅ Code review approved

## 🧪 Testing Strategy

### Unit Tests
```bash
# Run adapter tests
yarn test:adapters

# Test files structure
__tests__/
├── adapters/
│   ├── navigation/
│   │   ├── web.adapter.test.ts
│   │   └── zalo.adapter.test.ts
│   └── storage/
│       ├── web.adapter.test.ts
│       └── zalo.adapter.test.ts
```

### Integration Tests
- Test với real Zalo Mini App environment
- Test với multiple browsers
- Test với different screen sizes
- Test offline scenarios

### Manual Testing Checklist
- [ ] Test all navigation flows on Web
- [ ] Test all navigation flows on Zalo
- [ ] Verify storage persistence
- [ ] Check platform detection
- [ ] Validate API calls

## 📚 Documentation Requirements

### Code Documentation
- JSDoc cho all public APIs
- README cho each adapter
- Migration guide từ old code
- Example usage snippets

### Architecture Documentation
- Sequence diagrams cho navigation flow
- Class diagrams cho adapter pattern
- Decision log for design choices

## 🚨 Risk Mitigation

### Identified Risks
1. **Zalo API changes:** Monitor Zalo updates, add version checks
2. **Browser incompatibility:** Test on multiple browsers, add polyfills
3. **Performance regression:** Add performance monitoring
4. **Breaking changes:** Use feature flags for gradual rollout

## ✅ Phase 1 Completion Checklist

### Code Complete
- [ ] All adapters implemented
- [ ] All hooks created
- [ ] All tests passing
- [ ] Documentation complete
- [ ] Code reviewed and approved

### Integration Complete
- [ ] Integrated with existing codebase
- [ ] No regression in existing features
- [ ] Performance benchmarks met
- [ ] Security review passed

### Handoff Ready
- [ ] Demo video recorded
- [ ] Training materials prepared
- [ ] Known issues documented
- [ ] Phase 2 dependencies identified

---

## 📈 Metrics & Monitoring

### Track These Metrics
- Navigation success rate
- Storage operation latency
- Platform detection accuracy
- API call success rate
- Error rates by platform

### Monitoring Setup
```typescript
// Example monitoring
import { monitor } from '@/shared/utils/monitoring';

monitor.track('navigation', {
  from: currentRoute,
  to: newRoute,
  platform: getPlatform(),
  duration: endTime - startTime
});
```

## 🎉 Phase 1 Deliverables

By end of Week 1, we will have:
1. ✅ Fully functional navigation adapter system
2. ✅ Unified storage layer
3. ✅ Enhanced platform detection
4. ✅ Adapted API client
5. ✅ Complete test coverage
6. ✅ Comprehensive documentation

---

**Next:** [Phase 2 - Shared Components](./phase-2-components.md)

**Status:** 🟡 IN PLANNING
**Last Updated:** ${new Date().toISOString()}