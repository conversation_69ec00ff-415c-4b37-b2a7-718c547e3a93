# 💼 Phase 3: Business Logic Migration (Tuần 3)

## 🎯 <PERSON>ụ<PERSON>uyển 100% business logic vào shared package, đảm bảo zero business logic trong platform-specific code.

## ⏰ Timeline: 5 ngày

| Ngày | Focus Area | Deliverables |
|------|------------|--------------|
| Ngày 1 | State Management | Unified stores setup |
| Ngày 2 | API Services | All services migrated |
| Ngày 3 | Custom Hooks | Hooks library complete |
| Ngày 4 | Utilities & Helpers | Utils consolidated |
| Ngày 5 | Testing & Documentation | Full coverage |

## 📋 TASK BREAKDOWN

### 1️⃣ State Management Unification (Ngày 1)

#### 1.1 Store Architecture
**Strategy:** Sử dụng Zustand cho cả 2 platforms (đã có sẵn)

**Current Stores to Migrate:**
```typescript
// shared/stores/index.ts
export * from './auth.store';      // ✅ Already shared
export * from './home.store';       // ✅ Already shared
export * from './ocr.store';        // ✅ Already shared
export * from './merchant.store';   // ⚠️ Needs review
export * from './reward.store';     // ⚠️ Needs review
export * from './search.store';     // ❌ Create new
export * from './ui.store';         // ❌ Create new
```

#### 1.2 Auth Store Enhancement
**File:** `shared/stores/auth.store.ts`

**Checklist:**
- [ ] Add platform-specific auth methods
- [ ] Handle Zalo getUserInfo()
- [ ] Handle Web JWT flow
- [ ] Unify user profile structure
- [ ] Add auth persistence layer

#### 1.3 UI Store Creation
**File:** `shared/stores/ui.store.ts`

```typescript
interface UIStore {
  // Navigation state
  activeRoute: string;
  navigationHistory: string[];
  
  // Modal/Sheet state
  activeModal: string | null;
  modalData: any;
  
  // Loading states
  globalLoading: boolean;
  loadingTasks: Map<string, boolean>;
  
  // Platform-specific UI
  platform: Platform;
  theme: 'light' | 'dark';
  
  // Actions
  setActiveModal: (modal: string | null, data?: any) => void;
  setLoading: (task: string, loading: boolean) => void;
  // ...
}
```

**Checklist:**
- [ ] Create UI store for shared UI state
- [ ] Handle modal/bottomsheet state
- [ ] Manage loading states globally
- [ ] Track navigation state
- [ ] Handle platform-specific UI state

#### 1.4 Search Store
**File:** `shared/stores/search.store.ts`

**Checklist:**
- [ ] Create search history management
- [ ] Handle search suggestions
- [ ] Cache search results
- [ ] Implement recent searches
- [ ] Add search filters state

---

### 2️⃣ API Services Migration (Ngày 2)

#### 2.1 Service Architecture
```
shared/services/
├── api/
│   ├── auth/
│   │   ├── auth.service.ts
│   │   ├── auth.types.ts
│   │   └── index.ts
│   ├── merchant/
│   ├── reward/
│   ├── ocr/
│   └── search/
├── http/
│   ├── httpClient.ts (enhanced in Phase 1)
│   └── interceptors/
└── index.ts
```

#### 2.2 Auth Service
**File:** `shared/services/api/auth/auth.service.ts`

**Checklist:**
- [ ] Migrate login/logout logic
- [ ] Handle platform-specific auth
- [ ] Add token refresh logic
- [ ] Implement session management
- [ ] Add biometric auth support (Zalo)

#### 2.3 OCR Service Enhancement
**File:** `shared/services/api/ocr/ocr.service.ts`

**Current Status:** Partially implemented

**Checklist:**
- [ ] Complete all OCR endpoints
- [ ] Add image upload handling
- [ ] Implement retry logic
- [ ] Add progress tracking
- [ ] Handle large file uploads

#### 2.4 Merchant Service
**File:** `shared/services/api/merchant/merchant.service.ts`

```typescript
class MerchantService {
  async searchMerchants(query: string, filters?: MerchantFilters) {
    // Implementation
  }
  
  async getMerchantDetail(id: string) {
    // Implementation
  }
  
  async getNearbyMerchants(location: Location) {
    // Platform-specific location handling
    if (isZaloMiniApp()) {
      const { getLocation } = await import('zmp-sdk');
      const loc = await getLocation();
      // ...
    }
  }
}
```

**Checklist:**
- [ ] Implement all merchant endpoints
- [ ] Add location-based services
- [ ] Handle merchant categories
- [ ] Add favorite merchants
- [ ] Implement merchant reviews

#### 2.5 Reward Service
**Checklist:**
- [ ] Get rewards list with filters
- [ ] Get reward detail
- [ ] Handle reward redemption
- [ ] Track reward history
- [ ] Implement point calculation

---

### 3️⃣ Custom Hooks Library (Ngày 3)

#### 3.1 Core Hooks
**Location:** `shared/hooks/`

**Existing Hooks to Enhance:**
```typescript
// Already exists, need enhancement
useAuth()         // ⚠️ Add platform detection
useStorage()      // ⚠️ Use new adapter
useNavigation()   // ✅ Created in Phase 1

// New hooks to create
useApi()          // ❌ API call wrapper
useInfiniteScroll() // ❌ Pagination
useDebounce()     // ❌ Input debouncing
useMediaQuery()   // ❌ Responsive design
usePlatform()     // ❌ Platform detection
```

#### 3.2 useApi Hook
**File:** `shared/hooks/useApi.ts`

```typescript
function useApi<T>(
  apiCall: () => Promise<T>,
  options?: UseApiOptions
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Implementation with:
  // - Auto retry
  // - Caching
  // - Error handling
  // - Loading states
  
  return { data, loading, error, refetch };
}
```

**Checklist:**
- [ ] Create generic API hook
- [ ] Add caching layer
- [ ] Implement retry logic
- [ ] Handle loading states
- [ ] Add error boundaries

#### 3.3 useInfiniteScroll Hook
**File:** `shared/hooks/useInfiniteScroll.ts`

**Checklist:**
- [ ] Implement infinite scroll logic
- [ ] Handle pagination
- [ ] Add loading indicators
- [ ] Optimize performance
- [ ] Support both platforms

#### 3.4 Platform-Specific Hooks
```typescript
// Camera/Scanner hook
useCameraScanner() {
  if (isZaloMiniApp()) {
    // Use Zalo scanQRCode
  } else {
    // Use web camera API
  }
}

// Location hook
useLocation() {
  if (isZaloMiniApp()) {
    // Use Zalo getLocation
  } else {
    // Use browser geolocation
  }
}
```

**Checklist:**
- [ ] Create camera/scanner hook
- [ ] Create location hook
- [ ] Create share hook
- [ ] Create notification hook
- [ ] Document platform differences

---

### 4️⃣ Utilities Consolidation (Ngày 4)

#### 4.1 Utility Functions
**Location:** `shared/utils/`

**Current Utils:**
```typescript
shared/utils/
├── platform.ts       // ✅ Enhanced in Phase 1
├── formatters.ts     // ⚠️ Need consolidation
├── validators.ts     // ❌ Create new
├── constants.ts      // ⚠️ Need review
├── helpers.ts        // ❌ Create new
└── date.utils.ts     // ❌ Create new
```

#### 4.2 Formatters
**File:** `shared/utils/formatters.ts`

```typescript
export const formatters = {
  currency: (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  },
  
  phone: (phone: string) => {
    // Format Vietnamese phone numbers
  },
  
  date: (date: Date, format: string) => {
    // Date formatting
  },
  
  points: (points: number) => {
    // Format loyalty points
  }
};
```

**Checklist:**
- [ ] Currency formatter
- [ ] Phone number formatter
- [ ] Date/time formatter
- [ ] Points formatter
- [ ] Distance formatter

#### 4.3 Validators
**File:** `shared/utils/validators.ts`

**Checklist:**
- [ ] Phone number validation
- [ ] Email validation
- [ ] OTP validation
- [ ] Bill/Receipt validation
- [ ] Form validation utilities

#### 4.4 Constants
**File:** `shared/constants/index.ts`

**Checklist:**
- [ ] API endpoints
- [ ] Error messages
- [ ] Validation rules
- [ ] App configuration
- [ ] Feature flags

---

### 5️⃣ Data Models & Types (Ngày 5)

#### 5.1 Type Definitions
**Location:** `shared/types/`

```typescript
shared/types/
├── api/           // API response types
├── models/        // Data models
├── platform/      // Platform-specific types
└── index.ts       // Export all
```

#### 5.2 Unified Data Models
**File:** `shared/types/models/index.ts`

```typescript
// User model works for both platforms
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  email?: string;
  avatar?: string;
  loyaltyPoints: number;
  membershipTier: MembershipTier;
  // Platform-specific fields
  zaloId?: string;
  webUserId?: string;
}
```

**Checklist:**
- [ ] User model
- [ ] Merchant model
- [ ] Reward model
- [ ] Transaction model
- [ ] Bill/OCR model

#### 5.3 API Response Types
**Checklist:**
- [ ] Standardize API responses
- [ ] Error response types
- [ ] Pagination types
- [ ] Status types
- [ ] Meta types

---

## 📊 Migration Progress Tracker

| Category | Items | Migrated | Tested | Documented |
|----------|-------|----------|--------|------------|
| **Stores** | 7 | 3/7 | 2/7 | 2/7 |
| **Services** | 12 | 5/12 | 3/12 | 3/12 |
| **Hooks** | 15 | 8/15 | 5/15 | 5/15 |
| **Utils** | 20 | 10/20 | 8/20 | 5/20 |
| **Types** | 30 | 15/30 | 15/30 | 10/30 |

## 🧪 Testing Strategy

### Unit Tests Structure
```
shared/
├── __tests__/
│   ├── stores/
│   │   ├── auth.store.test.ts
│   │   └── ...
│   ├── services/
│   │   ├── auth.service.test.ts
│   │   └── ...
│   ├── hooks/
│   │   ├── useApi.test.ts
│   │   └── ...
│   └── utils/
│       ├── formatters.test.ts
│       └── ...
```

### Test Coverage Requirements
- Stores: 100% coverage
- Services: 95% coverage
- Hooks: 90% coverage
- Utils: 100% coverage

## 🚨 Common Issues & Solutions

### Issue: Circular Dependencies
**Solution:** Use dependency injection, lazy imports

### Issue: Platform-Specific Business Logic
**Solution:** Use strategy pattern with platform detection

### Issue: State Synchronization
**Solution:** Use single source of truth in stores

### Issue: Type Safety
**Solution:** Strict TypeScript, no any types

## ✅ Phase 3 Completion Checklist

### Code Migration
- [ ] All stores in shared/
- [ ] All services in shared/
- [ ] All hooks in shared/
- [ ] All utils in shared/
- [ ] All types defined

### Quality Assurance
- [ ] Unit tests >90% coverage
- [ ] Integration tests passing
- [ ] TypeScript strict mode
- [ ] No console warnings
- [ ] Performance benchmarks met

### Documentation
- [ ] API documentation
- [ ] Hook usage guides
- [ ] Service documentation
- [ ] Migration notes
- [ ] Best practices guide

## 📈 Success Metrics

- **Business Logic in Shared:** 100%
- **Code Duplication:** 0%
- **Test Coverage:** >90%
- **Type Coverage:** 100%
- **Bundle Size Impact:** <20KB

## 🎉 Phase 3 Deliverables

By end of Week 3:
1. ✅ All business logic in shared package
2. ✅ Complete hook library
3. ✅ Unified service layer
4. ✅ Comprehensive type system
5. ✅ Zero platform-specific business logic

---

**Next:** [Phase 4 - Page Migration](./phase-4-pages.md)

**Status:** 🟡 IN PLANNING
**Last Updated:** ${new Date().toISOString()}