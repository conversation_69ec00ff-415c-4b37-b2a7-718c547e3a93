# Image Resize Examples

## Basic Usage Examples

```typescript
import { resizeImage, resizeImageWithPreset, getAssetUrl, isImageResizeEnabled } from '@taptap/shared';

// Basic resize - creates the exact URL format you requested
const resizedUrl = resizeImage('stag/media/image/42d30801b74e7cf62b49279acf8906e5.png', {
  width: 80,
  quality: 75
});
// Result: https://vui-cdn.taptap.vn/cdn-cgi/image/width=80,quality=75/stag/media/image/42d30801b74e7cf62b49279acf8906e5.png
```

## Environment-Based Behavior

### When `VITE_ENABLE_IMAGE_RESIZE=true` (default)
```typescript
const imageUrl = resizeImage('path/to/image.jpg', { width: 300, quality: 85 });
// Result: https://vui-cdn.taptap.vn/cdn-cgi/image/width=300,quality=85/path/to/image.jpg
```

### When `VITE_ENABLE_IMAGE_RESIZE=false`
```typescript
// Option 1: Return original URL
const imageUrl = resizeImage('path/to/image.jpg', { width: 300, quality: 85 });
// Result: path/to/image.jpg

// Option 2: Fallback to CDN asset URL
const imageUrl = resizeImage('path/to/image.jpg', { width: 300, quality: 85 }, true);
// Result: https://vui-cdn.taptap.vn/path/to/image.jpg
```

## React Component Example

```tsx
import React from 'react';
import { resizeImage, resizeImageWithPreset, isImageResizeEnabled } from '@taptap/shared';

interface OptimizedImageProps {
  src: string;
  width?: number;
  height?: number;
  quality?: number;
  preset?: 'thumbnail' | 'avatar' | 'cardImage' | 'banner';
  fallbackToCDN?: boolean;
  alt: string;
  className?: string;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  width,
  height,
  quality = 80,
  preset,
  fallbackToCDN = false,
  alt,
  className
}) => {
  const getOptimizedUrl = () => {
    if (preset) {
      return resizeImageWithPreset(src, preset);
    }
    
    if (width || height) {
      return resizeImage(src, { width, height, quality }, fallbackToCDN);
    }
    
    return src;
  };

  return (
    <img 
      src={getOptimizedUrl()} 
      alt={alt} 
      className={className}
      loading="lazy" 
    />
  );
};

// Usage examples
export const ImageExamples = () => {
  return (
    <div>
      {/* Using presets */}
      <OptimizedImage 
        src="stag/media/image/42d30801b74e7cf62b49279acf8906e5.png" 
        preset="thumbnail" 
        alt="Thumbnail"
      />
      
      {/* Custom dimensions */}
      <OptimizedImage 
        src="path/to/product.jpg" 
        width={300} 
        height={300} 
        quality={90}
        alt="Product image"
      />
      
      {/* With CDN fallback when resizing is disabled */}
      <OptimizedImage 
        src="path/to/hero.jpg" 
        width={800} 
        quality={85}
        fallbackToCDN={true}
        alt="Hero image"
      />
      
      {/* Conditional rendering based on resize availability */}
      {isImageResizeEnabled() ? (
        <OptimizedImage 
          src="path/to/image.jpg" 
          preset="banner" 
          alt="Optimized banner"
        />
      ) : (
        <img src="path/to/image.jpg" alt="Original banner" />
      )}
    </div>
  );
};
```

## Development vs Production Configuration

### Development (.env.development)
```env
# Disable resizing for faster development
VITE_ENABLE_IMAGE_RESIZE=false
VITE_CDN_IMAGE_BASE_URL=https://vui-cdn.taptap.vn/cdn-cgi/image
VITE_CDN_ASSETS_BASE_URL=https://vui-cdn.taptap.vn
```

### Production (.env.production)
```env
# Enable resizing for optimized performance
VITE_ENABLE_IMAGE_RESIZE=true
VITE_CDN_IMAGE_BASE_URL=https://vui-cdn.taptap.vn/cdn-cgi/image
VITE_CDN_ASSETS_BASE_URL=https://vui-cdn.taptap.vn
VITE_CDN_DEFAULT_QUALITY=80
VITE_CDN_DEFAULT_FORMAT=auto
```

## Responsive Images Setup

```tsx
import { generateResponsiveImages, resizeImage } from '@taptap/shared';

const ResponsiveImageComponent: React.FC<{ src: string; alt: string }> = ({ src, alt }) => {
  const responsiveUrls = generateResponsiveImages(src, [320, 640, 1024, 1440], 80);
  
  return (
    <picture>
      <source 
        media="(min-width: 1440px)" 
        srcSet={responsiveUrls[1440]} 
      />
      <source 
        media="(min-width: 1024px)" 
        srcSet={responsiveUrls[1024]} 
      />
      <source 
        media="(min-width: 640px)" 
        srcSet={responsiveUrls[640]} 
      />
      <img 
        src={responsiveUrls[320]} 
        alt={alt}
        loading="lazy"
        style={{ width: '100%', height: 'auto' }}
      />
    </picture>
  );
};
```

## Performance Tips

1. **Use Presets**: Leverage predefined presets for consistency
2. **Enable in Production**: Always enable resizing in production for better performance
3. **Disable in Development**: Consider disabling in development for faster builds
4. **WebP Format**: Use `format: 'webp'` for modern browsers
5. **Quality Settings**: Balance quality vs file size (80-85 is usually optimal)
6. **Lazy Loading**: Always use `loading="lazy"` for images below the fold