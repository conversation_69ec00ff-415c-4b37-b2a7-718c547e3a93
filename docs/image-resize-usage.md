# Image Resize Utility

The `resizeImage` utility provides easy image transformation using TapTap's CDN powered by Cloudflare Image Resizing.

## Environment Configuration

The CDN base URL is configurable via environment variables:

```env
# .env file
VITE_ENABLE_IMAGE_RESIZE=true                                    # Enable/disable image resizing
VITE_CDN_IMAGE_BASE_URL=https://vui-cdn.taptap.vn/cdn-cgi/image  # Image resize CDN URL
VITE_CDN_ASSETS_BASE_URL=https://vui-cdn.taptap.vn               # Static assets CDN URL
VITE_CDN_DEFAULT_QUALITY=80                                      # Default image quality
VITE_CDN_DEFAULT_FORMAT=auto                                     # Default image format
```

The function automatically uses `CDN_CONFIG` from the shared configuration.

## Basic Usage

```typescript
import { resizeImage, resizeImageWithPreset, IMAGE_PRESETS } from '@taptap/shared';

// Basic resize with width and quality
const resizedUrl = resizeImage('stag/media/image/42d30801b74e7cf62b49279acf8906e5.png', {
  width: 80,
  quality: 75
});
// Result: https://vui-cdn.taptap.vn/cdn-cgi/image/width=80,quality=75/stag/media/image/42d30801b74e7cf62b49279acf8906e5.png
```

## Available Options

```typescript
interface ImageResizeOptions {
  width?: number;       // Resize width
  height?: number;      // Resize height  
  quality?: number;     // Image quality (1-100)
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  format?: 'auto' | 'avif' | 'webp' | 'json';
}
```

## Using Presets

```typescript
// Use predefined presets
const thumbnailUrl = resizeImageWithPreset(imageUrl, 'thumbnail'); // 80x80, quality 75
const avatarUrl = resizeImageWithPreset(imageUrl, 'avatar');       // 100x100, quality 85, cover fit
const cardImageUrl = resizeImageWithPreset(imageUrl, 'cardImage'); // width 300, quality 80

// Available presets
IMAGE_PRESETS = {
  thumbnail: { width: 80, quality: 75 },
  thumbnailSmall: { width: 50, quality: 75 },
  thumbnailLarge: { width: 120, quality: 75 },
  cardImage: { width: 300, quality: 80 },
  cardImageSmall: { width: 200, quality: 80 },
  avatar: { width: 100, height: 100, quality: 85, fit: 'cover' },
  avatarSmall: { width: 50, height: 50, quality: 85, fit: 'cover' },
  avatarLarge: { width: 200, height: 200, quality: 85, fit: 'cover' },
  banner: { width: 800, quality: 85 },
  bannerSmall: { width: 400, quality: 80 },
  highQuality: { quality: 95 },
  webp: { format: 'webp', quality: 80 },
}
```

## Responsive Images

```typescript
// Generate multiple sizes for responsive images
const responsiveImages = generateResponsiveImages(imageUrl, [320, 640, 1024], 80);
// Returns: { 320: 'url320', 640: 'url640', 1024: 'url1024' }

// Use in React components
<picture>
  <source media="(min-width: 1024px)" srcSet={responsiveImages[1024]} />
  <source media="(min-width: 640px)" srcSet={responsiveImages[640]} />
  <img src={responsiveImages[320]} alt="Responsive image" />
</picture>
```

## Enable/Disable Feature

```typescript
import { resizeImage, isImageResizeEnabled, getAssetUrl } from '@taptap/shared';

// Check if resizing is enabled
if (isImageResizeEnabled()) {
  console.log('Image resizing is enabled');
}

// When VITE_ENABLE_IMAGE_RESIZE=false
const imageUrl = resizeImage('path/to/image.jpg', { width: 300 });
// Returns: 'path/to/image.jpg' (original URL)

// With fallback to CDN assets
const imageUrl = resizeImage('path/to/image.jpg', { width: 300 }, true);
// Returns: 'https://vui-cdn.taptap.vn/path/to/image.jpg' (CDN asset URL)

// Static assets (always works regardless of resize setting)
const logoUrl = getAssetUrl('images/logo.png');
// Returns: 'https://vui-cdn.taptap.vn/images/logo.png'
```

## Advanced Examples

```typescript
// High quality product image
const productImage = resizeImage(imageUrl, {
  width: 500,
  height: 500,
  quality: 95,
  fit: 'cover',
  format: 'webp'
});

// Banner with exact dimensions
const bannerImage = resizeImage(imageUrl, {
  width: 800,
  height: 400,
  quality: 85,
  fit: 'crop'
});

// Thumbnail with padding
const paddedThumb = resizeImage(imageUrl, {
  width: 150,
  height: 150,
  quality: 80,
  fit: 'pad'
});
```

## Component Integration

```tsx
// React component example
interface ImageProps {
  src: string;
  size?: keyof typeof IMAGE_PRESETS;
  width?: number;
  quality?: number;
  className?: string;
}

const OptimizedImage: React.FC<ImageProps> = ({ 
  src, 
  size = 'cardImage', 
  width, 
  quality = 80,
  className 
}) => {
  const imageUrl = width 
    ? resizeImage(src, { width, quality })
    : resizeImageWithPreset(src, size);
    
  return <img src={imageUrl} className={className} />;
};

// Usage
<OptimizedImage src="path/to/image.jpg" size="avatar" />
<OptimizedImage src="path/to/image.jpg" width={300} quality={90} />
```