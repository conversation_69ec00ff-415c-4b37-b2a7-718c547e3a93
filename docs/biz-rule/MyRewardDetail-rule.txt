Dựa trên mã nguồn được cung cấp, đây là tài liệu về các quy tắc và điều kiện để hiển thị giao diện người dùng (UI) của màn hình chi tiết phần thưởng (MyRewardDetail).

Tổng quan

Màn hình MyRewardDetail hiển thị chi tiết một phần thưởng cụ thể của người dùng. Giao diện của màn hình này rất linh hoạt và thay đổi dựa trên nhiều yếu-tố-như trạng thái của voucher, loại voucher, thông tin từ nhà cung cấp, và các hành động của người dùng.

1. Trạng thái toàn màn hình

Giao diện màn hình có hai trạng thái chính:

<PERSON><PERSON> tải hoặc Lỗi (Loading/Error State):

Đ<PERSON>ều kiện: <PERSON><PERSON> dữ liệu chi tiết của phần thưởng (detail) chưa được tải về.

UI:

Hiển thị một StatusOnScreen component với trạng thái đang tải (loading) hoặc lỗi tải (loading_error).

Một thanh header giả với màu gradient và nút "Quay lại" được hiển thị để người dùng có thể thoát ra.

Đã tải dữ liệu (Data Loaded State):

Điều kiện: Khi dữ liệu chi tiết phần thưởng (detail) đã có.

UI: Toàn bộ nội dung chi tiết của phần thưởng sẽ được hiển thị trong một Animated.ScrollView. Các thành phần chính bao gồm:

Header

Thẻ thông tin chính (Voucher Card)

Các mục thông tin chi tiết

Thanh hành động dưới cùng (Bottom Panel)

2. Các thành phần giao diện chi tiết
A. Header

Nền và Animation:

Hiển thị một BackgroundAnimation có màu nền được lấy từ detail?.bgColor (mặc định là màu vàng COLORS.primaryYellow).

Hiển thị một HeaderAnimation mờ dần khi người dùng cuộn trang.

Nút Quay lại (Back Button): Luôn hiển thị.

Nút Mã thành viên (Member Code Button): Luôn hiển thị, cho phép điều hướng đến màn hình barcode thành viên.

B. Thẻ thông tin chính (Voucher Card - DetailHeader)

Đây là khu vực trung tâm, chứa các thông tin quan trọng nhất và có nhiều logic hiển thị phức tạp.

Thông tin cơ bản: Luôn hiển thị logo, tên nhà cung cấp (merchantName), và tên phần thưởng (name).

Thông tin chuyển/nhận voucher (SectionVoucherTransfer):

Điều kiện: Chỉ hiển thị khi voucher không thuộc sở hữu gốc của người dùng (ownershipStatus khác OWNED).

UI:

Nếu là voucher nhận (RECEIVED): Hiển thị thông tin người tặng (tên, số điện thoại, avatar) và ngày nhận.

Nếu là voucher đã tặng (TRANSFERRED): Hiển thị thông tin người nhận (tên, số điện thoại, avatar) và ngày tặng.

Khu vực hiển thị thời gian (renderMiddleTopSection):

Điều kiện 1: Nếu là voucher tự động kích hoạt (autoRedeem là true) và đã có ngày kích hoạt (activeAutoRedeemDate tồn tại) -> Không hiển thị gì cả.

Điều kiện 2: Các trường hợp còn lại -> Hiển thị SectionTimerReward.

SectionTimerReward sẽ hiển thị thông tin hạn sử dụng dựa trên nhiều quy tắc phức tạp (được giải mã từ settingRedeemData), số lượt sử dụng còn lại (remainRedeemCount), và ngày bắt đầu/kết thúc. Các câu chữ sẽ thay đổi tùy theo voucher có hiệu lực ngay lập tức, theo ngày cụ thể, dùng 1 lần hay nhiều lần.

Khu vực hành động chính (renderMiddleBottonSection): Đây là khu vực có nhiều điều kiện lồng nhau nhất. Chỉ một trong các mục sau sẽ được hiển thị theo thứ tự ưu tiên:

Voucher Khảo sát/Điền form (SectionSurveyForm):

Điều kiện: posIdentifier của voucher có định dạng là merchandise,... hoặc form_access_trade,....

UI thay đổi dựa trên trạng thái submitted:

Chưa điền form (submitted là false): Hiển thị mô tả và nút "Điền thông tin nhận quà". Nút này sẽ bị vô hiệu hóa nếu voucher đã hết hạn.

Đã điền form (submitted là true): Hiển thị trạng thái "Đã gửi thông tin thành công" và mô tả tương ứng. Với loại merch, sẽ có thêm nút "Xem lại thông tin" để mở lại form.

Voucher 7-Eleven và chưa liên kết (SectionLink711):

Điều kiện: Voucher chỉ áp dụng cho thương hiệu 7-Eleven (redeemBrands[0] là BRAND_7_ELEVEN) VÀ người dùng chưa liên kết tài khoản 7-Eleven.

UI: Hiển thị một ảnh mã vạch bị làm mờ và nút "Liên kết ngay" để mở bottom sheet liên kết tài khoản.

Voucher Tự động kích hoạt (SectionAutoRedeem):

Điều kiện: Voucher có autoRedeem là true.

UI thay đổi dựa trên executeAutoRedeemDate:

Chưa kích hoạt (executeAutoRedeemDate không tồn tại): Hiển thị SectionWarningUseVoucher. Giao diện này bao gồm một cảnh báo về việc voucher sẽ tự động có hiệu lực trong X phút sau khi bấm nút, và một nút "Dùng ngay" để bắt đầu quá trình.

Đã kích hoạt (executeAutoRedeemDate tồn tại): Hiển thị SectionCountDownReward. Giao diện này bao gồm:

Một đồng hồ đếm ngược thời gian sử dụng còn lại.

Mã voucher và barcode.

Nút "Sao chép mã".

Khi hết thời gian đếm ngược hoặc voucher được sử dụng, giao diện sẽ chuyển sang trạng thái "Đã sử dụng".

Mặc định: Nếu không rơi vào các trường hợp trên, khu vực này sẽ không hiển thị gì.

C. Khu vực nội dung chi tiết (bên dưới thẻ voucher)

Thông tin nhà cung cấp bên thứ ba (SectionVendorInfo):

Điều kiện: Voucher đến từ nguồn là bên thứ ba (source === 'THIRD_PARTY').

UI: Hiển thị thông tin chi tiết về nhà cung cấp đó.

Đánh dấu đã sử dụng (SectionMarkUsed):

Điều kiện:

Voucher cho phép đánh dấu đã sử dụng (enableMarkAsUsed là true).

VÀ voucher chưa ở trạng thái cuối cùng (chưa DONE hoặc EXPIRED).

VÀ voucher chưa hết hạn (endTime).

UI: Hiển thị một dòng chữ "Đánh dấu là đã sử dụng" cùng với một nút gạt (Switch). Nút gạt này sẽ bị vô hiệu hóa nếu voucher đã được tặng đi (ownershipStatus === OwnershipStatus.TRANSFERRED).

Điều khoản & Điều kiện (HtmlReader):

Điều kiện: Luôn hiển thị. Nếu tnc có nội dung, nó sẽ được render dưới dạng HTML. Nếu không, sẽ hiển thị thông báo "Đang cập nhật".

Thông tin cửa hàng (MerchantInfo):

Điều kiện: Nếu thông tin chi tiết của cửa hàng (merchantInfo) được tải thành công.

UI: Hiển thị mô tả và thông tin liên hệ của cửa hàng.

Thư viện ảnh (ImageGallery):

Điều kiện: Cửa hàng có danh sách hình ảnh (imageList.length > 0).

Khoảng trống (Spacing):

Điều kiện: Hiển thị nếu voucher cho phép chuyển tặng (detail?.allowTransfer là true) để tránh nội dung bị che bởi nút "Tặng bạn bè".

D. Thanh hành động dưới cùng (Bottom Panel) và Tutorial

Nút Tặng bạn bè và Panel:

Điều kiện hiển thị Panel: Voucher cho phép chuyển tặng (allowTransfer là true) VÀ voucher đang ở trạng thái hoạt động (status là ACTIVE).

UI:

Thông báo hết lượt tặng: Nếu người dùng đã hết lượt tặng trong ngày (remainTransferDailyPerUser <= 0), một dòng thông báo màu đỏ sẽ hiển thị phía trên nút.

Nút "Tặng bạn bè":

Hiển thị trong BottomPanel.

Nút sẽ bị vô hiệu hóa (disabled) nếu người dùng đã hết lượt tặng trong ngày.

Hướng dẫn (Tutorial):

Điều kiện: Chỉ hiển thị cho các voucher cho phép tặng (allowTransfer là true).

UI:

Hướng dẫn sẽ được kích hoạt (showTutorial) sau khi BottomPanel (chứa nút tặng) hiển thị xong.

Hướng dẫn này chỉ hiển thị 1 lần duy nhất cho người dùng. Nó sẽ làm nổi bật nút "Tặng bạn bè" và giải thích tính năng này.