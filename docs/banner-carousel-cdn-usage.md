# BannerCarousel with CDN Image Optimization

The `BannerCarousel` component now includes automatic CDN image optimization using Cloudflare Image Resizing.

## Features Added

- ✅ **Automatic image resizing** via Cloudflare CDN
- ✅ **Responsive images** for different screen sizes (mobile: 414px, tablet: 768px, desktop: 1200px)
- ✅ **Optimized fallback images** for error states
- ✅ **Configurable image quality** (1-100)
- ✅ **Lazy loading** for performance
- ✅ **Preloading** for smooth UX
- ✅ **Environment-based toggling** (respects `VITE_ENABLE_IMAGE_RESIZE`)

## Usage Examples

### Basic Usage (Default Optimization)
```tsx
import { BannerCarousel } from '@taptap/shared';

const banners = [
  {
    id: '1',
    imageUrl: 'stag/media/image/banner-1.jpg',
    title: 'Welcome Banner',
    link: '/welcome'
  },
  {
    id: '2',
    imageUrl: 'stag/media/image/banner-2.jpg',
    title: 'Promotion Banner',
    link: '/promo'
  }
];

// Default: 85% quality, responsive images enabled
<BannerCarousel 
  banners={banners}
  onBannerClick={(banner) => console.log('Clicked:', banner)}
/>
```

### Custom Quality Settings
```tsx
// High quality for important banners
<BannerCarousel 
  banners={banners}
  imageQuality={95}
  onBannerClick={handleBannerClick}
/>

// Lower quality for faster loading
<BannerCarousel 
  banners={banners}
  imageQuality={70}
  onBannerClick={handleBannerClick}
/>
```

### Disable Responsive Images (Single Size)
```tsx
// Use only mobile-optimized images for all screen sizes
<BannerCarousel 
  banners={banners}
  enableResponsiveImages={false}
  imageQuality={85}
  onBannerClick={handleBannerClick}
/>
```

## Generated Image URLs

For a banner with `imageUrl: 'stag/media/image/banner.jpg'`:

### Default Settings (Quality: 85%, Responsive: true)
- **Mobile (414px)**: `https://vui-cdn.taptap.vn/cdn-cgi/image/width=414,quality=85,fit=cover,format=auto/stag/media/image/banner.jpg`
- **Tablet (768px)**: `https://vui-cdn.taptap.vn/cdn-cgi/image/width=768,quality=85,fit=cover,format=auto/stag/media/image/banner.jpg`
- **Desktop (1200px)**: `https://vui-cdn.taptap.vn/cdn-cgi/image/width=1200,quality=85,fit=cover,format=auto/stag/media/image/banner.jpg`

### With Responsive Disabled
- **All screens**: Uses mobile URL only (`width=414`)

## Environment Configuration

The component respects the global CDN configuration:

```env
# Enable/disable image resizing
VITE_ENABLE_IMAGE_RESIZE=true

# CDN URLs
VITE_CDN_IMAGE_BASE_URL=https://vui-cdn.taptap.vn/cdn-cgi/image
VITE_CDN_ASSETS_BASE_URL=https://vui-cdn.taptap.vn
```

### When Image Resizing is Disabled
- **Component behavior**: Falls back to CDN asset URLs
- **Result**: `https://vui-cdn.taptap.vn/stag/media/image/banner.jpg`

## Performance Benefits

1. **Bandwidth Savings**: 
   - Mobile users get 414px images (~30KB vs ~200KB)
   - Tablet users get optimally sized images
   - Desktop users get high-quality images

2. **Faster Loading**:
   - Lazy loading prevents blocking
   - Preloading ensures smooth UX
   - WebP format auto-detection

3. **Better UX**:
   - Responsive images prevent layout shifts
   - Optimized error states with proper fallbacks
   - Smooth transitions between slides

## Implementation Details

### Responsive Image Sources
```tsx
// Generated picture element
<picture className="w-full h-full">
  <source media="(min-width: 1024px)" srcSet={desktopUrl} />
  <source media="(min-width: 768px)" srcSet={tabletUrl} />
  <img src={mobileUrl} alt="Banner" loading="lazy" />
</picture>
```

### Error Handling
```tsx
// Fallback image with CDN optimization
<img
  src={getAssetUrl(imagesAssets.mascot.skeleton)}
  alt="Skeleton placeholder"
  className="w-full h-full opacity-50"
  loading="lazy"
/>
```

## Migration Guide

### Before (Original)
```tsx
<BannerCarousel banners={banners} onBannerClick={handleClick} />
```

### After (With Optimization)
```tsx
// Same API - optimization is automatic!
<BannerCarousel banners={banners} onBannerClick={handleClick} />

// Or with custom settings
<BannerCarousel 
  banners={banners} 
  onBannerClick={handleClick}
  imageQuality={90}
  enableResponsiveImages={true}
/>
```

**No breaking changes** - the component maintains full backward compatibility!