# Dependencies
node_modules/
**/node_modules/

# Build output
dist/
**/dist/
build/
**/build/

# Development files
.git/
.gitignore
.env
.env.local
.env.*.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# Test files
coverage/
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx

# Documentation
*.md
docs/

# Storybook
.storybook/
storybook-static/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
tmp/
temp/

# Legacy project (not needed for current build)
taptap-mobile/

# Backlog files
backlog/