import { defineConfig, devices } from '@playwright/test';
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  testDir: './e2e',
  /* Global setup for authentication */
  globalSetup: './e2e/global-setup.ts',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html'],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    // baseURL: 'http://127.0.0.1:3000',

    /* Collect trace for all tests */
    trace: 'on',
    
    /* Take screenshot for all tests */
    screenshot: 'on',
    
    /* Record video for all tests */
    video: 'on',
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'web-chromium',
      use: { 
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:9070',
        storageState: 'e2e/auth-state.json',
      },
      testDir: './e2e/web',
    },
    
    {
      name: 'web-firefox',
      use: { 
        ...devices['Desktop Firefox'],
        baseURL: 'http://localhost:9070',
        storageState: 'e2e/auth-state.json',
      },
      testDir: './e2e/web',
    },

    {
      name: 'web-webkit',
      use: { 
        ...devices['Desktop Safari'],
        baseURL: 'http://localhost:9070',
        storageState: 'e2e/auth-state.json',
      },
      testDir: './e2e/web',
    },

    /* Test against mobile viewports. */
    {
      name: 'web-mobile-chrome',
      use: { 
        ...devices['Pixel 5'],
        baseURL: 'http://localhost:9070',
        storageState: 'e2e/auth-state.json',
      },
      testDir: './e2e/web',
    },
    
    {
      name: 'web-mobile-safari',
      use: { 
        ...devices['iPhone 12'],
        baseURL: 'http://localhost:9070',
        storageState: 'e2e/auth-state.json',
      },
      testDir: './e2e/web',
    },

    /* Zalo Mini App testing */
    {
      name: 'zalo-chromium',
      use: { 
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3000', // Zalo dev server port
      },
      testDir: './e2e/zalo',
    },

    {
      name: 'zalo-mobile-chrome',
      use: { 
        ...devices['Pixel 5'],
        baseURL: 'http://localhost:3000',
      },
      testDir: './e2e/zalo',
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: [
    {
      command: 'yarn dev:web',
      url: 'http://localhost:9070',
      reuseExistingServer: !process.env.CI,
      timeout: 120 * 1000,
    },
    // Uncomment when testing Zalo Mini App
    // {
    //   command: 'yarn dev:zalo',
    //   url: 'http://localhost:3000',
    //   reuseExistingServer: !process.env.CI,
    //   timeout: 120 * 1000,
    // },
  ],
});