import React, { useState, useEffect } from "react";
import {
  perWidth,
  perHeight,
  resWidth,
  resHeight,
  moderateScale,
  responsiveFontSize,
  isLargeView,
  isTabletMode,
  getWindowWidth,
  getWindowHeight,
} from "./responsive";

/**
 * Demo component để minh họa cách sử dụng các function responsive
 */
const ResponsiveDemo: React.FC = () => {
  const [dimensions, setDimensions] = useState({
    width: getWindowWidth(),
    height: getWindowHeight(),
    isLarge: isLargeView(),
    isTablet: isTabletMode(),
  });

  useEffect(() => {
    const handleResize = () => {
      setDimensions({
        width: getWindowWidth(),
        height: getWindowHeight(),
        isLarge: isLargeView(),
        isTablet: isTabletMode(),
      });
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Demo các function responsive
  const demoValues = {
    perWidth50: perWidth(50), // 50% của short dimension
    perHeight30: perHeight(30), // 30% của long dimension
    resWidth100: resWidth(100), // Scale 100px từ standard width
    resHeight200: resHeight(200), // Scale 200px từ standard height
    moderateScale20: moderateScale(20, 0.5), // Moderate scale với factor 0.5
    responsiveFont16: responsiveFontSize(16, 0.1), // Responsive font size
  };

  return (
    <div className="responsive-container p-6 bg-gray-100 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="responsive-text font-bold text-2xl mb-6 text-center">
          Responsive Utility Demo
        </h1>

        {/* Current Dimensions */}
        <div className="responsive-card bg-white shadow-lg mb-6">
          <h2 className="text-lg font-semibold mb-4">Current Dimensions</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Window Width:</span>{" "}
              {dimensions.width}px
            </div>
            <div>
              <span className="font-medium">Window Height:</span>{" "}
              {dimensions.height}px
            </div>
            <div>
              <span className="font-medium">Is Large View:</span>{" "}
              {dimensions.isLarge ? "Yes" : "No"}
            </div>
            <div>
              <span className="font-medium">Is Tablet Mode:</span>{" "}
              {dimensions.isTablet ? "Yes" : "No"}
            </div>
          </div>
        </div>

        {/* Responsive Function Results */}
        <div className="responsive-card bg-white shadow-lg mb-6">
          <h2 className="text-lg font-semibold mb-4">
            Responsive Function Results
          </h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">perWidth(50):</span>{" "}
              {demoValues.perWidth50}px
            </div>
            <div>
              <span className="font-medium">perHeight(30):</span>{" "}
              {demoValues.perHeight30}px
            </div>
            <div>
              <span className="font-medium">resWidth(100):</span>{" "}
              {demoValues.resWidth100}px
            </div>
            <div>
              <span className="font-medium">resHeight(200):</span>{" "}
              {demoValues.resHeight200}px
            </div>
            <div>
              <span className="font-medium">moderateScale(20, 0.5):</span>{" "}
              {demoValues.moderateScale20}px
            </div>
            <div>
              <span className="font-medium">responsiveFontSize(16, 0.1):</span>{" "}
              {demoValues.responsiveFont16}px
            </div>
          </div>
        </div>

        {/* Responsive Components Demo */}
        <div className="space-y-6">
          <div className="responsive-button bg-primary-pink text-white rounded-lg text-center cursor-pointer">
            Responsive Button
          </div>

          <div className="responsive-input bg-white border border-gray-300 rounded-lg px-4">
            Responsive Input Field
          </div>

          <div className="responsive-grid-3">
            <div className="bg-white p-4 rounded-lg shadow text-center">
              <div className="responsive-icon bg-primary-pink rounded-full mx-auto mb-2"></div>
              <p className="text-sm">Grid Item 1</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow text-center">
              <div className="responsive-icon bg-secondary-yellow-70 rounded-full mx-auto mb-2"></div>
              <p className="text-sm">Grid Item 2</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow text-center">
              <div className="responsive-icon bg-grey-3 rounded-full mx-auto mb-2"></div>
              <p className="text-sm">Grid Item 3</p>
            </div>
          </div>

          <div className="responsive-image bg-gray-200 rounded-lg flex items-center justify-center">
            <span className="text-gray-500">Responsive Image Placeholder</span>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="responsive-card bg-blue-50 border border-blue-200 mt-6">
          <h3 className="text-lg font-semibold mb-3 text-blue-800">
            Usage Instructions
          </h3>
          <div className="text-sm text-blue-700 space-y-2">
            <p>
              <strong>JavaScript/TypeScript:</strong> Import functions from
              './responsive'
            </p>
            <p>
              <strong>CSS Classes:</strong> Use responsive-* classes for
              automatic responsive behavior
            </p>
            <p>
              <strong>Tailwind:</strong> Use responsive-* spacing, font sizes,
              and breakpoints
            </p>
            <p>
              <strong>Custom CSS:</strong> Use CSS custom properties like
              --window-width, --short-dimension
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveDemo;
