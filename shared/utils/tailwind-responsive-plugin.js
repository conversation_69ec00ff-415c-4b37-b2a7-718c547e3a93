/**
 * Tailwind CSS Plugin for Responsive Utilities
 * Provides CSS custom properties and utilities for responsive design
 */

const plugin = require("tailwindcss/plugin");

module.exports = plugin(
  function ({ addUtilities, addComponents, theme, variants }) {
    // Add CSS custom properties for responsive values
    const responsiveUtilities = {
      ".responsive-container": {
        "--window-width": "100vw",
        "--window-height": "100vh",
        "--short-dimension": "min(100vw, 100vh)",
        "--long-dimension": "max(100vw, 100vh)",
        "--standard-width": "375px",
        "--standard-height": "667px",
      },
      ".responsive-text": {
        "font-size": "clamp(12px, 2.5vw, 24px)",
        "line-height": "1.4",
      },
      ".responsive-spacing": {
        "--spacing-unit": "clamp(4px, 1vw, 16px)",
      },
    };

    // Add responsive component classes
    const responsiveComponents = {
      ".responsive-card": {
        width: "clamp(280px, 90vw, 400px)",
        height: "clamp(200px, 60vh, 300px)",
        padding: "clamp(16px, 4vw, 24px)",
        "border-radius": "clamp(8px, 2vw, 16px)",
      },
      ".responsive-button": {
        padding: "clamp(8px, 2vw, 16px) clamp(16px, 4vw, 24px)",
        "font-size": "clamp(14px, 2.5vw, 18px)",
        "min-height": "clamp(40px, 8vh, 56px)",
      },
      ".responsive-input": {
        height: "clamp(40px, 8vh, 56px)",
        padding: "clamp(8px, 2vw, 16px)",
        "font-size": "clamp(14px, 2.5vw, 16px)",
      },
      ".responsive-icon": {
        width: "clamp(16px, 4vw, 24px)",
        height: "clamp(16px, 4vw, 24px)",
      },
      ".responsive-image": {
        width: "clamp(200px, 60vw, 400px)",
        height: "clamp(150px, 45vh, 300px)",
        "object-fit": "cover",
      },
    };

    // Add responsive grid utilities
    const responsiveGridUtilities = {
      ".responsive-grid-2": {
        display: "grid",
        "grid-template-columns":
          "repeat(auto-fit, minmax(clamp(140px, 45vw, 200px), 1fr))",
        gap: "clamp(8px, 2vw, 16px)",
      },
      ".responsive-grid-3": {
        display: "grid",
        "grid-template-columns":
          "repeat(auto-fit, minmax(clamp(120px, 35vw, 180px), 1fr))",
        gap: "clamp(8px, 2vw, 16px)",
      },
      ".responsive-grid-4": {
        display: "grid",
        "grid-template-columns":
          "repeat(auto-fit, minmax(clamp(100px, 30vw, 150px), 1fr))",
        gap: "clamp(8px, 2vw, 16px)",
      },
    };

    // Add all utilities and components
    addUtilities(responsiveUtilities);
    addComponents(responsiveComponents);
    addUtilities(responsiveGridUtilities);
  },
  {
    theme: {
      extend: {
        // Add responsive breakpoints
        screens: {
          xs: "375px",
          sm: "640px",
          md: "768px",
          lg: "1024px",
          xl: "1280px",
          "2xl": "1536px",
          // Custom breakpoints for mobile-first approach
          mobile: "375px",
          tablet: "768px",
          desktop: "1024px",
          "large-desktop": "1280px",
        },
        // Add responsive spacing scale
        spacing: {
          "responsive-xs": "clamp(4px, 1vw, 8px)",
          "responsive-sm": "clamp(8px, 2vw, 16px)",
          "responsive-md": "clamp(16px, 4vw, 24px)",
          "responsive-lg": "clamp(24px, 6vw, 32px)",
          "responsive-xl": "clamp(32px, 8vw, 48px)",
          "responsive-2xl": "clamp(48px, 12vw, 64px)",
        },
        // Add responsive font sizes
        fontSize: {
          "responsive-xs": "clamp(10px, 2vw, 12px)",
          "responsive-sm": "clamp(12px, 2.5vw, 14px)",
          "responsive-base": "clamp(14px, 3vw, 16px)",
          "responsive-lg": "clamp(16px, 3.5vw, 18px)",
          "responsive-xl": "clamp(18px, 4vw, 24px)",
          "responsive-2xl": "clamp(24px, 5vw, 32px)",
          "responsive-3xl": "clamp(32px, 7vw, 48px)",
        },
        // Add responsive line heights
        lineHeight: {
          "responsive-tight": "1.2",
          "responsive-snug": "1.3",
          "responsive-normal": "1.4",
          "responsive-relaxed": "1.5",
          "responsive-loose": "1.6",
        },
      },
    },
  }
);
