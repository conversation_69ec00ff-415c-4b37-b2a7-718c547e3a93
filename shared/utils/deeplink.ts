/**
 * Deeplink URL conversion utilities for web app
 * Converts mobile deeplinks (taptapvui://) to web URLs
 */

export interface DeeplinkParams {
  [key: string]: string;
}

export interface ParsedDeeplink {
  screenName: string;
  params: DeeplinkParams;
  webUrl: string;
}

/**
 * Parse URL parameters from query string
 */
function parseUrlParams(url: string): DeeplinkParams {
  const params: DeeplinkParams = {};

  try {
    const urlObj = new URL(url);
    urlObj.searchParams.forEach((value, key) => {
      params[key] = decodeURIComponent(value);
    });
  } catch (error) {
    // Fallback for malformed URLs
    const regex = /[?&]([^=#]+)=([^&#]*)/g;
    let match;

    while ((match = regex.exec(url))) {
      params[match[1]] = decodeURIComponent(match[2] || "");
    }
  }

  return params;
}

/**
 * Convert deeplink screen name and params to web URL
 */
function convertToWebUrl(screenName: string, params: DeeplinkParams): string {
  // Game-related deeplinks
  if (screenName === "gameTAPTAP") {
    const { leaderboardId, gameType, title, screen } = params;

    if (screen === "main" && leaderboardId) {
      // Navigate to games page with specific game
      return `/games/${leaderboardId}?type=${
        gameType || ""
      }&title=${encodeURIComponent(title || "")}`;
    }

    // Default to games page
    return `/games`;
  }

  // Home tab navigation
  if (screenName === "homeTab") {
    return "/";
  }

  // Reward/Exchange navigation
  if (screenName === "rewardTab" || screenName === "exchange") {
    return "/exchange";
  }

  // Merchant navigation
  if (screenName === "merchantTab" || screenName === "merchant") {
    const { merchantId } = params;
    return merchantId ? `/merchants/${merchantId}` : "/exchange";
  }

  // Entertainment/Games navigation
  if (screenName === "entertainmentTab" || screenName === "entertainment") {
    return "/games";
  }

  // Account/Profile navigation
  if (
    screenName === "accountTab" ||
    screenName === "account" ||
    screenName === "profile"
  ) {
    return "/profile";
  }

  // News navigation
  if (screenName === "news") {
    const { newsId } = params;
    return newsId ? `/news/${newsId}` : "/news";
  }

  // Reward detail navigation
  if (screenName === "rewardDetail" || screenName === "reward") {
    const { rewardId, id } = params;
    const targetId = rewardId || id;
    return targetId ? `/reward/${targetId}` : "/exchange";
  }

  // Mission navigation
  if (screenName === "missions" || screenName === "mission") {
    return "/missions";
  }

  // Inbox navigation
  if (screenName === "inbox") {
    const { messageId, id } = params;
    const targetId = messageId || id;
    return targetId ? `/inbox/${targetId}` : "/inbox";
  }

  // My rewards navigation
  if (screenName === "myRewards") {
    const { rewardId, id } = params;
    const targetId = rewardId || id;
    return targetId ? `/my-rewards/${targetId}` : "/my-rewards";
  }

  // Camera/Bill scan navigation
  if (screenName === "camera" || screenName === "billScan") {
    return "/bill-scan/camera";
  }

  // Search navigation
  if (screenName === "search") {
    const { query } = params;
    return query ? `/search/results?q=${encodeURIComponent(query)}` : "/search";
  }

  // Gift code navigation
  if (screenName === "inputCode") {
    return "/gift-code";
  }

  if (screenName === "barcode") {
    return "/member-code";
  }

  // Default fallback to home
  return "/";
}

/**
 * Convert taptapvui:// deeplink to web URL
 */
export function convertDeeplinkToUrl(deeplink: string): ParsedDeeplink | null {
  if (!deeplink || !deeplink.startsWith("taptapvui://")) {
    return null;
  }

  try {
    // Remove the taptapvui:// prefix
    const urlPart = deeplink.replace("taptapvui://", "");

    if (!urlPart) {
      return {
        screenName: "home",
        params: {},
        webUrl: "/",
      };
    }

    // Split screen name and parameters
    const [screenName, ...queryParts] = urlPart.split("?");
    const queryString = queryParts.join("?");

    // Parse parameters
    const params = queryString
      ? parseUrlParams(`dummy://dummy?${queryString}`)
      : {};

    // Convert to web URL
    const webUrl = convertToWebUrl(screenName, params);

    return {
      screenName,
      params,
      webUrl,
    };
  } catch (error) {
    console.warn("Failed to parse deeplink:", deeplink, error);
    return {
      screenName: "home",
      params: {},
      webUrl: "/",
    };
  }
}

/**
 * Handle deeplink navigation - returns web URL for navigation
 */
export function handleDeeplinkNavigation(deeplink: string): string {
  // Handle regular HTTP/HTTPS URLs - return as-is
  if (deeplink.startsWith("http://") || deeplink.startsWith("https://")) {
    return deeplink;
  }

  // Handle taptapvui:// deeplinks
  const parsed = convertDeeplinkToUrl(deeplink);
  return parsed ? parsed.webUrl : "/";
}
