import { resizeImage, resizeImageWithPreset, generateResponsiveImages, IMAGE_PRESETS, isImageResizeEnabled, getCDNConfig, getAssetUrl } from '../imageResize';
import { CDN_CONFIG } from '../../constants/config';

describe('imageResize', () => {
  const testImagePath = 'stag/media/image/42d30801b74e7cf62b49279acf8906e5.png';
  const expectedBaseUrl = CDN_CONFIG.imageBaseUrl;

  describe('resizeImage', () => {
    it('should create correct URL with width and quality', () => {
      const result = resizeImage(testImagePath, { width: 80, quality: 75 });
      expect(result).toBe(`${expectedBaseUrl}/width=80,quality=75/${testImagePath}`);
    });

    it('should handle multiple parameters', () => {
      const result = resizeImage(testImagePath, { 
        width: 300, 
        height: 200, 
        quality: 85, 
        fit: 'cover' 
      });
      expect(result).toBe(`${expectedBaseUrl}/width=300,height=200,quality=85,fit=cover/${testImagePath}`);
    });

    it('should handle full URLs by extracting path', () => {
      const fullUrl = `https://example.com/${testImagePath}`;
      const result = resizeImage(fullUrl, { width: 80, quality: 75 });
      expect(result).toBe(`${expectedBaseUrl}/width=80,quality=75/${testImagePath}`);
    });

    it('should return original URL if no options provided', () => {
      const result = resizeImage(testImagePath, {});
      expect(result).toBe(testImagePath);
    });

    it('should return as-is if already a TapTap CDN URL with transformation', () => {
      const existingUrl = `${expectedBaseUrl}/width=100,quality=80/${testImagePath}`;
      const result = resizeImage(existingUrl, { width: 200 });
      expect(result).toBe(existingUrl);
    });

    it('should handle URLs with leading slash', () => {
      const pathWithSlash = `/${testImagePath}`;
      const result = resizeImage(pathWithSlash, { width: 80, quality: 75 });
      expect(result).toBe(`${expectedBaseUrl}/width=80,quality=75/${testImagePath}`);
    });
  });

  describe('resizeImageWithPreset', () => {
    it('should use thumbnail preset correctly', () => {
      const result = resizeImageWithPreset(testImagePath, 'thumbnail');
      expect(result).toBe(`${expectedBaseUrl}/width=80,quality=75/${testImagePath}`);
    });

    it('should use avatar preset correctly', () => {
      const result = resizeImageWithPreset(testImagePath, 'avatar');
      expect(result).toBe(`${expectedBaseUrl}/width=100,height=100,quality=85,fit=cover/${testImagePath}`);
    });
  });

  describe('generateResponsiveImages', () => {
    it('should generate multiple sizes', () => {
      const sizes = [320, 640, 1024];
      const result = generateResponsiveImages(testImagePath, sizes, 80);
      
      expect(result).toEqual({
        320: `${expectedBaseUrl}/width=320,quality=80/${testImagePath}`,
        640: `${expectedBaseUrl}/width=640,quality=80/${testImagePath}`,
        1024: `${expectedBaseUrl}/width=1024,quality=80/${testImagePath}`
      });
    });
  });

  describe('IMAGE_PRESETS', () => {
    it('should have expected preset configurations', () => {
      expect(IMAGE_PRESETS.thumbnail).toEqual({ width: 80, quality: 75 });
      expect(IMAGE_PRESETS.avatar).toEqual({ width: 100, height: 100, quality: 85, fit: 'cover' });
      expect(IMAGE_PRESETS.banner).toEqual({ width: 800, quality: 85 });
    });
  });

  describe('Enable/Disable Feature', () => {
    const originalEnableResize = CDN_CONFIG.enableImageResize;

    afterEach(() => {
      // Reset to original value
      (CDN_CONFIG as any).enableImageResize = originalEnableResize;
    });

    it('should return original URL when resizing is disabled', () => {
      (CDN_CONFIG as any).enableImageResize = false;
      
      const result = resizeImage(testImagePath, { width: 80, quality: 75 });
      expect(result).toBe(testImagePath);
    });

    it('should return CDN asset URL when resizing is disabled but fallbackToCDN is true', () => {
      (CDN_CONFIG as any).enableImageResize = false;
      
      const result = resizeImage(testImagePath, { width: 80, quality: 75 }, true);
      expect(result).toBe(`${CDN_CONFIG.assetsBaseUrl}/${testImagePath}`);
    });

    it('should work normally when resizing is enabled', () => {
      (CDN_CONFIG as any).enableImageResize = true;
      
      const result = resizeImage(testImagePath, { width: 80, quality: 75 });
      expect(result).toBe(`${expectedBaseUrl}/width=80,quality=75/${testImagePath}`);
    });
  });

  describe('Helper Functions', () => {
    it('should check if image resize is enabled', () => {
      const isEnabled = isImageResizeEnabled();
      expect(typeof isEnabled).toBe('boolean');
    });

    it('should get CDN configuration', () => {
      const config = getCDNConfig();
      expect(config).toHaveProperty('enableImageResize');
      expect(config).toHaveProperty('imageBaseUrl');
      expect(config).toHaveProperty('assetsBaseUrl');
      expect(config).toHaveProperty('defaultQuality');
      expect(config).toHaveProperty('defaultFormat');
    });

    it('should generate asset URL correctly', () => {
      const result = getAssetUrl('images/logo.png');
      expect(result).toBe(`${CDN_CONFIG.assetsBaseUrl}/images/logo.png`);
    });

    it('should handle asset URL with leading slash', () => {
      const result = getAssetUrl('/images/logo.png');
      expect(result).toBe(`${CDN_CONFIG.assetsBaseUrl}/images/logo.png`);
    });
  });
});