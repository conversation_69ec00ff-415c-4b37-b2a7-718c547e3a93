import { convertDeeplinkToUrl, handleDeeplinkNavigation } from '../deeplink';

describe('Deeplink Utils', () => {
  describe('convertDeeplinkToUrl', () => {
    it('should handle game deeplinks correctly', () => {
      const deeplink = 'taptapvui://gameTAPTAP?leaderboardId=673c0ea0deeda0f86ac45b84&gameType=baucua&title=DEMO 1&screen=main';
      const result = convertDeeplinkToUrl(deeplink);
      
      expect(result).toEqual({
        screenName: 'gameTAPTAP',
        params: {
          leaderboardId: '673c0ea0deeda0f86ac45b84',
          gameType: 'baucua',
          title: 'DEMO 1',
          screen: 'main'
        },
        webUrl: '/games/673c0ea0deeda0f86ac45b84?type=baucua&title=DEMO%201'
      });
    });

    it('should handle home navigation', () => {
      const deeplink = 'taptapvui://homeTab';
      const result = convertDeeplinkToUrl(deeplink);
      
      expect(result).toEqual({
        screenName: 'homeTab',
        params: {},
        webUrl: '/'
      });
    });

    it('should handle reward navigation', () => {
      const deeplink = 'taptapvui://reward?rewardId=123';
      const result = convertDeeplinkToUrl(deeplink);
      
      expect(result).toEqual({
        screenName: 'reward',
        params: {
          rewardId: '123'
        },
        webUrl: '/reward/123'
      });
    });

    it('should handle inbox navigation', () => {
      const deeplink = 'taptapvui://inbox?messageId=456';
      const result = convertDeeplinkToUrl(deeplink);
      
      expect(result).toEqual({
        screenName: 'inbox',
        params: {
          messageId: '456'
        },
        webUrl: '/inbox/456'
      });
    });

    it('should handle invalid deeplinks', () => {
      const deeplink = 'invalid://deeplink';
      const result = convertDeeplinkToUrl(deeplink);
      
      expect(result).toBeNull();
    });

    it('should handle empty deeplinks', () => {
      const deeplink = 'taptapvui://';
      const result = convertDeeplinkToUrl(deeplink);
      
      expect(result).toEqual({
        screenName: 'home',
        params: {},
        webUrl: '/'
      });
    });
  });

  describe('handleDeeplinkNavigation', () => {
    it('should handle HTTP URLs directly', () => {
      const url = 'https://example.com';
      const result = handleDeeplinkNavigation(url);
      
      expect(result).toBe('https://example.com');
    });

    it('should convert deeplinks to web URLs', () => {
      const deeplink = 'taptapvui://gameTAPTAP?leaderboardId=123&screen=main';
      const result = handleDeeplinkNavigation(deeplink);
      
      expect(result).toBe('/games/123?type=&title=');
    });

    it('should fallback to home for invalid deeplinks', () => {
      const deeplink = 'invalid://deeplink';
      const result = handleDeeplinkNavigation(deeplink);
      
      expect(result).toBe('/');
    });
  });
});