export interface Challenge {
  isDisplayRemainingTime?: boolean;
  endDate?: string | number | Date;
}

export const getTimeRemaining = (challenge: Challenge): string | undefined => {
  if (!challenge.isDisplayRemainingTime || !challenge.endDate) {
    return undefined;
  }

  const endDate = new Date(challenge.endDate);
  const now = new Date();
  const diffTime = endDate.getTime() - now.getTime();

  if (diffTime <= 0) {
    return "Hết hạn";
  }

  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  if (diffDays > 1) {
    return `${diffDays} ngày`;
  }

  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  if (diffHours > 1) {
    return `${diffHours} giờ`;
  }

  const diffMinutes = Math.ceil(diffTime / (1000 * 60));
  return `${diffMinutes} phút`;
};

/**
 * Formats a date to DD/MM/YYYY format
 */
export const formatDateToDDMMYYYY = (date: Date | string | number): string => {
  const d = new Date(date);
  const day = d.getDate().toString().padStart(2, '0');
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
};

/**
 * Formats expiry date for display in reward cards
 * Returns format like "31/12/2024" for future dates
 */
export const formatExpiryDate = (date: Date | string | number): string => {
  return formatDateToDDMMYYYY(date);
};
