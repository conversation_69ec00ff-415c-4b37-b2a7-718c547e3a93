/**
 * Responsive utility functions for web applications
 * Similar to React Native responsive functions but adapted for web
 */

// Standard window dimensions (iPhone 6/7/8 - 375x667)
const STANDARD_WINDOW = { width: 375, height: 667 };

// Get current viewport dimensions
const getViewportDimensions = () => {
  if (typeof window !== "undefined") {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    };
  }
  // Fallback for SSR
  return STANDARD_WINDOW;
};

// Get current dimensions
const getCurrentDimensions = () => {
  const { width: windowWidth, height: windowHeight } = getViewportDimensions();

  // Determine short and long dimensions
  const [shortDimension, longDimension] =
    windowWidth < windowHeight
      ? [windowWidth, windowHeight]
      : [windowHeight, windowWidth];

  return { shortDimension, longDimension, windowWidth, windowHeight };
};

// Check if current view is large (tablet or desktop)
export const isLargeView = (): boolean => {
  const { shortDimension } = getCurrentDimensions();
  return shortDimension >= 600;
};

// Check if current view is in tablet mode
export const isTabletMode = (): boolean => {
  const { shortDimension, longDimension } = getCurrentDimensions();
  return shortDimension / longDimension > 0.7;
};

// Get status bar height (web equivalent)
export const getStatusBarHeight = (): number => {
  // For web, we can use CSS custom properties or return 0
  // This can be overridden with CSS variables if needed
  return 0;
};

// Hit slop for touch targets (web equivalent)
export const hitSlop = {
  left: 20,
  right: 20,
  top: 20,
  bottom: 20,
};

/**
 * Percentage-based width calculation
 * @param size - Percentage value (0-100)
 * @returns Calculated width in pixels
 */
export const perWidth = (size: number): number => {
  const { shortDimension } = getCurrentDimensions();
  return Math.round((shortDimension * size) / 100);
};

/**
 * Percentage-based height calculation
 * @param size - Percentage value (0-100)
 * @returns Calculated height in pixels
 */
export const perHeight = (size: number): number => {
  const { longDimension } = getCurrentDimensions();
  return Math.round((longDimension * size) / 100);
};

/**
 * Responsive width calculation based on standard window
 * @param size - Size in pixels for standard window (375px width)
 * @returns Scaled size for current viewport
 */
export const resWidth = (size: number): number => {
  return size;
  const { shortDimension } = getCurrentDimensions();
  return Math.round((shortDimension / STANDARD_WINDOW.width) * size);
};

/**
 * Responsive height calculation based on standard window
 * @param size - Size in pixels for standard window (667px height)
 * @returns Scaled size for current viewport
 */
export const resHeight = (size: number): number => {
  const { longDimension } = getCurrentDimensions();
  return Math.round((longDimension / STANDARD_WINDOW.height) * size);
};

/**
 * Moderate scale function for non-linear scaling
 * @param size - Base size
 * @param factor - Scale factor (default: 0.5)
 * @returns Moderately scaled size
 */
export const moderateScale = (size: number, factor: number = 0.5): number => {
  const { shortDimension } = getCurrentDimensions();
  const scale = shortDimension / STANDARD_WINDOW.width;
  const newSize = size + (scale - 1) * factor * size;
  return Math.round(newSize);
};

/**
 * Responsive font size calculation
 * @param size - Base font size
 * @param factor - Scale factor (default: 0.1)
 * @returns Responsive font size
 */
export const responsiveFontSize = (
  size: number,
  factor: number = 0.1
): number => {
  return moderateScale(size, factor);
};

/**
 * Get current viewport width
 * @returns Current viewport width
 */
export const getWindowWidth = (): number => {
  return getCurrentDimensions().windowWidth;
};

/**
 * Get current viewport height
 * @returns Current viewport height
 */
export const getWindowHeight = (): number => {
  return getCurrentDimensions().windowHeight;
};

/**
 * Get short dimension (width for portrait, height for landscape)
 * @returns Short dimension value
 */
export const getShortDimension = (): number => {
  return getCurrentDimensions().shortDimension;
};

/**
 * Get long dimension (height for portrait, width for landscape)
 * @returns Long dimension value
 */
export const getLongDimension = (): number => {
  return getCurrentDimensions().longDimension;
};

// Export all functions as default object for convenience
export default {
  perWidth,
  perHeight,
  resWidth,
  resHeight,
  moderateScale,
  responsiveFontSize,
  isLargeView,
  isTabletMode,
  getStatusBarHeight,
  getWindowWidth,
  getWindowHeight,
  getShortDimension,
  getLongDimension,
  hitSlop,
};
