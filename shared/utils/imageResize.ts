import { CDN_CONFIG } from '../constants/config';

/**
 * Image resize utility using Cloudflare Image Resizing
 * Transforms image URLs to use TapTap's CDN with resize parameters
 */

export interface ImageResizeOptions {
  width?: number;
  height?: number;
  quality?: number;
  fit?: 'scale-down' | 'contain' | 'cover' | 'crop' | 'pad';
  format?: 'auto' | 'avif' | 'webp' | 'json';
}

/**
 * Get the full CDN URL for static assets (without resizing)
 * 
 * @param assetPath - Path to the asset
 * @returns Full CDN URL
 * 
 * @example
 * ```typescript
 * const logoUrl = getAssetUrl('images/logo.png');
 * // Returns: https://vui-cdn.taptap.vn/images/logo.png
 * ```
 */
export function getAssetUrl(assetPath: string): string {
  // If running locally, return the original asset path
  if (typeof window !== 'undefined') {
    const host = window.location.hostname;
    if (host === 'localhost' || host === '127.0.0.1' || host === '::1') {
      return assetPath;
    }
  }

  // If assetPath is already an absolute URL, return as-is
  if (typeof assetPath === 'string' && assetPath.startsWith('http')) {
    return assetPath;
  }

  // Remove leading slash if present
  const cleanPath = assetPath.startsWith('/') ? assetPath.slice(1) : assetPath;
  
  return `${CDN_CONFIG.assetsBaseUrl}/${cleanPath}`;
}

/**
 * Extract domain from image URL, or use fallback
 * 
 * @param imageUrl - Original image URL
 * @returns Domain to use for CDN transformation
 */
function extractDomainFromUrl(imageUrl: string): string {
  // If already a full URL, extract the domain
  if (typeof imageUrl === 'string' && imageUrl.startsWith('http')) {
    try {
      const url = new URL(imageUrl);
      return `${url.protocol}//${url.hostname}`;
    } catch {
      console.warn('Invalid URL provided to extractDomainFromUrl:', imageUrl);
    }
  }
  
  // For relative paths, use the configured fallback domain
  return CDN_CONFIG.assetsBaseUrl;
}

/**
 * Resizes an image URL using TapTap's CDN image transformation service
 * Automatically extracts domain from URL and adds CDN resize parameters
 * 
 * @param imageUrl - Original image URL (can be full URL or relative path)
 * @param options - Resize options
 * @param fallbackToCDN - Whether to use CDN URL when resizing is disabled
 * @returns Transformed image URL with resize parameters
 * 
 * @example
 * ```typescript
 * // Full URL - uses domain from URL
 * const newsUrl = resizeImage('https://vuivietnam-cdn-stag.taptap.vn/console/stag/media/cms/image/fe09d20dedc611090caf27a72f82b226.jpg', {
 *   width: 556, height: 240, quality: 85
 * });
 * // Returns: https://vuivietnam-cdn-stag.taptap.vn/cdn-cgi/image/width=556,height=240,quality=85/console/stag/media/cms/image/fe09d20dedc611090caf27a72f82b226.jpg
 * 
 * // Relative path - uses fallback domain
 * const rewardUrl = resizeImage('stag/media/image/e7a85703c12a5f2dd25ac42e10687b1a.png', {
 *   width: 64, height: 64, quality: 90
 * });
 * // Returns: https://vui-cdn.taptap.vn/cdn-cgi/image/width=64,height=64,quality=90/stag/media/image/e7a85703c12a5f2dd25ac42e10687b1a.png
 * ```
 */
export function resizeImage(imageUrl: string, options: ImageResizeOptions = {}, fallbackToCDN: boolean = false): string {
  // Handle non-string inputs
  if (typeof imageUrl !== 'string' || !imageUrl) {
    return imageUrl || '';
  }

  // Skip resize for data URLs (data:image/svg, data:image/png, etc.)
  if (imageUrl.startsWith('data:image')) {
    return imageUrl;
  }

  // Skip resize for AWS S3 images and return original URL
  if (imageUrl.includes('s3.ap-southeast-1.amazonaws.com')) {
    return imageUrl;
  }
  
  // Skip resize for cdn.dealtoday.vn images and return original URL
  if (imageUrl.includes('cdn.dealtoday.vn')) {
    return imageUrl;
  }

  // Skip resize for CloudFront images and return original URL
  if (imageUrl.includes('cloudfront.net')) {
    return imageUrl;
  }
  
  // If image resizing is disabled
  if (!CDN_CONFIG.enableImageResize) {
    // Option 1: Return original URL as-is
    if (!fallbackToCDN) {
      return imageUrl;
    }
    // Option 2: Return CDN asset URL (without resizing)
    return getAssetUrl(imageUrl);
  }
  
  // Extract domain from the original URL
  const domainUrl = extractDomainFromUrl(imageUrl);
  
  // If already a full URL, extract the path part
  let imagePath = imageUrl;
  
  // Handle full URLs - extract path after domain
  if (typeof imageUrl === 'string' && imageUrl.startsWith('http')) {
    try {
      const url = new URL(imageUrl);
      // If it's already a TapTap CDN URL with transformation, return as-is
      if (url.pathname.includes('/cdn-cgi/image/')) {
        return imageUrl;
      }
      // Extract path after domain
      imagePath = url.pathname.startsWith('/') ? url.pathname.slice(1) : url.pathname;
    } catch {
      console.warn('Invalid URL provided to resizeImage:', imageUrl);
      return imageUrl;
    }
  }
  
  // Remove leading slash if present
  if (typeof imagePath === 'string' && imagePath.startsWith('/')) {
    imagePath = imagePath.slice(1);
  }
  
  // Build transformation parameters
  const params: string[] = [];
  
  if (options.width) {
    params.push(`width=${options.width}`);
  }
  
  if (options.height) {
    params.push(`height=${options.height}`);
  }
  
  if (options.quality) {
    params.push(`quality=${options.quality}`);
  }
  
  if (options.fit) {
    params.push(`fit=${options.fit}`);
  }
  
  if (options.format) {
    params.push(`format=${options.format}`);
  }
  
  // If no parameters provided, return original URL
  if (params.length === 0) {
    return imageUrl;
  }
  
  // Build the transformation URL using extracted domain
  const transformationParams = params.join(',');
  
  return `${domainUrl}/cdn-cgi/image/${transformationParams}/${imagePath}`;
}

/**
 * Common preset sizes for quick image resizing
 */
export const IMAGE_PRESETS = {
  // Thumbnails
  thumbnail: { width: 80, quality: 75 },
  thumbnailSmall: { width: 50, quality: 75 },
  thumbnailLarge: { width: 120, quality: 75 },
  
  // Cards
  cardImage: { width: 300, quality: 80 },
  cardImageSmall: { width: 200, quality: 80 },
  
  // Profile images
  avatar: { width: 100, height: 100, quality: 85, fit: 'cover' as const },
  avatarSmall: { width: 50, height: 50, quality: 85, fit: 'cover' as const },
  avatarLarge: { width: 200, height: 200, quality: 85, fit: 'cover' as const },
  
  // Banner images
  banner: { width: 800, quality: 85 },
  bannerSmall: { width: 400, quality: 80 },
  
  // High quality
  highQuality: { quality: 95 },
  
  // WebP format for modern browsers
  webp: { format: 'webp' as const, quality: 80 },
} as const;

/**
 * Resize image using a preset configuration
 * 
 * @param imageUrl - Original image URL
 * @param preset - Preset name from IMAGE_PRESETS
 * @returns Transformed image URL
 * 
 * @example
 * ```typescript
 * const avatarUrl = resizeImageWithPreset('path/to/image.jpg', 'avatar');
 * const thumbnailUrl = resizeImageWithPreset('path/to/image.jpg', 'thumbnail');
 * ```
 */
export function resizeImageWithPreset(
  imageUrl: string, 
  preset: keyof typeof IMAGE_PRESETS
): string {
  return resizeImage(imageUrl, IMAGE_PRESETS[preset]);
}

/**
 * Generate multiple image sizes for responsive images
 * 
 * @param imageUrl - Original image URL
 * @param sizes - Array of widths to generate
 * @param quality - Quality setting (default: 80)
 * @returns Object with size as key and URL as value
 * 
 * @example
 * ```typescript
 * const responsiveImages = generateResponsiveImages('path/to/image.jpg', [320, 640, 1024]);
 * // Returns: { 320: 'url320', 640: 'url640', 1024: 'url1024' }
 * ```
 */
export function generateResponsiveImages(
  imageUrl: string, 
  sizes: number[], 
  quality: number = 80
): Record<number, string> {
  const result: Record<number, string> = {};
  
  sizes.forEach(width => {
    result[width] = resizeImage(imageUrl, { width, quality });
  });
  
  return result;
}

/**
 * Check if image resizing is currently enabled
 * 
 * @returns Boolean indicating if image resizing is enabled
 */
export function isImageResizeEnabled(): boolean {
  return CDN_CONFIG.enableImageResize;
}

/**
 * Get the current CDN configuration
 * 
 * @returns CDN configuration object
 */
export function getCDNConfig() {
  return {
    enableImageResize: CDN_CONFIG.enableImageResize,
    imageBaseUrl: CDN_CONFIG.imageBaseUrl,
    assetsBaseUrl: CDN_CONFIG.assetsBaseUrl,
    defaultQuality: CDN_CONFIG.defaultQuality,
    defaultFormat: CDN_CONFIG.defaultFormat
  };
}

/**
 * Debug function to test domain extraction and CDN transformation
 * 
 * @param imageUrl - Image URL to test
 * @returns Object with extracted domain info and sample transformation
 */
export function debugCDNTransformation(imageUrl: string) {
  const extractedDomain = extractDomainFromUrl(imageUrl);
  const sampleTransformation = resizeImage(imageUrl, { width: 100, quality: 80 });
  
  return {
    originalUrl: imageUrl,
    extractedDomain,
    sampleTransformation,
    isFullUrl: typeof imageUrl === 'string' && imageUrl.startsWith('http'),
    isRelativePath: !imageUrl.startsWith('http')
  };
}

// Export extractDomainFromUrl for external use if needed
export { extractDomainFromUrl };