import { formatCurrency } from './formatNumber';

interface EarnRateData {
  percentage?: number;
  earnValue?: number | null;
  baseValue?: number | null;
}

/**
 * Formats earn rate data into a display string
 * Following mobile pattern: "earnValue = baseValue VUI"
 * 
 * @param earnRate - The earn rate data object
 * @returns Formatted earn rate string
 */
export function formatEarnRate(earnRate: EarnRateData | undefined | null): string {
  if (!earnRate) {
    return '';
  }

  const { percentage, earnValue, baseValue } = earnRate;
  
  // Following mobile pattern: "earnValue = baseValue VUI"
  if (earnValue && baseValue) {
    // Standard format: "10.000đ = 100 VUI"
    return `${formatCurrency(earnValue)} = ${baseValue} VUI`;
  } else if (earnValue && !baseValue) {
    // Use default baseValue of 1 like mobile
    return `${formatCurrency(earnValue)} = 1 VUI`;
  } else if (percentage && earnValue) {
    // Alternative format with percentage
    return `${formatCurrency(earnValue)} = ${percentage}% VUI`;
  } else if (percentage) {
    // Only percentage available
    return `Tích ${percentage}%`;
  }
  
  return '';
}

/**
 * Formats earn rate specifically for bill scan merchants
 * Handles the specific format: "20k = 100 VUI"
 */
export function formatBillScanEarnRate(earnRate: EarnRateData | undefined | null): string {
  if (!earnRate) {
    return '';
  }
  
  const { percentage, earnValue, baseValue } = earnRate;
  
  if (percentage && percentage > 0) {
    return `${percentage}%`;
  } else if (earnValue && baseValue) {
    const formattedEarnValue = earnValue >= 1000 
      ? `${earnValue / 1000}k` 
      : earnValue.toString();
    return `${formattedEarnValue}đ = ${baseValue} VUI`;
  }
  
  return '';
}