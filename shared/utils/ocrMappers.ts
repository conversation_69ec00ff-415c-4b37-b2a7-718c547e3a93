import type { OCRTransaction, OCRTransactionStatus } from '../services/api/ocr';
import type { BillStatus } from '../components/ui';

// Bill state enum
export enum EBillState {
  NEW = 'NEW',
  MISSING_DATA = 'MISSING_DATA',
  INVALID_DATA = 'INVALID_DATA',
  NEW_USER = 'NEW_USER',
  MANUAL_APPROVE = 'MANUAL_APPROVE',
  AUTO_PENDING = 'AUTO_PENDING',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  FRAUDED = 'FRAUDED',
  UNSUPPORTED = 'UNSUPPORTED',
  EXCEED_SNAP_TIME = 'EXCEED_SNAP_TIME',
  DUPLICATED = 'DUPLICATED',
  BLOCKED = 'BLOCKED',
  EXPIRED = 'EXPIRED',
  EXCEED_POINT = 'EXCEED_POINT',
  INVALID_DATA_REJECTED = 'INVALID_DATA_REJECTED',
  INVALID_TNC = 'INVALID_TNC',
  MISSING_DATA_REJECTED = 'MISSING_DATA_REJECTED',
  APPROVED = 'APPROVED',
  COMPLETED = 'COMPLETED',
  REJECTED = 'REJECTED',
  PROCESSING = 'PROCESSING'
}

// Reward code enum
export enum ERewardCode {
  VUI_POINT = 'VUI_POINT',
  BRAND_POINT = 'BRAND_POINT',
  VOUCHER = 'VOUCHER'
}

// Colors constants
export const COLORS = {
  blue: '#3B82F6',
  red: '#EF4444',
  orange: '#F97316',
  green: '#10B981',
  grey: '#9A9A9A',
  darkGreen: '#23B082'
};

// Helper function to convert hex to RGB with alpha
export const hexToRGB = (hex: string, alpha: number = 1): string => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// Helper function for money formatting
export const moneyWithCommas = (amount: number | string): string => {
  return new Intl.NumberFormat('vi-VN').format(Number(amount));
};

// Helper function for date parsing
export const parseDateTimeCenterDot = (dateString: string): string => {
  const date = new Date(dateString);
  const dateStr = date.toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit', 
    year: 'numeric',
  });
  const timeStr = date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });
  return `${timeStr} • ${dateStr}`;
};

// Localization object (from mobile)
export const l10n = {
  ocr: {
    ocrHistory: {
      statusMessage: {
        FRAUDED: 'Không thể tích', // reject
        BLOCKED: 'Không thể tích',
        EXCEED_SNAP_TIME: 'giới hạn tích',
        UNSUPPORTED: 'Chưa áp dụng',
        DUPLICATED: 'Đã dùng',
        EXPIRED: 'Hết hạn',
        EXCEED_POINT: 'giới hạn tích',
        INVALID_TNC: 'Cần chụp lại',
        MISSING_DATA_REJECTED: 'Cần chụp lại',
        INVALID_DATA_REJECTED: 'Không thể tích',
        WAIT_PRECESS: 'Chờ xác nhận',
        NEW: 'Chờ xác nhận', // waiting
        NEW_USER: 'Chờ xác nhận',
        AUTO_PENDING: 'Chờ xác nhận',
        SYSTEM_ERROR: 'Chờ xác nhận',
        MANUAL_APPROVE: 'Chờ xác nhận',
        INVALID_DATA: 'Chờ xác nhận',
        MISSING_DATA: 'Chờ xác nhận',
        COMPLETED: 'đã tích', // success
      }
    }
  }
};

interface StatusInfo {
  background: string;
  color: string;
  text: string;
}

/**
 * Maps OCR transaction state to bill status with detailed status info
 */
export const mapOCRStateToBillStatus = (state: EBillState): StatusInfo => {
  let status: StatusInfo;

  switch (state) {
    case EBillState.NEW:
    case EBillState.MISSING_DATA:
    case EBillState.INVALID_DATA:
    case EBillState.NEW_USER:
    case EBillState.MANUAL_APPROVE:
    case EBillState.AUTO_PENDING:
    case EBillState.SYSTEM_ERROR:
      status = {
        background: hexToRGB(COLORS.blue, 0.1),
        color: COLORS.blue,
        text: l10n.ocr.ocrHistory.statusMessage.WAIT_PRECESS,
      };
      break;

    case EBillState.FRAUDED:
    case EBillState.UNSUPPORTED:
    case EBillState.EXCEED_SNAP_TIME:
    case EBillState.DUPLICATED:
    case EBillState.BLOCKED:
    case EBillState.EXPIRED:
    case EBillState.EXCEED_POINT:
    case EBillState.INVALID_DATA_REJECTED:
      status = {
        background: hexToRGB(COLORS.red, 0.1),
        color: COLORS.red,
        text: l10n.ocr.ocrHistory.statusMessage[state],
      };
      break;

    case EBillState.INVALID_TNC:
    case EBillState.MISSING_DATA_REJECTED:
      status = {
        background: hexToRGB(COLORS.orange, 0.1),
        color: COLORS.orange,
        text: l10n.ocr.ocrHistory.statusMessage[state],
      };
      break;

    case EBillState.APPROVED:
    case EBillState.COMPLETED:
      status = {
        background: hexToRGB(COLORS.green, 0.1),
        color: COLORS.green,
        text: 'Đã duyệt',
      };
      break;

    default:
      status = {
        background: hexToRGB(COLORS.blue, 0.1),
        color: COLORS.blue,
        text: l10n.ocr.ocrHistory.statusMessage.WAIT_PRECESS,
      };
      break;
  }

  return status;
};

/**
 * Maps OCR transaction state to simple bill status for backward compatibility
 */
export const mapOCRStateToSimpleBillStatus = (state: OCRTransaction['state']): BillStatus => {
  switch (state) {
    case 'PROCESSING':
      return 'waiting';
    case 'APPROVED':
    case 'MANUAL_APPROVE':
    case 'COMPLETED':
      return 'approved';
    case 'REJECTED':
      return 'rejected';
    default:
      return 'waiting';
  }
};

/**
 * Maps OCR transaction status to bill status
 */
export const mapOCRStatusToBillStatus = (status: OCRTransactionStatus): BillStatus => {
  switch (status) {
    case 'PROCESSING':
      return 'waiting';
    case 'APPROVED':
      return 'approved';
    case 'REJECTED':
      return 'rejected';
    default:
      return 'waiting';
  }
};

/**
 * Generates rewards from OCR transaction result
 */
export const generateRewardsFromOCR = (transaction: OCRTransaction): any[] => {
  const rewards: any[] = [];
  
  // If transaction is approved and has reward result, generate rewards
  if (transaction.state === 'APPROVED' && transaction.rewardResult) {
    // This would depend on the actual structure of rewardResult
    // For now, we'll generate some example rewards for approved transactions
    rewards.push({ type: 'coin', amount: '+100' });
    
    // Add more rewards based on brand or amount if available
    if (transaction.amount && transaction.amount > 1000000) {
      rewards.push({ type: 'vui', amount: '+50' });
    }
  }
  
  return rewards;
};

/**
 * Formats amount for display
 */
export const formatOCRAmount = (amount: number | null): string => {
  if (amount === null) {
    return '... đ';
  }
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    maximumFractionDigits: 0,
  }).format(amount).replace('₫', 'đ');
};

/**
 * Formats date for display
 */
export const formatOCRDate = (dateString: string): { date: string; time: string } => {
  const date = new Date(dateString);
  
  const dateOptions: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  };
  
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  };
  
  return {
    date: date.toLocaleDateString('vi-VN', dateOptions),
    time: date.toLocaleTimeString('en-US', timeOptions),
  };
};

/**
 * Maps OCR transaction to BillCardProps
 */
export const mapOCRTransactionToBillCard = (transaction: OCRTransaction) => {
  console.log('[mapOCRTransactionToBillCard]', transaction);
  
  return {
    id: transaction._id,
    brandName: transaction.brandName || 'Unknown Merchant',
    brandLogo: transaction.brandLogo || '',
    amount: transaction.amount,
    snapDate: transaction.snapDate,
    ocrImage: transaction.ocrImage,
    state: transaction.state as EBillState,
    isView: transaction.isView || false,
    rewardResult: transaction.rewardResult || [],
  };
};