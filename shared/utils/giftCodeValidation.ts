export const MIN_GIFT_CODE_LENGTH = 6;
export const MAX_GIFT_CODE_LENGTH = 20;

export function validateGiftCode(code: string): {
  isValid: boolean;
  error?: string;
} {
  if (!code || code.trim().length === 0) {
    return {
      isValid: false,
      error: 'Vui lòng nhập mã đổi quà',
    };
  }

  if (code.length < MIN_GIFT_CODE_LENGTH) {
    return {
      isValid: false,
      error: `Mã phải có ít nhất ${MIN_GIFT_CODE_LENGTH} ký tự`,
    };
  }

  if (code.length > MAX_GIFT_CODE_LENGTH) {
    return {
      isValid: false,
      error: `Mã không được vượt quá ${MAX_GIFT_CODE_LENGTH} ký tự`,
    };
  }

  // Check alphanumeric (allow hyphens)
  const cleanCode = code.replace(/-/g, '');
  const alphanumericPattern = /^[A-Za-z0-9]+$/;
  
  if (!alphanumericPattern.test(cleanCode)) {
    return {
      isValid: false,
      error: 'Mã chỉ được chứa chữ cái, số và dấu gạch ngang',
    };
  }

  return {
    isValid: true,
  };
}

export const GIFT_CODE_ERROR_CODES = {
  INVALID_STORE: 2130210,
  INVALID_OWNER: 2130222,
  DUPLICATED: 2130211,
  WRONG_CODE: 2130200,
  EXPIRED_1: 2130212,
  EXPIRED_2: 2130231,
  OVER_SCAN: 2130220,
  OVER_GLOBAL_DAY: 1301002,
  OVER_GLOBAL_WEEK: 1301003,
  OVER_GLOBAL_MONTH: 1301004,
  OVER_BRAND_DAY: 1301005,
  OVER_BRAND_WEEK: 1301006,
  OVER_BRAND_MONTH: 1301007,
} as const;

export function getGiftCodeErrorMessage(errorCode?: number): string {
  switch (errorCode) {
    case GIFT_CODE_ERROR_CODES.INVALID_STORE:
      return 'Cửa hàng không hợp lệ';
    
    case GIFT_CODE_ERROR_CODES.INVALID_OWNER:
      return 'Chủ sở hữu không hợp lệ';
    
    case GIFT_CODE_ERROR_CODES.DUPLICATED:
      return 'Mã đã được sử dụng';
    
    case GIFT_CODE_ERROR_CODES.WRONG_CODE:
      return 'Mã không đúng';
    
    case GIFT_CODE_ERROR_CODES.EXPIRED_1:
    case GIFT_CODE_ERROR_CODES.EXPIRED_2:
      return 'Mã đã hết hạn';
    
    case GIFT_CODE_ERROR_CODES.OVER_SCAN:
      return 'Hết lượt sử dụng mã này. Vui lòng thử mã khác!';
    
    case GIFT_CODE_ERROR_CODES.OVER_GLOBAL_DAY:
      return 'Đã đạt giới hạn số lần sử dụng trong ngày';
    
    case GIFT_CODE_ERROR_CODES.OVER_GLOBAL_WEEK:
      return 'Đã đạt giới hạn số lần sử dụng trong tuần';
    
    case GIFT_CODE_ERROR_CODES.OVER_GLOBAL_MONTH:
      return 'Đã đạt giới hạn số lần sử dụng trong tháng';
    
    case GIFT_CODE_ERROR_CODES.OVER_BRAND_DAY:
      return 'Đã đạt giới hạn số lần sử dụng trong ngày cho thương hiệu này';
    
    case GIFT_CODE_ERROR_CODES.OVER_BRAND_WEEK:
      return 'Đã đạt giới hạn số lần sử dụng trong tuần cho thương hiệu này';
    
    case GIFT_CODE_ERROR_CODES.OVER_BRAND_MONTH:
      return 'Đã đạt giới hạn số lần sử dụng trong tháng cho thương hiệu này';
    
    default:
      return 'Có lỗi xảy ra, vui lòng thử lại';
  }
}