# Responsive Utilities

Bộ utility functions và CSS classes để xử lý responsive design trong web app, tương tự như React Native responsive functions.

## Features

### 1. JavaScript/TypeScript Functions

#### Basic Responsive Functions

- `perWidth(size: number)`: Tính width dựa trên phần trăm của short dimension
- `perHeight(size: number)`: Tính height dựa trên phần trăm của long dimension
- `resWidth(size: number)`: Scale size từ standard window width (375px)
- `resHeight(size: number)`: Scale size từ standard window height (667px)

#### Advanced Functions

- `moderateScale(size: number, factor: number = 0.5)`: Scale không tuyến tính với factor có thể điều chỉnh
- `responsiveFontSize(size: number, factor: number = 0.1)`: Responsive font size
- `isLargeView()`: <PERSON><PERSON><PERSON> tra xem có phải view lớn (≥600px) không
- `isTabletMode()`: Ki<PERSON>m tra xem có phải tablet mode không

#### Utility Functions

- `getWindowWidth()`: L<PERSON>y width hiện tại của viewport
- `getWindowHeight()`: Lấy height hiện tại của viewport
- `getShortDimension()`: Lấy short dimension (width cho portrait, height cho landscape)
- `getLongDimension()`: Lấy long dimension (height cho portrait, width cho landscape)

### 2. CSS Classes

#### Responsive Container

```css
.responsive-container {
  --window-width: 100vw;
  --window-height: 100vh;
  --short-dimension: min(100vw, 100vh);
  --long-dimension: max(100vw, 100vh);
  --standard-width: 375px;
  --standard-height: 667px;
}
```

#### Responsive Components

- `.responsive-card`: Card với kích thước responsive
- `.responsive-button`: Button với padding và font size responsive
- `.responsive-input`: Input field với height và padding responsive
- `.responsive-icon`: Icon với kích thước responsive
- `.responsive-image`: Image với kích thước responsive

#### Responsive Grid

- `.responsive-grid-2`: Grid 2 cột với kích thước responsive
- `.responsive-grid-3`: Grid 3 cột với kích thước responsive
- `.responsive-grid-4`: Grid 4 cột với kích thước responsive

### 3. Tailwind CSS Extensions

#### Responsive Spacing

```css
.responsive-xs: clamp(4px, 1vw, 8px)
.responsive-sm: clamp(8px, 2vw, 16px)
.responsive-md: clamp(16px, 4vw, 24px)
.responsive-lg: clamp(24px, 6vw, 32px)
.responsive-xl: clamp(32px, 8vw, 48px)
.responsive-2xl: clamp(48px, 12vw, 64px)
```

#### Responsive Font Sizes

```css
.responsive-xs: clamp(10px, 2vw, 12px)
.responsive-sm: clamp(12px, 2.5vw, 14px)
.responsive-base: clamp(14px, 3vw, 16px)
.responsive-lg: clamp(16px, 3.5vw, 18px)
.responsive-xl: clamp(18px, 4vw, 24px)
.responsive-2xl: clamp(24px, 5vw, 32px)
.responsive-3xl: clamp(32px, 7vw, 48px)
```

#### Responsive Breakpoints

```css
.xs: 375px    /* Mobile */
.sm: 640px    /* Small tablet */
.md: 768px    /* Tablet */
.lg: 1024px   /* Desktop */
.xl: 1280px   /* Large desktop */
.2xl: 1536px  /* Extra large desktop */
```

## Usage Examples

### JavaScript/TypeScript

```typescript
import { perWidth, resWidth, moderateScale, isLargeView } from "./responsive";

// Tính width dựa trên phần trăm
const buttonWidth = perWidth(80); // 80% của short dimension

// Scale từ standard window
const cardWidth = resWidth(300); // Scale 300px từ 375px standard

// Moderate scale
const iconSize = moderateScale(24, 0.3); // Scale với factor 0.3

// Kiểm tra view size
if (isLargeView()) {
  // Hiển thị layout cho desktop
}
```

### CSS Classes

```html
<!-- Responsive container -->
<div class="responsive-container">
  <h1 class="responsive-text">Responsive Title</h1>

  <!-- Responsive card -->
  <div class="responsive-card bg-white shadow-lg">
    <h2 class="text-lg font-semibold">Card Title</h2>
    <p class="text-sm">Card content</p>
  </div>

  <!-- Responsive button -->
  <button class="responsive-button bg-primary-pink text-white rounded-lg">
    Click Me
  </button>

  <!-- Responsive grid -->
  <div class="responsive-grid-3">
    <div class="bg-white p-4 rounded-lg">Item 1</div>
    <div class="bg-white p-4 rounded-lg">Item 2</div>
    <div class="bg-white p-4 rounded-lg">Item 3</div>
  </div>
</div>
```

### Tailwind CSS

```html
<!-- Responsive spacing -->
<div class="p-responsive-md m-responsive-lg">
  <h1 class="text-responsive-xl font-bold">Title</h1>
  <p class="text-responsive-base">Content</p>
</div>

<!-- Responsive breakpoints -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <div class="bg-white p-4">Item 1</div>
  <div class="bg-white p-4">Item 2</div>
  <div class="bg-white p-4">Item 3</div>
</div>
```

## Installation

### 1. Import Functions

```typescript
// Import individual functions
import { perWidth, resWidth } from "./responsive";

// Import all functions
import * as Responsive from "./responsive";

// Import default object
import Responsive from "./responsive";
```

### 2. Add Tailwind Plugin

```javascript
// tailwind.config.js
module.exports = {
  plugins: [require("../../shared/utils/tailwind-responsive-plugin.js")],
};
```

### 3. Use CSS Classes

```css
/* Import responsive utilities */
@import "./responsive.css";

/* Or use directly in your CSS */
.responsive-container {
  /* Your custom styles */
}
```

## Browser Support

- **CSS clamp()**: Chrome 66+, Firefox 63+, Safari 13.1+
- **CSS custom properties**: Chrome 49+, Firefox 31+, Safari 9.1+
- **JavaScript**: ES6+ (Chrome 51+, Firefox 54+, Safari 10+)

## Performance Considerations

- Functions được tính toán mỗi lần gọi, nên cache kết quả nếu cần thiết
- CSS classes sử dụng `clamp()` có hiệu suất tốt hơn JavaScript
- Sử dụng `useCallback` hoặc `useMemo` trong React components để tối ưu

## Migration from React Native

| React Native                       | Web Equivalent                            |
| ---------------------------------- | ----------------------------------------- |
| `Dimensions.get('window')`         | `getWindowWidth()`, `getWindowHeight()`   |
| `PixelRatio.roundToNearestPixel()` | `Math.round()`                            |
| `Platform.isIos`                   | Browser detection hoặc CSS media queries  |
| `StatusBar.currentHeight`          | `getStatusBarHeight()` (có thể customize) |

## Contributing

Khi thêm tính năng mới:

1. Cập nhật TypeScript types
2. Thêm JSDoc comments
3. Cập nhật demo component
4. Cập nhật README này
5. Test trên nhiều kích thước màn hình
