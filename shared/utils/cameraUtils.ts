import type { CameraError } from '../services';
import type { CapturedReceipt } from '../hooks/useCameraCapture';

/**
 * Maps camera error types to user-friendly Vietnamese error messages
 */
export const getCameraErrorMessage = (error: CameraError): string => {
  switch (error.type) {
    case 'permission_denied':
      return 'Bạn cần cấp quyền truy cập camera để sử dụng tính năng này.';
    case 'no_camera':
      return 'Không tìm thấy camera trên thiết bị của bạn.';
    case 'switch_error':
      return 'Không thể chuyển đổi camera.';
    case 'capture_error':
      return 'Không thể chụp ảnh. Vui lòng thử lại.';
    default:
      return error.message || 'Có lỗi không xác định xảy ra.';
  }
};

/**
 * Storage key for captured receipts in localStorage
 */
export const CAPTURED_RECEIPTS_STORAGE_KEY = 'captured_receipts';

/**
 * Gets captured receipts from localStorage
 */
export const getCapturedReceipts = (): CapturedReceipt[] => {
  try {
    const stored = localStorage.getItem(CAPTURED_RECEIPTS_STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error reading captured receipts from localStorage:', error);
    return [];
  }
};

/**
 * Saves captured receipts to localStorage
 */
export const saveCapturedReceipts = (receipts: CapturedReceipt[]): boolean => {
  try {
    localStorage.setItem(CAPTURED_RECEIPTS_STORAGE_KEY, JSON.stringify(receipts));
    return true;
  } catch (error) {
    console.error('Error saving captured receipts to localStorage:', error);
    return false;
  }
};

/**
 * Adds a new captured receipt to localStorage
 */
export const addCapturedReceipt = (
  photoData: string,
  uploadResult: any
): CapturedReceipt => {
  const newReceipt: CapturedReceipt = {
    id: Date.now(),
    photoData,
    timestamp: new Date().toISOString(),
    status: 'processing',
    uploadResult,
    ocrImage: uploadResult.url,
  };

  const existingReceipts = getCapturedReceipts();
  const updatedReceipts = [...existingReceipts, newReceipt];
  saveCapturedReceipts(updatedReceipts);

  return newReceipt;
};

/**
 * Updates the status of a captured receipt
 */
export const updateReceiptStatus = (
  receiptId: number,
  status: string
): boolean => {
  const receipts = getCapturedReceipts();
  const receiptIndex = receipts.findIndex(r => r.id === receiptId);
  
  if (receiptIndex === -1) return false;
  
  receipts[receiptIndex].status = status;
  return saveCapturedReceipts(receipts);
};

/**
 * Removes a captured receipt from localStorage
 */
export const removeCapturedReceipt = (receiptId: number): boolean => {
  const receipts = getCapturedReceipts();
  const filteredReceipts = receipts.filter(r => r.id !== receiptId);
  return saveCapturedReceipts(filteredReceipts);
};

/**
 * Transforms user status data for preview component compatibility
 */
export const transformUserStatusForPreview = (userStatus: any) => {
  if (!userStatus) return null;
  
  return {
    maxSnapPerDay: userStatus.snapPerDay,
    currentSnapCount: 0,
    isAllowedToSnap: userStatus.snapPerDay > 0,
  };
};

/**
 * Gets the remaining snaps count from user status
 */
export const getRemainingSnaps = (userStatus: any): number => {
  return userStatus?.snapPerDay || 0;
};

/**
 * Processing status messages for different upload stages
 */
export const PROCESSING_MESSAGES = {
  PREPARING: 'Đang chuẩn bị upload hóa đơn...',
  UPLOADING: 'Đang upload hóa đơn lên server...',
  SUCCESS: 'Upload thành công! Đang xử lý hóa đơn...',
  DEFAULT: 'Vui lòng đợi trong giây lát...',
} as const;

/**
 * Processing delay for showing success state (in milliseconds)
 */
export const PROCESSING_SUCCESS_DELAY = 1500;

/**
 * Validation messages for camera capture
 */
export const VALIDATION_MESSAGES = {
  NO_PHOTO: 'Vui lòng chụp hóa đơn trước',
  NO_MOBILE: 'Không thể xác định số điện thoại. Vui lòng đăng nhập lại.',
  UPLOAD_ERROR: 'Lỗi upload hóa đơn',
} as const;

/**
 * Creates an error message for upload failures
 */
export const createUploadErrorMessage = (error: unknown): string => {
  const errorMessage = error instanceof Error ? error.message : 'Có lỗi xảy ra khi upload hóa đơn';
  return `${VALIDATION_MESSAGES.UPLOAD_ERROR}: ${errorMessage}. Vui lòng thử lại.`;
};