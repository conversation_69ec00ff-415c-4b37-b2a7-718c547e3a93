import type {
  MerchantType,
  HorizontalScrollTabItem,
  VoucherItem,
  OfflineStoreType,
} from '../types';
import type { RewardDetailResponse } from '../services/api/rewards';

/**
 * Builds tab configuration for merchant detail page based on merchant features
 */
export const buildMerchantTabsConfiguration = (
  merchant: MerchantType
): HorizontalScrollTabItem[] => {
  const tabs: HorizontalScrollTabItem[] = [];

  // Offers tab - always show if has rewards
  if (merchant.numberOfReward && merchant.numberOfReward > 0) {
    tabs.push({
      id: "offers",
      label: "ưu đãi Trên kệ",
      count: merchant.numberOfReward || 0,
    });
  }

  // Earn points tab - show if has earn models
  if (merchant.earnMethods && merchant.earnMethods.length > 0) {
    tabs.push({
      id: "earn-points",
      label: "Tích điểm",
    });
  }

  // Online store tab - show if enabled
  if (merchant.isShowOnlineStore && merchant.onlineStore) {
    tabs.push({
      id: "order-online",
      label: "Đặt hàng online",
    });
  }

  // Stores tab - show if has offline stores
  if (merchant.isEnableOfflineStore) {
    tabs.push({
      id: "stores",
      label: "Cửa hàng",
      count: merchant.offlineStores?.length || 0,
    });
  }

  // Info tab - always show
  tabs.push({
    id: "info",
    label: "THÔNG TIN",
  });

  return tabs;
};

/**
 * Converts reward data to voucher items for display
 */
export const convertRewardsToVouchers = (
  rewards: RewardDetailResponse | null,
  merchantDetail: MerchantType | null
): VoucherItem[] => {
  if (!rewards) return [];

  return rewards?.rewards?.map((reward) => ({
    id: reward.id,
    title: reward.name,
    brandName: (reward as any).brand?.name || merchantDetail?.name || "",
    brandLogo: (reward as any).brand?.logo || merchantDetail?.logo || '',
    price: (reward as any).burnCost || 0,
    productImage: reward.image1 || '',
  })) || [];
};

/**
 * Transforms store data to location format for display
 */
export const transformStoreLocations = (stores: OfflineStoreType[]) => {
  return stores.map((store) => ({
    id: store.id,
    storeName: store.name,
    address: store.address,
    distance: store.distance
      ? `${(store.distance / 1000).toFixed(1)} km`
      : "N/A",
    phoneNumber: store.phone || "N/A",
  }));
};

/**
 * Transforms merchant images to gallery format
 */
export const transformMerchantImages = (merchantDetail: MerchantType | null) => {
  if (!merchantDetail?.images) return [];
  
  return merchantDetail.images.map((src, index) => ({
    id: index.toString(),
    src,
    alt: `${merchantDetail.name} Image ${index + 1}`,
  }));
};

/**
 * Gets loyalty card status based on tier state
 */
export const getLoyaltyCardStatus = (
  tierDetail: any
): "no-join" | "max-rank" | "with-rank" | "without-rank" => {
  if (!tierDetail) return "no-join";
  
  // This logic should be moved from the hook to here when we refactor further
  return "no-join";
};

/**
 * Calculates default coordinates for store search
 * Currently defaults to Ho Chi Minh City
 */
export const getDefaultStoreSearchCoordinates = () => ({
  latitude: 10.8231,
  longitude: 106.6297,
  radius: 10000, // 10km
});

/**
 * Determines if merchant has online store features
 */
export const hasOnlineStoreFeatures = (merchant: MerchantType): boolean => {
  return Boolean(merchant.isShowOnlineStore && merchant.onlineStore);
};

/**
 * Determines if merchant has offline store features
 */
export const hasOfflineStoreFeatures = (merchant: MerchantType): boolean => {
  return Boolean(merchant.isEnableOfflineStore);
};

/**
 * Determines if merchant has rewards/offers
 */
export const hasRewardsFeatures = (merchant: MerchantType): boolean => {
  return Boolean(merchant.numberOfReward && merchant.numberOfReward > 0);
};

/**
 * Determines if merchant has earn points features
 */
export const hasEarnPointsFeatures = (merchant: MerchantType): boolean => {
  return Boolean(merchant.earnModels && merchant.earnModels.length > 0);
};

/**
 * Gets the online method from merchant earn methods
 */
export const getOnlineMethod = (merchant: MerchantType) => {
  return merchant.earnMethods?.find((method) => method.code === "online");
};

/**
 * Gets safe merchant information with fallbacks
 */
export const getSafeMerchantInfo = (
  merchantDetail: MerchantType | null,
  fallbackName = "Merchant",
  fallbackLogo = ""
) => ({
  name: merchantDetail?.name || fallbackName,
  logo: merchantDetail?.logo || fallbackLogo,
  banner: merchantDetail?.banner || "",
  id: merchantDetail?.id || "",
});