import type {
  RewardItemType,
  GalleryImage,
  ContactInfo,
} from '../types';

/**
 * Converts string array to GalleryImage format for ImageGallery component
 */
export const convertImagesToGallery = (
  images: string[],
  altPrefix = 'Image'
): GalleryImage[] => {
  return images
    .filter(Boolean)
    .map((image, index) => ({
      id: `image-${index}`,
      src: image,
      alt: `${altPrefix} ${index + 1}`,
      thumbnail: image, // Use same image for thumbnail
    }));
};

/**
 * Gets gallery images from reward data
 */
export const getRewardGalleryImages = (reward: RewardItemType | null): GalleryImage[] => {
  if (!reward) return [];

  const images = [
    reward.image1 || reward.imageUrl || reward.thumbnailUrl,
    ...(reward.merchant?.images || [])
  ].filter(Boolean) as string[];

  return convertImagesToGallery(images, reward?.merchantName || 'Reward');
};

/**
 * Formats expiry date for Vietnamese locale
 */
export const formatRewardExpiryDate = (dateString?: string): string => {
  if (!dateString) return 'Không có hạn sử dụng';
  
  try {
    // Handle different date formats from API
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  } catch {
    return dateString;
  }
};

/**
 * Extracts contact information from reward data
 */
export const getRewardContactInfo = (reward: RewardItemType | null): ContactInfo => {
  if (!reward) return { website: '', phone: '', email: '', facebook: '', zalo: '' };
  
  return {
    website: reward.merchant?.contact?.websiteURL || reward.merchant?.onlineStore?.websiteURL || '',
    phone: reward.merchant?.contact?.phone || '',
    email: reward.merchant?.contact?.email || '',
    facebook: reward.merchant?.contact?.fanpage || '',
    zalo: reward.merchant?.contact?.zaloOA || '',
  };
};

/**
 * Gets fallback logo for reward/merchant
 */
export const getRewardFallbackLogo = (_merchantName?: string): string => {
  // Return empty string or implement fallback logo logic
  return '';
};

/**
 * Determines if a reward is a flash sale reward
 */
export const isRewardFlashSale = (reward: RewardItemType): boolean => {
  return Boolean(reward.isFlashSale || reward.campaignId);
};

/**
 * Gets the current price for a reward (handling flash sale prices)
 */
export const getRewardCurrentPrice = (reward: RewardItemType): number => {
  return reward.issueBrandCurrencyPoint || reward.issueVUIPoint || reward.pointsCost || 0;
};

/**
 * Gets the currency label for a reward
 */
export const getRewardCurrencyLabel = (reward: RewardItemType): string => {
  return reward.brandCurrency?.name || 'điểm';
};

/**
 * Gets the coin icon for a reward
 */
export const getRewardCoinIcon = (reward: RewardItemType, defaultIcon?: string): string => {
  return reward.brandCurrency?.logo || defaultIcon || '';
};

/**
 * Checks if user can purchase a reward
 */
export const canPurchaseReward = (
  reward: RewardItemType,
  userPoints: number,
  userBrandCurrencyPoints: number
): boolean => {
  if (!reward.canPurchase) return false;
  
  const requiredPoints = reward.issueVUIPoint || 0;
  const requiredBrandCurrencyPoints = reward.issueBrandCurrencyPoint || 0;
  
  // Check if user has enough points
  if (requiredPoints > 0 && userPoints < requiredPoints) return false;
  if (requiredBrandCurrencyPoints > 0 && userBrandCurrencyPoints < requiredBrandCurrencyPoints) return false;
  
  // Check stock
  if (reward.stock && reward.stock <= 0) return false;
  
  // Check per-user limits
  if (reward.stateMaxPerUser && reward.stateMaxPerUser <= 0) return false;
  
  return true;
};

/**
 * Checks if reward sale has started
 */
export const hasRewardSaleStarted = (reward: RewardItemType): boolean => {
  if (!reward.startTime) return true;
  return new Date() >= new Date(reward.startTime);
};

/**
 * Gets reward brand information
 */
export const getRewardBrandInfo = (reward: RewardItemType) => {
  return {
    name: reward.merchant?.name || reward.merchantName || 'Thương hiệu',
    logo: reward.merchant?.logo || reward.merchantLogo || '',
    id: reward.merchant?.id || '',
  };
};

/**
 * Gets reward description/terms content
 */
export const getRewardDescription = (reward: RewardItemType): string => {
  return reward.termsConditions || reward.description || reward.terms?.join('\n') || '';
};

/**
 * Checks if reward description contains HTML content
 */
export const isRewardDescriptionHtml = (reward: RewardItemType): boolean => {
  const description = getRewardDescription(reward);
  return Boolean(description && /<[^>]*>/.test(description));
};

/**
 * Formats store data from reward for display
 */
export const formatRewardStores = (reward: RewardItemType) => {
  if (!reward.merchant?.offlineStores || !Array.isArray(reward.merchant.offlineStores)) {
    return [];
  }

  return reward.merchant.offlineStores.map((store: { 
    id?: string; 
    name?: string; 
    storeName?: string; 
    address?: string; 
    distance?: string; 
    phone?: string; 
    phoneNumber?: string 
  }, index: number) => ({
    id: store.id || `store-${index}`,
    storeName: store.name || store.storeName || '',
    address: store.address || '',
    distance: store.distance || '',
    phoneNumber: store.phone || store.phoneNumber || ''
  }));
};

/**
 * Gets the required points for a reward
 */
export const getRewardRequiredPoints = (reward: RewardItemType): number => {
  return reward.issueBrandCurrencyPoint || reward.issueVUIPoint || reward.pointsCost || 0;
};

/**
 * Handles contact action clicks with proper URL formatting
 */
export const handleContactAction = {
  website: (url: string) => {
    if (url) {
      const formattedUrl = url.startsWith('http') ? url : `https://${url}`;
      window.open(formattedUrl, '_blank');
    }
  },
  
  phone: (phone: string) => {
    if (phone) {
      window.open(`tel:${phone}`, '_self');
    }
  },
  
  email: (email: string) => {
    if (email) {
      window.open(`mailto:${email}`, '_self');
    }
  },
  
  copyEmail: (email: string): Promise<boolean> => {
    if (email && navigator.clipboard) {
      return navigator.clipboard.writeText(email)
        .then(() => true)
        .catch(() => false);
    }
    return Promise.resolve(false);
  },
  
  facebook: (url: string) => {
    if (url) {
      const formattedUrl = url.startsWith('http') ? url : `https://${url}`;
      window.open(formattedUrl, '_blank');
    }
  },
  
  zalo: (url: string) => {
    if (url) {
      const formattedUrl = url.startsWith('http') ? url : `https://${url}`;
      window.open(formattedUrl, '_blank');
    }
  },
};