// Brand Currency Types
export interface BrandCurrencyType {
  currencyCode: string;
  logo: string;
  name: string;
  totalBalance: number;
  merchantCode: string;
  details?: {
    point: number;
    date: string;
  }[];
}

// Point History Types
export interface PointHistoryItem {
  id: string;
  adjustedPoint: number;
  adjustedType: 'REDEEM' | 'ISSUE' | 'EXPIRE' | 'REFUND';
  description: {
    note: string | null;
  };
  createdAt: string;
  logo: string | null;
  currencyCode: string;
  brandCode: string;
}

// Request Types
export enum ActionBalance {
  GET_BALANCE = 'GET_BALANCE',
  GET_BALANCE_DETAIL = 'GET_BALANCE_DETAIL',
  GET_BALANCE_TOP_THREE = 'GET_BALANCE_TOP_THREE',
  GET_BALANCE_BY_MERCHANT = 'GET_BALANCE_BY_MERCHANT',
}

export enum ActionHistory {
  GET_HISTORY_BY_CURRENCY = 'GET_HISTORY_BY_CURRENCY',
  GET_HISTORY_BY_MONTH = 'GET_HISTORY_BY_MONTH',
}

export interface RequestBalance {
  limit?: number;
  offset?: number;
  merchantCode?: string;
  currencyCode?: string;
  action: keyof typeof ActionBalance;
}

export interface RequestHistory {
  createdFrom: string;
  createdTo: string;
  offset?: number;
  limit?: number;
  action: keyof typeof ActionHistory;
  currencyCode?: string;
  brandCode?: string;
}

// Point Expiry Types
export interface PointExpiryDate {
  points: number;
  expiryDate: string;
}

// API Response Types
export interface BrandCurrencyListResponse {
  brandCurrencyList: BrandCurrencyType[];
  loading: boolean;
  isError: boolean;
  onRefresh: () => void;
}

export interface BrandCurrencyInfoResponse {
  brandInfo: BrandCurrencyType | undefined;
  onRefresh: () => void;
}

export interface PointHistoryResponse {
  data: PointHistoryItem[];
  isLoading: boolean;
  isRefreshing: boolean;
  isError: boolean;
  onChangeDate: (dateSelect: Date) => void;
  onRefresh: (dateSelect?: Date) => void;
  onLoadMore: () => void;
  dateSelect: Date;
}