import { getHttpClient } from '../http';
import type { BaseResponse } from './types';
import type { RewardItemType } from '../../types/reward';
import type { FlashSaleCampaign } from '../../types/flashSale';

interface ListRewardByCollectionRequest {
  collectionCode: string;
  mobileType: 'running' | 'active' | 'coming' | 'flashSale';
  offset: number;
  limit: number;
  sortType?: string;
}

interface ListRewardByCollectionResponse {
  rewards: RewardItemType[];
  total: number;
}

class FlashSaleService {
  /**
   * Get active flash sale campaign
   */
  async getActiveCampaign(collectionCode: string): Promise<BaseResponse<FlashSaleCampaign>> {
    const httpClient = getHttpClient();
    return httpClient.get('/b2c/v3/mobile/reward/activeCampaign', {
      params: { collectionCode }
    });
  }

  /**
   * Get list of rewards by collection (for flash sale)
   */
  async getListRewardByCollection(params: ListRewardByCollectionRequest): Promise<BaseResponse<RewardItemType[]>> {
    const httpClient = getHttpClient();
    return httpClient.get('/b2c/v3/mobile/reward', {
      params
    });
  }

  /**
   * Get flash sale rewards
   */
  async getFlashSaleRewards(params: {
    collectionCode: string;
    offset?: number;
    limit?: number;
  }): Promise<BaseResponse<ListRewardByCollectionResponse>> {
    const httpClient = getHttpClient();
    return httpClient.get('/b2c/v3/mobile/rewards/flash-sale', {
      params: {
        ...params,
        mobileType: 'flashSale',
        offset: params.offset || 0,
        limit: params.limit || 10
      }
    });
  }
}

export const flashSaleService = new FlashSaleService();
export default flashSaleService;