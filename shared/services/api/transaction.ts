import { getHttpClient } from '../http';
import { BaseResponse } from '../../stores/types';
import moment from 'moment';

export interface IHistoryPoint {
  id: string;
  adjustedPoint: number;
  adjustedType: 'REDEEM' | 'ISSUE' | 'EXPIRE' | 'REFUND';
  description: {
    note: string | null;
  };
  createdAt: string;
  logo: string | null;
  currencyCode: string;
  brandCode: string;
}

export interface TransactionHistoryParams {
  createdFrom: string;
  createdTo: string;
  offset: number;
  limit: number;
  action: string;
}

export interface TransactionHistoryResponse extends BaseResponse<IHistoryPoint[]> {}

class TransactionApi {
  async getTransactionHistory(params: TransactionHistoryParams): Promise<TransactionHistoryResponse> {
    const httpClient = getHttpClient();
    return httpClient.get<TransactionHistoryResponse>('/b2c/v3/mobile/points/history', { params });
  }

  async getTransactionHistoryByMonth(date: Date, offset: number = 0, limit: number = 10): Promise<TransactionHistoryResponse> {
    const dateFrom = moment(date).startOf('month').format('YYYY-MM-DD HH:mm:ss');
    const dateTo = moment(date).endOf('month').format('YYYY-MM-DD HH:mm:ss');
    
    const params: TransactionHistoryParams = {
      createdFrom: dateFrom,
      createdTo: dateTo,
      offset,
      limit,
      action: 'GET_HISTORY_BY_MONTH'
    };
    
    return this.getTransactionHistory(params);
  }
}

export const transactionApi = new TransactionApi();