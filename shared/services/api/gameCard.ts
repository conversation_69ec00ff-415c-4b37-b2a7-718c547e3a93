import { getHttpClient } from "../http";
import type { IGameCard, IRequestGameCard } from "@taptap/shared";

export interface GameCardResponse {
  status: {
    message: string;
    code: number;
    success: boolean;
  };
  data: IGameCard[];
}

class GameCardService {
  private readonly BASE_URL = "/b2c/v3/mobile";

  /**
   * Fetch game cards
   * @param params Request parameters (offset, limit, brandCode)
   * @returns Game cards data
   */
  async getGameCards(params: IRequestGameCard): Promise<IGameCard[]> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.get<GameCardResponse>(
        `${this.BASE_URL}/game-cards`,
        {
          params,
          headers: {
            isV3: true,
          },
        }
      );

      // The httpClient returns the full response structure
      // We need to access response.data to get the actual game cards array
      return response.data || [];
    } catch (error) {
      console.error("Failed to fetch game cards:", error);
      return [];
    }
  }

  /**
   * Fetch game cards with default pagination
   * @param offset Offset for pagination
   * @param limit Number of items to fetch (default: 8)
   * @param brandCode Optional brand code filter
   * @returns Game cards data
   */
  async fetchGameCards(
    offset: number = 0,
    limit: number = 8,
    brandCode?: string
  ): Promise<IGameCard[]> {
    return this.getGameCards({
      offset,
      limit,
      ...(brandCode && { brandCode }),
    });
  }
}

export const gameCardService = new GameCardService();
