import { getHttpClient } from '../http';

// API Response wrapper type
export interface ApiResponse<T> {
  status: {
    message: string;
    code: number;
    success: boolean;
  };
  data: T;
}

export interface ITier {
  value: number;
  code: string;
  name: string;
}

export interface IMerchantTier {
  tierLevel: number;
  tierId: string;
  tierName: string;
  conditions: Array<{
    value: number;
    code: string;
    name: string;
  }>;
}

export interface ITierDetail {
  id: string;
  userId: string;
  tierLevel: number;
  tierId: string;
  tierName: string;
  tierBackground: string;
  tierLabelBackground: string;
  tierBenefit: string;
  current: ITier[];
  merchantTiers: IMerchantTier[];
  offerId: string;
  voucherName: string;
  voucherCode?: string;
  voucherCodeId?: string;
  triggerId: string;
  merchantId: string;
  merchantCode: string;
  giftStatus: 'NO_GIFT' | 'ERROR' | 'SUCCESS';
  tierProgressBarColor: string;
  merchantBackgroundColor: string;
}

export interface ITierClassDetail extends Omit<ITierDetail, 'id' | 'userId' | 'tierId' | 'offerId' | 'voucherName' | 'triggerId' | 'merchantId' | 'tierLabelBackground' | 'tierBenefit'> {
  currencyLogo: string;
  merchantLogo?: string;
  merchantDescription?: string;
  merchantName?: string;
  currencyName: string;
  currencyCode: string;
}

export interface IBenefits {
  image: string;
  description: string;
}

export interface ITierReward {
  id: string;
  level: number;
  name: string;
  background: string;
  labelBackground: string;
  progressBarColor: string;
  logo: string | null;
  conditions: Array<{
    value: number;
    code: string;
    name: string;
  }>;
  benefit: string | null;
  benefits: IBenefits[];
  description: string;
  offerId: string;
  offerName: string;
  offerMerchantCode?: string;
  offerMerchantLogo: string;
  triggerId: string | null;
  merchantId: string;
  merchantCode: string;
  tierRange?: string;
}

export type IStateTier = 'noRank' | 'hasRankTier' | 'hasRankAndVoucher' | 'maxTier';

export interface ITierMember {
  current: ITier[];
  giftStatus: string;
  id: string;
  merchantCode: string;
  merchantLogo: string;
  merchantName: string;
  merchantTiers: IMerchantTier[];
  tierBackground: string;
  tierBenefit: string;
  tierId: string;
  tierLabelBackground: string;
  tierLevel: number;
  tierLogo: string;
  tierName: string;
  tierProgressBarColor: string;
  merchantBackgroundColor: string;
  userId: string;
}

const API_PATHS = {
  TIER_DETAIL: '/api/v3.1/tier',
  TIER_DETAIL_WEBVIEW: '/b2c/v3/mobile/merchant/merchants/tiers/detail',
  REGISTER_TIER: '/api/v3.1/tier/register',
  TIER_REWARD: '/api/v3.1/tier/reward',
  TIER_REWARD_WEBVIEW: '/b2c/v3/mobile/merchant/merchants/tiers',
  MERCHANT_DETAIL: '/api/v3.1/merchant',
  BALANCE_DETAIL: '/api/v3.1/balance',
  TIER_MEMBER_LIST: '/b2c/v3/mobile/merchant/merchants/tiers/list-merchant',
  VOUCHER_DETAIL: '/b2c/v3/mobile/voucher/detail',
};

export const membershipApi = {
  async getTierDetail(merchantCode: string) {
    const httpClient = getHttpClient();
    const response = await httpClient.get<ITierDetail>(API_PATHS.TIER_DETAIL, {
      params: { merchantCode }
    });
    return response;
  },

  async getTierDetailWebview(merchantCode: string): Promise<ApiResponse<ITierClassDetail>> {
    const httpClient = getHttpClient();
    const response = await httpClient.get<ApiResponse<any>>(API_PATHS.TIER_DETAIL_WEBVIEW, {
      params: { merchantCode }
    });
    
    // Map the webview API response to ITierClassDetail format
    if (response?.data) {
      const data = response.data;
      
      // Map to ITierClassDetail structure
      const mappedData: ITierClassDetail = {
        tierLevel: data.tierLevel || 0,
        tierName: data.tierName || '',
        tierBackground: data.tierBackground || '',
        tierProgressBarColor: data.tierProgressBarColor || '#4CAF50',
        merchantBackgroundColor: data.merchantBackgroundColor || '#F7CC15',
        current: data.current || [],
        merchantTiers: data.merchantTiers || [],
        merchantCode: data.merchantCode || merchantCode,
        merchantName: data.merchantName || '',
        merchantLogo: data.merchantLogo || '',
        merchantDescription: data.merchantDescription || '',
        giftStatus: data.giftStatus || 'NO_GIFT',
        voucherCode: data.voucherCode,
        voucherCodeId: data.voucherCodeId,
        currencyLogo: data.currencyLogo || '',
        currencyName: data.currencyName || 'điểm',
        currencyCode: data.currencyCode || 'POINT',
      };
      
      return { ...response, data: mappedData };
    }
    
    return response as ApiResponse<ITierClassDetail>;
  },

  async registerTier(merchantCode: string): Promise<ApiResponse<ITierDetail>> {
    const httpClient = getHttpClient();
    const response = await httpClient.post<ApiResponse<ITierDetail>>(API_PATHS.REGISTER_TIER, {
      merchantCode
    });
    return response;
  },

  async getTierReward(merchantCode: string) {
    const httpClient = getHttpClient();
    const response = await httpClient.get<ITierReward[]>(API_PATHS.TIER_REWARD, {
      params: { merchantCode }
    });
    return response;
  },

  async getTierRewardWebview(merchantCode: string): Promise<ApiResponse<ITierReward[]>> {
    const httpClient = getHttpClient();
    const response = await httpClient.get<ApiResponse<ITierReward[]>>(API_PATHS.TIER_REWARD_WEBVIEW, {
      params: { merchantCode }
    });
    return response;
  },

  async getMerchantDetail(merchantCode: string) {
    const httpClient = getHttpClient();
    const response = await httpClient.get(API_PATHS.MERCHANT_DETAIL, {
      params: {
        action: 'DETAIL',
        code: merchantCode
      }
    });
    return response;
  },

  async getBalanceDetail(merchantCode: string, currencyCode: string) {
    const httpClient = getHttpClient();
    const response = await httpClient.get(API_PATHS.BALANCE_DETAIL, {
      params: {
        merchantCode,
        currencyCode,
        action: 'GET_BALANCE_DETAIL'
      }
    });
    return response;
  },

  async getTierMemberList(offset: number = 0, limit: number = 5) {
    const httpClient = getHttpClient();
    const response = await httpClient.get<ITierMember[]>(API_PATHS.TIER_MEMBER_LIST, {
      params: {
        offset,
        limit
      }
    });
    return response;
  },

  async getVoucherDetail(voucherCodeId: string): Promise<ApiResponse<any>> {
    const httpClient = getHttpClient();
    const response = await httpClient.get<ApiResponse<any>>(`${API_PATHS.VOUCHER_DETAIL}/${voucherCodeId}`);
    return response;
  }
};

export const membershipHelpers = {
  calculateProgress(currentValue: number, listTier: Array<{ value: number; tierName: string }>): number {
    const positiveNumers = currentValue > 0 ? currentValue : 0;
    const currValPercent = Math.abs(positiveNumers - listTier?.[0].value);
    const totalValPercentNextTier = Math.abs(listTier?.[0]?.value - listTier?.[1]?.value);
    const percentWidthProgress = listTier.length === 2 ? 100 : 50;
    const currentProgress = Math.floor((currValPercent / totalValPercentNextTier) * percentWidthProgress);
    return currentProgress;
  },

  totalEarnCurrency(listTier: Array<{ value: number }>, currentValue: number): number {
    if (listTier.length <= 1) return listTier?.[0].value - currentValue;
    return listTier[1].value - currentValue;
  },

  getListProgressTier(tierLevel: number, listMerchantTier: IMerchantTier[], limitTier = 2): IMerchantTier[] {
    const indexCurrentTier = listMerchantTier.findIndex(e => e.tierLevel === tierLevel);
    if (indexCurrentTier === listMerchantTier?.length - 1) {
      return listMerchantTier.slice(indexCurrentTier - 1, indexCurrentTier + limitTier);
    }
    return listMerchantTier.slice(indexCurrentTier, indexCurrentTier + limitTier);
  },

  getStateTier(dataTier: ITierClassDetail): IStateTier {
    if (!dataTier) return 'noRank';
    const isOnlyRankTier = dataTier.giftStatus === 'NO_GIFT';
    const isRankAndVoucher = !isOnlyRankTier;
    const maxValueTier = dataTier.merchantTiers[dataTier.merchantTiers.length - 1].conditions?.[0].value;
    const isMaxLevel = dataTier.current[0].value >= maxValueTier;

    if (isMaxLevel) return 'maxTier';
    if (isRankAndVoucher) return 'hasRankAndVoucher';
    if (isOnlyRankTier) return 'hasRankTier';
    return 'noRank';
  }
};