import { apiService } from '../http/httpService';

export interface ScanAndEarnRequest {
  mobile: string;
  voucherCode: string;
  source: 'INPUT_CODE' | 'SCAN_AND_EARN';
}

export interface ScanAndEarnResponse {
  status: {
    message: string;
    code: number;
    success: boolean;
  };
  data?: {
    requestId?: string;
    message?: string;
    gifts?: any[];
  };
}

export interface ContentSharingResponse {
  status: number;
  data: {
    id: string;
    idx: number;
    description: string;
    contentUrl: string;
    placeId?: string | null;
    status: string;
    featureId: string;
    featureKey: string;
    featureName: string;
    createdDate: string;
    createdBy?: string | null;
    updatedDate: string;
    updatedBy: string;
  };
  description: string;
}

const API_ENDPOINTS = {
  SCAN_AND_EARN: '/b2c/v3/mobile/scanandearn',
  GET_CONTENT_SHARING: '/api/sns/v3/fbshare/v1.0.0/mobile/facebook-sharing/',
};

export const giftCodeApi = {
  async submitGiftCode(params: {
    mobile: string;
    voucherCode: string;
  }): Promise<ScanAndEarnResponse> {
    const response = await apiService.post<ScanAndEarnResponse>(
      API_ENDPOINTS.SCAN_AND_EARN,
      {
        mobile: params.mobile,
        voucherCode: params.voucherCode.toUpperCase(),
        source: 'INPUT_CODE',
      }
    );
    
    return response;
  },

  async getGiftCodeDescription(featureKey: string = 'REDEEM_CODE'): Promise<ContentSharingResponse> {
    const response = await apiService.get<ContentSharingResponse>(
      `${API_ENDPOINTS.GET_CONTENT_SHARING}${featureKey}`
    );
    
    return response;
  },
};