import { getHttpClient } from '../http';

export interface ContactUsConfig {
  companyAddress: string;
  companyInfo: string;
  email: string;
  facebookLink: string;
  instagramLink: string;
  youtubeLink: string;
  phone: string;
  website: string;
  lat: number;
  lng: number;
}

export interface ApiResponse<T> {
  status: {
    message: string;
    code: number;
    success: boolean;
  };
  data: T;
}

const API_PATHS = {
  CONTACT_US_CONFIG: '/b2c/v3/mobile/contact-us-config',
};

export const contactApi = {
  async getContactUsConfig(): Promise<ApiResponse<ContactUsConfig>> {
    const httpClient = getHttpClient();
    const response = await httpClient.get<ApiResponse<ContactUsConfig>>(API_PATHS.CONTACT_US_CONFIG);
    return response;
  },
};