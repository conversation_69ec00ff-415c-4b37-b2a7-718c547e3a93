import { getHttpClient } from "../http";

// API Endpoints - Replace with your actual base URL
const BASE_URL = ""; // Replace with your actual base URL

const API_ENDPOINTS = {
  NEW_CHALLENGE_V3: `${BASE_URL}/b2c/v3/mobile/new-challenge`,
  BRANDED_WHEEL: `${BASE_URL}/b2c/v3/mobile/branded-wheel`,
  GAME_CARDS: `${BASE_URL}/b2c/v3/mobile/game-cards`,
  GET_BANNERS: `${BASE_URL}/b2c/v3/mobile/banner-v3`,
  GET_LINK_CREDENTIAL: `${BASE_URL}/b2c/v3/mobile/v1/3rd-party/credential/get_link`,
};

// Base response interface
export interface BaseResponse<T> {
  status: {
    message: string;
    code: number;
    success: boolean;
  };
  data?: T;
}

// Challenge Types
export type ChallengeState = "PUBLISH" | "UNPUBLISH" | "OUT_OF_STOCK";

export type UserProgressState =
  | "RUNNING"
  | "RELEASING"
  | "SUCCESSFULLY"
  | "FAILED"
  | "NOT_RECEIVED_YET"
  | "ARCHIVE"
  | "";

export type ConditionType =
  | "BRAND"
  | "EXCLUDE_SEGMENT"
  | "BILL_AMOUNT"
  | "OPEN_LINK"
  | "ITEM";

export type GiftType = "VOUCHER" | "POINT" | "BRAND_CURRENCY";

export interface GiftDisplay {
  giftTab: string;
  logoLink?: string;
}

export interface GiftDetails {
  giftImageLink: string;
  giftContent: string;
  giftType: GiftType;
}

export interface ChallengeBrand {
  brandId?: string;
  logoLink: string;
}

export interface Condition {
  conditionType: ConditionType;
  transactionsWithinBrands?: number;
  transactionEachBrand?: number;
  brands?: ChallengeBrand[];
  buttonLabel?: string;
  directBtnLink?: string;
}

export interface PackageGift {
  content?: string;
  stock: number;
  remain: number;
  gifts: GiftDetails[];
}

export interface ChallengeUserProgress {
  brandId?: string;
  logoLink: string;
  validTransactions?: string[];
}

export interface UserProgress {
  currentProgress: number;
  maxProgress: number;
  percentProgress: number;
  // Keep old fields for backward compatibility
  challengeId?: string;
  userId?: string;
  challengeName?: string;
  mobile?: string;
  displayName?: string;
  state?: UserProgressState;
  progress?: ChallengeUserProgress[];
  hadJoinChallenge?: boolean;
}

export enum ConditionChallengeType {
  EARN = "EARN",
  OPEN_LINK = "OPEN_LINK",
  REDEEM = "REDEEM",
  REDEEM_POINT = "REDEEM_POINT",
  EARN_POINT = "EARN_POINT",
  PURCHASE = "PURCHASE",
}

export interface ChallengeV3 {
  _id: string;
  state: ChallengeState;
  status: string; // New field from API response
  challengeType: string;
  joinChallengeType: "AUTO" | "MANUAL";
  receiveGiftType: "AUTO" | "MANUAL";
  isProgressNotification?: boolean; // New field
  title?: string; // New field for notification title
  body?: string; // New field for notification body
  challengeImage: string;
  displayChallengeName: string;
  hexBackgroundColor: string;
  challengeNameColor: string;
  index: number;
  startDate: string;
  endDate: string;
  isDisplayRemainingTime: boolean;
  isNewWayGiftIssue?: boolean; // New field
  giftDisplay: GiftDisplay[];
  description?: string;
  note?: string;
  conditionChallengeType: ConditionChallengeType;
  conditions: Condition[];
  packageGift: PackageGift;
  newPackageGift?: NewPackageGift; // New field
  isDeleted?: boolean; // New field
  createdAt?: string; // New field
  updatedAt?: string; // New field
  __v?: number; // New field
  userProgress?: UserProgress | null;
  canUserJoinChallenge?: boolean;
}

// New interface for new package gift structure
export interface NewPackageGift {
  remain: number;
  content: string;
  stock: number;
  gifts: NewGiftDetails[];
}

// New interface for gift details in new package
export interface NewGiftDetails {
  giftImageLink: string;
  giftContent: string;
  giftType: string;
  offerId?: string;
}

export interface RequestListChallengeV3 {
  offset?: number;
  limit?: number;
  userProgressState?: UserProgressState;
  home?: boolean;
}

export interface ChallengeCount {
  challengeId: string;
  challengeImage: string;
}

// Gift Types
export enum GiftTypeEnum {
  POINT = "POINT",
  BC = "BC",
  VOUCHER = "VOUCHER",
  WISHES = "WISHES",
  UNLUCKY = "UNLUCKY",
  BRAND_CURRENCY = "BRAND_CURRENCY",
}

export interface ResReceiveGift {
  giftType: keyof typeof GiftTypeEnum;
  giftImageLink: string;
  giftContent: string;
}

// Wheel Types
export interface GiftDescription {
  _id: string;
  name: string;
  logoUrl?: string;
}

export interface WheelGift {
  index: number;
  name: string;
  type: "VOUCHER" | "WISHES";
  logoUrl: string;
  label: string;
  rate: number;
  quantity: number;
  _id: string;
  brandCurrencyPoint?: number;
  unitPoint?: number;
}

export interface BrandPoints {
  totalBalance: number;
}

export interface WheelUser {
  spinTurns: number;
  vuiPoints: number;
  brandPoints?: BrandPoints;
}

export interface WheelData {
  _id: string;
  wheelName: string;
  displayWheelName: string;
  coverImageUrl: string;
  thumbnailImageUrl: string;
  logoUrl: string;
  isDisplayRemainingTime: boolean;
  tncLink?: string;
  backgroundColor?: string;
  textColor?: string;
  giftDescriptions: GiftDescription[];
  giftDetails: WheelGift[];
  type: "RESETDAILY" | "CAMPAIGN";
  defaultNumberOfSpin: number;
  maxSpin: number;
  brandCode?: string;
  startDate: string;
  endDate: string;
  teaserDate?: string;
  purchaseByVUIPoint?: number;
  purchaseByBrandCurrency?: number;
  brandCurrencyId?: string;
  brandCurrencyIcon?: string;
}

export interface WheelDetail {
  user: WheelUser;
  wheel: WheelData;
}

// Game Card Types
export interface RequestGameCard {
  offset: number;
  limit: number;
  brandCode?: string;
}

export interface ParamsWebViewGameCard {
  appShow?: "INAPP" | "WEBVIEW";
  hideHeader?: "true" | "false";
  [key: string]: string | undefined;
}

export interface DataGameCard {
  closeTime: string;
  params?: ParamsWebViewGameCard;
  displayName?: string;
}

export interface GameCard {
  _id: string;
  type: "branded_wheel" | "game" | "daily_checkin";
  data: WheelData & DataGameCard;
}

export type ResponseGameCard = GameCard[];

// Request/Response Types
export interface RequestOneSpinTurn {
  wheelId: string;
}

export interface RequestPurchaseTurn {
  wheelId: string;
  turns: number;
}

export interface RequestCBOpenLink {
  topic: string;
  action: string;
  data: {
    userId: string;
    mobile: string;
    date: string;
    link: string;
  };
}

// Banner Types
export interface BannerType {
  _id: string;
  title: string;
  description?: string;
  imageUrl: string;
  linkUrl?: string;
  displayScreen: string;
  isActive: boolean;
  startDate: string;
  endDate: string;
  order: number;
}

// Response interfaces
export type ChallengeListResponse = BaseResponse<ChallengeV3[]>;
export type ChallengeDetailResponse = BaseResponse<ChallengeV3>;
export type ChallengeCountResponse = BaseResponse<{
  challenges: ChallengeCount[];
  count: number;
}>;
export type WheelListResponse = BaseResponse<WheelData[]>;
export type WheelDetailResponse = BaseResponse<WheelDetail>;
export type GameCardResponse = BaseResponse<ResponseGameCard>;
export type BannerResponse = BaseResponse<BannerType[]>;
export type LinkCredentialResponse = BaseResponse<string>;
export type SpinResponse = BaseResponse<WheelGift>;
export type PurchaseWheelTurnsResponse = BaseResponse<WheelUser>;
export type CBOpenLinkResponse = BaseResponse<string>;
export type ReceiveGiftResponse = BaseResponse<ResReceiveGift>;

class EntertainmentService {
  /**
   * Challenge APIs
   */

  /**
   * Get list of challenges
   * @param params Request parameters (offset, limit, userProgressState, home)
   * @param signal AbortSignal for request cancellation
   * @returns List of challenges
   */
  async getListChallengeV3(
    params?: RequestListChallengeV3,
    signal?: AbortSignal
  ): Promise<ChallengeV3[]> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.get<ChallengeListResponse>(
        API_ENDPOINTS.NEW_CHALLENGE_V3,
        {
          params,
          headers: { isV3: true },
          signal,
        }
      );
      return response.data || [];
    } catch (error) {
      console.error("Failed to fetch challenge list:", error);
      return [];
    }
  }

  /**
   * Get challenge list with count
   * @param params Request parameters
   * @param signal AbortSignal for request cancellation
   * @returns Challenge list with count
   */
  async getListChallengeV3WithCount(
    params?: RequestListChallengeV3,
    signal?: AbortSignal
  ): Promise<{ challenges: ChallengeCount[]; count: number }> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.get<ChallengeCountResponse>(
        API_ENDPOINTS.NEW_CHALLENGE_V3,
        {
          params: {
            ...params,
            count: true,
          },
          headers: { isV3: true },
          signal,
        }
      );
      return response.data || { challenges: [], count: 0 };
    } catch (error) {
      console.error("Failed to fetch challenge list with count:", error);
      return { challenges: [], count: 0 };
    }
  }

  /**
   * Get challenge detail by ID
   * @param challengeId Challenge ID
   * @param signal AbortSignal for request cancellation
   * @returns Challenge detail
   */
  async getChallengeV3Detail(
    challengeId: string,
    signal?: AbortSignal
  ): Promise<ChallengeV3 | null> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.get<ChallengeDetailResponse>(
        `${API_ENDPOINTS.NEW_CHALLENGE_V3}/${challengeId}`,
        {
          headers: { isV3: true },
          signal,
        }
      );
      return response.data || null;
    } catch (error) {
      console.error("Failed to fetch challenge detail:", error);
      return null;
    }
  }

  /**
   * Join a challenge
   * @param challengeId Challenge ID
   * @returns Join response
   */
  async joinChallenge(challengeId: string): Promise<BaseResponse<null> | null> {
    try {
      const httpClient = getHttpClient();
      return await httpClient.post(
        `${API_ENDPOINTS.NEW_CHALLENGE_V3}/${challengeId}/join`,
        {},
        { headers: { isV3: true } }
      );
    } catch (error) {
      console.error("Failed to join challenge:", error);
      return null;
    }
  }

  /**
   * Receive gift for a challenge
   * @param challengeId Challenge ID
   * @returns Gift receive response
   */
  async receiveGift(challengeId: string): Promise<ResReceiveGift | null> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.post<ReceiveGiftResponse>(
        `${API_ENDPOINTS.NEW_CHALLENGE_V3}/${challengeId}`,
        {},
        { headers: { isV3: true } }
      );
      return response.data || null;
    } catch (error) {
      console.error("Failed to receive gift:", error);
      return null;
    }
  }

  /**
   * Callback for opening link
   * @param data Callback data
   * @returns Callback response
   */
  async callbackOpenLink(data: RequestCBOpenLink): Promise<string | null> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.post<CBOpenLinkResponse>(
        `${API_ENDPOINTS.NEW_CHALLENGE_V3}/callback/open-link`,
        data,
        { headers: { isV3: true } }
      );
      return response.data || null;
    } catch (error) {
      console.error("Failed to callback open link:", error);
      return null;
    }
  }

  /**
   * Wheel APIs
   */

  /**
   * Get list of wheels
   * @param offset Pagination offset
   * @param limit Number of items to fetch
   * @param brandCode Optional brand code filter
   * @param signal AbortSignal for request cancellation
   * @returns List of wheels
   */
  async getListWheel(
    offset: number = 0,
    limit: number = 8,
    brandCode?: string,
    signal?: AbortSignal
  ): Promise<WheelData[]> {
    try {
      const url = `${
        API_ENDPOINTS.BRANDED_WHEEL
      }?offset=${offset}&limit=${limit}${
        brandCode ? `&brandCode=${brandCode}` : ""
      }`;

      const httpClient = getHttpClient();
      const response = await httpClient.get<WheelListResponse>(url, {
        headers: { isV3: true },
        signal,
      });
      return response.data || [];
    } catch (error) {
      console.error("Failed to fetch wheel list:", error);
      return [];
    }
  }

  /**
   * Get wheel detail by ID
   * @param wheelId Wheel ID
   * @param signal AbortSignal for request cancellation
   * @returns Wheel detail
   */
  async getWheel(
    wheelId: string,
    signal?: AbortSignal
  ): Promise<WheelDetail | null> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.get<WheelDetailResponse>(
        `${API_ENDPOINTS.BRANDED_WHEEL}/${wheelId}`,
        {
          headers: { isV3: true },
          signal,
        }
      );
      return response.data || null;
    } catch (error) {
      console.error("Failed to fetch wheel detail:", error);
      return null;
    }
  }

  /**
   * Spin the wheel
   * @param data Spin request data
   * @returns Spin result
   */
  async spinWheel(data: RequestOneSpinTurn): Promise<BaseResponse<WheelGift>> {
    try {
      const httpClient = getHttpClient();
      return httpClient.post<SpinResponse>(
        `${API_ENDPOINTS.BRANDED_WHEEL}/spin-wheel`,
        data,
        { headers: { isV3: true } }
      );
    } catch (error) {
      console.error("Failed to spin wheel:", error);
      return {
        status: {
          message: "Failed to spin wheel",
          code: 500,
          success: false,
        },
      };
    }
  }

  /**
   * Purchase wheel turns
   * @param data Purchase request data
   * @returns Purchase response
   */
  async purchaseTurn(
    data: RequestPurchaseTurn
  ): Promise<BaseResponse<WheelUser> | null> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.post<PurchaseWheelTurnsResponse>(
        `${API_ENDPOINTS.BRANDED_WHEEL}/purchase-wheel`,
        data,
        { headers: { isV3: true } }
      );
      return response;
    } catch (error) {
      console.error("Failed to purchase wheel turns:", error);
      return null;
    }
  }

  /**
   * Game Card APIs
   */

  /**
   * Get game cards and wheels
   * @param params Request parameters
   * @param signal AbortSignal for request cancellation
   * @returns Game cards data
   */
  async getCardGameAndWheel(
    params: RequestGameCard,
    signal?: AbortSignal
  ): Promise<ResponseGameCard> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.get<GameCardResponse>(
        API_ENDPOINTS.GAME_CARDS,
        {
          headers: { isV3: true },
          params,
          signal,
        }
      );
      return response.data || [];
    } catch (error) {
      console.error("Failed to fetch game cards and wheels:", error);
      return [];
    }
  }

  /**
   * Get game cards with default pagination
   * @param offset Offset for pagination
   * @param limit Number of items to fetch (default: 8)
   * @param brandCode Optional brand code filter
   * @returns Game cards data
   */
  async fetchGameCards(
    offset: number = 0,
    limit: number = 8,
    brandCode?: string
  ): Promise<ResponseGameCard> {
    return this.getCardGameAndWheel({
      offset,
      limit,
      ...(brandCode && { brandCode }),
    });
  }

  /**
   * Banner APIs
   */

  /**
   * Get banners by screen
   * @param displayScreen Screen name
   * @param signal AbortSignal for request cancellation
   * @returns List of banners
   */
  async getBanners(
    displayScreen: string,
    signal?: AbortSignal
  ): Promise<BannerType[]> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.get<BannerResponse>(
        `${API_ENDPOINTS.GET_BANNERS}?action=LIST_BY_SCREEN`,
        {
          headers: { isV3: true },
          params: { displayScreen },
          signal,
        }
      );
      return response.data || [];
    } catch (error) {
      console.error("Failed to fetch banners:", error);
      return [];
    }
  }

  /**
   * Link Credential APIs
   */

  /**
   * Get link game credential
   * @param params Request parameters
   * @returns Link credential
   */
  async getLinkGameCredential(
    params: Record<string, string>
  ): Promise<string | null> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.get<LinkCredentialResponse>(
        API_ENDPOINTS.GET_LINK_CREDENTIAL,
        {
          headers: { isV3: true },
          params,
        }
      );
      return response.data || null;
    } catch (error) {
      console.error("Failed to get link game credential:", error);
      return null;
    }
  }

  /**
   * Utility methods
   */

  /**
   * Get challenges for home screen
   * @param limit Number of challenges to fetch
   * @returns Home challenges
   */
  async getHomeChallenges(limit: number = 5): Promise<ChallengeV3[]> {
    return this.getListChallengeV3({
      limit,
      home: true,
    });
  }

  /**
   * Get active challenges
   * @param offset Pagination offset
   * @param limit Number of items to fetch
   * @returns Active challenges
   */
  async getActiveChallenges(
    offset: number = 0,
    limit: number = 10
  ): Promise<ChallengeV3[]> {
    return this.getListChallengeV3({
      offset,
      limit,
      userProgressState: "RUNNING",
    });
  }

  /**
   * Get completed challenges
   * @param offset Pagination offset
   * @param limit Number of items to fetch
   * @returns Completed challenges
   */
  async getCompletedChallenges(
    offset: number = 0,
    limit: number = 10
  ): Promise<ChallengeV3[]> {
    return this.getListChallengeV3({
      offset,
      limit,
      userProgressState: "SUCCESSFULLY",
    });
  }

  /**
   * Get wheels by brand
   * @param brandCode Brand code
   * @param limit Number of wheels to fetch
   * @returns Wheels for the brand
   */
  async getWheelsByBrand(
    brandCode: string,
    limit: number = 10
  ): Promise<WheelData[]> {
    return this.getListWheel(0, limit, brandCode);
  }

  /**
   * Get popular wheels
   * @param limit Number of wheels to fetch
   * @returns Popular wheels
   */
  async getPopularWheels(limit: number = 8): Promise<WheelData[]> {
    return this.getListWheel(0, limit);
  }
}

export const entertainmentService = new EntertainmentService();
