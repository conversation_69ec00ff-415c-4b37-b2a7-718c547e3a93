import { getHttpClient } from '../http';
import type { 
  RequestBalance, 
  RequestHistory, 
  BrandCurrencyType, 
  PointHistoryItem 
} from './types';

class CurrencyApi {
  async getBalance(params: RequestBalance): Promise<{
    status: { success: boolean; code: number; message: string };
    data: BrandCurrencyType[] | BrandCurrencyType;
  }> {
    const httpClient = getHttpClient();
    return httpClient.get('/b2c/v3/mobile/brand/point/balance', { params });
  }

  async getHistory(params: RequestHistory): Promise<{
    status: { success: boolean; code: number; message: string };
    data: PointHistoryItem[];
  }> {
    const httpClient = getHttpClient();
    return httpClient.get('/b2c/v3/mobile/currencies/history', { params });
  }

  async getPointsHistory(params: RequestHistory): Promise<{
    status: { success: boolean; code: number; message: string };
    data: PointHistoryItem[];
  }> {
    const httpClient = getHttpClient();
    return httpClient.get('/b2c/v3/mobile/points/history', { params });
  }
}

export const currencyApi = new CurrencyApi();