export interface NewsDetail {
  _id: string;
  title: string;
  imageUrl?: string;
  startDate?: string;
  endDate?: string;
  contentType: 'External link' | 'Internal content' | string;
  externalLink?: string;
  content?: string;
  priority?: number;
  author?: string;
  category?: string;
  views?: number;
  likes?: number;
  summary?: string;
  tags?: string[];
  relatedNews?: NewsItem[];
}

export interface NewsItem {
  id: string;
  title: string;
  imageUrl?: string;
  summary?: string;
  publishedAt?: string;
  category?: string;
}

export interface NewsDetailResponse {
  status: {
    message: string;
    code: number;
    success: boolean;
  };
  data: NewsDetail;
}

export interface NewsListResponse {
  status: {
    message: string;
    code: number;
    success: boolean;
  };
  data: NewsItem[];
}