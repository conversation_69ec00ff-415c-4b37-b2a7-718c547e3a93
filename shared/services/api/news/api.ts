import { getHttpClient } from '../../http';
import { NewsDetailResponse, NewsListResponse } from './types';

export const newsAPI = {
  /**
   * Get news detail by ID
   * @param id - News ID
   * @returns News detail response
   */
  getNewsDetail: async (id: string): Promise<NewsDetailResponse> => {
    console.log('🔄 newsAPI.getNewsDetail called:', { id });
    
    const httpClient = getHttpClient();
    const endpoint = `/news-config/${id}`;
    
    console.log('📡 Making HTTP request to:', endpoint);
    
    try {
      const response = await httpClient.get<NewsDetailResponse>(endpoint);
      console.log('✅ newsAPI response received:', {
        status: response?.status,
        hasData: !!response?.data,
        dataKeys: response?.data ? Object.keys(response.data) : []
      });
      return response;
    } catch (error) {
      console.error('🚨 newsAPI.getNewsDetail error:', {
        error,
        message: error?.message,
        endpoint,
        id
      });
      throw error;
    }
  },

  /**
   * Get news list
   * @returns News list response
   */
  getNewsList: async (): Promise<NewsListResponse> => {
    console.log('🔄 newsAPI.getNewsList called');
    const httpClient = getHttpClient();
    const endpoint = '/news-config';
    
    try {
      const response = await httpClient.get<NewsListResponse>(endpoint);
      console.log('✅ newsAPI list response received:', {
        status: response?.status,
        hasData: !!response?.data
      });
      return response;
    } catch (error) {
      console.error('🚨 newsAPI.getNewsList error:', error);
      throw error;
    }
  },
};