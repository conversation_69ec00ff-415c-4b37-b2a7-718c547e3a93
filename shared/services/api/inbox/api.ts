import { getHttpClient } from '../../http';
import type { InboxListParams, InboxListResponse, InboxDetailResponse } from './types';

export class InboxAPI {
  private readonly basePath = '/b2c/v3/mobile/inbox';

  async getInboxList(params: InboxListParams = {}): Promise<InboxListResponse> {
    const {
      limit = 10,
      offset = 0,
      action = 'LIST_FOR_MOBILE'
    } = params;

    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
      action
    });

    const response = await getHttpClient().get(`${this.basePath}?${queryParams}`);
    
    return {
      data: response?.data || [],
      totalCount: response?.meta?.totalCount || response?.totalCount || 0,
      limit,
      offset,
      status: response?.status,
      meta: response?.meta
    };
  }

  async getInboxDetail(id: string): Promise<InboxDetailResponse> {
    const response = await getHttpClient().get(`${this.basePath}?id=${id}&action=SHOW_FOR_MOBILE`);
    
    return {
      data: response?.data,
      status: response?.status
    };
  }

  // async markAsRead(id: string): Promise<void> {
  //   await getHttpClient().put(`${this.basePath}/${id}/read`);
  // }

  async markAllAsRead(): Promise<void> {
    await getHttpClient().put(`${this.basePath}/read-all`);
  }

  async getUnseenCount(): Promise<number> {
    const response = await getHttpClient().get(`${this.basePath}?action=GET_UNSEEN_NUMBER`);
    return response?.data || 0;
  }
}

export const inboxAPI = new InboxAPI();