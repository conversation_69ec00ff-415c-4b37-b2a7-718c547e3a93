export interface InboxTemplateBasicData {
  title: string;
  body: string;
  type: string;
  image: string;
  topic: string;
  directLink: string;
  action: string;
  subTitle?: string;
  ctaButton?: string;
  ctaLink?: string;
}

export interface InboxTemplateMetaData {
  vuiPoint?: number;
  brandCurrencyCode?: string;
  brandCurrencyLogo?: string;
  brandCurrencyName?: string;
  brandCurrencyPoint?: number;
  voucherCodeIdList?: string[];
}

export interface InboxTemplateParams {
  surveyGifts?: string;
  [key: string]: any;
}

export interface InboxNotificationBasicData {
  title: string;
  body: string;
  type: string;
  image: string;
  topic: string;
  directLink: string;
  action: string;
}

export interface InboxItem {
  id: string;
  userId: string;
  mobile: string;
  storeCode: string;
  requestSource: string;
  isSeen: boolean;
  templateBasicData: InboxTemplateBasicData;
  templateMetaData: InboxTemplateMetaData;
  notificationBasicData: InboxNotificationBasicData;
  templateParams: InboxTemplateParams;
  templateCode?: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
}

export interface InboxListParams {
  limit?: number;
  offset?: number;
  action?: string;
}

import { BaseResponse } from '../../../stores/types';

export interface InboxListResponse extends BaseResponse<InboxItem[]> {
  totalCount?: number;
  limit?: number;
  offset?: number;
}

export interface InboxDetailResponse extends BaseResponse<InboxItem> {}