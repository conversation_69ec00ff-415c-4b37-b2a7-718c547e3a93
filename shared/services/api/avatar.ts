import { getHttpClient } from '../http';
import type { BaseResponse } from '../../stores/types';

export interface AvatarCategory {
  id: string;
  name: string;
  image: string;
  isDefault?: boolean;
}

export interface Avatar {
  id: string;
  categoryId: string;
  avatarName: string;
  avatarImage: string;
  point: number;
  isDefault: boolean;
  enable: boolean;
}

export interface CurrentAvatar {
  id: string;
  image: string;
}

export interface AvatarCategoriesResponse extends BaseResponse<AvatarCategory[]> {}

export interface AvatarListResponse extends BaseResponse<Avatar[]> {}

export interface CurrentAvatarResponse extends BaseResponse<CurrentAvatar> {}

class AvatarAPI {
  async getAvatarCategories(): Promise<AvatarCategoriesResponse> {
    const httpClient = getHttpClient();
    const params = {
      page: 0,
      size: 0,
      sort: 'isDefault,desc'
    };
    
    return httpClient.get<AvatarCategoriesResponse>('gf/v3/avatar/v1/mobile/categories', { params });
  }

  async getAvatarsByCategory(categoryId: string, isMyAvatar: boolean = false, mobile?: string): Promise<AvatarListResponse> {
    const httpClient = getHttpClient();
    const params = {
      page: 0,
      size: 100,
      sort: isMyAvatar ? 'isDefault,desc' : '',
      mobile: mobile || '' // Pass mobile phone number
    };
    
    return httpClient.get<AvatarListResponse>(
      `gf/v3/avatar/v1/mobile/categories/${categoryId}/avatars`,
      { params }
    );
  }

  async buyOrUseAvatar(avatarId: string): Promise<CurrentAvatarResponse> {
    const httpClient = getHttpClient();
    return httpClient.put<CurrentAvatarResponse>('gf/v3/avatar/user-avatars', {
      id: avatarId,
      action: 'USE'
    });
  }

  async getCurrentAvatar(): Promise<CurrentAvatarResponse> {
    const httpClient = getHttpClient();
    return httpClient.get<CurrentAvatarResponse>('gf/v3/avatar/user-avatars');
  }
}

export const avatarAPI = new AvatarAPI();
export default avatarAPI;