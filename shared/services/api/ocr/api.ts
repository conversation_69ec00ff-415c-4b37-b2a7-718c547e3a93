import { getHttpClient } from '../../http';
import type { OCRTransactionResponse, OCRTransactionParams, OCRTransactionDetailResponse, OCRStatisticsResponse } from './types';

export const OCR_ENDPOINTS = {
  TRANSACTIONS: '/b2c/v3/mobile/v2/ocr/transactions',
  STATISTICS: '/b2c/v3/mobile/v2/ocr/transactions/statistics'
} as const;

export const ocrAPI = {
  /**
   * Get OCR transactions with filtering and pagination
   */
  async getTransactions(params: OCRTransactionParams = {}): Promise<OCRTransactionResponse> {
    const { status, page = 1, size = 10 } = params;
    
    const queryParams = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });

    if (status) {
      queryParams.append('status', status);
    }

    const url = `${OCR_ENDPOINTS.TRANSACTIONS}?${queryParams.toString()}`;
    const httpClient = getHttpClient();
    return await httpClient.get<OCRTransactionResponse>(url);
  },

  /**
   * Get OCR transaction detail by ID
   */
  async getTransactionDetail(transactionId: string): Promise<OCRTransactionDetailResponse> {
    const url = `${OCR_ENDPOINTS.TRANSACTIONS}/${transactionId}`;
    const httpClient = getHttpClient();
    return await httpClient.get<OCRTransactionDetailResponse>(url);
  },

  /**
   * Get OCR transaction statistics
   */
  async getStatistics(): Promise<OCRStatisticsResponse> {
    const httpClient = getHttpClient();
    return await httpClient.get<OCRStatisticsResponse>(OCR_ENDPOINTS.STATISTICS);
  }
};