import { BaseResponse } from '../../../stores/types';

export interface RewardResult {
  rewardCode: 'VUI_POINT' | 'BRAND_POINT' | 'VOUCHER';
  meta: {
    vuiPoint?: number;
    brandCurrencyPoint?: number;
    brandCurrencyCode?: string;
    brandCurrencyName?: string;
    brandCurrencyLogo?: string;
    voucherCodeIdList?: string;
    voucherCode?: string;
  };
}

export interface OCRTransaction {
  _id: string;
  brandCode: string;
  amount: number | null;
  ocrImage: string;
  state: 'PROCESSING' | 'MANUAL_APPROVE' | 'APPROVED' | 'REJECTED' | 'COMPLETED';
  rewardResult: RewardResult[] | null;
  isView: boolean;
  number: string | null;
  result: string | null;
  brandName: string;
  brandLogo: string;
  billingTime: string | null;
  snapDate: string;
}

export interface OCRTransactionResponse extends BaseResponse<OCRTransaction[]> {}

export interface OCRTransactionParams {
  status?: 'PROCESSING' | 'APPROVED' | 'REJECTED' | 'COMPLETED';
  page?: number;
  size?: number;
}

export type OCRTransactionStatus = 'PROCESSING' | 'APPROVED' | 'REJECTED' | 'COMPLETED';

export interface OCRTransactionDetailResponse extends BaseResponse<OCRTransaction> {}

export interface OCRStatistics {
  PROCESSING: number;
  COMPLETED: number;
  FAILED: number;
}

export interface OCRStatisticsResponse extends BaseResponse<OCRStatistics> {}