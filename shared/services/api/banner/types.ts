// Banner types for banner detail API
export interface BannerData {
  id: string;
  bannerCollectionId: string;
  name: string;
  code: string;
  isDisplayOnMobileApplication: boolean;
  isHideWebviewHeader: boolean;
  url: string;
  order: number;
  imageUrl: string;
  orientation: 'PORTRAIT' | 'LANDSCAPE';
  appShow: 'DIRECTLINK' | 'WEBVIEW';
  directInfo: {
    buttonLabel: string;
    buttonIcon: string;
    buttonLink: string;
    status: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface BannerDetailResponse {
  status: {
    message: string;
    code: number;
    success: boolean;
  };
  data: BannerData[];
}

export interface BannerDetailParams {
  bannerId: string;
}