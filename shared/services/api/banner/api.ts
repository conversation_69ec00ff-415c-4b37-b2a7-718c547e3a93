import { getHttpClient } from '../../http';
import type { BannerDetailResponse, BannerData } from './types';

export class BannerAPI {
  private readonly basePath = '/b2c/v3/mobile/banner';

  /**
   * Get banner details by ID
   */
  async getBannerDetail(bannerId: string): Promise<BannerData | null> {
    try {
      const response = await getHttpClient().get(`${this.basePath}/${bannerId}`);
      
      if (response?.status?.success && response?.data) {
        // If response.data is an array, return the first item
        if (Array.isArray(response.data) && response.data.length > 0) {
          return response.data[0];
        }
        // If response.data is a single object
        if (typeof response.data === 'object' && !Array.isArray(response.data)) {
          return response.data;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Failed to fetch banner detail:', error);
      throw error;
    }
  }

  /**
   * Get all banners (for reference/fallback)
   */
  async getAllBanners(): Promise<BannerData[]> {
    try {
      const response = await getHttpClient().get(this.basePath);
      
      if (response?.status?.success && response?.data) {
        return Array.isArray(response.data) ? response.data : [response.data];
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch banners:', error);
      throw error;
    }
  }
}

export const bannerAPI = new BannerAPI();