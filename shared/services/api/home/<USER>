import { getHttpClient } from '../../http';
import { buildApiUrl } from '../../http/config';
import { BaseResponse } from '../../../stores/types';
import {
  HotNewsArticle,
  BannerItem,
  HomeConfigResponse,
  CustomerGift,
  QuickAction,
  HomeFeedItem,
  AppAnnouncement,
  HotNewsResponse,
  HomeConfigApiResponse,
  BannersResponse,
  CustomerGiftResponse,
  QuickActionsResponse,
  HomeFeedResponse,
  AnnouncementsResponse,
} from './types';

class HomeAPI {
  private get httpClient() {
    return getHttpClient();
  }

  // Get hot news articles - matching mobile implementation
  async getHotNews(
    params?: {
      limit?: number;
      category?: string;
      featured?: boolean;
    }
  ): Promise<HotNewsResponse> {
    return this.httpClient.get<HotNewsResponse>(
      buildApiUrl('B2C', 'MOBILE', 'news-config'),
      {
        params,
        headers: { isV3: true },
      }
    );
  }

  // Get home screen configuration - matching mobile implementation
  async getHomeConfig(params?: { panelGroup?: string }): Promise<HomeConfigApiResponse> {
    const url = buildApiUrl('B2C', 'MOBILE', 'home-config-v3') + '?action=GET_BY_SEGMENT_LIST_FOR_MOBILE';
    return this.httpClient.get<HomeConfigApiResponse>(url, {
      params,
      headers: { isV3: true },
    });
  }

  // Get customer gifts
  async getCustomerGift(): Promise<CustomerGiftResponse> {
    return this.httpClient.get<CustomerGiftResponse>(
      buildApiUrl('B2C', 'MOBILE', 'home/gifts'),
      {
        headers: { isV3: true },
      }
    );
  }

  // Get banners - matching mobile implementation
  async getBanners(params: {
    bannerCollectionId: string;
  }): Promise<BannersResponse> {
    const url = buildApiUrl('B2C', 'MOBILE', 'banner-v3') + `?action=LIST&bannerCollectionId=${params.bannerCollectionId}`;
    return this.httpClient.get<BannersResponse>(url, {
      headers: { isV3: true },
    });
  }

  // Get quick actions
  async getQuickActions(): Promise<QuickActionsResponse> {
    return this.httpClient.get<QuickActionsResponse>(
      buildApiUrl('B2C', 'MOBILE', 'home/quick-actions'),
      {
        headers: { isV3: true },
      }
    );
  }

  // Get home feed (aggregated content)
  async getHomeFeed(
    params?: {
      offset?: number;
      limit?: number;
      types?: string[];
    }
  ): Promise<HomeFeedResponse> {
    return this.httpClient.get<HomeFeedResponse>(
      buildApiUrl('B2C', 'MOBILE', 'home/feed'),
      {
        params,
        headers: { isV3: true },
      }
    );
  }

  // Get app announcements
  async getAnnouncements(): Promise<AnnouncementsResponse> {
    return this.httpClient.get<AnnouncementsResponse>(
      buildApiUrl('B2C', 'MOBILE', 'home/announcements'),
      {
        headers: { isV3: true },
      }
    );
  }

  // Claim customer gift
  async claimGift(giftId: string): Promise<CustomerGiftResponse> {
    return this.httpClient.post<CustomerGiftResponse>(
      buildApiUrl('B2C', 'MOBILE', `home/gifts/${giftId}/claim`),
      {},
      {
        headers: { isV3: true },
      }
    );
  }

  // Track banner click
  async trackBannerClick(
    bannerId: string,
    metadata?: { [key: string]: any }
  ): Promise<BaseResponse<void>> {
    return this.httpClient.post<BaseResponse<void>>(
      buildApiUrl('B2C', 'MOBILE', `home/banners/${bannerId}/click`),
      { metadata },
      {
        headers: { isV3: true },
      }
    );
  }

  // Track news article view
  async trackNewsView(
    articleId: string,
    metadata?: { [key: string]: any }
  ): Promise<BaseResponse<void>> {
    return this.httpClient.post<BaseResponse<void>>(
      buildApiUrl('B2C', 'MOBILE', `home/news/${articleId}/view`),
      { metadata },
      {
        headers: { isV3: true },
      }
    );
  }

  // Dismiss announcement
  async dismissAnnouncement(announcementId: string): Promise<BaseResponse<void>> {
    return this.httpClient.post(
      buildApiUrl('B2C', 'MOBILE', `home/announcements/${announcementId}/dismiss`),
      {},
      {
        headers: { isV3: true },
      }
    );
  }

  // Get personalized recommendations
  async getPersonalizedContent(
    params?: {
      types?: string[];
      limit?: number;
    }
  ): Promise<BaseResponse<HomeFeedItem[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'home/personalized'),
      {
        params,
        headers: { isV3: true },
      }
    );
  }

  // Search home content
  async searchHomeContent(
    keyword: string,
    types?: string[]
  ): Promise<BaseResponse<HomeFeedItem[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'home/search'),
      {
        params: {
          keyword,
          types: types?.join(','),
        },
        headers: { isV3: true },
      }
    );
  }

  // Get active flash sale campaigns - matching mobile API_GLOBAL.GET_ACTIVE_CAMPAIGN
  async getActiveCampaign(collectionCode?: string): Promise<any[]> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'reward/activeCampaign'),
      {
        params: { collectionCode },
        headers: { isV3: true },
      }
    );
  }

  // Get flash sale rewards using list reward endpoint - matching mobile getListRewardByCollection
  async getFlashSaleRewards(params: {
    collectionCode: string;
    mobileType: string;
    offset?: number;
    limit?: number;
  }): Promise<any[]> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'reward'),
      {
        params,
        headers: { isV3: true },
      }
    );
  }

  // Get popular merchants - already implemented in merchant API but alias here for convenience
  async getPopularMerchants(params?: {
    limit?: number;
  }): Promise<any[]> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchant/merchants'),
      {
        params: {
          action: 'LOAD_MORE',
          offset: 0,
          limit: params?.limit || 6,
          isPopular: true,
        },
        headers: { isV3: true },
      }
    );
  }

  // Get suggested rewards - already implemented in rewards API but alias here for convenience  
  async getSuggestedRewards(params?: {
    limit?: number;
  }): Promise<any[]> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'v2/reward/suggestCollection'),
      {
        params,
        headers: { isV3: true },
      }
    );
  }

  // Get challenges for home - matching mobile new-challenge endpoint
  async getChallenges(params: {
    offset?: number;
    limit?: number;
    home?: boolean;
  } = {}): Promise<any[]> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'new-challenge'),
      {
        params: {
          offset: params.offset || 0,
          limit: params.limit || 8,
          home: params.home !== false, // default to true for home
        },
        headers: { isV3: true },
      }
    );
  }
}

export const homeAPI = new HomeAPI();