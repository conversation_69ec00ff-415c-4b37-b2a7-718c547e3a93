import { getHttpClient } from '../../http';
import { buildApiUrl } from '../../http/config';
import { BaseResponse } from '../../../stores/types';
import {
  CollectionType,
  MerchantType,
  OfflineStoreType,
  IParamsGetMerchantListByCollection,
  IRequestLinked,
  IRequestTier,
  GetStoreListParams,
  IResponseGetLinked,
  IResTierDetail,
  ITierReward,
  EarnMethodType,
  EarnMethodsResponse,
  EarnMethodsRequest,
} from './types';

class MerchantAPI {
  private get httpClient() {
    return getHttpClient();
  }

  // Get merchant collections/categories  
  async getCollections(): Promise<BaseResponse<CollectionType[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchant/merchant-collections'),
      {
        params: { action: 'LIST_ALL' },
        headers: { isV3: true },
      }
    );
  }

  // Get merchant collections with merchants (detailed version)
  async getCollection(): Promise<BaseResponse<CollectionType[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchant/merchant-collections'),
      {
        params: { action: 'LIST_ALL' },
        headers: { isV3: true },
      }
    );
  }

  // Get merchants by collection  
  async getMerchantListByCollection(
    params: IParamsGetMerchantListByCollection
  ): Promise<BaseResponse<MerchantType[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchant/merchants'),
      {
        params: {
          action: 'LOAD_MORE',
          merchantCollectionCode: params.collectionCode,
          offset: params.offset || 0,
          limit: params.limit || 8,
        },
        headers: { isV3: true },
      }
    );
  }

  // Get merchant by code
  async getMerchantByCode(code: string): Promise<BaseResponse<MerchantType>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchant/merchants'),
      {
        params: {
          action: 'DETAIL',
          code,
        },
        headers: { isV3: true },
      }
    );
  }

  // Get popular merchants
  async getPopularMerchantList(): Promise<BaseResponse<MerchantType[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchants'),
      {
        params: {
          action: 'LOAD_MORE',
          offset: 0,
          limit: 6,
          isPopular: true,
        },
        headers: { isV3: true },
      }
    );
  }

  // Get stores with distance
  async getStoreListWithDistance(
    params: GetStoreListParams
  ): Promise<BaseResponse<OfflineStoreType[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'stores/nearby'),
      {
        params: {
          action: 'NEAR_BY',
          ...params,
        },
        headers: { isV3: true },
      }
    );
  }

  // Get merchant stores
  async getMerchantStores(params: GetStoreListParams): Promise<BaseResponse<OfflineStoreType[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchant/stores'),
      {
        params,
        headers: { isV3: true },
      }
    );
  }

  // Get user loyalty consent status
  async getUserConsent(merchantCode: string): Promise<BaseResponse<IResponseGetLinked>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'loyalty/consent'),
      {
        params: { merchantCode },
        headers: { isV3: true },
      }
    );
  }

  // Create/update user loyalty consent
  async createUserConsent(body: IRequestLinked): Promise<BaseResponse<IResponseGetLinked>> {
    return this.httpClient.post(
      buildApiUrl('B2C', 'MOBILE', 'loyalty/consent'),
      body,
      {
        headers: { isV3: true },
      }
    );
  }

  // Get tier details
  async getTierDetail(params: IRequestTier): Promise<BaseResponse<IResTierDetail>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'loyalty/tier'),
      {
        params,
        headers: { isV3: true },
      }
    );
  }

  // Register for loyalty tier
  async registerTier(body: IRequestTier): Promise<BaseResponse<IResTierDetail>> {
    return this.httpClient.post(
      buildApiUrl('B2C', 'MOBILE', 'loyalty/tier/register'),
      body,
      {
        headers: { isV3: true },
      }
    );
  }

  // Get merchant tier rewards
  async getMerchantTierReward(
    merchantCode: string,
    tierId?: string
  ): Promise<BaseResponse<ITierReward[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'loyalty/tier/rewards'),
      {
        params: {
          merchantCode,
          tierId,
        },
        headers: { isV3: true },
      }
    );
  }

  // Search merchants
  async searchMerchants(
    keyword: string,
    params?: Partial<IParamsGetMerchantListByCollection>
  ): Promise<BaseResponse<MerchantType[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchants/search'),
      {
        params: {
          keyword,
          ...params,
        },
        headers: { isV3: true },
      }
    );
  }

  // Get nearby merchants
  async getNearbyMerchants(
    latitude: number,
    longitude: number,
    radius: number = 5000
  ): Promise<BaseResponse<MerchantType[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchants/nearby'),
      {
        params: {
          latitude,
          longitude,
          radius,
        },
        headers: { isV3: true },
      }
    );
  }

  // Get earn methods - matching mobile GET_EARN_METHOD
  async getEarnMethods(): Promise<BaseResponse<EarnMethodType[]>> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchant/earn-methods'),
      {
        params: {
          action: 'LIST_ALL'
        },
        headers: { isV3: true },
      }
    );
  }

  // Get earn methods for member code page
  async getEarnMethodsForMemberCode(params: EarnMethodsRequest): Promise<EarnMethodsResponse> {
    return this.httpClient.get(
      buildApiUrl('B2C', 'MOBILE', 'merchant/earn-methods'),
      {
        params,
        headers: { isV3: true },
      }
    );
  }

  // Toggle merchant favorite status
  async toggleFavorite(merchantCodes: string[], action: 'ADD' | 'REMOVE'): Promise<BaseResponse<any>> {
    const httpMethod = action === 'ADD' ? 'post' : 'put';
    return this.httpClient[httpMethod](
      buildApiUrl('B2C', 'MOBILE', 'merchant/favorite'),
      {
        merchantCodes,
        action,
      },
      {
        headers: { isV3: true },
      }
    );
  }

    // Get brand point balance
    async getBrandPointBalance(params?: IRequestBalance): Promise<BaseResponse<any>> {
      return this.httpClient.get(
        buildApiUrl('B2C', 'MOBILE', 'brand/point/balance'),
        {
          params,
          headers: { isV3: true },
        }
      );
    }
  
  
    // Add merchant to favorite
    async addMerchantToFavorite(merchantCodes: string[], action: 'ADD' | 'REMOVE'): Promise<BaseResponse<any>> {
      if (action === 'ADD') {
        return this.httpClient.post(
          buildApiUrl('B2C', 'MOBILE', 'merchant/favorite'),
          { merchantCodes, action },
          { headers: { isV3: true } }
        );
      } else {
        return this.httpClient.put(
          buildApiUrl('B2C', 'MOBILE', 'merchant/favorite'),
          { merchantCodes, action },
          { headers: { isV3: true } }
        );
      }
    }
  
    // Get favorite merchants
    async getFavoriteMerchants(action: 'LOAD_MORE', offset: number, limit: number): Promise<BaseResponse<any>> {
      return this.httpClient.get(
        buildApiUrl('B2C', 'MOBILE', 'v2/merchant/favorite'),
        {
          params: { action, offset, limit },
          headers: { isV3: true }
        }
      );
    }
  
}

export const merchantAPI = new MerchantAPI();