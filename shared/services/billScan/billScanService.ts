import { apiService } from '../http/httpService';

export interface BillConfigGlobal {
  _id: string;
  pointPerDay: number;
  pointPerWeek: number;
  pointPerMonth: number;
  limitSnapPerDay: number;
  instructionPopup?: string;
  instruction?: string;
  sampleBillUrl?: string;
  merchantContent?: string;
  merchantImageUrl?: string;
  maximumBillAmount?: number;
  minimumBillAmount?: number;
}

export interface EarnMethodDetail {
  earnMethodCode: string;
  earnMethodName: string;
  additionRate: string;
  description: string | null;
  image: string | null;
}

export interface EarnRate {
  percentage: number;
  earnValue: number | null;
  baseValue: number | null;
  description: string;
  image: string;
}

export interface SubMerchant {
  id: string;
  index: number;
  code: string;
  name: string;
  banner: string;
  note: string;
  earnRate: EarnRate;
  logo: string;
  earnModels: EarnMethodDetail[];
  description: string;
  image: string;
  status: string;
  backgroundColor: string;
  isFavorite: boolean | null;
  favoriteAt: string | null;
  homeMerchantDescription: string;
}

export interface Step {
  title: string;
  description: string;
  image: string;
  index: number;
}

export interface DirectButton {
  label: string;
  image: string;
  pageCode: string;
  sections: {
    activeIndex: string;
  };
  deepLink: string;
}

export interface EarnMethodInfo {
  id: string;
  code: string;
  name: string;
  description: string;
  image: string;
  index: number;
  status: string;
  steps: Step[];
  directButton: DirectButton;
  subMerchants: SubMerchant[];
  createdAt: string;
  updatedAt: string;
}

interface BillConfigResponse {
  data: BillConfigGlobal[];
  status: {
    success: boolean;
    code: number;
    message: string;
  };
}

interface EarnMethodInfoResponse {
  status: {
    message: string;
    code: number;
    success: boolean;
  };
  data: EarnMethodInfo;
}

export const billScanService = {
  async getBillConfigGlobal(): Promise<BillConfigGlobal | null> {
    try {
      const response = await apiService.get<BillConfigResponse>(
        '/b2c/v3/mobile/v2/ocr/config/global'
      );
      
      if (response?.data && response.data.length > 0) {
        return response.data[0];
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching bill config:', error);
      return null;
    }
  },

  async getEarnMethodInfo(): Promise<EarnMethodInfo | null> {
    try {
      const response = await apiService.get<EarnMethodInfoResponse>(
        '/b2c/v3/mobile/merchant/earn-methods',
        {
          params: {
            action: 'DETAIL',
            code: 'billScan'
          }
        }
      );
      
      if (response?.status?.success && response?.data) {
        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching earn method info:', error);
      return null;
    }
  }
};