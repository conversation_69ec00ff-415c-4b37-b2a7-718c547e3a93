import { getHttpClient } from '../http';
import axios from 'axios';

// OCR API endpoints - following mobile project pattern
//TODO check lại endpoint
const OCR_ENDPOINTS = {
  UPLOAD_IMAGE_TEST: 'https://ocr-engine-servicetest.taptap.com.vn/user/api/v2/receipt/image',
  UPLOAD_IMAGE_STG: 'https://ocr-engine-servicestag.taptap.com.vn/user/api/v2/receipt/image',
  UPLOAD_IMAGE_PROD: 'https://ocr-engine-service.taptap.com.vn/user/api/v2/receipt/image',
  BILL_OCR: '/mobile/v2/ocr',
  GET_LIST_BILL_OCR: '/mobile/v2/ocr/transactions',
  GET_OCR_INSTRUCTION: '/mobile/v1/ocr/configs/popup-instruction',
  GET_OCR_USER_STATUS: '/b2c/v3/mobile/v2/ocr/user/snap-per-day',
  GET_BILL_DETAIL: '/mobile/v2/ocr/transactions',
};

// Bill processing states from mobile project
export enum BillState {
  NEW = 'NEW',
  NEW_USER = 'NEW_USER',
  AUTO_PENDING = 'AUTO_PENDING',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  MISSING_DATA = 'MISSING_DATA',
  INVALID_DATA = 'INVALID_DATA',
  MANUAL_APPROVE = 'MANUAL_APPROVE',
  COMPLETED = 'COMPLETED',
  FRAUDED = 'FRAUDED',
  BLOCKED = 'BLOCKED',
  EXCEED_SNAP_TIME = 'EXCEED_SNAP_TIME',
  UNSUPPORTED = 'UNSUPPORTED',
  DUPLICATED = 'DUPLICATED',
  EXPIRED = 'EXPIRED',
  EXCEED_POINT = 'EXCEED_POINT',
  INVALID_TNC = 'INVALID_TNC',
  MISSING_DATA_REJECTED = 'MISSING_DATA_REJECTED',
  INVALID_DATA_REJECTED = 'INVALID_DATA_REJECTED',
}

export enum RewardCode {
  VUI_POINT = 'VUI_POINT',
  BRAND_CURRENCY = 'BRAND_CURRENCY',
  VOUCHER = 'VOUCHER',
}

// OCR DTOs based on mobile project
export interface UploadImageRequest {
  imageFile: File;
  mobile: string;
}

export interface UploadImageResponse {
  name: string;
  mime: string;
  url: string;
  size: number;
  ocrProceed: number;
}

export interface BillHistory {
  _id: string;
  amount?: string;
  ocrImage: string;
  state: BillState;
  isView: boolean;
  number?: string;
  result: string;
  brandName?: string;
  brandLogo?: string;
  brandCode?: string;
  billingTime?: string;
  snapDate?: string;
  rewardResult?: {
    rewardCode: RewardCode;
    meta: {
      vuiPoint?: number;
      brandCurrencyPoint?: number;
      brandCurrencyCode?: string;
      brandCurrencyName?: string;
      brandCurrencyLogo?: string;
      voucherCodeIdList?: string;
      voucherCode?: string;
    };
  }[];
}

export interface BillHistoryRequest {
  page?: number;
  pageSize?: number;
  state?: BillState;
}

export interface BillHistoryResponse {
  bills: BillHistory[];
  meta: {
    totalPages: number;
    pageSize: number;
    totalRows: number;
    currentPage: number;
  };
}

export interface UserStatusResponse {
  snapPerDay: number;
}

export interface OCRInstructionResponse {
  title: string;
  content: string;
  imageUrl?: string;
}

// OCR Service Class
export class OCRService {
  private static getUploadEndpoint(): string {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const isTest = process.env.REACT_APP_IS_TEST_MODE === 'true';
    
    if (isDevelopment || isTest) {
      return OCR_ENDPOINTS.UPLOAD_IMAGE_STG;
    }
    return OCR_ENDPOINTS.UPLOAD_IMAGE_PROD;
  }

  /**
   * Upload bill image for OCR processing
   * Using axios directly for external URL with multipart/form-data
   */
  static async uploadBillImage(request: UploadImageRequest): Promise<UploadImageResponse> {
    const fileName = `OCR-${new Date().toISOString().replace(/[:.]/g, '-')}.jpg`;
    const url = this.getUploadEndpoint();

    const formData = new FormData();
    formData.append('file', request.imageFile, fileName);

    try {
      // Import authStore dynamically to get token
      const { useAuthStore } = await import('../../stores/authStore');
      const authToken = useAuthStore.getState().token;
      
      // Get device ID from localStorage (same as httpClient)
      const deviceId = localStorage.getItem('taptap_device_id') || 
                      'web_' + Math.random().toString(36).substring(2, 11) + Date.now().toString(36);
      
      // Build headers matching httpClient pattern
      const headers: any = {
        'cap_mobile': request.mobile,
        'Accept': 'application/json',
        'Accept-Language': 'vi-VN',
        'Platform': 'web',
        'DeviceId': deviceId,
      };
      
      // Add authorization header if token exists
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      // Use axios for better error handling and consistency
      const response = await axios.post(url, formData, {
        headers,
        timeout: 60000, // 60 seconds timeout
      });

      // Handle response
      if (response.data?.status?.success) {
        return response.data.data;
      } else if (response.data?.status?.message) {
        throw new Error(response.data.status.message);
      } else {
        throw new Error('Upload failed');
      }
    } catch (error: any) {
      console.error('OCR upload error:', error);
      
      // Handle axios errors
      if (error.response) {
        // Server responded with error status
        if (error.response.status === 401) {
          throw new Error('Unauthorized: Please login to use OCR feature');
        }
        const errorMessage = error.response.data?.status?.message || 
                           error.response.data?.message ||
                           `Upload failed: ${error.response.status}`;
        throw new Error(errorMessage);
      } else if (error.request) {
        // Request was made but no response
        throw new Error('Network error: Unable to connect to OCR service');
      } else {
        // Something else happened
        throw new Error(error.message || 'Upload failed');
      }
    }
  }

  /**
   * Get user OCR daily snap limit
   */
  static async getUserStatus(): Promise<UserStatusResponse> {
    try {
      const response = await getHttpClient().get(OCR_ENDPOINTS.GET_OCR_USER_STATUS);
      
      console.log('[getUserStatus]',response)
      // Handle new API response format
      if (response?.status?.success) {
        return {
          snapPerDay: response.data.snapPerDay
        };
      }
      
      // Fallback to 0 if no data
      return {
        snapPerDay: 0
      };
    } catch (error) {
      console.error('Failed to get user status:', error);
      // Return 0 on error
      return {
        snapPerDay: 0
      };
    }
  }


  /**
   * Get OCR instruction content
   */
  static async getOCRInstruction(): Promise<OCRInstructionResponse> {
    const response = await getHttpClient().get(OCR_ENDPOINTS.GET_OCR_INSTRUCTION);
    return response.data;
  }

  /**
   * Get bill history with pagination
   */
  static async getBillHistory(request: BillHistoryRequest = {}): Promise<BillHistoryResponse> {
    const params = {
      page: request.page || 1,
      pageSize: request.pageSize || 20,
      ...(request.state && { state: request.state }),
    };

    const response = await getHttpClient().get(OCR_ENDPOINTS.GET_LIST_BILL_OCR, { params });
    return {
      bills: response.data.bills || [],
      meta: response.data.meta || {
        totalPages: 1,
        pageSize: 20,
        totalRows: 0,
        currentPage: 1,
      },
    };
  }

  /**
   * Get bill details by ID
   */
  static async getBillDetail(billId: string): Promise<BillHistory> {
    const response = await getHttpClient().get(`${OCR_ENDPOINTS.GET_BILL_DETAIL}/${billId}`);
    return response.data;
  }

  /**
   * Helper method to convert data URL to File object
   */
  static dataURLtoFile(dataURL: string, filename: string = 'receipt.jpg'): File {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    
    return new File([u8arr], filename, { type: mime });
  }

  /**
   * Helper method to get user-friendly state descriptions
   */
  static getStateDescription(state: BillState): string {
    const stateDescriptions: Record<BillState, string> = {
      [BillState.NEW]: 'Mới',
      [BillState.NEW_USER]: 'Người dùng mới',
      [BillState.AUTO_PENDING]: 'Đang xử lý tự động',
      [BillState.SYSTEM_ERROR]: 'Lỗi hệ thống',
      [BillState.MISSING_DATA]: 'Thiếu dữ liệu',
      [BillState.INVALID_DATA]: 'Dữ liệu không hợp lệ',
      [BillState.MANUAL_APPROVE]: 'Chờ duyệt thủ công',
      [BillState.COMPLETED]: 'Hoàn thành',
      [BillState.FRAUDED]: 'Phát hiện gian lận',
      [BillState.BLOCKED]: 'Bị chặn',
      [BillState.EXCEED_SNAP_TIME]: 'Vượt quá thời gian chụp',
      [BillState.UNSUPPORTED]: 'Không được hỗ trợ',
      [BillState.DUPLICATED]: 'Trùng lặp',
      [BillState.EXPIRED]: 'Hết hạn',
      [BillState.EXCEED_POINT]: 'Vượt quá điểm',
      [BillState.INVALID_TNC]: 'Điều khoản không hợp lệ',
      [BillState.MISSING_DATA_REJECTED]: 'Từ chối do thiếu dữ liệu',
      [BillState.INVALID_DATA_REJECTED]: 'Từ chối do dữ liệu không hợp lệ',
    };

    return stateDescriptions[state] || 'Không xác định';
  }

  /**
   * Helper method to check if state is successful
   */
  static isSuccessState(state: BillState): boolean {
    return state === BillState.COMPLETED;
  }

  /**
   * Helper method to check if state is pending
   */
  static isPendingState(state: BillState): boolean {
    return [
      BillState.NEW,
      BillState.NEW_USER,
      BillState.AUTO_PENDING,
      BillState.MANUAL_APPROVE,
    ].includes(state);
  }

  /**
   * Helper method to check if state is failed/rejected
   */
  static isFailedState(state: BillState): boolean {
    return [
      BillState.SYSTEM_ERROR,
      BillState.MISSING_DATA,
      BillState.INVALID_DATA,
      BillState.FRAUDED,
      BillState.BLOCKED,
      BillState.EXCEED_SNAP_TIME,
      BillState.UNSUPPORTED,
      BillState.DUPLICATED,
      BillState.EXPIRED,
      BillState.EXCEED_POINT,
      BillState.INVALID_TNC,
      BillState.MISSING_DATA_REJECTED,
      BillState.INVALID_DATA_REJECTED,
    ].includes(state);
  }
}

export default OCRService;