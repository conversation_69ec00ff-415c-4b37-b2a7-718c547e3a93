import { create } from 'zustand';
import { membershipApi, ITierClassDetail, ITierReward, IStateTier, membershipHelpers } from '../services/api/membership';

interface MembershipState {
  tierDetail: ITierClassDetail | null;
  tierRewards: ITierReward[];
  loading: boolean;
  error: string | null;
  stateTier: IStateTier;
  
  fetchTierDetail: (merchantCode: string) => Promise<void>;
  registerTier: (merchantCode: string) => Promise<boolean>;
  fetchTierRewards: (merchantCode: string) => Promise<void>;
  reset: () => void;
}

export const useMembershipStore = create<MembershipState>((set) => ({
  tierDetail: null,
  tierRewards: [],
  loading: false,
  error: null,
  stateTier: 'noRank',

  fetchTierDetail: async (merchantCode: string) => {
    try {
      set({ loading: true, error: null });
      
      // Use the new webview API
      const tierResponse = await membershipApi.getTierDetailWebview(merchantCode);

      if (tierResponse.status?.success && tierResponse.data) {
        const tierClassDetail = tierResponse.data as ITierClassDetail;
        const stateTier = membershipHelpers.getStateTier(tierClassDetail);

        set({ 
          tierDetail: tierClassDetail, 
          stateTier,
          loading: false 
        });
      } else {
        set({ error: 'Failed to fetch tier details', loading: false });
      }
    } catch (error) {
      console.error('Error fetching tier detail:', error);
      set({ error: 'An error occurred while fetching tier details', loading: false });
    }
  },

  registerTier: async (merchantCode: string) => {
    try {
      set({ loading: true, error: null });
      
      const response = await membershipApi.registerTier(merchantCode);
      
      if (response?.status?.success && response?.data) {
        const tierData = response.data;
        let currencyData = null;

        if (tierData.current?.length > 0) {
          const currencyCode = tierData.current[0].code;
          const balanceResponse = await membershipApi.getBalanceDetail(merchantCode, currencyCode);
          
          if (balanceResponse.data.status.success && balanceResponse.data.data) {
            currencyData = balanceResponse.data.data;
          }
        }

        const tierClassDetail: ITierClassDetail = {
          ...tierData,
          currencyLogo: currencyData?.logo || '',
          currencyCode: currencyData?.currencyCode || '',
          currencyName: currencyData?.name || ''
        };

        const stateTier = membershipHelpers.getStateTier(tierClassDetail);
        
        set({ 
          tierDetail: tierClassDetail, 
          stateTier,
          loading: false 
        });
        
        return true;
      } else {
        set({ error: 'Failed to register tier', loading: false });
        return false;
      }
    } catch (error) {
      console.error('Error registering tier:', error);
      set({ error: 'An error occurred while registering tier', loading: false });
      return false;
    }
  },

  fetchTierRewards: async (merchantCode: string) => {
    try {
      set({ loading: true, error: null });
      
      const response = await membershipApi.getTierRewardWebview(merchantCode);
      
      if (response?.status?.success && response?.data) {
        set({ tierRewards: response.data || [], loading: false });
      } else {
        set({ tierRewards: [], loading: false });
      }
    } catch (error) {
      console.error('Error fetching tier rewards:', error);
      set({ tierRewards: [], loading: false });
    }
  },

  reset: () => {
    set({
      tierDetail: null,
      tierRewards: [],
      loading: false,
      error: null,
      stateTier: 'noRank'
    });
  }
}));