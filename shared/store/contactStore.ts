import { create } from 'zustand';
import { contactApi, ContactUsConfig } from '../services/api/contact';

interface ContactState {
  contactInfo: ContactUsConfig | null;
  loading: boolean;
  error: string | null;
  
  fetchContactInfo: () => Promise<void>;
  reset: () => void;
}

export const useContactStore = create<ContactState>((set) => ({
  contactInfo: null,
  loading: false,
  error: null,

  fetchContactInfo: async () => {
    try {
      set({ loading: true, error: null });
      
      const response = await contactApi.getContactUsConfig();
      
      if (response?.status?.success && response?.data) {
        set({ contactInfo: response.data, loading: false });
      } else {
        set({ error: 'Failed to fetch contact info', loading: false });
      }
    } catch (error) {
      console.error('Error fetching contact info:', error);
      set({ error: 'An error occurred while fetching contact info', loading: false });
    }
  },

  reset: () => {
    set({
      contactInfo: null,
      loading: false,
      error: null
    });
  }
}));