/**
 * App Path Helper - <PERSON><PERSON><PERSON><PERSON> lý tất cả các path route của hệ thống
 * Hỗ trợ cả path tĩnh và path động với parameters
 */

// Type definitions
type PathParams = {
  id?: string | number;
  category?: string;
  gameId?: string | number;
  merchantId?: string | number;
  billId?: string | number;
  newsId?: string | number;
  missionId?: string | number;
  rewardId?: string | number;
  dealId?: string | number;
  flashSaleId?: string | number;
  inboxId?: string | number;
  myRewardId?: string | number;
  merchantCode?: string;
};

/**
 * App Path Helper Object
 * Sử dụng template literals trực tiếp để dễ maintain và thêm mới
 */
export const appPath = {
  // Base paths
  home: () => "/",
  login: () => "/login",
  profile: () => "/profile",
  demo: () => "/demo",
  debugSitemap: () => "/debug/sitemap",

  // Game paths
  games: () => "/games",
  gamesHistory: () => "/games/history",
  gamesMissionsHistory: () => "/games/missions/history",
  gameDetail: (gameId: string | number = ":id") => `/games/${gameId}`,
  gameWheel: (gameId: string | number = ":id") => `/games/wheel/${gameId}`,

  // Exchange and rewards paths
  exchange: () => "/exchange",
  exchangeAllRewards: () => "/exchange/all-rewards",
  exchangeSuperDeals: () => "/exchange/super-deals",
  exchangeCategory: (category: string = ":category") =>
    `/exchange/category/${category}`,
  exchangeCollection: (category: string = ":category") =>
    `/exchange/collection/${category}`,
  giftCode: () => "/gift-code",
  memberCode: () => "/member-code",
  rewardDetail: (rewardId: string | number = ":id") => `/reward/${rewardId}`,
  rewardsDetail: (rewardId: string | number = ":id") => `/rewards/${rewardId}`,
  allRewards: () => "/exchange/all-rewards",
  superDeals: () => "/exchange/super-deals",

  // Search paths
  search: () => "/search",
  searchResults: () => "/search/results",

  // QR and payment paths
  qrPayment: () => "/qr-payment",
  scanQr: () => "/scan-qr",
  scanQrInstructions: () => "/scan-qr/instructions",

  // Bill scan paths
  billScan: () => "/bill-scan",
  billScanBills: () => "/bill-scan/bills",
  billScanCamera: () => "/bill-scan/camera",
  billScanCaptured: () => "/bill-scan/captured",
  billScanInfo: () => "/bill-scan/info",
  billScanDetail: (billId: string | number = ":id") => `/bill-scan/${billId}`,

  // Merchant and deals paths
  merchants: () => "/merchants",
  merchantDetail: (merchantId: string | number = ":code") =>
    `/merchants/${merchantId}`,
  merchant: (merchantId: string | number = ":id") => `/merchant/${merchantId}`,
  deals: () => "/deals",
  dealDetail: (dealId: string | number = ":id") => `/deals/${dealId}`,
  flashSale: (flashSaleId: string | number = ":id") =>
    `/flash-sale/${flashSaleId}`,

  // Mission paths
  missions: () => "/missions",
  missionDetail: (missionId: string | number = ":id") =>
    `/games/missions/${missionId}`,

  // News paths
  news: () => "/news",
  newsDetail: (newsId: string | number = ":id") => `/news/${newsId}`,

  // User communication paths
  messages: () => "/messages",
  inbox: () => "/inbox",
  inboxDetail: (inboxId: string | number = ":id") => `/inbox/${inboxId}`,
  myRewards: () => "/my-rewards",
  myRewardDetail: (myRewardId: string | number = ":id") =>
    `/my-rewards/${myRewardId}`,
  membership: () => "/membership",
  membershipTierList: () => "/membership/tier-list",
  membershipDetails: (merchantCode: string = ":merchantCode") =>
    `/membership/details/${merchantCode}`,
  membershipOverview: (merchantCode: string = ":merchantCode") =>
    `/membership/${merchantCode}`,
  membershipBenefits: (merchantCode: string = ":merchantCode") =>
    `/membership/${merchantCode}/benefits`,
  contact: () => "/contact",
  favorites: () => "/favorites",

  // Utility methods
  /**
   * Kiểm tra xem path có phải là dynamic path (có parameters) hay không
   */
  isDynamicPath: (path: string): boolean => {
    return path.includes(":");
  },

  /**
   * Lấy tên parameter từ dynamic path
   */
  getPathParams: (path: string): string[] => {
    const matches = path.match(/:\w+/g);
    return matches ? matches.map((match) => match.slice(1)) : [];
  },

  /**
   * Tạo path với parameters tùy chỉnh
   * Ví dụ: createPath('/games/:id', { id: '123' }) => '/games/123'
   */
  createPath: (
    pathTemplate: string,
    params: Record<string, string | number>
  ): string => {
    return pathTemplate.replace(/:(\w+)/g, (match, paramName) => {
      const value = params[paramName];
      return value ? String(value) : match;
    });
  },
} as const;

// Export types
export type AppPath = typeof appPath;
export type PathParamsType = PathParams;
