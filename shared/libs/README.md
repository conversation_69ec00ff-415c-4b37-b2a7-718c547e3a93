# App Path Helper

Helper quản lý tất cả các path route củ<PERSON> hệ thống, sử dụng template literals trực tiếp để dễ maintain và thêm mới.

## Tính năng

- ✅ Quản lý tập trung tất cả các path route
- ✅ Hỗ trợ path tĩnh (không có parameters)
- ✅ Hỗ trợ path động (có parameters như `:id`, `:category`)
- ✅ Type-safe với TypeScript
- ✅ Sử dụng template literals trực tiếp - dễ maintain
- ✅ Utility methods để kiểm tra và xử lý paths
- ✅ Chỉ cần thêm 1 dòng để tạo path mới

## Cách sử dụng

### Import

```typescript
import { appPath } from "@shared/libs";
// hoặc
import { appPath } from "@shared";
```

### Path tĩnh

```typescript
// Không có parameters
appPath.home(); // "/"
appPath.games(); // "/games"
appPath.exchange(); // "/exchange"
appPath.search(); // "/search"
appPath.profile(); // "/profile"
```

### Path động với parameters

```typescript
// Có parameters - sử dụng template literals trực tiếp
appPath.gameDetail("123"); // "/games/123"
appPath.gameDetail(); // "/games/:id" (default parameter)

appPath.gameWheel("456"); // "/games/wheel/456"
appPath.gameWheel(); // "/games/wheel/:id"

appPath.rewardDetail("789"); // "/reward/789"
appPath.merchantDetail("merchant-123"); // "/merchants/merchant-123"
appPath.exchangeCategory("electronics"); // "/exchange/category/electronics"
appPath.billScanDetail("bill-456"); // "/bill-scan/bill-456"
```

### Sử dụng trong React Router

#### Navigation

```typescript
import { useNavigate } from "react-router-dom";

const navigate = useNavigate();

// Navigate to static path
navigate(appPath.games());

// Navigate to dynamic path
navigate(appPath.gameDetail(gameId));
```

#### Link Component

```typescript
import { Link } from "react-router-dom";

// Static link
<Link to={appPath.games()}>Games</Link>

// Dynamic link
<Link to={appPath.gameDetail(game.id)}>Game Detail</Link>
```

#### Redirect

```typescript
import { Navigate } from "react-router-dom";

// Redirect to static path
<Navigate to={appPath.home()} replace />

// Redirect to dynamic path
<Navigate to={appPath.gameDetail(gameId)} replace />
```

### Utility Methods

```typescript
// Kiểm tra path có phải là dynamic path không
appPath.isDynamicPath("/games/:id"); // true
appPath.isDynamicPath("/games"); // false

// Lấy tên parameters từ dynamic path
appPath.getPathParams("/games/:id"); // ["id"]

// Tạo path với parameters tùy chỉnh
appPath.createPath("/games/:id", { id: "123" }); // "/games/123"
appPath.createPath("/exchange/category/:category", { category: "electronics" }); // "/exchange/category/electronics"
```

## Cấu trúc Paths

### Base Paths

- `home()` - Trang chủ
- `login()` - Đăng nhập
- `profile()` - Hồ sơ người dùng
- `demo()` - Trang demo
- `debugSitemap()` - Debug sitemap

### Game Paths

- `games()` - Danh sách game
- `gamesHistory()` - Lịch sử game
- `gamesMissionsHistory()` - Lịch sử nhiệm vụ
- `gameDetail(gameId = ":id")` - Chi tiết game
- `gameWheel(gameId = ":id")` - Game vòng quay

### Exchange & Rewards Paths

- `exchange()` - Trang đổi thưởng
- `exchangeAllRewards()` - Tất cả phần thưởng
- `exchangeSuperDeals()` - Ưu đãi đặc biệt
- `exchangeCategory(category = ":category")` - Danh mục cụ thể
- `exchangeCollection(category = ":category")` - Collection theo danh mục
- `giftCode()` - Mã quà tặng
- `memberCode()` - Mã thành viên
- `rewardDetail(rewardId = ":id")` - Chi tiết phần thưởng
- `rewardsDetail(rewardId = ":id")` - Chi tiết phần thưởng (alias)

### Search Paths

- `search()` - Trang tìm kiếm
- `searchResults()` - Kết quả tìm kiếm

### QR & Payment Paths

- `qrPayment()` - Thanh toán QR
- `scanQr()` - Quét mã QR
- `scanQrInstructions()` - Hướng dẫn quét QR

### Bill Scan Paths

- `billScan()` - Quét hóa đơn
- `billScanBills()` - Danh sách hóa đơn
- `billScanCamera()` - Camera quét
- `billScanCaptured()` - Hóa đơn đã quét
- `billScanInfo()` - Thông tin quét
- `billScanDetail(billId = ":id")` - Chi tiết hóa đơn

### Merchant & Deals Paths

- `merchantDetail(merchantId = ":id")` - Chi tiết merchant
- `deals()` - Danh sách ưu đãi
- `dealDetail(dealId = ":id")` - Chi tiết ưu đãi
- `flashSale(flashSaleId = ":id")` - Flash sale

### Mission Paths

- `missions()` - Danh sách nhiệm vụ
- `missionDetail(missionId = ":id")` - Chi tiết nhiệm vụ

### News Paths

- `news()` - Danh sách tin tức
- `newsDetail(newsId = ":id")` - Chi tiết tin tức

### User Communication Paths

- `messages()` - Tin nhắn
- `inbox()` - Hộp thư
- `inboxDetail(inboxId = ":id")` - Chi tiết hộp thư
- `myRewards()` - Phần thưởng của tôi
- `myRewardDetail(myRewardId = ":id")` - Chi tiết phần thưởng
- `membership()` - Hạng thành viên
- `contact()` - Liên hệ

## Lợi ích của cách viết mới

1. **Dễ maintain**: Chỉ cần thêm 1 dòng để tạo path mới
2. **Template literals trực tiếp**: Sử dụng ES6 template literals, dễ đọc và hiểu
3. **Default parameters**: Tự động có giá trị mặc định `:id` khi không truyền parameter
4. **Type safety**: TypeScript support đầy đủ với type inference
5. **Performance**: Không cần function helper phức tạp
6. **Flexibility**: Có thể truyền parameter hoặc để mặc định

## Thêm path mới

Để thêm path mới, chỉ cần thêm 1 dòng:

```typescript
// Thêm vào appPath object
newFeature: () => "/new-feature",
newFeatureDetail: (featureId: string | number = ":id") => `/new-feature/${featureId}`,
```

## Best Practices

1. **Luôn sử dụng appPath helper** thay vì hardcode string paths
2. **Sử dụng default parameters** để có template path khi không truyền giá trị
3. **Sử dụng template literals** để dễ đọc và maintain
4. **Sử dụng utility methods** khi cần xử lý phức tạp
5. **Export constants** nếu cần truy cập trực tiếp

## Migration Guide

Nếu bạn đang sử dụng hardcode paths, hãy thay thế:

```typescript
// ❌ Không nên
navigate("/games/123");
<Link to="/games">Games</Link>;

// ✅ Nên sử dụng
navigate(appPath.gameDetail("123"));
<Link to={appPath.games()}>Games</Link>;
```

## So sánh với cách viết cũ

### Cách viết cũ (phức tạp):

```typescript
// Phải khai báo constants
const GAME_PATHS = {
  GAME_DETAIL: "/games/:id",
} as const;

// Phải có helper function
const replacePathParams = (path: string, params?: PathParams): string => { ... };

// Phải gọi helper function
gameDetail: (params?: { id: string | number }) =>
  replacePathParams(GAME_PATHS.GAME_DETAIL, params),
```

### Cách viết mới (đơn giản):

```typescript
// Chỉ cần 1 dòng với template literal
gameDetail: (gameId: string | number = ":id") => `/games/${gameId}`,
```

**Kết quả**: Giảm từ ~10 dòng xuống còn 1 dòng, dễ maintain hơn nhiều!
