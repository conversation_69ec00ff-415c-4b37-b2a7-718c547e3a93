import React from 'react';

export interface CardRewardThumbnailNormalShapeProps {
  className?: string;
  width?: string | number;
  height?: string | number;
}

const CardRewardThumbnailNormalShape: React.FC<CardRewardThumbnailNormalShapeProps> = ({
  className,
  width = 252,
  height = 86,
}) => {
  // Calculate scaled coordinates based on width
  const w = typeof width === 'number' ? width : 252;
  const rightEdge = w - 9; // Right edge coordinate (252 - 9 = 243 in original)
  const rightEdge1 = w - 1; // Right edge - 1 (252 - 1 = 251 in original)
  const lineX = 57.5; // Keep the dashed line at fixed position like original design
  
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width={width} 
      height={height} 
      viewBox={`0 0 ${width} 86`} 
      fill="none"
      className={className}
    >
      <mask id="path-1-outside-1_2006_16" maskUnits="userSpaceOnUse" x="0" y="0" width={w} height="86" fill="black">
        <rect fill="white" width={w} height="86"/>
        <path d={`M50 1C50 4.86599 53.134 8 57 8C60.866 8 64 4.86599 64 1H${rightEdge}C${rightEdge + 4.418} 1 ${rightEdge1} 4.58172 ${rightEdge1} 9V77C${rightEdge1} 81.4183 ${rightEdge + 4.418} 85 ${rightEdge} 85H64C64 81.134 60.866 78 57 78C53.134 78 50 81.134 50 85H9C4.58172 85 1 81.4183 1 77V9C1 4.58172 4.58172 1 9 1H50Z`}/>
      </mask>
      <path d={`M50 1C50 4.86599 53.134 8 57 8C60.866 8 64 4.86599 64 1H${rightEdge}C${rightEdge + 4.418} 1 ${rightEdge1} 4.58172 ${rightEdge1} 9V77C${rightEdge1} 81.4183 ${rightEdge + 4.418} 85 ${rightEdge} 85H64C64 81.134 60.866 78 57 78C53.134 78 50 81.134 50 85H9C4.58172 85 1 81.4183 1 77V9C1 4.58172 4.58172 1 9 1H50Z`} fill="white"/>
      <path d={`M50 1H51V0H50V1ZM64 1V0H63V1H64ZM${rightEdge} 1V0V1ZM${rightEdge1} 77H${rightEdge1 + 1}H${rightEdge1}ZM${rightEdge} 85V86V85ZM64 85H63V86H64V85ZM50 85V86H51V85H50ZM9 85V86V85ZM1 77H0H1ZM9 1V0V1ZM50 1H49C49 5.41828 52.5817 9 57 9V8V7C53.6863 7 51 4.31371 51 1H50ZM57 8V9C61.4183 9 65 5.41828 65 1H64H63C63 4.31371 60.3137 7 57 7V8ZM64 1V2H${rightEdge}V1V0H64V1ZM${rightEdge} 1V2C${rightEdge + 3.866} 2 ${rightEdge1 - 1} 5.13401 ${rightEdge1 - 1} 9H${rightEdge1}H${rightEdge1 + 1}C${rightEdge1 + 1} 4.02944 ${rightEdge + 4.971} 3.27826e-06 ${rightEdge} 0V1ZM${rightEdge1} 9H${rightEdge1 - 1}V77H${rightEdge1}H${rightEdge1 + 1}V9H${rightEdge1}ZM${rightEdge1} 77H${rightEdge1 - 1}C${rightEdge1 - 1} 80.866 ${rightEdge + 3.866} 84 ${rightEdge} 84V85V86C${rightEdge + 4.971} 86 ${rightEdge1 + 1} 81.9706 ${rightEdge1 + 1} 77H${rightEdge1}ZM${rightEdge} 85V84H64V85V86H${rightEdge}V85ZM64 85H65C65 80.5817 61.4183 77 57 77V78V79C60.3137 79 63 81.6863 63 85H64ZM57 78V77C52.5817 77 49 80.5817 49 85H50H51C51 81.6863 53.6863 79 57 79V78ZM50 85V84H9V85V86H50V85ZM9 85V84C5.13401 84 2 80.866 2 77H1H0C1.66893e-06 81.9706 4.02944 86 9 86V85ZM1 77H2V9H1H0V77H1ZM1 9H2C2 5.13401 5.13401 2 9 2V1V0C4.02944 1.78814e-07 0 4.02944 0 9H1ZM9 1V2H50V1V0H9V1Z`} fill="#F65D79" fillOpacity="0.5" mask="url(#path-1-outside-1_2006_16)"/>
      <line x1={lineX} y1="15" x2={lineX} y2="71" stroke="#F65D79" strokeOpacity="0.5" strokeDasharray="3 4"/>
    </svg>
  );
};

export default CardRewardThumbnailNormalShape;