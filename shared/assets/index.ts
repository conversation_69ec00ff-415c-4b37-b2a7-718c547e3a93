// Asset exports for shared components
// This helps with proper asset loading across different build systems

// For web app, assets should be imported from the app's asset directory
// For mobile app, assets might be bundled differently

export const getLoaderGif = () => {
  // This will be resolved differently by each platform's build system
  try {
    // Web apps using Vite can resolve this path
    return new URL("./gif/loader.gif", import.meta.url).href;
  } catch {
    // Fallback path for other build systems
    return "/assets/gif/loader.gif";
  }
};

export const getTaptapLoadingGif = () => {
  try {
    return new URL("./gif/taptap_loading_2.gif", import.meta.url).href;
  } catch {
    return "/assets/gif/taptap_loading_2.gif";
  }
};

export { default as MinusIcon } from "./icons/icon-32px-solid-minus.svg";
export { default as PlusIcon } from "./icons/icon-32px-solid-plus.svg";
export { iconsAssets } from "./icons";

export const getDefaultInboxImage = () => {
  try {
    return new URL("./images/defaultInbox.png", import.meta.url).href;
  } catch {
    return "/assets/images/defaultInbox.png";
  }
};

export const getMascotInboxImage = () => {
  try {
    return new URL("./images/mascot/inbox.png", import.meta.url).href;
  } catch {
    return "/assets/images/mascot/inbox.png";
  }
};
