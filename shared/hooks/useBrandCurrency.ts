import { useState, useEffect } from 'react';
import { currencyApi } from '../services/api/currency';
import { 
  BrandCurrencyType, 
  ActionBalance, 
  BrandCurrencyListResponse, 
  BrandCurrencyInfoResponse 
} from '../services/api/types';

export const useBrandCurrencyList = (): BrandCurrencyListResponse => {
  const [data, setData] = useState<BrandCurrencyType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isError, setError] = useState<boolean>(false);

  const getInfoBrandCurrency = async () => {
    try {
      setLoading(true);
      const result = await currencyApi.getBalance({
        action: ActionBalance.GET_BALANCE,
      });

      console.log('getBalance',result)
      
      if (result?.status?.success && result?.status.code === 200) {
        if (Array.isArray(result.data)) {
          setData(result.data);
        }
      }
    } catch (error) {
      setError(true);
      console.error('Error fetching brand currency list:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = () => {
    getInfoBrandCurrency();
  };

  useEffect(() => {
    getInfoBrandCurrency();
  }, []);

  return {
    brandCurrencyList: data,
    loading,
    isError,
    onRefresh,
  };
};

export const useBrandCurrencyInfo = ({ 
  currencyCode 
}: { 
  currencyCode: string 
}): BrandCurrencyInfoResponse => {
  const [data, setData] = useState<BrandCurrencyType>();

  const getInfoBrandCurrency = async () => {
    try {
      const result = await currencyApi.getBalance({
        currencyCode,
        action: ActionBalance.GET_BALANCE_DETAIL,
      });
      
      if (result?.status?.success && result?.status.code === 200 && !Array.isArray(result.data)) {
        setData(result.data);
      }
    } catch (error) {
      console.error('Error fetching brand currency info:', error);
    }
  };

  const onRefresh = () => {
    getInfoBrandCurrency();
  };

  useEffect(() => {
    getInfoBrandCurrency();
  }, [currencyCode]);

  return {
    brandInfo: data,
    onRefresh,
  };
};