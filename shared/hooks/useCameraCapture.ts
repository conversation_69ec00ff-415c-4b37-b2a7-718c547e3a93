import { useState, useEffect, useCallback } from 'react';
import { useAuthStore } from '../stores/authStore';
import { useOCRStore } from '../stores/ocrStore';
import { OCRService, CameraError } from '../services';
import { useToast } from './useToast';
import {
  getCameraErrorMessage,
  addCapturedReceipt,
  transformUserStatusForPreview,
  getRemainingSnaps,
  PROCESSING_MESSAGES,
  PROCESSING_SUCCESS_DELAY,
  VALIDATION_MESSAGES,
  createUploadErrorMessage,
} from '../utils/cameraUtils';

export type CameraPageState = 'camera' | 'preview' | 'success';

export interface CapturedReceipt {
  id: number;
  photoData: string;
  timestamp: string;
  status: string;
  uploadResult: any;
  ocrImage: string;
}

export interface UseCameraCaptureOptions {
  onNavigate?: (path: string) => void;
  onNavigateBack?: (steps?: number) => void;
}

export interface UseCameraCaptureReturn {
  // State
  state: CameraPageState;
  capturedPhoto: string;
  isProcessing: boolean;
  processingStatus: string;
  userStatus: any;
  loadingUserStatus: boolean;
  
  // Actions
  handleCapture: (photoData: string) => void;
  handleToggleFlash: (flashOn: boolean) => void;
  handleCameraError: (error: CameraError) => void;
  handleShowInfo: () => void;
  handleShowCapturedReceipts: () => void;
  handleBack: () => void;
  handleComplete: () => Promise<void>;
  handleGoHome: () => void;
  
  // Computed values
  remainingSnaps: number;
  previewUserStatus: any;
  
  // Toast
  toast: any;
  showErrorToast: (message: string) => void;
  hideToast: () => void;
}

export const useCameraCapture = ({
  onNavigate,
  onNavigateBack,
}: UseCameraCaptureOptions = {}): UseCameraCaptureReturn => {
  // Stores
  const { profile } = useAuthStore();
  const { userStatus, loadingUserStatus, fetchUserStatus } = useOCRStore();
  const { toast, showErrorToast, hideToast } = useToast();
  
  // State
  const [state, setState] = useState<CameraPageState>('camera');
  const [capturedPhoto, setCapturedPhoto] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<string>('');

  // Load user status on component mount
  useEffect(() => {
    fetchUserStatus();
  }, [fetchUserStatus]);

  const handleCapture = useCallback(async (photoData: string) => {
    setCapturedPhoto(photoData);
    setState('preview');
  }, []);

  const handleToggleFlash = useCallback((flashOn: boolean) => {
    console.log('Flash toggled:', flashOn);
  }, []);
  
  const handleCameraError = useCallback((error: CameraError) => {
    console.error('Camera error:', error);
    const userMessage = getCameraErrorMessage(error);
    showErrorToast(userMessage);
  }, [showErrorToast]);

  const handleShowInfo = useCallback(() => {
    onNavigate?.('/bill-scan/info');
  }, [onNavigate]);

  const handleShowCapturedReceipts = useCallback(() => {
    onNavigate?.('/bill-scan');
  }, [onNavigate]);

  const handleBack = useCallback(() => {
    switch (state) {
      case 'success':
        setState('camera');
        setCapturedPhoto('');
        break;
      case 'preview':
        setState('camera');
        setCapturedPhoto('');
        break;
      default:
        onNavigateBack?.(1);
    }
  }, [state, onNavigateBack]);

  const handleComplete = useCallback(async () => {
    if (!capturedPhoto) {
      showErrorToast(VALIDATION_MESSAGES.NO_PHOTO);
      return;
    }

    if (!profile?.mobile) {
      showErrorToast(VALIDATION_MESSAGES.NO_MOBILE);
      return;
    }

    setIsProcessing(true);
    setProcessingStatus(PROCESSING_MESSAGES.PREPARING);

    try {
      // Convert photo to File object
      const imageFile = OCRService.dataURLtoFile(capturedPhoto);
      
      setProcessingStatus(PROCESSING_MESSAGES.UPLOADING);

      // Upload image for OCR processing
      const uploadResult = await OCRService.uploadBillImage({
        imageFile,
        mobile: profile.mobile,
      });

      console.log('OCR upload successful:', uploadResult);
      
      // Store in localStorage for local tracking using utility function
      addCapturedReceipt(capturedPhoto, uploadResult);

      setProcessingStatus(PROCESSING_MESSAGES.SUCCESS);

      // Show success state after brief delay
      setTimeout(() => {
        setIsProcessing(false);
        setProcessingStatus('');
        setState('success');
      }, PROCESSING_SUCCESS_DELAY);

    } catch (error) {
      setIsProcessing(false);
      setProcessingStatus('');
      console.error('OCR upload failed:', error);
      
      const errorMessage = createUploadErrorMessage(error);
      showErrorToast(errorMessage);
    }
  }, [capturedPhoto, profile?.mobile, showErrorToast]);

  const handleGoHome = useCallback(() => {
    onNavigate?.('/');
  }, [onNavigate]);

  // Calculate remaining snaps using utility function
  const remainingSnaps = getRemainingSnaps(userStatus);

  // Create simplified user status for preview component using utility function
  const previewUserStatus = transformUserStatusForPreview(userStatus);

  return {
    // State
    state,
    capturedPhoto,
    isProcessing,
    processingStatus,
    userStatus,
    loadingUserStatus,
    
    // Actions
    handleCapture,
    handleToggleFlash,
    handleCameraError,
    handleShowInfo,
    handleShowCapturedReceipts,
    handleBack,
    handleComplete,
    handleGoHome,
    
    // Computed values
    remainingSnaps,
    previewUserStatus,
    
    // Toast
    toast,
    showErrorToast,
    hideToast,
  };
};