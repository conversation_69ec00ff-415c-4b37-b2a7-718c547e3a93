import { useEffect } from 'react';
import { useInboxStore } from '../stores/inboxStore';

export const useNotificationBell = () => {
  const { unseenCount, fetchUnseenCount } = useInboxStore();

  useEffect(() => {
    // Fetch unseen count only on mount
    fetchUnseenCount();
  }, [fetchUnseenCount]);

  return {
    hasNotification: unseenCount > 0,
    unseenCount,
    refreshCount: fetchUnseenCount
  };
};