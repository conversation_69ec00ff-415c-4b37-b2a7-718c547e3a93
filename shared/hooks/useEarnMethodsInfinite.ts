import { useState, useEffect, useCallback } from 'react';
import { merchantAPI } from '../services/api/merchant';
import { EarnMethodsResponse, SubMerchant } from '../services/api/merchant/types';

interface UseEarnMethodsInfiniteResult {
  merchants: SubMerchant[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
}

const LIMIT = 10;

export const useEarnMethodsInfinite = (): UseEarnMethodsInfiniteResult => {
  const [merchants, setMerchants] = useState<SubMerchant[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);

  const fetchEarnMethods = async (currentOffset: number, append: boolean = false) => {
    try {
      if (append) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }
      setError(null);
      
      const response: EarnMethodsResponse = await merchantAPI.getEarnMethodsForMemberCode({
        action: 'LOAD_MORE',
        offset: currentOffset,
        limit: LIMIT
      });

      if (response.status?.success && response.data) {
        // Extract all subMerchants from all earn methods
        const newSubMerchants: SubMerchant[] = [];
        response.data.forEach(earnMethod => {
          if (earnMethod.subMerchants && earnMethod.subMerchants.length > 0) {
            newSubMerchants.push(...earnMethod.subMerchants);
          }
        });
        
        if (append) {
          setMerchants(prev => [...prev, ...newSubMerchants]);
        } else {
          setMerchants(newSubMerchants);
        }

        // Check if there are more items to load
        setHasMore(newSubMerchants.length === LIMIT);
        setOffset(currentOffset + LIMIT);
      } else {
        const errorMsg = response.status?.message || 'Failed to fetch earn methods';
        throw new Error(errorMsg);
      }
    } catch (err) {
      console.error('Error fetching earn methods:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch earn methods');
      
      if (!append) {
        // Keep merchants empty on initial load error
        setMerchants([]);
      }
    } finally {
      if (append) {
        setLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  const loadMore = useCallback(async () => {
    if (loadingMore || !hasMore) return;
    await fetchEarnMethods(offset, true);
  }, [offset, loadingMore, hasMore]);

  const refresh = useCallback(async () => {
    setOffset(0);
    setHasMore(true);
    await fetchEarnMethods(0, false);
  }, []);

  useEffect(() => {
    fetchEarnMethods(0, false);
  }, []);

  return {
    merchants,
    loading,
    loadingMore,
    error,
    hasMore,
    loadMore,
    refresh,
  };
};