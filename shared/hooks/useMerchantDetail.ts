import { useState, useEffect, useCallback, useMemo } from "react";
import {
  merchantService,
  rewardService,
  currencyApi,
  membershipHelpers,
} from "../services";
import { useMembershipStore } from "../stores";
import type {
  MerchantType,
  OfflineStoreType,
  BrandCurrencyType,
  HorizontalScrollTabItem,
  VoucherItem,
} from "../types";
import type { RewardDetailResponse } from "../services/api/rewards";
import { ActionBalance } from "../services/api/types";
import {
  buildMerchantTabsConfiguration,
  convertRewardsToVouchers,
  transformStoreLocations,
  transformMerchantImages,
  getDefaultStoreSearchCoordinates,
  getOnlineMethod,
} from "../utils/merchantUtils";

export interface UseMerchantDetailOptions {
  code?: string;
  onNavigate?: (path: string) => void;
}

export interface UseMerchantDetailReturn {
  // State
  merchantDetail: MerchantType | null;
  stores: OfflineStoreType[];
  rewards: RewardDetailResponse | null;
  tabsConfig: HorizontalScrollTabItem[];
  activeTab: string;
  favorite: boolean;
  loading: boolean;
  error: string | null;

  // Actions
  setActiveTab: (tab: string) => void;
  handleToggleFavorite: () => Promise<void>;
  handleLoyaltyCardAction: () => Promise<boolean | void>;
  fetchMerchantData: () => Promise<void>;

  // Computed values
  convertedVouchers: VoucherItem[];
  storeLocations: any[];
  galleryImages: any[];
  loyaltyCardData: any;

  // Tier state from membership store
  tierDetail: any;
  tierLoading: boolean;
  stateTier: string;
}

export const useMerchantDetail = ({
  code,
  onNavigate,
}: UseMerchantDetailOptions = {}): UseMerchantDetailReturn => {
  // Local state
  const [activeTab, setActiveTab] = useState("offers");
  const [favorite, setFavorite] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [merchantDetail, setMerchantDetail] = useState<MerchantType | null>(
    null
  );
  const [stores, setStores] = useState<OfflineStoreType[]>([]);
  const [rewards, setRewards] = useState<RewardDetailResponse | null>(null);
  const [tabsConfig, setTabsConfig] = useState<HorizontalScrollTabItem[]>([]);

  // Membership store
  const {
    tierDetail,
    loading: tierLoading,
    stateTier,
    fetchTierDetail,
    registerTier,
  } = useMembershipStore();

  const id = merchantDetail?.id || "";

  // Business logic functions using extracted utilities

  const fetchStores = useCallback(async (merchant: MerchantType) => {
    if (!merchant.isEnableOfflineStore) return;

    try {
      const { latitude, longitude, radius } =
        getDefaultStoreSearchCoordinates();

      const storesResponse = await merchantService.getStoreListWithDistance({
        merchantCode: merchant.code,
        latitude,
        longitude,
        radius,
        offset: 0,
        limit: 10,
      });

      if (storesResponse.status.success && storesResponse.data) {
        setStores(storesResponse.data);
      }
    } catch (error) {
      console.error("Failed to fetch stores:", error);
    }
  }, []);

  const fetchRewards = useCallback(async (merchant: MerchantType) => {
    if (!merchant.numberOfReward || merchant.numberOfReward === 0) return;

    try {
      const rewardsResponse = await rewardService.getListRewardV2({
        brandCode: merchant.code,
        mobileType: "running",
        offset: 0,
        limit: 8,
      });

      if (rewardsResponse.status.success && rewardsResponse.data) {
        setRewards(rewardsResponse.data);
      }
    } catch (error) {
      console.error("Failed to fetch rewards:", error);
    }
  }, []);

  const fetchMerchantData = useCallback(async () => {
    if (!code) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch merchant details
      const merchantResponse = await merchantService.getMerchantByCode(code);
      if (!merchantResponse.status.success) {
        throw new Error(merchantResponse.status.message);
      }

      const merchant = merchantResponse.data;

      const balanceData = await currencyApi.getBalance({
        merchantCode: code,
        action: ActionBalance.GET_BALANCE_BY_MERCHANT,
      });

      // Defensive check for balanceData
      if (merchant) {
        if (balanceData?.data) {
          merchant.balanceData = balanceData?.data as BrandCurrencyType[];
        } else {
          merchant.balanceData = [];
        }
      }

      merchant.onlineContent = getOnlineMethod(merchant);

      setMerchantDetail(merchant);
      setFavorite(merchant.isFavorite || false);

      // Build tabs based on merchant features
      const tabs = buildMerchantTabsConfiguration(merchant);
      setActiveTab(tabs[0].id);
      setTabsConfig(tabs);

      // Fetch additional data in parallel
      await Promise.all([fetchStores(merchant), fetchRewards(merchant)]);
    } catch (err) {
      console.error("Failed to fetch merchant data:", err);
      setError(
        err instanceof Error ? err.message : "Có lỗi xảy ra khi tải dữ liệu"
      );
    } finally {
      setLoading(false);
    }
  }, [code, fetchStores, fetchRewards]);

  const handleToggleFavorite = useCallback(async () => {
    if (!code) return;

    try {
      const newFavoriteStatus = !favorite;
      setFavorite(newFavoriteStatus);

      const action = newFavoriteStatus ? "ADD" : "REMOVE";
      const response = await merchantService.toggleFavorite([code], action);

      if (!response.status.success) {
        // Revert on failure
        setFavorite(!newFavoriteStatus);
        console.error("Failed to toggle favorite:", response.status.message);
      }
    } catch (error) {
      // Revert on error
      setFavorite(!favorite);
      console.error("Error toggling favorite:", error);
    }
  }, [code, favorite]);

  const handleLoyaltyCardAction = useCallback(async () => {
    if (!code) return;

    if (stateTier === "noRank") {
      // Register for membership - the modal will be handled inside TierNoRank
      const success = await registerTier(code);
      if (success) {
        // Refresh tier details after successful registration
        await fetchTierDetail(code);
      }
      // Return the success status for the TierNoRank component to handle
      return success;
    } else {
      // Navigate to membership details or voucher page
      onNavigate?.(`/membership/${id}/details`);
    }
  }, [code, stateTier, registerTier, fetchTierDetail, onNavigate, id]);

  // Fetch all merchant data when code changes
  useEffect(() => {
    if (code) {
      fetchMerchantData();
      fetchTierDetail(code);
    }
  }, [code, fetchMerchantData, fetchTierDetail]);

  // Computed values using extracted utilities
  const convertedVouchers: VoucherItem[] = useMemo(() => {
    return convertRewardsToVouchers(rewards, merchantDetail);
  }, [rewards, merchantDetail]);

  const storeLocations = useMemo(() => {
    return transformStoreLocations(stores);
  }, [stores]);

  const galleryImages = useMemo(() => {
    return transformMerchantImages(merchantDetail);
  }, [merchantDetail]);

  // Prepare loyalty card data based on tier details
  const getLoyaltyCardStatus = useCallback(() => {
    if (!tierDetail) return "no-join" as const;
    return stateTier === "noRank"
      ? ("no-join" as const)
      : stateTier === "maxTier"
      ? ("max-rank" as const)
      : stateTier === "hasRankTier" || stateTier === "hasRankAndVoucher"
      ? ("with-rank" as const)
      : ("without-rank" as const);
  }, [tierDetail, stateTier]);

  const loyaltyCardData = useMemo(() => {
    const merchantName = merchantDetail?.name || "Merchant";

    if (!tierDetail) {
      return {
        status: "no-join" as const,
        brandName: merchantName,
        theme: "merchant-detail" as const,
        onActionClick: handleLoyaltyCardAction,
      };
    }

    const currentValue = tierDetail.current?.[0]?.value || 0;
    const listTier = membershipHelpers.getListProgressTier(
      tierDetail.tierLevel || 0,
      tierDetail.merchantTiers || []
    );
    const totalEarnNeeded = membershipHelpers.totalEarnCurrency(
      listTier.map((tier) => ({
        value: tier.conditions[0]?.value || 0,
      })),
      currentValue
    );
    const progress = membershipHelpers.calculateProgress(
      currentValue,
      listTier.map((tier) => ({
        value: tier.conditions[0]?.value || 0,
        tierName: tier.tierName,
      }))
    );

    return {
      status: getLoyaltyCardStatus(),
      theme: "merchant-detail" as const,
      brandName: tierDetail.merchantName || merchantName,
      userTitle: tierDetail.tierName,
      pointsToNextLevel: totalEarnNeeded,
      currentProgress: progress,
      maxProgress: 100,
      badgeIconSrc: tierDetail.currencyLogo,
      onActionClick: handleLoyaltyCardAction,
      onVoucherClick: () => onNavigate?.(`/membership/${id}/benefits`),
    };
  }, [
    tierDetail,
    merchantDetail,
    getLoyaltyCardStatus,
    handleLoyaltyCardAction,
    onNavigate,
    id,
  ]);

  return {
    // State
    merchantDetail,
    stores,
    rewards,
    tabsConfig,
    activeTab,
    favorite,
    loading,
    error,

    // Actions
    setActiveTab,
    handleToggleFavorite,
    handleLoyaltyCardAction,
    fetchMerchantData,

    // Computed values
    convertedVouchers,
    storeLocations,
    galleryImages,
    loyaltyCardData,

    // Tier state from membership store
    tierDetail,
    tierLoading,
    stateTier,
  };
};
