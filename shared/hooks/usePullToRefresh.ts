import React, { useState, useEffect, useCallback } from 'react';

export interface UsePullToRefreshOptions {
  onRefresh: (() => void) | (() => Promise<void>);
  maximumPullLength?: number;
  refreshThreshold?: number;
  isDisabled?: boolean;
}

export interface UsePullToRefreshReturn {
  isRefreshing: boolean;
  pullPosition: number;
}

const usePullToRefresh = ({
  onRefresh,
  maximumPullLength = 240,
  refreshThreshold = 180,
  isDisabled = false,
}: UsePullToRefreshOptions): UsePullToRefreshReturn => {
  console.log('🔄 usePullToRefresh hook initialized with options:', { maximumPullLength, refreshThreshold, isDisabled });
  
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullPosition, setPullPosition] = useState(0);
  const [startY, setStartY] = useState(0);
  const [currentY, setCurrentY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  // Validation
  if (maximumPullLength < refreshThreshold) {
    console.warn(
      'usePullToRefresh: maximumPullLength should be greater than refreshThreshold'
    );
  }

  const handleTouchStart = useCallback(
    (event: TouchEvent) => {
      console.log('🔄 usePullToRefresh - TouchStart:', { 
        isDisabled, 
        isRefreshing, 
        scrollY: window.scrollY,
        touchY: event.touches[0].clientY 
      });
      
      if (isDisabled || isRefreshing) {
        console.log('🔄 TouchStart blocked:', { isDisabled, isRefreshing });
        return;
      }

      // Only allow pull-to-refresh when at the top of the page
      if (window.scrollY > 0) {
        console.log('🔄 TouchStart blocked - not at top:', { scrollY: window.scrollY });
        return;
      }

      const touch = event.touches[0];
      setStartY(touch.clientY);
      setCurrentY(touch.clientY);
      setIsDragging(true);
      console.log('🔄 TouchStart initiated:', { startY: touch.clientY });
    },
    [isDisabled, isRefreshing]
  );

  const handleTouchMove = useCallback(
    (event: TouchEvent) => {
      if (isDisabled || isRefreshing || !isDragging) {
        if (isDragging) {
          console.log('🔄 TouchMove blocked:', { isDisabled, isRefreshing, isDragging });
        }
        return;
      }

      const touch = event.touches[0];
      const deltaY = touch.clientY - startY;

      console.log('🔄 TouchMove:', { 
        deltaY, 
        scrollY: window.scrollY, 
        touchY: touch.clientY, 
        startY 
      });

      if (deltaY > 0 && window.scrollY === 0) {
        // Prevent default scroll behavior when pulling down
        event.preventDefault();
        
        // Calculate pull position with resistance
        const pullDistance = Math.min(deltaY, maximumPullLength);
        const resistance = 1 - pullDistance / (maximumPullLength * 2);
        const position = pullDistance * Math.max(0.3, resistance);
        
        console.log('🔄 Pull calculation:', { 
          pullDistance, 
          resistance, 
          position,
          maximumPullLength 
        });
        
        setPullPosition(position);
        setCurrentY(touch.clientY);
      }
    },
    [isDisabled, isRefreshing, isDragging, startY, maximumPullLength]
  );

  const handleTouchEnd = useCallback(
    async (event: TouchEvent) => {
      console.log('🔄 TouchEnd:', { 
        isDisabled, 
        isRefreshing, 
        isDragging,
        pullPosition, 
        refreshThreshold,
        shouldRefresh: pullPosition >= refreshThreshold 
      });
      
      if (isDisabled || isRefreshing || !isDragging) {
        console.log('🔄 TouchEnd blocked');
        return;
      }

      setIsDragging(false);

      if (pullPosition >= refreshThreshold) {
        console.log('🔄 Starting refresh process...');
        setIsRefreshing(true);
        
        try {
          // Add a small delay for better UX
          await new Promise(resolve => setTimeout(resolve, 500));
          console.log('🔄 Calling onRefresh...');
          await onRefresh();
          console.log('🔄 onRefresh completed');
        } catch (error) {
          console.error('🔄 Pull to refresh error:', error);
        } finally {
          setIsRefreshing(false);
          console.log('🔄 Refresh process finished');
        }
      }

      // Reset position
      setPullPosition(0);
      setStartY(0);
      setCurrentY(0);
      console.log('🔄 Touch state reset');
    },
    [isDisabled, isRefreshing, isDragging, pullPosition, refreshThreshold, onRefresh]
  );

  useEffect(() => {
    if (isDisabled) {
      console.log('🔄 usePullToRefresh hook disabled');
      return;
    }

    console.log('🔄 usePullToRefresh hook setting up event listeners...');
    
    document.addEventListener('touchstart', handleTouchStart, { passive: false });
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd, { passive: false });

    console.log('🔄 Touch event listeners added to document');

    return () => {
      console.log('🔄 Cleaning up touch event listeners');
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, isDisabled]);

  return {
    isRefreshing,
    pullPosition,
  };
};

export default usePullToRefresh;