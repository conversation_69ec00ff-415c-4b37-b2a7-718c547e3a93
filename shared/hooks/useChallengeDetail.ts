import { useState, useEffect, useCallback } from "react";
import {
  entertainmentService,
  ChallengeV3,
} from "../services/api/entertainmentService";

export interface UseChallengeDetailReturn {
  challenge: ChallengeV3 | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useChallengeDetail = (
  challengeId: string
): UseChallengeDetailReturn => {
  const [challenge, setChallenge] = useState<ChallengeV3 | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchChallengeDetail = useCallback(async () => {
    if (!challengeId) {
      setError("Challenge ID is required");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const challengeDetail = await entertainmentService.getChallengeV3Detail(
        challengeId
      );

      if (challengeDetail) {
        setChallenge(challengeDetail);
      } else {
        setError("Challenge not found");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch challenge detail";
      setError(errorMessage);
      console.error("Error fetching challenge detail:", err);
    } finally {
      setLoading(false);
    }
  }, [challengeId]);

  useEffect(() => {
    fetchChallengeDetail();
  }, [fetchChallengeDetail]);

  return {
    challenge,
    loading,
    error,
    refetch: fetchChallengeDetail,
  };
};
