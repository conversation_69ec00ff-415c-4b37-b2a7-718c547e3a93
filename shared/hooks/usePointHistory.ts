import { useState, useEffect } from 'react';
import { currencyApi } from '../services/api/currency';
import { 
  PointHistoryItem, 
  ActionHistory, 
  RequestHistory, 
  PointHistoryResponse 
} from '../services/api/types';

const OFFSET_DEFAULT = 0;
const LIMIT = 10;

export const usePointVUIHistory = (
  action: keyof typeof ActionHistory = 'GET_HISTORY_BY_MONTH',
  currencyCode?: string,
): PointHistoryResponse => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [data, setData] = useState<PointHistoryItem[]>([]);
  const [endOfLine, setEndOfLine] = useState<boolean>(false);
  const [offset, setOffSet] = useState<number>(OFFSET_DEFAULT);
  const [dateSelect, setDateSelect] = useState(new Date());
  const [isError, setError] = useState<boolean>(false);

  const formatDateRange = (date: Date) => {
    const dateFrom = new Date(date.getFullYear(), date.getMonth(), 1);
    const dateTo = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59);
    
    return {
      dateFrom: dateFrom.toISOString().replace('T', ' ').substring(0, 19),
      dateTo: dateTo.toISOString().replace('T', ' ').substring(0, 19),
    };
  };

  const handleLoopHistory = async (
    params: RequestHistory,
    currentData: PointHistoryItem[],
  ): Promise<PointHistoryItem[]> => {
    let dataTotal: PointHistoryItem[] = currentData;

    const result = await currencyApi.getPointsHistory(params);

    if (result?.status?.success && result?.status.code === 200) {
      if (Array.isArray(result.data)) {
        const dataPublish = result.data.filter(
          (e: PointHistoryItem) => Object.keys(e).length > 0,
        );
        
        if (params.offset === OFFSET_DEFAULT) {
          dataTotal = dataPublish;
        } else {
          dataTotal = [...currentData, ...dataPublish];
        }
        
        if (result.data.length < LIMIT) {
          setEndOfLine(true);
        } else if (dataTotal.length < LIMIT && result.data.length > 0) {
          const newOffset = (params?.offset || 0) + LIMIT;
          params.offset = newOffset;
          setOffSet(newOffset);
          dataTotal = await handleLoopHistory(params, dataTotal);
        }
      }
    }
    return dataTotal;
  };

  const requestPointVui = async (
    currentOffSet: number,
    dateRequest: Date,
    isRefresh = false,
  ) => {
    try {
      setIsLoading(true);
      const dateRange = formatDateRange(dateRequest);
      const params: RequestHistory = {
        createdFrom: dateRange?.dateFrom || '',
        createdTo: dateRange?.dateTo || '',
        offset: currentOffSet,
        limit: LIMIT,
        action,
        currencyCode,
      };
      
      const dataTotal = await handleLoopHistory(params, data);
      setData(dataTotal);
    } catch (error) {
      console.error('Error fetching point history:', error);
      setError(true);
    } finally {
      if (isRefresh) setIsRefreshing(false);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    requestPointVui(OFFSET_DEFAULT, dateSelect);
  }, []);

  useEffect(() => {
    if (isRefreshing) {
      requestPointVui(OFFSET_DEFAULT, dateSelect, isRefreshing);
    }
  }, [isRefreshing]);

  const onRefresh = () => {
    setOffSet(OFFSET_DEFAULT);
    setEndOfLine(false);
    setIsRefreshing(true);
  };

  const onLoadMore = () => {
    if (!isLoading && !endOfLine) {
      requestPointVui(offset + LIMIT, dateSelect);
      setOffSet(offset + LIMIT);
    }
  };

  const onChangeDate = (dateChange: Date) => {
    setData([]);
    setOffSet(OFFSET_DEFAULT);
    setEndOfLine(false);
    setDateSelect(dateChange);
    requestPointVui(OFFSET_DEFAULT, dateChange);
  };

  return {
    data,
    isLoading,
    isRefreshing,
    isError,
    onRefresh,
    onChangeDate,
    onLoadMore,
    dateSelect,
  };
};

export const useBrandCurrencyHistory = (
  action: keyof typeof ActionHistory = 'GET_HISTORY_BY_MONTH',
  currencyCode?: string,
): Omit<PointHistoryResponse, 'data'> & { listHistoryBrand: PointHistoryItem[] } => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [data, setData] = useState<PointHistoryItem[]>([]);
  const [endOfLine, setEndOfLine] = useState<boolean>(false);
  const [offset, setOffSet] = useState<number>(OFFSET_DEFAULT);
  const [dateSelect, setDateSelect] = useState(new Date());
  const [isError, setError] = useState<boolean>(false);

  const formatDateRange = (date: Date) => {
    const dateFrom = new Date(date.getFullYear(), date.getMonth(), 1);
    const dateTo = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59);
    
    return {
      dateFrom: dateFrom.toISOString().replace('T', ' ').substring(0, 19),
      dateTo: dateTo.toISOString().replace('T', ' ').substring(0, 19),
    };
  };

  const handleLoopHistory = async (
    params: RequestHistory,
    currentData: PointHistoryItem[],
  ): Promise<PointHistoryItem[]> => {
    let dataTotal: PointHistoryItem[] = currentData;

    const result = await currencyApi.getHistory(params);

    if (result?.status?.success && result?.status.code === 200) {
      if (Array.isArray(result.data)) {
        const dataPublish = result.data.filter(
          (e: PointHistoryItem) => Object.keys(e).length > 0,
        );
        
        if (params.offset === OFFSET_DEFAULT) {
          dataTotal = dataPublish;
        } else {
          dataTotal = [...currentData, ...dataPublish];
        }
        
        if (result.data.length < LIMIT) {
          setEndOfLine(true);
        } else if (dataTotal.length < LIMIT && result.data.length > 0) {
          const newOffset = (params?.offset || 0) + LIMIT;
          params.offset = newOffset;
          setOffSet(newOffset);
          dataTotal = await handleLoopHistory(params, dataTotal);
        }
      }
    }
    return dataTotal;
  };

  const getPointBrandCurrency = async (
    currentOffSet: number,
    dateRequest: Date,
    isRefresh = false,
  ) => {
    try {
      setIsLoading(true);
      const dateRange = formatDateRange(dateRequest);
      const params: RequestHistory = {
        createdFrom: dateRange?.dateFrom || '',
        createdTo: dateRange?.dateTo || '',
        offset: currentOffSet,
        limit: LIMIT,
        currencyCode,
        action,
      };
      
      const dataTotal = await handleLoopHistory(params, data);
      setData(dataTotal);
    } catch (error) {
      console.error('Error fetching brand currency history:', error);
      setError(true);
    } finally {
      if (isRefresh) setIsRefreshing(false);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getPointBrandCurrency(OFFSET_DEFAULT, dateSelect);
  }, []);

  useEffect(() => {
    if (isRefreshing) {
      getPointBrandCurrency(OFFSET_DEFAULT, dateSelect, isRefreshing);
    }
  }, [isRefreshing]);

  const onRefresh = () => {
    setOffSet(OFFSET_DEFAULT);
    setEndOfLine(false);
    setIsRefreshing(true);
  };

  const onLoadMore = () => {
    if (!isLoading && !endOfLine) {
      getPointBrandCurrency(offset + LIMIT, dateSelect);
      setOffSet(offset + LIMIT);
    }
  };

  const onChangeDate = (dateChange: Date) => {
    setData([]);
    setOffSet(OFFSET_DEFAULT);
    setEndOfLine(false);
    setDateSelect(dateChange);
    getPointBrandCurrency(OFFSET_DEFAULT, dateChange);
  };

  return {
    listHistoryBrand: data,
    isLoading,
    isRefreshing,
    isError,
    onRefresh,
    onChangeDate,
    onLoadMore,
    dateSelect,
  };
};