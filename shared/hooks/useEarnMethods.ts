import { useState, useEffect } from 'react';
import { merchantAPI } from '../services/api/merchant';
import { EarnMethodsResponse, SubMerchant } from '../services/api/merchant/types';

interface UseEarnMethodsResult {
  merchants: SubMerchant[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useEarnMethods = (): UseEarnMethodsResult => {
  const [merchants, setMerchants] = useState<SubMerchant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchEarnMethods = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response: EarnMethodsResponse = await merchantAPI.getEarnMethodsForMemberCode({
        action: 'LIST_ALL'
      });

      if (response.status?.success && response.data) {
        // Extract all subMerchants from all earn methods
        const allSubMerchants: SubMerchant[] = [];
        response.data.forEach(earnMethod => {
          if (earnMethod.subMerchants && earnMethod.subMerchants.length > 0) {
            allSubMerchants.push(...earnMethod.subMerchants);
          }
        });
        
        setMerchants(allSubMerchants);
      } else {
        const errorMsg = response.status?.message || 'Failed to fetch earn methods';
        throw new Error(errorMsg);
      }
    } catch (err) {
      console.error('Error fetching earn methods:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch earn methods');
      // Keep merchants empty on error
      setMerchants([]);
    } finally {
      setLoading(false);
    }
  };

  const refetch = async () => {
    await fetchEarnMethods();
  };

  useEffect(() => {
    fetchEarnMethods();
  }, []);

  return {
    merchants,
    loading,
    error,
    refetch,
  };
};