import { useState, useEffect, useCallback } from "react";
import {
  entertainmentService,
  BannerType,
} from "../services/api/entertainmentService";

export interface UseBannersOptions {
  displayScreen?: string;
  autoFetch?: boolean;
}

export interface UseBannersReturn {
  banners: BannerType[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useBanners = (
  options: UseBannersOptions = {}
): UseBannersReturn => {
  const { displayScreen = "CHALLENGE", autoFetch = true } = options;

  const [banners, setBanners] = useState<BannerType[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchBanners = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const fetchedBanners = await entertainmentService.getBanners(
        displayScreen
      );

      setBanners(fetchedBanners);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch banners";
      setError(errorMessage);
      console.error("Error fetching banners:", err);
    } finally {
      setLoading(false);
    }
  }, [displayScreen]);

  useEffect(() => {
    if (autoFetch) {
      fetchBanners();
    }
  }, [fetchBanners, autoFetch]);

  return {
    banners,
    loading,
    error,
    refetch: fetchBanners,
  };
};
