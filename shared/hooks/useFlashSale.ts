import { useState, useEffect, useCallback } from 'react';
import { flashSaleService } from '../services';
import type { RewardItemType } from '../types';
import type { FlashSaleCampaign } from '../types/flashSale';

export interface UseFlashSaleOptions {
  collectionCode?: string;
  collectionName?: string;
  hasFlashSale?: boolean;
  status?: string;
  onNavigate?: (path: string) => void;
  onNavigateBack?: (steps?: number) => void;
}

export interface UseFlashSaleReturn {
  // State
  flashSaleCampaign: FlashSaleCampaign | null;
  rewardList: RewardItemType[];
  loading: boolean;
  refreshing: boolean;
  error: string | null;
  currentDateTime: string;
  offset: number;
  endOfList: boolean;
  
  // Actions
  fetchRewardListByCode: (offset: number, isRefresh?: boolean) => Promise<void>;
  handleCountdownCallback: () => void;
  handleRewardClick: (reward: RewardItemType) => void;
  handleBack: () => void;
  
  // Computed values
  isFlashSaleActive: boolean;
  isCountdownActive: boolean;
  hasTeasing: boolean;
  backgroundColor: string;
  
  // Constants
  NUMBER_ITEM_PER_BATCH: number;
}

export const useFlashSale = ({
  collectionCode = 'flash-sale',
  collectionName = 'Flash Sale',
  hasFlashSale = false,
  status = '',
  onNavigate,
  onNavigateBack,
}: UseFlashSaleOptions = {}): UseFlashSaleReturn => {
  // State management
  const [flashSaleCampaign, setFlashSaleCampaign] = useState<FlashSaleCampaign | null>(null);
  const [rewardList, setRewardList] = useState<RewardItemType[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentDateTime, setCurrentDateTime] = useState<string>(new Date().toISOString());
  const [offset, setOffset] = useState(0);
  const [endOfList, setEndOfList] = useState(false);
  
  const NUMBER_ITEM_PER_BATCH = 10;

  // Update current time every second for countdown
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentDateTime(new Date().toISOString());
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, []);

  // Fetch rewards function
  const fetchRewardListByCode = useCallback(async (
    _offset: number,
    isRefresh: boolean = false
  ) => {
    if (_offset === 0 && !isRefresh) setLoading(true);
    
    let mobileType: 'running' | 'active' | 'coming' | 'flashSale' = 'running';
    
    try {
      // Get active flash sale campaign if needed
      if (hasFlashSale || collectionCode === 'flash-sale') {
        const campaignResponse = await flashSaleService.getActiveCampaign(collectionCode);
        console.log('[useFlashSale] Campaign Response:', campaignResponse);
        console.log('[useFlashSale] Campaign Data:', campaignResponse?.data);
        if (campaignResponse?.data?.campaignCountdownTime) {
          mobileType = 'flashSale';
          setFlashSaleCampaign(campaignResponse.data);
          console.log('[useFlashSale] Set Flash Sale Campaign:', campaignResponse.data);
        }
      }

      // Get rewards
      const response = await flashSaleService.getListRewardByCollection({
        collectionCode,
        mobileType,
        offset: _offset,
        limit: NUMBER_ITEM_PER_BATCH
      });
      
      console.log('[useFlashSale] API Response:', response);
      console.log('[useFlashSale] Response Status:', response?.status);
      console.log('[useFlashSale] Response Data:', response?.data);

      if (response?.status?.success && response?.data) {
        const rewards = Array.isArray(response.data) ? response.data : [];
        
        if (_offset === 0) {
          setRewardList(rewards);
        } else {
          setRewardList(prev => [...prev, ...rewards]);
        }
        
        setOffset(_offset + NUMBER_ITEM_PER_BATCH);
        
        if (rewards.length < NUMBER_ITEM_PER_BATCH) {
          setEndOfList(true);
        }
      } else {
        setError('Không thể tải danh sách sản phẩm');
      }
    } catch (err) {
      console.error('Error loading flash sale data:', err);
      setError('Có lỗi xảy ra khi tải dữ liệu Flash Sale');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [collectionCode, hasFlashSale, NUMBER_ITEM_PER_BATCH]);

  // Initial load
  useEffect(() => {
    fetchRewardListByCode(0);
    setOffset(0);
    setEndOfList(false);
  }, [fetchRewardListByCode]);

  // Check flash sale status
  const isFlashSaleActive = flashSaleCampaign && 
    flashSaleCampaign.campaignType === 'flashSale' &&
    new Date(currentDateTime) >= new Date(flashSaleCampaign.campaignStartTime || '') &&
    new Date(currentDateTime) <= new Date(flashSaleCampaign.campaignEndTime || '');

  const isCountdownActive = flashSaleCampaign &&
    new Date(currentDateTime) >= new Date(flashSaleCampaign.campaignCountdownTime || '') &&
    new Date(currentDateTime) < new Date(flashSaleCampaign.campaignStartTime || '');

  const hasTeasing = flashSaleCampaign &&
    flashSaleCampaign.campaignType === 'theme' &&
    new Date(currentDateTime) >= new Date(flashSaleCampaign.campaignCountdownTime || '') &&
    new Date(currentDateTime) < new Date(flashSaleCampaign.campaignStartTime || '');

  const backgroundColor = isFlashSaleActive && flashSaleCampaign?.campaignType === 'flashSale'
    ? '#feefbe' // yellow30
    : '#EFF3F6'; // grey4

  const handleBack = useCallback(() => {
    onNavigateBack?.(1);
  }, [onNavigateBack]);

  const handleRewardClick = useCallback((reward: RewardItemType) => {
    onNavigate?.(`/reward/${reward.id}`);
  }, [onNavigate]);

  const handleCountdownCallback = useCallback(() => {
    // Refresh data when countdown ends or status changes
    setCurrentDateTime(new Date().toISOString());
    if (flashSaleCampaign && new Date() > new Date(flashSaleCampaign.campaignEndTime || '')) {
      // Campaign ended, navigate back
      onNavigateBack?.(1);
    }
  }, [flashSaleCampaign, onNavigateBack]);

  return {
    // State
    flashSaleCampaign,
    rewardList,
    loading,
    refreshing,
    error,
    currentDateTime,
    offset,
    endOfList,
    
    // Actions
    fetchRewardListByCode,
    handleCountdownCallback,
    handleRewardClick,
    handleBack,
    
    // Computed values
    isFlashSaleActive,
    isCountdownActive,
    hasTeasing,
    backgroundColor,
    
    // Constants
    NUMBER_ITEM_PER_BATCH,
  };
};