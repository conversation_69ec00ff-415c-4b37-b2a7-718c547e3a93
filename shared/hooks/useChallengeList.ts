import { useState, useEffect, useCallback } from 'react';

export interface Challenge {
  _id: string;
  displayChallengeName: string;
  challengeImage?: string;
  userProgress?: {
    state: string;
    currentProgress?: number;
    maxProgress?: number;
    percentProgress?: number;
  };
  startDate?: string;
  endDate?: string;
  canUserJoinChallenge?: boolean;
  isDisplayRemainingTime?: boolean;
  packageGift?: {
    remain?: number;
  };
  [key: string]: any;
}

export interface Banner {
  id: string;
  image: string;
  title: string;
  [key: string]: any;
}

export interface UseChallengeListOptions {
  onNavigate?: (challengeId: string) => void;
}

export interface UseChallengeListReturn {
  // Data
  runningList: Challenge[];
  notReceiveList: Challenge[];
  banners: Banner[];
  
  // Loading states
  isLoading: boolean;
  loadingHeader: boolean;
  refreshing: boolean;
  batchLoading: boolean;
  
  // Actions
  fetchChallengeList: () => Promise<void>;
  fetchHeaderData: () => Promise<void>;
  handleRefresh: () => Promise<void>;
  handleChallengeClick: (challengeId: string) => void;
  handleBannerClick: (banner: Banner) => void;
}

// Mock data - replace with actual API calls
const mockRunningChallenges: Challenge[] = [
  {
    _id: '1',
    displayChallengeName: 'Mua sắm tuần này',
    challengeImage: 'https://via.placeholder.com/150',
    userProgress: {
      state: 'RUNNING',
      currentProgress: 3,
      maxProgress: 5,
      percentProgress: 60,
    },
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    canUserJoinChallenge: true,
    isDisplayRemainingTime: true,
    packageGift: { remain: 10 },
  },
  {
    _id: '2',
    displayChallengeName: 'Thử thách hàng ngày',
    challengeImage: 'https://via.placeholder.com/150',
    userProgress: {
      state: 'RUNNING',
      currentProgress: 7,
      maxProgress: 10,
      percentProgress: 70,
    },
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    canUserJoinChallenge: true,
    isDisplayRemainingTime: true,
    packageGift: { remain: 5 },
  },
];

const mockNotReceiveChallenges: Challenge[] = [
  {
    _id: '3',
    displayChallengeName: 'Hoàn thành mua sắm',
    challengeImage: 'https://via.placeholder.com/150',
    userProgress: {
      state: 'NOT_RECEIVED_YET',
    },
    canUserJoinChallenge: true,
  },
];

const mockBanners: Banner[] = [
  {
    id: '1',
    image: 'https://via.placeholder.com/400x150',
    title: 'Thử thách mới',
  },
];

export const useChallengeList = ({
  onNavigate,
}: UseChallengeListOptions = {}): UseChallengeListReturn => {
  // State
  const [runningList, setRunningList] = useState<Challenge[]>([]);
  const [notReceiveList, setNotReceiveList] = useState<Challenge[]>([]);
  const [banners, setBanners] = useState<Banner[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingHeader, setLoadingHeader] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [batchLoading, setBatchLoading] = useState(true);

  const fetchChallengeList = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API call - replace with actual ChallengesAPI call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setRunningList(mockRunningChallenges);
    } catch (error) {
      console.error('Error fetching challenges:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchHeaderData = useCallback(async () => {
    setLoadingHeader(true);
    try {
      // Simulate API call - replace with actual API calls
      await new Promise(resolve => setTimeout(resolve, 500));
      setNotReceiveList(mockNotReceiveChallenges);
      setBanners(mockBanners);
    } catch (error) {
      console.error('Error fetching header data:', error);
    } finally {
      setLoadingHeader(false);
    }
  }, []);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await Promise.all([fetchChallengeList(), fetchHeaderData()]);
    setRefreshing(false);
  }, [fetchChallengeList, fetchHeaderData]);

  const handleChallengeClick = useCallback((challengeId: string) => {
    onNavigate?.(challengeId);
  }, [onNavigate]);

  const handleBannerClick = useCallback((banner: Banner) => {
    // Handle banner click
    console.log('Banner clicked:', banner);
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchChallengeList();
    fetchHeaderData();
  }, [fetchChallengeList, fetchHeaderData]);

  // Update batch loading state
  useEffect(() => {
    if (!isLoading && !loadingHeader) {
      setBatchLoading(false);
    }
  }, [isLoading, loadingHeader]);

  return {
    // Data
    runningList,
    notReceiveList,
    banners,
    
    // Loading states
    isLoading,
    loadingHeader,
    refreshing,
    batchLoading,
    
    // Actions
    fetchChallengeList,
    fetchHeaderData,
    handleRefresh,
    handleChallengeClick,
    handleBannerClick,
  };
};