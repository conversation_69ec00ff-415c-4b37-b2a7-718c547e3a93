import { useState, useEffect, useCallback, useRef } from 'react';
import { myRewardsAPI, MyRewardDetail } from '../services/api/myrewards';

export interface UseMyRewardsResult {
  activeRewards: MyRewardDetail[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useMyRewards = (): UseMyRewardsResult => {
  const [activeRewards, setActiveRewards] = useState<MyRewardDetail[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fetchedRef = useRef(false);

  const fetchActiveRewards = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch active vouchers using the specific API endpoint
      const response = await myRewardsAPI.getActiveVouchers({
        offset: 0,
        limit: 8,
        tab: 'active'
      });

      // Extract active rewards directly from response data
      const activeRewardsData = response?.data || [];

      setActiveRewards(activeRewardsData);
    } catch (err) {
      console.error('Failed to fetch active rewards:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch rewards');
      setActiveRewards([]);
    } finally {
      setLoading(false);
    }
  }, []); // Remove dependency array since this function doesn't depend on external values

  const refetch = useCallback(async () => {
    await fetchActiveRewards();
  }, [fetchActiveRewards]);

  // Use ref to prevent multiple fetches
  useEffect(() => {
    if (!fetchedRef.current) {
      fetchedRef.current = true;
      fetchActiveRewards();
    }
  }, []); // Only run once on mount

  return {
    activeRewards,
    loading,
    error,
    refetch,
  };
};