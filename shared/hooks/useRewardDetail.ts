import { useState, useEffect, useCallback } from 'react';
import {
  RewardsAPI,
  currencyApi,
} from '../services';
import { useToast } from './useToast';
import type {
  RewardItemType,
  BrandCurrencyType,
  GalleryImage,
  ContactInfo,
} from '../types';
import { ActionBalance } from '../services/api/types';
import {
  getRewardGalleryImages,
  formatRewardExpiryDate,
  getRewardContactInfo,
  getRewardFallbackLogo,
} from '../utils/rewardUtils';

export interface UseRewardDetailOptions {
  id?: string;
  onNavigate?: (path: string) => void;
}

export interface UseRewardDetailReturn {
  // State
  reward: RewardItemType | null;
  loading: boolean;
  actionLoading: boolean;
  error: string | null;
  showConfirmDialog: boolean;
  userBrandCurrencyBalance: BrandCurrencyType | null;

  // Actions
  setShowConfirmDialog: (show: boolean) => void;
  fetchRewardDetail: () => Promise<void>;
  handleUseReward: () => Promise<void>;

  // Computed values
  galleryImages: GalleryImage[];
  contactInfo: ContactInfo;
  formattedExpiryDate: string;
  fallbackLogo: string;

  // Toast
  toast: any;
  showSuccessToast: (message: string) => void;
  showErrorToast: (message: string) => void;
  showInfoToast: (message: string) => void;
  hideToast: () => void;
}

export const useRewardDetail = ({
  id,
  onNavigate,
}: UseRewardDetailOptions = {}): UseRewardDetailReturn => {
  // State
  const [reward, setReward] = useState<RewardItemType | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [userBrandCurrencyBalance, setUserBrandCurrencyBalance] = useState<BrandCurrencyType | null>(null);

  // Toast functionality
  const { toast, showSuccessToast, showErrorToast, showInfoToast, hideToast } = useToast();

  // Fetch brand currency balance
  const fetchBrandCurrencyBalance = useCallback(async (merchantCode: string) => {
    try {
      const response = await currencyApi.getBalance({
        merchantCode,
        action: ActionBalance.GET_BALANCE_BY_MERCHANT
      });

      if (response.status.success && response.data) {
        // API returns array, get first item
        const balanceData = Array.isArray(response.data) ? response.data[0] : response.data;
        
        if (balanceData) {
          setUserBrandCurrencyBalance(balanceData);
        }
      }
    } catch (error) {
      console.warn('Failed to fetch brand currency balance:', error);
      // Don't show error to user, just use fallback
    }
  }, []);

  const fetchRewardDetail = useCallback(async () => {
    if (!id) {
      setError('ID phần thưởng không hợp lệ');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Call the Rewards API to get reward detail
      const response = await RewardsAPI.getRewardDetail(id);
      // Handle BaseResponse wrapper
      const rewardData = response.data || response;
      setReward(rewardData);

      // Always fetch brand currency balance to get user points
      if (rewardData.merchant?.code) {
        await fetchBrandCurrencyBalance(rewardData.merchant.code);
      }
    } catch (err: unknown) {
      const errorMessage = (err as Error)?.message || 'Không thể tải thông tin phần thưởng';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [id, fetchBrandCurrencyBalance]);

  const handleUseReward = useCallback(async () => {
    if (!reward || actionLoading) return;

    try {
      setActionLoading(true);
      
      let response;
      
      // Check if this is a flash sale reward
      if (reward.isFlashSale || reward.campaignId) {
        response = await RewardsAPI.purchaseFlashSaleReward(
          reward.id,
          reward.campaignId,
          1
        );
      } else {
        response = await RewardsAPI.purchaseReward(reward.id, 1);
      }

      // Handle the response
      if (response?.data?.voucherId) {
        showSuccessToast(`Đổi thành công! Mã voucher: ${response.data.voucherCode || response.data.voucherId}. Điểm đã sử dụng: ${response.data.pointsUsed}`);
        
        // Close the dialog
        setShowConfirmDialog(false);
        
        // Optionally navigate to my rewards page
        // onNavigate?.('/my-rewards');
      } else {
        showSuccessToast('Đổi thành công! Vui lòng kiểm tra ví voucher của bạn.');
        setShowConfirmDialog(false);
      }
      
    } catch (error: unknown) {
      console.error('Error purchasing reward:', error);
      
      const errorResponse = error as { status?: number; message?: string };
      
      // Handle specific error cases
      if (errorResponse?.status === 400) {
        showErrorToast('Không đủ điểm để đổi phần thưởng này');
      } else if (errorResponse?.status === 409) {
        showErrorToast('Phần thưởng đã hết hoặc bạn đã đạt giới hạn đổi');
      } else if (errorResponse?.status === 404) {
        showErrorToast('Phần thưởng không tồn tại hoặc đã kết thúc');
      } else {
        showErrorToast(`Lỗi: ${errorResponse?.message || 'Không thể đổi phần thưởng'}`);
      }
      
      setShowConfirmDialog(false);
    } finally {
      setActionLoading(false);
    }
  }, [reward, actionLoading, showSuccessToast, showErrorToast]);

  // Fetch reward detail when id changes
  useEffect(() => {
    fetchRewardDetail();
  }, [id, fetchRewardDetail]);

  // Computed values using extracted utilities
  const galleryImages: GalleryImage[] = getRewardGalleryImages(reward);
  const fallbackLogo: string = getRewardFallbackLogo(reward?.merchantName);
  const formattedExpiryDate: string = formatRewardExpiryDate(reward?.endTime || reward?.validTo);
  const contactInfo: ContactInfo = getRewardContactInfo(reward);

  return {
    // State
    reward,
    loading,
    actionLoading,
    error,
    showConfirmDialog,
    userBrandCurrencyBalance,

    // Actions
    setShowConfirmDialog,
    fetchRewardDetail,
    handleUseReward,

    // Computed values
    galleryImages,
    contactInfo,
    formattedExpiryDate,
    fallbackLogo,

    // Toast
    toast,
    showSuccessToast,
    showErrorToast,
    showInfoToast,
    hideToast,
  };
};