import { useState, useEffect, useCallback, useRef } from "react";
import {
  entertainmentService,
  ChallengeV3,
  RequestListChallengeV3,
} from "../services/api/entertainmentService";

export interface UseChallengesOptions extends RequestListChallengeV3 {
  autoFetch?: boolean;
  // Add new filter options based on new API structure
  status?: string; // Filter by status field
}

export interface UseChallengesReturn {
  challenges: ChallengeV3[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  hasMore: boolean;
  loadMore: () => Promise<void>;
}

export const useChallenges = (
  options: UseChallengesOptions = {}
): UseChallengesReturn => {
  const {
    offset = 0,
    limit = 15,
    userProgressState = "RUNNING",
    home,
    autoFetch = true,
    status, // New status filter
  } = options;

  const [challenges, setChallenges] = useState<ChallengeV3[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentOffset, setCurrentOffset] = useState(offset);
  const [hasMore, setHasMore] = useState(true);

  // Guard to prevent double fetch in React.StrictMode (dev only)
  const hasFetchedOnceRef = useRef(false);

  // Initial fetch (offset = 0)
  const fetchInitial = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: RequestListChallengeV3 = {
        offset: 0,
        limit,
        userProgressState,
        home,
      };

      const newChallenges = await entertainmentService.getListChallengeV3(
        params
      );

      let filteredChallenges = newChallenges;
      if (status) {
        filteredChallenges = newChallenges.filter(
          (challenge) => challenge.status === status
        );
      }

      setChallenges(filteredChallenges);
      setCurrentOffset(limit);
      setHasMore(filteredChallenges.length === limit);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch challenges";
      setError(errorMessage);
      console.error("Error fetching challenges:", err);
    } finally {
      setLoading(false);
    }
  }, [limit, userProgressState, home, status]);

  // Load more (uses currentOffset)
  const fetchMore = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: RequestListChallengeV3 = {
        offset: currentOffset,
        limit,
        userProgressState,
        home,
      };

      const newChallenges = await entertainmentService.getListChallengeV3(
        params
      );

      let filteredChallenges = newChallenges;
      if (status) {
        filteredChallenges = newChallenges.filter(
          (challenge) => challenge.status === status
        );
      }

      setChallenges((prev) => [...prev, ...filteredChallenges]);
      setCurrentOffset((prev) => prev + limit);
      setHasMore(filteredChallenges.length === limit);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch challenges";
      setError(errorMessage);
      console.error("Error fetching challenges:", err);
    } finally {
      setLoading(false);
    }
  }, [currentOffset, limit, userProgressState, home, status]);

  const refetch = useCallback(async () => {
    setCurrentOffset(0);
    await fetchInitial();
  }, [fetchInitial]);

  const loadMore = useCallback(async () => {
    if (!loading && hasMore) {
      await fetchMore();
    }
  }, [loading, hasMore, fetchMore]);

  useEffect(() => {
    if (!autoFetch) return;
    if (hasFetchedOnceRef.current) return; // prevent double fetch in StrictMode
    hasFetchedOnceRef.current = true;
    fetchInitial();
  }, [autoFetch, fetchInitial]);

  return {
    challenges,
    loading,
    error,
    refetch,
    hasMore,
    loadMore,
  };
};
