import { useState, useEffect } from 'react';
import { avatarAPI, type CurrentAvatar } from '../services/api/avatar';

export const useAvatar = () => {
  const [currentAvatar, setCurrentAvatar] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchCurrentAvatar();
  }, []);

  const fetchCurrentAvatar = async () => {
    try {
      setLoading(true);
      const response = await avatarAPI.getCurrentAvatar();
      const avatar = response?.data;
      setCurrentAvatar(avatar?.image || null);
    } catch (err) {
      console.error('Failed to fetch current avatar:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateAvatar = (avatarImage: string) => {
    // Update local state
    setCurrentAvatar(avatarImage);
  };

  return {
    currentAvatar,
    loading,
    updateAvatar,
    refetch: fetchCurrentAvatar
  };
};