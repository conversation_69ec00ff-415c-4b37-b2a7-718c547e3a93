import { useState, useEffect } from 'react';
import { inboxAPI, type InboxItem, type InboxListParams } from '../services/api/inbox';

export interface UseInboxResult {
  messages: InboxItem[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  hasMore: boolean;
  refresh: () => Promise<void>;
  loadMore: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
}

export function useInbox(params: InboxListParams = {}): UseInboxResult {
  const [messages, setMessages] = useState<InboxItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [offset, setOffset] = useState(0);
  
  const limit = params.limit || 10;
  const hasMore = messages.length < totalCount;

  const fetchMessages = async (currentOffset = 0, reset = false) => {
    try {
      setError(null);
      if (reset) setLoading(true);

      const response = await inboxAPI.getInboxList({
        ...params,
        limit,
        offset: currentOffset
      });

      if (reset) {
        setMessages(response.data);
      } else {
        setMessages(prev => [...prev, ...response.data]);
      }
      
      setTotalCount(response.totalCount);
      setOffset(currentOffset + limit);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch messages');
    } finally {
      setLoading(false);
    }
  };

  const refresh = async () => {
    await fetchMessages(0, true);
  };

  const loadMore = async () => {
    if (hasMore && !loading) {
      await fetchMessages(offset, false);
    }
  };

  const markAsRead = async (id: string) => {
    try {
      await inboxAPI.markAsRead(id);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === id ? { ...msg, isSeen: true } : msg
        )
      );
    } catch (err) {
      console.error('Failed to mark message as read:', err);
    }
  };

  const markAllAsRead = async () => {
    try {
      await inboxAPI.markAllAsRead();
      setMessages(prev => 
        prev.map(msg => ({ ...msg, isSeen: true }))
      );
    } catch (err) {
      console.error('Failed to mark all messages as read:', err);
    }
  };

  useEffect(() => {
    fetchMessages(0, true);
  }, [params.action]);

  return {
    messages,
    loading,
    error,
    totalCount,
    hasMore,
    refresh,
    loadMore,
    markAsRead,
    markAllAsRead
  };
}