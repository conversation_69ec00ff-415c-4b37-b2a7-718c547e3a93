import { create } from 'zustand';
import { OCRService, type UserStatusResponse } from '../services/ocr/ocrService';
import { ocrAPI } from '../services/api/ocr';
import type { 
  OCRTransaction, 
  OCRTransactionStatus, 
  OCRTransactionParams, 
  OCRStatistics,
  OCRTransactionResponse,
  OCRTransactionDetailResponse,
  OCRStatisticsResponse
} from '../services/api/ocr/types';

interface OCRTransactionsByStatus {
  PROCESSING: OCRTransaction[];
  APPROVED: OCRTransaction[];
  REJECTED: OCRTransaction[];
  FAILED: OCRTransaction[];
  COMPLETED: OCRTransaction[];
}

interface OCRMetaByStatus {
  PROCESSING: { currentPage: number; pageSize: number; totalPages: number; totalRows: number; } | null;
  APPROVED: { currentPage: number; pageSize: number; totalPages: number; totalRows: number; } | null;
  REJECTED: { currentPage: number; pageSize: number; totalPages: number; totalRows: number; } | null;
  FAILED: { currentPage: number; pageSize: number; totalPages: number; totalRows: number; } | null;
  COMPLETED: { currentPage: number; pageSize: number; totalPages: number; totalRows: number; } | null;
}

interface OCRLoadingByStatus {
  PROCESSING: boolean;
  APPROVED: boolean;
  REJECTED: boolean;
  FAILED: boolean;
  COMPLETED: boolean;
}

interface OCRState {
  // State - organized by transaction status
  transactionsByStatus: OCRTransactionsByStatus;
  metaByStatus: OCRMetaByStatus;
  loadingByStatus: OCRLoadingByStatus;
  error: string | null;
  currentStatus: OCRTransactionStatus | null;
  
  // State for transaction detail
  currentTransaction: OCRTransaction | null;
  loadingDetail: boolean;
  
  // State for statistics
  statistics: OCRStatistics | null;
  loadingStatistics: boolean;

  // State for user status (snap limit)
  userStatus: UserStatusResponse | null;
  loadingUserStatus: boolean;

  // Actions
  fetchTransactions: (status: OCRTransactionStatus, params?: Omit<OCRTransactionParams, 'status'>) => Promise<void>;
  fetchAllStatuses: () => Promise<void>;
  fetchTransactionDetail: (transactionId: string) => Promise<void>;
  fetchStatistics: () => Promise<void>;
  fetchUserStatus: () => Promise<void>;
  setCurrentStatus: (status: OCRTransactionStatus | null) => void;
  clearError: () => void;
  reset: () => void;
  
  // Getters
  getTransactionsForStatus: (status: OCRTransactionStatus) => OCRTransaction[];
  getMetaForStatus: (status: OCRTransactionStatus) => { currentPage: number; pageSize: number; totalPages: number; totalRows: number; } | null;
  isLoadingForStatus: (status: OCRTransactionStatus) => boolean;
  getTotalCountForStatus: (status: OCRTransactionStatus) => number;
}

const initialState = {
  transactionsByStatus: {
    PROCESSING: [],
    APPROVED: [],
    REJECTED: [],
    FAILED: [],
    COMPLETED: [],
  },
  metaByStatus: {
    PROCESSING: null,
    APPROVED: null,
    REJECTED: null,
    FAILED: null,
    COMPLETED: null,
  },
  loadingByStatus: {
    PROCESSING: false,
    APPROVED: false,
    REJECTED: false,
    FAILED: false,
    COMPLETED: false,
  },
  error: null,
  currentStatus: null,
  currentTransaction: null,
  loadingDetail: false,
  statistics: null,
  loadingStatistics: false,
  userStatus: null,
  loadingUserStatus: false,
};

export const useOCRStore = create<OCRState>((set, get) => ({
  ...initialState,

  fetchTransactions: async (status: OCRTransactionStatus, params = {}) => {
    const { page = 1, size = 10 } = params;
    console.log('Fetching transactions for status:', status, 'page:', page, 'size:', size);
    
    // Set loading for specific status
    set((state) => ({
      loadingByStatus: {
        ...state.loadingByStatus,
        [status]: true,
      },
      error: null,
    }));

    try {
      const response = await ocrAPI.getTransactions({ status, page, size });
      console.log('[getTransactions] :', response)
      if (response?.status?.success) {
        set((state) => ({
          
          transactionsByStatus: {
            ...state.transactionsByStatus,
            [status]: page === 1 ? response.data : [...state.transactionsByStatus[status], ...response.data],
          },
          metaByStatus: {
            ...state.metaByStatus,
            [status]: response.meta || null,
          },
          loadingByStatus: {
            ...state.loadingByStatus,
            [status]: false,
          },
          error: null,
        }));
      } else {
        throw new Error(response?.status?.message || `Failed to fetch ${status} transactions`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      set((state) => ({
        loadingByStatus: {
          ...state.loadingByStatus,
          [status]: false,
        },
        error: errorMessage,
      }));
      console.error(`Error fetching ${status} transactions:`, error);
    }
  },

  fetchAllStatuses: async () => {
    const statuses: OCRTransactionStatus[] = ['PROCESSING', 'APPROVED', 'REJECTED', 'FAILED', 'COMPLETED'];
    const promises = statuses.map(status => get().fetchTransactions(status, { page: 1, size: 10 }));
    await Promise.all(promises);
  },

  fetchTransactionDetail: async (transactionId: string) => {
    set({ loadingDetail: true, error: null });
    console.log('Fetching transaction detail for ID:', transactionId);

    try {
      const response = await ocrAPI.getTransactionDetail(transactionId);
      
      if (response?.status?.success) {
        set({
          currentTransaction: response.data,
          loadingDetail: false,
          error: null,
        });
      } else {
        throw new Error(response?.status?.message || 'Failed to fetch transaction detail');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      set({
        currentTransaction: null,
        loadingDetail: false,
        error: errorMessage,
      });
      console.error('Error fetching transaction detail:', error);
    }
  },

  fetchStatistics: async () => {
    set({ loadingStatistics: true, error: null });

    try {
      const response = await ocrAPI.getStatistics();
      
      if (response?.status?.success) {
        set({
          statistics: response.data,
          loadingStatistics: false,
          error: null,
        });
      } else {
        throw new Error(response?.status?.message || 'Failed to fetch statistics');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      set({
        statistics: null,
        loadingStatistics: false,
        error: errorMessage,
      });
      console.error('Error fetching OCR statistics:', error);
    }
  },

  fetchUserStatus: async () => {
    set({ loadingUserStatus: true, error: null });

    try {
      const userStatus = await OCRService.getUserStatus();
      
      set({
        userStatus,
        loadingUserStatus: false,
        error: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      set({
        userStatus: null,
        loadingUserStatus: false,
        error: errorMessage,
      });
      console.error('Error fetching user status:', error);
    }
  },

  setCurrentStatus: (status: OCRTransactionStatus | null) => {
    set({ currentStatus: status });
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set(initialState);
  },

  // Getters
  getTransactionsForStatus: (status: OCRTransactionStatus) => {
    return get().transactionsByStatus[status];
  },

  getMetaForStatus: (status: OCRTransactionStatus) => {
    return get().metaByStatus[status];
  },

  isLoadingForStatus: (status: OCRTransactionStatus) => {
    return get().loadingByStatus[status];
  },

  getTotalCountForStatus: (status: OCRTransactionStatus) => {
    const meta = get().metaByStatus[status];
    return meta ? meta.totalRows : 0;
  },
}));