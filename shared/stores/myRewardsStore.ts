import { create } from 'zustand';
import { myRewardsAPI } from '../services/api/myrewards/api';
import { VoucherCount } from '../services/api/myrewards/types';

interface MyRewardsStore {
  // Voucher counts
  voucherCounts: VoucherCount;
  countsLoading: boolean;
  countsError: string | null;
  
  // Actions
  fetchVoucherCounts: () => Promise<void>;
  resetCounts: () => void;
}

const initialCounts: VoucherCount = {
  numberOfUnRead: 0,
  numberOfActive: 0,
  numberOfRedeemed: 0,
  numberOfExpired: 0,
};

// Cache for voucher counts to prevent duplicate calls
let countsCache: {
  data: VoucherCount | null;
  loading: boolean;
  promise: Promise<any> | null;
} = {
  data: null,
  loading: false,
  promise: null
};

export const useMyRewardsStore = create<MyRewardsStore>((set, get) => ({
  // Initial state
  voucherCounts: initialCounts,
  countsLoading: false,
  countsError: null,

  // Fetch voucher counts from API with caching
  fetchVoucherCounts: async () => {
    // If there's an ongoing request, wait for it
    if (countsCache.loading && countsCache.promise) {
      try {
        await countsCache.promise;
        return;
      } catch (err) {
        // Continue with new request if cached promise failed
      }
    }
    
    // If we have cached data, use it
    if (countsCache.data) {
      set({ 
        voucherCounts: countsCache.data,
        countsLoading: false,
        countsError: null
      });
      return;
    }
    
    set({ countsLoading: true, countsError: null });
    
    // Create and cache the promise
    const fetchPromise = (async () => {
      const response = await myRewardsAPI.getCountVoucher();
      
      if (response.status?.success && response.data) {
        // Cache the result
        countsCache = {
          data: response.data,
          loading: false,
          promise: null
        };
        
        set({ 
          voucherCounts: response.data,
          countsLoading: false,
          countsError: null
        });
      } else {
        set({ 
          countsLoading: false,
          countsError: response.status?.message || 'Failed to fetch voucher counts'
        });
      }
    })();
    
    // Set cache as loading
    countsCache = {
      data: null,
      loading: true,
      promise: fetchPromise
    };
    
    try {
      await fetchPromise;
    } catch (error) {
      set({ 
        countsLoading: false,
        countsError: error instanceof Error ? error.message : 'An error occurred'
      });
      
      // Clear loading state from cache
      countsCache = {
        data: null,
        loading: false,
        promise: null
      };
    }
  },

  // Reset counts to initial state
  resetCounts: () => {
    set({
      voucherCounts: initialCounts,
      countsLoading: false,
      countsError: null
    });
  },
}));