import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { homeAPI } from '../services/api/home/<USER>';
import { rewardsAPI } from '../services/api/rewards/api';
import { merchantAPI } from '../services/api/merchant/api';
import {
  HomeConfig,
  BannerItem,
  HotNewsArticle,
  AppAnnouncement,
} from '../services/api/home/<USER>';
import { RewardItemType, FlashSaleCampaign, RewardCard } from '../services/api/rewards/types';
import { MerchantType } from '../services/api/merchant/types';
import { createAsyncState, setSuccess, setError } from './utils';
import { BaseAsyncState } from './types';

interface HomeState {
  // Section configuration
  sections: HomeConfig[];
  
  // Panel configuration for ActionCategories
  panels: any[];
  
  // Banner data
  banners: BannerItem[];
  
  // News data
  news: HotNewsArticle[];
  
  // Flash sale data
  flashSale: FlashSaleCampaign | null;
  flashSaleRewards: RewardCard[];
  flashSaleItems: RewardCard[];  // Alias for flashSaleRewards
  
  // Popular merchants
  popularMerchants: MerchantType[];
  
  // My rewards - now supports multiple reward collections
  myRewards: RewardItemType[];
  rewardsByCollection: Record<string, RewardItemType[]>;
  
  // Announcements
  announcements: AppAnnouncement[];
  
  // Challenges
  challenges: any[];
  
  // Earn methods
  earnMethods: any[];
  
  // Loading states
  loading: boolean;
  bannersLoading: boolean;
  newsLoading: boolean;
  flashSaleLoading: boolean;
  merchantsLoading: boolean;
  rewardsLoading: boolean;
  challengesLoading: boolean;
  earnMethodsLoading: boolean;
  
  // Async states
  configState: BaseAsyncState<HomeConfig[]>;
  bannersState: BaseAsyncState<BannerItem[]>;
  newsState: BaseAsyncState<HotNewsArticle[]>;
  
  // Pagination
  currentPage: number;
  totalPages: number;
  segmentIds: string[];
}

interface HomeActions {
  // Fetch actions
  fetchHomeConfig: () => Promise<void>;
  fetchBanners: (collectionId: string) => Promise<void>;
  fetchNews: (params?: { limit?: number; offset?: number }) => Promise<void>;
  fetchFlashSale: (collectionCode?: string) => Promise<void>;
  fetchPopularMerchants: (params?: { offset?: number; limit?: number }) => Promise<void>;
  fetchRewards: (params?: { collectionCode?: string; mobileType?: string }) => Promise<void>;
  fetchAnnouncements: () => Promise<void>;
  fetchChallenges: (params?: { offset?: number; limit?: number }) => Promise<void>;
  fetchEarnMethods: () => Promise<void>;
  
  // Cascading data loading
  fetchSectionData: () => Promise<void>;
  
  // Refresh all data
  refreshAll: () => Promise<void>;
  
  // Section management
  addMoreSections: () => void;
  
  // Reset
  reset: () => void;
}

const initialState: HomeState = {
  sections: [],
  panels: [],
  banners: [],
  news: [],
  flashSale: null,
  flashSaleRewards: [],
  flashSaleItems: [],
  popularMerchants: [],
  myRewards: [],
  rewardsByCollection: {},
  announcements: [],
  challenges: [],
  earnMethods: [],
  loading: false,
  bannersLoading: false,
  newsLoading: false,
  flashSaleLoading: false,
  merchantsLoading: false,
  rewardsLoading: false,
  challengesLoading: false,
  earnMethodsLoading: false,
  configState: createAsyncState([]),
  bannersState: createAsyncState([]),
  newsState: createAsyncState([]),
  currentPage: 1,
  totalPages: 1,
  segmentIds: [],
};

export const useHomeStore = create<HomeState & HomeActions>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      fetchHomeConfig: async () => {
        console.log('🚨 API CALL: fetchHomeConfig started');
        set({ loading: true });
        try {
          const response = await homeAPI.getHomeConfig();
          // Handle new BaseResponse structure: { status, data }
          const configData = response.data || response;
          if (configData.sections) {
            set({
              sections: configData.sections || [],
              panels: configData.panels || [],
              segmentIds: configData.segmentIds || [],
              loading: false,
              configState: setSuccess(get().configState, configData.sections || []),
            });
          }
        } catch (error: any) {
          console.error('🏠 [fetchHomeConfig] Error:', error);
          set({
            loading: false,
            configState: setError(get().configState, error.message),
          });
        }
      },
      
      fetchBanners: async (collectionId: string) => {
        console.log('🚨 API CALL: fetchBanners started for collection:', collectionId);
        set({ bannersLoading: true });
        try {
          const response = await homeAPI.getBanners({ 
            bannerCollectionId: collectionId
          });
          
          // Handle potential BaseResponse wrapper or direct array response
          const banners = Array.isArray(response) ? response : (response as any)?.data || [];
          
          set({
            banners: banners,
            bannersLoading: false,
            bannersState: setSuccess(get().bannersState, banners),
          });
        } catch (error: any) {
          console.error('🏷️ [fetchBanners] Error:', error);
          set({
            bannersLoading: false,
            bannersState: setError(get().bannersState, error.message),
          });
        }
      },
      
      fetchNews: async (params) => {
        console.log('🚨 API CALL: fetchNews started with params:', params);
        set({ newsLoading: true });
        try {
          const response = await homeAPI.getHotNews({
            limit: params?.limit || 10,
          });
          
          // Handle potential BaseResponse wrapper or direct array response
          const news = Array.isArray(response) ? response : (response as any)?.data || [];
          
          set({
            news: news,
            newsLoading: false,
            newsState: setSuccess(get().newsState, news),
          });
        } catch (error: any) {
          console.error('📰 [fetchNews] Error:', error);
          set({
            newsLoading: false,
            newsState: setError(get().newsState, error.message),
          });
        }
      },
      
      fetchFlashSale: async (collectionCode?: string) => {
        set({ flashSaleLoading: true });
        try {
          // Get active flash sale campaigns with collectionCode from config
          const campaignsResponse = await homeAPI.getActiveCampaign(collectionCode);
          
          // Handle potential BaseResponse wrapper or direct array response
          const campaigns = Array.isArray(campaignsResponse) ? campaignsResponse : (campaignsResponse as any)?.data || [];
          
          // Get flash sale rewards using collection-based approach
          const rewardsResponse = await rewardsAPI.getListRewardByCollection({
            collectionCode: collectionCode || 'flashsale',
            mobileType: 'flashSale',
            offset: 0,
            limit: 8,
          });
          
          const flashSaleItems = Array.isArray(rewardsResponse) ? rewardsResponse : (rewardsResponse as any)?.data || [];
          
          // Update both flashSaleItems (backward compatibility) and rewardsByCollection
          const currentRewardsByCollection = get().rewardsByCollection;
          
          set({
            flashSale: campaigns.length > 0 ? campaigns[0] : null,
            flashSaleRewards: flashSaleItems,
            flashSaleItems: flashSaleItems, // Keep for backward compatibility
            flashSaleLoading: false,
            // Store flash sale data in rewardsByCollection for collection-specific access
            rewardsByCollection: {
              ...currentRewardsByCollection,
              [collectionCode || 'flashsale']: flashSaleItems
            }
          });
        } catch (error: any) {
          console.error('⚡ [fetchFlashSale] Error:', error);
          set({
            flashSale: null,
            flashSaleRewards: [],
            flashSaleItems: [],
            flashSaleLoading: false,
          });
        }
      },
      
      fetchPopularMerchants: async (params = {}) => {
        set({ merchantsLoading: true });
        try {
          // Get popular merchants using mobile pattern: action=LOAD_MORE with isPopular=true
          const response = await merchantAPI.getMerchantListByCollection({
            offset: params.offset || 0,
            limit: params.limit || 6,
            isPopular: true,
          });
          
          // Handle potential BaseResponse wrapper or direct array response
          const merchants = Array.isArray(response) ? response : (response as any)?.data || [];
          
          set({
            popularMerchants: merchants,
            merchantsLoading: false,
          });
        } catch (error: any) {
          console.error('🏪 [fetchPopularMerchants] Error:', error);
          set({
            popularMerchants: [],
            merchantsLoading: false,
          });
        }
      },
      
      fetchRewards: async (params = {}) => {
        set({ rewardsLoading: true });
        try {
          const requestParams = {
            collectionCode: params.collectionCode || 'suggestion',
            mobileType: params.mobileType || 'running',
            offset: 0,
            limit: 10,
          };
          
          // Get suggested rewards using getListReward with collection parameters from home-config
          const response = await rewardsAPI.getListRewardSuggestion(requestParams);
          
          // Handle potential BaseResponse wrapper or direct array response
          const rewards = Array.isArray(response) ? response : (response as any)?.data || [];
          
          const collectionCode = params.collectionCode || 'suggestion';
          
          set((state) => ({
            myRewards: collectionCode === 'suggestion' ? rewards as RewardItemType[] : state.myRewards,
            rewardsByCollection: {
              ...state.rewardsByCollection,
              [collectionCode]: rewards as RewardItemType[]
            },
            rewardsLoading: false,
          }));
        } catch (rewardError: unknown) {
          console.error('🎁 [fetchRewards] Error:', rewardError);
          const collectionCode = params?.collectionCode || 'suggestion';
          
          set((state) => ({
            myRewards: collectionCode === 'suggestion' ? [] : state.myRewards,
            rewardsByCollection: {
              ...state.rewardsByCollection,
              [collectionCode]: []
            },
            rewardsLoading: false,
          }));
        }
      },
      
      fetchAnnouncements: async () => {
        try {
          const response = await homeAPI.getAnnouncements();
          
          // Handle potential BaseResponse wrapper or direct array response
          const announcements = Array.isArray(response) ? response : (response as any)?.data || [];
          const activeAnnouncements = announcements.filter((a: any) => a.isActive) || [];
          
          set({
            announcements: activeAnnouncements,
          });
        } catch (error: any) {
          console.error('📢 [fetchAnnouncements] Error:', error);
          // Silent fail for announcements
        }
      },
      
      fetchChallenges: async (params = {}) => {
        set({ challengesLoading: true });
        try {
          const response = await homeAPI.getChallenges({
            offset: params.offset || 0,
            limit: params.limit || 8,
            home: true,
          });
          
          // Handle potential BaseResponse wrapper or direct array response
          const challenges = Array.isArray(response) ? response : (response as any)?.data || [];
          
          set({
            challenges: challenges,
            challengesLoading: false,
          });
        } catch (challengeError: unknown) {
          console.error('🏆 [fetchChallenges] Error:', challengeError);
          set({
            challenges: [],
            challengesLoading: false,
          });
        }
      },
      
      fetchEarnMethods: async () => {
        set({ earnMethodsLoading: true });
        try {
          const response = await merchantAPI.getEarnMethods();
          
          // Handle potential BaseResponse wrapper
          const earnMethods = Array.isArray(response) ? response : (response as any)?.data || [];
          
          set({
            earnMethods: earnMethods,
            earnMethodsLoading: false,
          });
        } catch (earnError: unknown) {
          console.error('💰 [fetchEarnMethods] Error:', earnError);
          set({
            earnMethods: [],
            earnMethodsLoading: false,
          });
        }
      },
      
      fetchSectionData: async () => {
        console.log('🚨 API CALL: fetchSectionData started');
        const { sections } = get();
        
        if (sections.length === 0) {
          console.warn('No sections available for data loading');
          return;
        }

        // Build actions array based on available sections
        const actions: Promise<void>[] = [];
        
        // Check each section type and add corresponding data fetch
        sections.forEach(section => {
          switch (section.code) {
            case 'banner':
              if (section.collectionId) {
                actions.push(get().fetchBanners(section.collectionId));
              }
              break;
              
            case 'news':
              actions.push(get().fetchNews());
              break;
              
            case 'rewardflashsale':
              if (section.collectionCode) {
                actions.push(get().fetchFlashSale(section.collectionCode));
              } else {
                actions.push(get().fetchFlashSale());
              }
              break;
              
            case 'merchant':
              actions.push(get().fetchPopularMerchants());
              break;
              
            case 'reward':
              // Pass collectionCode and determine mobileType based on section config
              if (section.collectionCode) {
                actions.push(get().fetchRewards({
                  collectionCode: section.collectionCode,
                  mobileType: section.patternType === 'Special' ? 'special' : 'running'
                }));
              } else {
                actions.push(get().fetchRewards());
              }
              break;
              
            case 'challenge':
            case 'pinnedchallenge':
              actions.push(get().fetchChallenges());
              break;
              
            case 'earn':
              actions.push(get().fetchEarnMethods());
              break;
          }
        });
        
        // Always fetch announcements
        //actions.push(get().fetchAnnouncements());
        
        // Execute all section data fetching in parallel
        await Promise.all(actions);
      },
      
      refreshAll: async () => {
        console.log('🔄 HomeStore refreshAll started');
        try {
          // First fetch the config to get section information
          console.log('🔄 Fetching home config...');
          await get().fetchHomeConfig();
          
          // Then fetch section-specific data based on configuration
          console.log('🔄 Fetching section data...');
          await get().fetchSectionData();
          
          console.log('🔄 HomeStore refreshAll completed successfully');
        } catch (error) {
          console.error('🔄 HomeStore refreshAll error:', error);
          throw error;
        }
      },
      
      addMoreSections: () => {
        const { currentPage, totalPages } = get();
        if (currentPage < totalPages) {
          set({ currentPage: currentPage + 1 });
        }
      },
      
      reset: () => {
        set({
          ...initialState,
          rewardsByCollection: {},
        });
      },
    }),
    {
      name: 'taptap-home-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        sections: state.sections,
        segmentIds: state.segmentIds,
        announcements: state.announcements,
      }),
    }
  )
);