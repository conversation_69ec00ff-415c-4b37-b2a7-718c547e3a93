import { create } from 'zustand';
import { InboxItem } from '../services/api/inbox/types';
import { inboxAPI } from '../services/api/inbox';

interface InboxState {
  // State
  items: InboxItem[];
  currentItem: InboxItem | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  offset: number;
  limit: number;
  hasMore: boolean;
  unseenCount: number;

  // Actions
  fetchInboxList: (reset?: boolean) => Promise<void>;
  fetchInboxItem: (id: string) => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  fetchUnseenCount: () => Promise<void>;
  setCurrentItem: (item: InboxItem | null) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  items: [],
  currentItem: null,
  loading: false,
  error: null,
  totalCount: 0,
  offset: 0,
  limit: 10,
  hasMore: true,
  unseenCount: 0,
};

export const useInboxStore = create<InboxState>((set, get) => ({
  ...initialState,

  fetchInboxList: async (reset = false) => {
    const { offset, limit, loading } = get();
    
    if (loading) return;

    try {
      set({ loading: true, error: null });
      
      const currentOffset = reset ? 0 : offset;
      const response = await inboxAPI.getInboxList({
        limit,
        offset: currentOffset,
        action: 'LIST_FOR_MOBILE'
      });

      // Handle BaseResponse format: {status, data, meta}
      if (response?.status?.success && response?.data) {
        set(state => ({
          items: reset ? response.data : [...state.items, ...response.data],
          totalCount: response.meta?.totalCount || response.totalCount || 0,
          offset: currentOffset + response.data.length,
          hasMore: response.data.length === limit,
          loading: false
        }));
      } else {
        set({ loading: false });
      }
    } catch (error) {
      set({ 
        error: 'Không thể tải tin nhắn. Vui lòng thử lại.',
        loading: false 
      });
    }
  },

  fetchInboxItem: async (id: string) => {
    try {
      set({ loading: true, error: null });
      
      // First check if item exists in list
      const { items } = get();
      const existingItem = items.find(item => item.id === id);
      
      if (existingItem) {
        set({ currentItem: existingItem, loading: false });
        // Mark as read
        await get().markAsRead(id);
        return;
      }

      // Try to fetch single item
      try {
        const response = await inboxAPI.getInboxDetail(id);
        if (response?.status?.success && response?.data) {
          set({ currentItem: response.data, loading: false });
          await get().markAsRead(id);
          return;
        }
      } catch {
        // If single item endpoint fails, fetch from list
        const listResponse = await inboxAPI.getInboxList({ limit: 100 });
        const listData = listResponse?.data || [];
        const foundItem = listData?.find((item: InboxItem) => item.id === id) || null;
        
        if (foundItem) {
          set({ currentItem: foundItem, loading: false });
          await get().markAsRead(id);
        } else {
          set({ 
            error: 'Không tìm thấy thông báo',
            loading: false 
          });
        }
      }
    } catch (error: any) {
      let errorMessage = 'Có lỗi xảy ra khi tải dữ liệu';
      
      // Handle specific error types
      if (error?.response?.status === 500) {
        errorMessage = 'Has error when system connect TCL Inbox server';
      } else if (error?.response?.status === 404) {
        errorMessage = 'Không tìm thấy thông báo';
      } else if (error?.response?.status >= 500) {
        errorMessage = 'Hệ thống đang bảo trì. Vui lòng thử lại sau.';
      } else if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('network')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối và thử lại.';
      } else if (error?.code === 'TIMEOUT') {
        errorMessage = 'Hết thời gian chờ. Vui lòng thử lại.';
      } else if (error?.response?.data?.status?.message) {
        errorMessage = error.response.data.status.message;
      }
      
      set({ 
        error: errorMessage,
        loading: false 
      });
    }
  },

  markAsRead: async (id: string) => {
    try {
      await inboxAPI.markAsRead(id);
      
      // Update local state
      set(state => {
        const item = state.items.find(item => item.id === id);
        const wasUnseen = item && !item.isSeen;
        
        return {
          items: state.items.map(item =>
            item.id === id ? { ...item, isSeen: true } : item
          ),
          currentItem: state.currentItem?.id === id 
            ? { ...state.currentItem, isSeen: true }
            : state.currentItem,
          unseenCount: wasUnseen ? Math.max(0, state.unseenCount - 1) : state.unseenCount
        };
      });
    } catch (error) {
      // Silently fail - marking as read is not critical
    }
  },

  markAllAsRead: async () => {
    try {
      await inboxAPI.markAllAsRead();
      
      // Update local state
      set(state => ({
        items: state.items.map(item => ({ ...item, isSeen: true })),
        unseenCount: 0
      }));
    } catch (error) {
      set({ error: 'Không thể đánh dấu tất cả là đã đọc' });
    }
  },

  fetchUnseenCount: async () => {
    try {
      const count = await inboxAPI.getUnseenCount();
      set({ unseenCount: count });
    } catch (error) {
      // Silently fail - unseen count is not critical
    }
  },

  setCurrentItem: (item) => set({ currentItem: item }),

  clearError: () => set({ error: null }),

  reset: () => set(initialState),
}));