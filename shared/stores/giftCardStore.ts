import { create } from 'zustand';
import { merchantAPI } from '../services/api/merchant/api';

export interface GiftCardStep {
  title: string;
  description: string;
  image: string;
  index: number;
}

export interface GiftCardMerchant {
  id: string;
  index: number;
  code: string;
  name: string;
  banner: string;
  note: string;
  earnRate: {
    percentage: number;
    earnValue: number;
    baseValue: number;
    description: string;
    image: string;
  };
  logo: string;
  earnModels: Array<{
    earnMethodCode: string;
    earnMethodName: string;
    additionRate: string;
    description: string;
    image: string | null;
  }>;
  description: string;
  image: string;
  status: string;
  backgroundColor: string;
  isFavorite: boolean | null;
  favoriteAt: string | null;
  homeMerchantDescription: string;
}

export interface GiftCardData {
  id: string;
  code: string;
  name: string;
  description: string;
  image: string;
  index: number;
  status: string;
  steps: GiftCardStep[];
  directButton: {
    label: string;
    image: string;
    pageCode: string;
    sections: any;
    deepLink: string;
  };
  subMerchants: GiftCardMerchant[];
  createdAt: string;
  updatedAt: string;
}

interface GiftCardStore {
  giftCardData: GiftCardData | null;
  steps: GiftCardStep[];
  merchants: GiftCardMerchant[];
  isLoading: boolean;
  error: string | null;
  fetchGiftCardInfo: () => Promise<void>;
}

export const useGiftCardStore = create<GiftCardStore>((set) => ({
  giftCardData: null,
  steps: [],
  merchants: [],
  isLoading: false,
  error: null,
  
  fetchGiftCardInfo: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await merchantAPI.getEarnMethodsForMemberCode({
        action: 'DETAIL',
        code: 'giftCard'
      });
      
      console.log('Gift card response:', response);
      
      // Handle the BaseResponse structure: {status, data}
      if (response?.status?.success && response?.data && Array.isArray(response.data) && response.data.length > 0) {
        const data = response.data[0] as GiftCardData; // Take first item from array
        set({
          giftCardData: data,
          steps: data.steps || [],
          merchants: data.subMerchants || [],
          isLoading: false,
        });
      } else {
        set({
          isLoading: false,
          error: 'Failed to fetch gift card data',
        });
      }
    } catch (error) {
      console.error('Error fetching gift card data:', error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'An error occurred',
      });
    }
  },
}));