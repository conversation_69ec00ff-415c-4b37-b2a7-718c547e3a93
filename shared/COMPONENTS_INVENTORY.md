# TapTap Shared Components Inventory

## 📊 Component Overview
Tracks all reusable components to avoid duplication and maintain consistency.

## 🧩 UI Components

### Navigation & Layout
- **BottomNavigation** - Mobile bottom tab navigation with 5 tabs
- **NavigationTab** - Individual tab component for bottom navigation
- **NavigationHeader** - Header component with ZaloMiniApp variant, icons, and actions (92px height)
- **TabNavigation** - Horizontal tab navigation with two variants: 'scrollable' (default) for search results with active states and badges (fixed-width layout, no text wrapping) and 'full-width' for equal-width tabs distributed across the full width with count displays (58px height, 3px indicator)
- **FullWidthTabNavigation** - Full-width tab navigation for 2-tab layouts where each tab takes 50% width (perfect for inbox-style dual tabs)
- **RoundedTabNavigation** - Rounded tab navigation with pill-style active states (Figma category filters)
- **SwiperTabNavigation** - Swipeable tab navigation with auto-width tabs and no text wrapping
- **HorizontalScrollTabs** - Horizontal scrollable tabs with underline indicator, uppercase text, and optional counts (54px height, 3px indicator)
- **SearchLayout** - Main layout for search screens (375px width, #EFF3F6 background)
- **SearchResultsLayout** - Layout for search results screens (375x1278 viewport, #EFF3F6 background)

### Empty States
- **EmptyBillState** - Empty state component for bill/receipt pages with mascot, title, description, and action button. Based on mobile StatusOnScreen component (165px mascot icon, flexible height)

### Content Cards
- **NewsCard** - News/article card with image and title (278px width)
- **ProductCard** - Product display card with image, name, rating
- **ReviewCard** - User review card with avatar, rating, comment
- **RewardCard** - Reward/prize card display with brand, title, expiry date, logo (250px width, 84px height)
- **RewardDetailHeader** - Header component for reward detail pages showing title, brand info, expiry date, and points exchange rate (345px width, 190px height)
- **RewardThumbnailCard** - Large reward card with brand background, product image, pricing and status info (262px width, 196px height)
- **RewardListCard** - Horizontal reward card for list views with V5 UI Revamp design, enhanced VUI coin icon, optimized layout, and improved visual hierarchy (344px width, 124px height)
- **RewardItemList** - Updated horizontal reward card component based on Figma design "Card/Reward/Thumbnail/NoDiscount/Normal_V5" with exact positioning, Archia font, and proper asset integration (343px width, 124px height)
- **FlashSaleCard** - Flash sale card with image, logo, store name, promotion, pricing (132px width)
- **EnhancedFlashSaleCard** - Enhanced flash sale card with discount labels, offers remaining indicator, and distance display (132px width, based on TTLoyalty Figma design)
- **CountdownTimer** - Timer component with hours:minutes:seconds format
- **FlashIcon** - Gradient flash/lightning icon SVG
- **FlashSaleCountdown** - Complete countdown section with flash icon, timer, and remind button
- **BrandDetailSection** - Brand detail section with title, description, image and customizable content area (375px width, flexible height)
- **StoreLocationCard** - Individual store location card with name, address, distance, and action buttons (call/map)
- **StoreNearbySection** - Complete nearby stores section with store cards and show all button
- **LoyaltyCard** - Multi-state loyalty card with 4 variants: no-join, without-rank, with-rank, max-rank. Features progress tracking, badges, and contextual actions (343px width, 208px height). Supports two themes: default (green) and merchant-detail (pink with decorative patterns and barcode scan action)
- **MissionCard** - Multi-state mission card with 6 variants: completed, inProgress, inProgressWithLabel, received, lost, releasing. Features VUI/brand rewards, progress tracking, status indicators, and contextual actions (343px width, 128px height)
- **GameCard** - Multi-state game card with 4 variants: countdown, remaining-time, occurring, ended. Features game rewards, play counts, status overlays, and contextual actions (343px width, 136px height)
- **ImageGallery** - Image gallery section with 3x2 thumbnail grid and "+N" overflow indicator. Includes popup modal for image viewing (375px width, 298px height)
- **ImagePopup** - Full-screen image modal with navigation, thumbnails strip, and keyboard controls. Supports image galleries with prev/next navigation
- **VoucherSection** - Voucher/offers section with horizontal ticket-style cards, section header, and show all button. Each card has brand info, title, price with VUI coin icon (344px width, 124px height cards)
- **HowToEarnSection** - Information section explaining how to earn points with title, optional image, and description text (375px width, flexible height)

### Form Components
- **Button** - Primary component with variants (primary, secondary, outline, text)
- **BackButton** - Reusable back navigation button using shared assets (replaces 14+ inline SVG duplications)
- **BottomPurchaseSection** - Fixed bottom purchase/exchange section with coin icon, pricing display, and action button (responsive width, fixed positioning)
- **Input** - Text input with multiple styles (styled, tailwind)
- **TextArea** - Multi-line text input
- **Checkbox** - Checkbox with custom styling
- **RadioButton** - Radio button component
- **SearchBar** - Search input with icon
- **SearchIcon** - Reusable search icon component using shared assets
- **SearchInput** - Dedicated search input for search screens with clear button and focus states
- **ActionButton** - Icon button with label and optional notification badge (36px icon, centered)
- **HotSearchChip** - Popular/trending search keyword chip with trend-up icon (28px height, 6px border radius, #EFF3F6 background)
- **RecentSearchChip** - Recent search keyword chip without icon (28px height, 6px border radius, #EFF3F6 background)
- **ProfileNavSection** - Profile navigation with avatar, user info, and action buttons (375px width, voucher and notification bell with badge)
- **ProfileSectionHeader** - Section header for profile pages with title (375px width, 58px height, white background)
- **ProfileMenuItem** - Individual profile menu item with icon and title (375px width, 56px height, with bottom border)

### Layout & Structure
- **Card** - Base card component with shadow and padding
- **SectionHeader** - Section title with optional action button (375px width)
- **SearchContentArea** - Content area for search screens with loading/empty states and scrolling
- **SearchResultsCounter** - Counter component displaying search result counts with dynamic formatting

### Display Components
- **Typography** - Text component with design system styles
- **Icon** - Icon wrapper component
- **Avatar** - User avatar with fallback
- **Rating** - Star rating display
- **LoyaltyPoints** - Points display component
- **PointsCard** - Enhanced points display with coin icon and navigation (modern design)
- **Mascot** - Character/mascot display

### Lists & Collections
- **NewsSection** - News cards section with horizontal scroll
- **FlashSaleSection** - Flash sale cards section with horizontal scroll
- **MerchantResultsSection** - Search results section for merchants with section header
- **Categories** - Category selection grid
- **CategoryButton** - Individual category button

### User Interaction Components

### Page Components
- **MembershipTierPage** - Full membership tier page component showing current tier status, progress to next tier, avatar display, and action cards for benefits and history (375px width, gradient background)

### Specialized Components
- **BannerSwiper** - Image carousel/slider
- **BannerCarousel** - Banner carousel with Swiper.js integration, navigation dots, and click handlers (343px x 156px banners, 8px gap)
- **MyRewardsSection** - My rewards section with horizontal scroll and click handlers (375px width)
- **ActionCategories** - Points card + action buttons grid (member code, QR scan, receipt scan, gift exchange)
- **UserProfile** - User profile display
- **ProfileSearchHeader** - Profile with search functionality
- **ScreenStatus** - Status/loading screen component
- **CameraInterface** - Receipt capture camera interface with overlay, corners, flash toggle, and action buttons
- **DealsTabHeader** - Deals page header with NavigationHeader + TabNavigation (3 tabs: All, Active, Upcoming)
- **RewardsGrid** - Grid layout for rewards/exchange items with loading states, empty states, and load more functionality
- **WaveBackground** - Decorative wave background element with customizable color and height

### Icon Components
- **BarcodeIcon** - Barcode scanner icon for member code
- **QRCodeIcon** - QR code scanner icon 
- **ReceiptIcon** - Receipt/bill scan icon
- **ExchangeIcon** - Gift exchange/redeem icon

## 🎨 Design Patterns

### Layout Patterns
- **Mobile-first**: 414px max-width (set by Layout container, all components use w-full)
- **Horizontal scroll**: For card collections
- **Vertical stacking**: Main layout pattern
- **Fixed positioning**: Bottom navigation

### Typography Hierarchy
- **Primary**: 16px bold for section headers
- **Secondary**: 14px semibold for card titles
- **Body**: 14px regular for content
- **Caption**: 10px for navigation labels

### Color System (Centralized)
- **Design Tokens**: Defined in `shared/styles/colors.ts`
- **Primary**: #F65D79 (TapTap pink) with hover/active variants
- **Text**: #1A1818 (near black), #9A9A9A (gray), #5A5A5A (light gray)
- **Background**: #FFFFFF, #F8F8F8, #EFF3F6
- **Mission Cards**: Specific colors for received (#016A32), lost (#BF0301), releasing (#14A197)
- **Status**: Success (#0DC98B), warning (#FFA726), error (#BF0301), info (#14A197)

### Component Sizes
- **Cards**: 278px width (NewsCard), 132px width (FlashSaleCard), variable heights
- **Navigation**: 73px width per tab, 72px height
- **Container**: 414px max-width (Layout controls width, components responsive with w-full)
- **Images**: 120px height (NewsCard), 132px height (FlashSaleCard)

## 🔄 Reusability Guidelines

### Before Creating New Components:
1. Check existing components for similar functionality
2. Consider extending existing components with props
3. Evaluate if it's a variant of existing component
4. Check if layout can be achieved with composition

### Component Composition Examples:
- NewsSection = SectionHeader + NewsCard array
- FlashSaleSection = SectionHeader + FlashSaleCard array
- MyRewardsSection = SectionHeader + RewardCard array
- ActionCategories = PointsCard + ActionButton array
- ProfileSearchHeader = UserProfile + SearchBar
- Categories = CategoryButton array

### Extension Patterns:
- Button variants (primary, secondary, outline, text)
- Input styles (styled, tailwind)
- Card types (News, Product, Review, Reward)

## 📱 Usage Patterns

### Mobile Navigation:
```typescript
<BottomNavigation 
  activeItemId="home" 
  onItemClick={handleTabClick} 
/>
```

### Content Sections:
```typescript
<NewsSection 
  title="Bản tin nóng hổi"
  newsItems={items}
  onNewsClick={handleClick}
/>
```

### Form Elements:
```typescript
<Button variant="primary" size="medium">
  Action Text
</Button>
```

## 🏗️ Architecture Notes

### Shared Package Structure:
- All components export from `shared/components/ui/index.ts`
- Each component has own folder with index.ts, Component.tsx, stories
- Platform-aware but unified API
- Tailwind CSS with design tokens

### Dependencies:
- React 18.3.1
- TypeScript 5.5.0
- Tailwind CSS
- Storybook for documentation
- Platform detection utilities

---
*Last updated: July 2025*
*Components: 51+ reusable UI components*