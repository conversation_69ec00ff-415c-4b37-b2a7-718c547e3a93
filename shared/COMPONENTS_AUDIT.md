# Component Audit Report - Independent Platforms Refactor

**Date:** ${new Date().toISOString()}
**Approach:** Independent Platforms (Web và Zalo chạy độc lập)
**Goal:** Identify và refactor components thành pure, không có platform-specific code

## 📊 Summary

- **Total Components:** 100+
- **Pure Components (Keep):** ~60%
- **Need Refactor:** ~25%
- **Platform-Specific (Move):** ~15%

## ✅ PURE COMPONENTS (Giữ nguyên trong shared/)

These components are already pure and can be kept in shared:

### Basic UI Components
- ✅ **Button** - Pure presentation component
- ✅ **Input** - Pure form input
- ✅ **Card** - Pure container component
- ✅ **Badge** - Pure display component
- ✅ **Avatar** - Pure image display
- ✅ **Icon** - Pure icon component
- ✅ **Checkbox** - Pure form control
- ✅ **RadioButton** - Pure form control
- ✅ **TextArea** - Pure text input
- ✅ **Typography** - Pure text styles

### Display Components
- ✅ **Rating** - Pure rating display
- ✅ **CountdownTimer** - Pure timer display
- ✅ **FlashIcon** - Pure icon
- ✅ **PointsCard** - Pure points display
- ✅ **BrandCurrency** - Pure currency display
- ✅ **MessageCard** - Pure message display
- ✅ **Mascot** - Pure mascot images

### Card Components
- ✅ **NewsCard** - Pure news display
- ✅ **FlashSaleCard** - Pure sale card
- ✅ **RewardCard** - Pure reward display
- ✅ **BrandCard** - Pure brand display
- ✅ **MissionCard** - Pure mission display
- ✅ **GameCard** - Pure game display
- ✅ **BillCard** - Pure bill display
- ✅ **InboxCard** - Pure inbox item

### Layout Components
- ✅ **SectionHeader** - Pure section header
- ✅ **WaveBackground** - Pure background
- ✅ **ScrollableCarousel** - Pure carousel
- ✅ **ImageGallery** - Pure image viewer

### Form Components
- ✅ **Form** - Pure form wrapper
- ✅ **FormTextField** - Pure text field
- ✅ **FormCheckbox** - Pure checkbox
- ✅ **FormRadioButton** - Pure radio
- ✅ **FormTextBox** - Pure textarea

## ⚠️ NEED REFACTOR (Remove platform code)

These components have platform-specific code that needs to be removed:

### Components with Platform Detection
- ⚠️ **ZaloEntrance** - Has `isZaloMiniApp()` → Move to apps/taptap-zalo/
- ⚠️ **StoreNearby** - Has platform detection → Split into 2 versions
- ⚠️ **LoyaltyCard** - Has localStorage → Use props instead

### Components with Navigation
- ⚠️ **SectionMyReward** - Has navigation logic → Remove onClick, pass from parent
- ⚠️ **SectionFlashSale** - Has navigation → Make it pure display
- ⚠️ **SectionPopularMerchant** - Has navigation → Remove navigation
- ⚠️ **SectionGame** - Has navigation → Make pure
- ⚠️ **SectionNews** - Has navigation → Make pure
- ⚠️ **SectionChallenge** - Has navigation → Make pure
- ⚠️ **SectionEarnBy** - Has navigation → Make pure

### Components with Storage
- ⚠️ **WheelGame/PointsDisplay** - Has localStorage → Use props
- ⚠️ **MembershipTierPage** - Full page component → Move to apps/

## ❌ PLATFORM-SPECIFIC (Move to apps/)

These components are platform-specific and should be moved:

### Web-Only Components (→ apps/web/src/components/)
- ❌ **BottomNavigation** - Web-specific navigation
- ❌ **NavigationHeader** - Has platform branching
- ❌ **SearchLayout** - Has routing logic
- ❌ **CameraInterface** - Browser camera API
- ❌ **NotificationBell** - Web notifications

### Zalo-Only Components (→ apps/taptap-zalo/src/components/)
- ❌ **ZaloEntrance** - Zalo-specific onboarding
- ❌ **ZaloOnboarding** - Zalo tutorial

### Full Page Components (→ apps/*/src/pages/)
- ❌ **MembershipTierPage** - Full page component
- ❌ **ContactUsPage** - Full page component
- ❌ **MerchantDetailPage** - Full page component

## 📋 REFACTOR TASKS

### Priority 1: Remove Platform Detection (Day 1)
```typescript
// BEFORE (ZaloEntrance.tsx)
if (!isZaloMiniApp()) {
  return null;
}

// AFTER - Move entire component to apps/taptap-zalo/
```

### Priority 2: Remove Navigation Logic (Day 2)
```typescript
// BEFORE (SectionMyReward.tsx)
const handleClick = () => {
  navigate('/rewards');
};

// AFTER - Pass onClick from parent
interface Props {
  onRewardClick?: (reward: Reward) => void;
}
```

### Priority 3: Remove Storage Access (Day 3)
```typescript
// BEFORE (LoyaltyCard.tsx)
const points = localStorage.getItem('points');

// AFTER - Pass as props
interface Props {
  points: number;
}
```

### Priority 4: Split Mixed Components (Day 4)
Components that need different implementations per platform:
- **Modal** → WebModal + ZaloBottomSheet
- **SearchBar** → WebSearchBar + ZaloSearchBar
- **NavigationHeader** → WebHeader + ZaloHeader (uses native)

## 🎯 ACTION ITEMS

### Immediate Actions
1. [ ] Create new folders:
   - `apps/web/src/components/`
   - `apps/taptap-zalo/src/components/`

2. [ ] Move platform-specific components:
   - Move BottomNavigation → apps/web/
   - Move ZaloEntrance → apps/taptap-zalo/
   - Move page components → apps/*/pages/

3. [ ] Refactor navigation components:
   - Remove all `navigate()` calls
   - Pass callbacks from parent pages

4. [ ] Remove platform detection:
   - No `isZaloMiniApp()` in shared/
   - No `detectPlatform()` in shared/
   - No conditional rendering based on platform

5. [ ] Make components controlled:
   - No direct localStorage access
   - No direct API calls
   - All data via props

## 📊 METRICS

### Before Refactor
- Platform imports in shared/: 21 files
- Navigation logic in shared/: 15+ components
- Storage access in shared/: 5+ components

### After Refactor (Target)
- Platform imports in shared/: 0
- Navigation logic in shared/: 0
- Storage access in shared/: 0
- Pure components: 100%

## ✅ CHECKLIST

### Phase 1 Completion Criteria
- [ ] Zero platform detection in shared/
- [ ] Zero navigation imports in shared/
- [ ] Zero storage access in shared/
- [ ] All components are pure presentation
- [ ] All components fully typed
- [ ] Storybook stories for all components
- [ ] Components work in both apps

---

**Next Step:** Start refactoring components in priority order