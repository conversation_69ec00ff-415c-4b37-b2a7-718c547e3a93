import React from 'react';

interface Condition {
  buttonLabel?: string;
  directBtnLink?: string;
  conditionType?: string;
}

interface ChallengeFloatButtonProps {
  condition?: Condition;
  isExpired: boolean;
  canUserJoinChallenge: boolean;
  endDate: string;
  progressState: string;
  displayChallengeName: string;
  challengeId: string;
  isJoinedChallenge: boolean;
  isLoading?: boolean;
  onReceiveGiftNow?: () => void;
  onJoinChallenge?: () => void;
  onGoToEarnPage?: () => void;
}

const ChallengeFloatButton: React.FC<ChallengeFloatButtonProps> = ({
  condition,
  isExpired,
  canUserJoinChallenge,
  progressState,
  displayChallengeName,
  isJoinedChallenge,
  isLoading = false,
  onReceiveGiftNow,
  onJoinChallenge,
  onGoToEarnPage,
  endDate,
}) => {
  const { buttonLabel = 'buttonLabel', directBtnLink } = condition || {};

  if (
    !canUserJoinChallenge ||
    isExpired ||
    ['SUCCESSFULLY', 'FAILED', 'RELEASING'].includes(progressState)
  ) {
    return null;
  }

  // Format deadline display
  const formatDeadline = () => {
    const endDateTime = new Date(endDate);
    const hour = endDateTime.getHours();
    const dateStr = endDateTime.toLocaleDateString('vi-VN');
    return `${hour}h ${dateStr}`;
  };

  // Receive gift button
  if (progressState === 'NOT_RECEIVED_YET' && isJoinedChallenge) {
    return (
      <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-gray-200 p-4 z-50">
        <p className="text-red-500 text-sm mb-3">
          Hạn nhận quà{' '}
          <span className="font-bold">
            {formatDeadline()}
          </span>
        </p>
        <button
          onClick={onReceiveGiftNow}
          className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg font-medium disabled:opacity-50"
          disabled={isLoading}
        >
          {isLoading ? 'Đang xử lý...' : 'Nhận quà ngay'}
        </button>
      </div>
    );
  }

  // Join challenge button
  if (!isJoinedChallenge) {
    return (
      <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-gray-200 p-4 z-50">
        <button
          onClick={onJoinChallenge}
          disabled={isLoading}
          className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg font-medium disabled:opacity-50"
        >
          {isLoading ? 'Đang xử lý...' : 'Tham gia thử thách'}
        </button>
      </div>
    );
  }

  // Action button (go to earn page)
  if (buttonLabel && directBtnLink) {
    return (
      <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-gray-200 p-4 z-50">
        <button
          onClick={onGoToEarnPage}
          className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg font-medium"
        >
          {buttonLabel}
        </button>
      </div>
    );
  }

  return null;
};

export default React.memo(ChallengeFloatButton);