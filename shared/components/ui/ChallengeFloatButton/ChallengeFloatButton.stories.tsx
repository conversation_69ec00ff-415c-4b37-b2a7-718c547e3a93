import type { Meta, StoryObj } from '@storybook/react';
import ChallengeFloatButton from './ChallengeFloatButton';

const meta: Meta<typeof ChallengeFloatButton> = {
  title: 'Components/Challenge/ChallengeFloatButton',
  component: ChallengeFloatButton,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    onReceiveGiftNow: { action: 'receive gift now' },
    onJoinChallenge: { action: 'join challenge' },
    onGoToEarnPage: { action: 'go to earn page' },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const baseProps = {
  displayChallengeName: 'Mua sắm tuần này',
  challengeId: '123',
  endDate: '2024-12-31T23:59:59Z',
  isExpired: false,
  canUserJoinChallenge: true,
};

export const ReceiveGiftButton: Story = {
  args: {
    ...baseProps,
    progressState: 'NOT_RECEIVED_YET',
    isJoinedChallenge: true,
  },
};

export const JoinChallengeButton: Story = {
  args: {
    ...baseProps,
    progressState: 'PENDING',
    isJoinedChallenge: false,
  },
};

export const ActionButton: Story = {
  args: {
    ...baseProps,
    progressState: 'RUNNING',
    isJoinedChallenge: true,
    condition: {
      buttonLabel: 'Mua sắm ngay',
      directBtnLink: 'https://example.com/shop',
      conditionType: 'BRAND',
    },
  },
};

export const LoadingState: Story = {
  args: {
    ...baseProps,
    progressState: 'PENDING',
    isJoinedChallenge: false,
    isLoading: true,
  },
};

export const ExpiredChallenge: Story = {
  args: {
    ...baseProps,
    isExpired: true,
    progressState: 'RUNNING',
    isJoinedChallenge: true,
  },
};

export const CompletedChallenge: Story = {
  args: {
    ...baseProps,
    progressState: 'SUCCESSFULLY',
    isJoinedChallenge: true,
  },
};

export const FailedChallenge: Story = {
  args: {
    ...baseProps,
    progressState: 'FAILED',
    isJoinedChallenge: true,
  },
};

export const CannotJoinChallenge: Story = {
  args: {
    ...baseProps,
    canUserJoinChallenge: false,
    progressState: 'PENDING',
    isJoinedChallenge: false,
  },
};