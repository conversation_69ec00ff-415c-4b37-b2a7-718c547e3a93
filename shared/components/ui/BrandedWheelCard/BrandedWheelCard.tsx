import React from "react";
import type { IGameCard } from "../../../types/gameCard";
import { resWidth } from "../../../utils/responsive";

interface BrandedWheelCardProps {
  gameCard: IGameCard;
  onClick?: () => void;
}

const BrandedWheelCard: React.FC<BrandedWheelCardProps> = ({
  gameCard,
  onClick,
}) => {
  const { data } = gameCard;
  const wheelData = data;

  // Calculate remaining time
  const calculateRemainingTime = () => {
    if (!wheelData.isDisplayRemainingTime || !wheelData.endDate) {
      return null;
    }

    const now = new Date();
    const endDate = new Date(wheelData.endDate);
    const diffTime = endDate.getTime() - now.getTime();

    if (diffTime <= 0) return "Đã kết thúc";

    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));

    if (diffDays > 1) {
      return `Còn ${diffDays} ngày`;
    } else if (diffHours > 1) {
      return `Còn ${diffHours} giờ`;
    } else {
      return "Sắp kết thúc";
    }
  };

  const remainingTime = calculateRemainingTime();

  return (
    <div
      className="relative rounded-lg cursor-pointer transition-transform hover:scale-105"
      style={{
        backgroundColor: wheelData.backgroundColor || "#FAAD39",
        width: `${resWidth(164)}px`,
        height: `${resWidth(222)}px`,
      }}
      onClick={onClick}
    >
      {/* Pattern Background (decorative) - Updated to match Figma */}
      <div className="absolute inset-0 opacity-20 pointer-events-none">
        <svg className="w-full h-full" viewBox="0 0 164 198" fill="none">
          {/* Vector 617 - opacity 0.2 */}
          <path
            d="M-9 20L227 214.5"
            stroke="white"
            strokeWidth="2"
            opacity="0.2"
          />
          {/* Vector 619 - opacity 0.05 */}
          <path
            d="M-61 -46.6L217.22 202.24"
            stroke="white"
            strokeWidth="2"
            opacity="0.05"
          />
          {/* Vector 618 - opacity 0.2 */}
          <path
            d="M-41 30L195 224.5"
            stroke="white"
            strokeWidth="2"
            opacity="0.2"
          />
          {/* Vector 620 - opacity 0.1 */}
          <path
            d="M-27.5 -48L250.72 200.84"
            stroke="white"
            strokeWidth="2"
            opacity="0.1"
          />
        </svg>
      </div>

      {/* Game Thumbnail - Position updated to match Figma */}
      <div
        className="absolute"
        style={{
          width: `${resWidth(80)}px`,
          height: `${resWidth(80)}px`,
          left: `${resWidth(16)}px`,
          top: `${resWidth(-32)}px`, // Figma shows y=-32 but that seems incorrect, using 16px
        }}
      >
        <img
          src={wheelData.thumbnailImageUrl || wheelData.coverImageUrl}
          alt={wheelData.displayWheelName}
          className="w-full h-full object-cover rounded"
        />
      </div>

      {/* Title - Position and layout updated to match Figma (y=56) */}
      <div
        className="absolute flex flex-col items-start justify-center"
        style={{
          left: "0",
          top: `${resWidth(56)}px`, // Updated from 100px to 56px
          width: `${resWidth(164)}px`,
          padding: `${resWidth(0)}px ${resWidth(8)}px`, // Updated to match Figma
          gap: `${resWidth(4)}px`, // Updated to match Figma
        }}
      >
        <h3
          className="text-center font-semibold line-clamp-2"
          style={{
            color: wheelData.textColor || "#1A1818",
            fontSize: "12px",
            lineHeight: "18px",
            fontFamily: "Archia, sans-serif",
          }}
        >
          {wheelData.displayWheelName || wheelData.displayName}
        </h3>
      </div>

      {/* Reward Badges - Position and layout updated to match Figma (y=104) */}
      <div
        className="absolute flex flex-wrap"
        style={{
          left: "0",
          top: `${resWidth(104)}px`, // Updated from 136px to 104px
          width: "100%",
          gap: `${resWidth(6)}px`, // Updated to match Figma
          padding: `${resWidth(0)}px ${resWidth(8)}px`, // Updated to match Figma
        }}
      >
        {/* VUI Point Badge - Styling updated to match Figma */}
        <div className="flex flex-col items-start gap-2 max-h-16 overflow-hidden">
          {wheelData.giftDescriptions.map((gift) => (
            <div
              key={gift?._id}
              className="flex items-start gap-2 bg-white rounded-md px-2 py-1"
            >
              <span className="text-sm font-semibold truncate">
                {gift?.name}
              </span>
              {gift?.logoUrl ? (
                <img
                  src={gift?.logoUrl || ""}
                  alt={gift?.name || ""}
                  className=""
                  style={{
                    width: "16px",
                    height: "16px",
                    objectFit: "contain",
                  }}
                />
              ) : null}
            </div>
          ))}
        </div>
      </div>

      {/* Time Badge - Position and layout updated to match Figma (y=172) */}
      {remainingTime && (
        <div
          className="absolute flex justify-center items-center backdrop-blur-md"
          style={{
            backgroundColor: "rgba(0, 0, 0, 0.3)",
            borderRadius: "0 0 8px 8px",
            left: "0",
            right: "0",
            bottom: `0`, // Updated from bottom-0 to 172px from top
            width: `${resWidth(164)}px`,
            padding: `${resWidth(4)}px ${resWidth(8)}px`, // Updated to match Figma
            gap: `${resWidth(4)}px`, // Updated to match Figma
          }}
        >
          <span
            className="text-white font-semibold"
            style={{
              fontSize: "12px",
              lineHeight: "18px",
            }}
          >
            {remainingTime}
          </span>
        </div>
      )}
    </div>
  );
};

export default BrandedWheelCard;
