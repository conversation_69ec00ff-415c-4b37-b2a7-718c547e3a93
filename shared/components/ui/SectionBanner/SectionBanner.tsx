import React from 'react';
import { HomeConfig, SectionHeader, useHomeStore } from '@taptap/shared';
import { BannerCarousel } from '../BannerCarousel/BannerCarousel';
import type { BannerItem as CarouselBannerItem } from '../BannerCarousel/BannerCarousel';

interface SectionBannerProps {
  data: HomeConfig;
  sectionIndex: number;
  onBannerClick?: (bannerId: string, actionType: string, actionUrl: string) => void;
}

const SectionBanner: React.FC<SectionBannerProps> = ({ data, sectionIndex, onBannerClick }) => {
  // Get banners from the home store instead of the data prop
  const { banners: storeBanners, bannersLoading } = useHomeStore();
  
  const banners = storeBanners || [];

  // console.log('🎪 SectionBanner render:', {
  //   bannersCount: banners.length,
  //   banners: banners.map(b => ({
  //     id: b.id,
  //     title: b.title,
  //     actionType: b.actionType,
  //     actionUrl: b.actionUrl
  //   })),
  //   hasOnBannerClick: !!onBannerClick
  // });

  if (!banners || banners.length === 0) {
    //console.log('❌ No banners to display');
    return null;
  }

  // Convert home store banners to BannerCarousel format
  const carouselBanners: CarouselBannerItem[] = banners.map(banner => ({
    id: banner.id,
    imageUrl: banner.imageUrl,
    title: banner.title,
    subtitle: banner.subtitle,
    link: banner.actionUrl,
  }));

  // console.log('🎠 Carousel banners mapped:', carouselBanners);

  const handleBannerClick = (banner: CarouselBannerItem) => {
    console.log('🔥 SectionBanner handleBannerClick called:', {
      bannerId: banner.id,
      bannerTitle: banner.title,
      bannerLink: banner.link,
      hasOnBannerClick: !!onBannerClick,
      totalBanners: banners.length
    });

    // Find the original banner to get actionType
    const originalBanner = banners.find(b => b.id === banner.id);
    console.log('🔍 Original banner found:', originalBanner);

    if (originalBanner && banner.link) {
      if (onBannerClick) {
        console.log('📞 Calling onBannerClick callback:', {
          bannerId: banner.id,
          actionType: originalBanner.actionType,
          actionUrl: banner.link
        });
        // Pass to parent component to handle navigation
        onBannerClick(banner.id, originalBanner.actionType, banner.link);
      } else {
        console.log('❌ No onBannerClick callback provided - using fallback');
        // Fallback behavior for external links
        if (originalBanner.actionType === 'external') {
          console.log('🌐 Opening external link:', banner.link);
          window.open(banner.link, '_blank');
        }
      }
    } else {
      console.log('❌ Missing data:', {
        hasOriginalBanner: !!originalBanner,
        hasBannerLink: !!banner.link,
        bannerLink: banner.link,
        originalBanner: originalBanner
      });
    }
  };

  return (
    <div className="mb-4">
      {/* Section Header */}
      {data.isDisplayName && (
        <div className="px-4 pb-3">
          <SectionHeader title={data.name || 'Banner'} />
        </div>
      )}

      {/* Banner Carousel */}
      <BannerCarousel
        banners={carouselBanners}
        onBannerClick={handleBannerClick}
        className=""
      />
    </div>
  );
};

export default SectionBanner;