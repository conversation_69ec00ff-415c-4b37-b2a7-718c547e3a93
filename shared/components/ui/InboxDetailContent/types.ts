export type InboxDetailType = 'points' | 'voucher' | 'campaign';

export interface InboxDetailData {
  id: string;
  type: InboxDetailType;
  title: string;
  mainMessage: string;
  subMessage: string;
  actionText?: string;
  backgroundImage?: string;
  pointsEarned?: number;
  location?: string;
  campaignDetails?: string;
  rewards?: Array<{
    id: string;
    name: string;
    image: string;
    points?: number;
  }>;
  // Survey specific properties
  isSurvey?: boolean;
  formData?: any;
  surveyGifts?: Array<{
    giftTab: string;
    imageUrl: string;
  }>;
  ctaLink?: string;
  // Additional properties for enhanced functionality
  brandCurrencyCode?: string;
  brandCurrencyName?: string;
  brandCurrencyLogo?: string;
  brandCurrencyPoint?: string;
  vuiPoint?: number | string;
  voucherCodeIds?: string[];
  webviewUrl?: string;
}

export interface InboxDetailContentProps {
  data: InboxDetailData;
  onAction: () => void;
}