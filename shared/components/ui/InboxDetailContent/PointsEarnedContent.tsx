import React, { useEffect, useState } from 'react';
import { Button } from '../Button';
import { VUIPointsIcon } from '../VUIPointsIcon';
import { RewardThumbnailCard } from '../RewardThumbnailCard';
import { SectionHeader } from '../SectionHeader';
import { InboxDetailContentProps } from './types';
import { formatNumber } from '../../../utils';
import { rewardsAPI, type RewardItemType } from '../../../services/api/rewards';

interface ExtendedInboxDetailContentProps extends InboxDetailContentProps {
  vuiPoints?: number | string;
  brandCurrencyPoints?: string;
  brandCurrencyLogo?: string;
  brandCurrencyName?: string;
  brandCurrencyCode?: string;
  onViewVUIHistory?: () => void;
  onViewBrandHistory?: () => void;
}

export const PointsEarnedContent: React.FC<ExtendedInboxDetailContentProps> = ({ 
  data, 
  onAction,
  vuiPoints,
  brandCurrencyPoints,
  brandCurrencyLogo,
  brandCurrencyName,
  onViewVUIHistory,
  onViewBrandHistory,
}) => {
  const [rewardList, setRewardList] = useState<RewardItemType[]>([]);
  const [privateRewardList, setPrivateRewardList] = useState<RewardItemType[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch general rewards
  useEffect(() => {
    const fetchRewards = async () => {
      if (!vuiPoints || Number(vuiPoints) <= 0) return;
      
      try {
        setLoading(true);
        const response = await rewardsAPI.getRewardsByCollection({
          collectionCode: 'amazing',
          offset: 0,
          limit: 8,
        });
        
        if (response?.data?.rewards) {
          setRewardList(response.data.rewards);
        }
      } catch (error) {
        console.error('Failed to fetch rewards:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRewards();
  }, [vuiPoints]);

  // Fetch brand-specific rewards
  useEffect(() => {
    const fetchBrandRewards = async () => {
      if (!data.brandCurrencyCode || !brandCurrencyPoints || Number(brandCurrencyPoints) <= 0) return;
      
      try {
        const response = await rewardsAPI.getRewardsByBrandCurrency({
          brandCurrencyCode: data.brandCurrencyCode,
          offset: 0,
          limit: 8,
        });
        
        if (response?.data?.rewards) {
          setPrivateRewardList(response.data.rewards);
        }
      } catch (error) {
        console.error('Failed to fetch brand rewards:', error);
      }
    };

    fetchBrandRewards();
  }, [data.brandCurrencyCode, brandCurrencyPoints]);

  const vuiPointsNumber = Number(vuiPoints);
  const brandPointsNumber = Number(brandCurrencyPoints);

  // Don't render if no points
  if ((!vuiPointsNumber || vuiPointsNumber <= 0) && (!brandPointsNumber || brandPointsNumber <= 0)) {
    return null;
  }

  return (
    <div className="pb-4">
      {/* Title and Message */}
      <div className="px-4 py-6 text-center">
        <h1 className="text-[24px] font-[700] text-[#1A1818] mb-1 leading-[32px]" 
            style={{ fontFamily: 'Archia' }}>
          {data.title || 'Nhận điểm thưởng'}
        </h1>
        <p className="text-[16px] font-[400] text-[#1A1818] leading-[24px] mb-6" 
           style={{ fontFamily: 'Archia' }}>
          {data.mainMessage}
        </p>
      </div>

      {/* Currency Cards */}
      <div className="px-4 mb-6 flex gap-4 justify-center">
        {/* VUI Points Card */}
        {vuiPointsNumber > 0 && (
          <div className="bg-white rounded-xl p-4 shadow-lg flex-1 max-w-[160px]">
            <div className="text-center mb-4">
              <p className="text-[14px] font-[400] text-[#1A1818] leading-[20px] mb-2">
                Bạn vừa nhận{' '}
                <span className="font-bold text-green-600">
                  +{formatNumber(vuiPointsNumber)}
                </span>{' '}
                VUI
              </p>
            </div>
            
            <div className="flex justify-center mb-4 relative">
              <VUIPointsIcon size={48} />
              <div className="absolute -top-1 -right-1 bg-[#F65D79] text-white text-xs font-bold px-2 py-1 rounded-full min-w-[26px] h-[26px] flex items-center justify-center border-2 border-white">
                +{formatNumber(vuiPointsNumber, true)}
              </div>
            </div>
            
            <Button 
              onClick={onViewVUIHistory}
              className="w-full h-8 text-sm"
              size="sm"
            >
              Xem ngay
            </Button>
          </div>
        )}

        {/* Brand Currency Card */}
        {brandPointsNumber > 0 && brandCurrencyLogo && brandCurrencyName && (
          <div className="bg-white rounded-xl p-4 shadow-lg flex-1 max-w-[160px]">
            <div className="text-center mb-4">
              <p className="text-[14px] font-[400] text-[#1A1818] leading-[20px] mb-2">
                Bạn vừa nhận{' '}
                <span className="font-bold text-green-600">
                  +{formatNumber(brandPointsNumber)}
                </span>{' '}
                <span className="font-semibold">{brandCurrencyName}</span>
              </p>
            </div>
            
            <div className="flex justify-center mb-4 relative">
              <img 
                src={brandCurrencyLogo} 
                alt={brandCurrencyName}
                className="w-12 h-12 rounded-full"
              />
              <div className="absolute -top-1 -right-1 bg-[#F65D79] text-white text-xs font-bold px-2 py-1 rounded-full min-w-[26px] h-[26px] flex items-center justify-center border-2 border-white">
                +{formatNumber(brandPointsNumber, true)}
              </div>
            </div>
            
            <Button 
              onClick={onViewBrandHistory}
              className="w-full h-8 text-sm"
              size="sm"
            >
              Xem ngay
            </Button>
          </div>
        )}
      </div>

      {/* Private Rewards Section */}
      {privateRewardList.length > 0 && (
        <div className="mb-4">
          <SectionHeader 
            title="Quà riêng dành cho bạn"
            actionText="Tất cả"
            onActionClick={onAction}
            className="px-4"
          />
          <div className="px-4">
            <div className="flex gap-3 overflow-x-auto pb-2">
              {privateRewardList.map((reward) => (
                <div key={reward.id} className="flex-shrink-0">
                  <RewardThumbnailCard
                    id={reward.id}
                    name={reward.name}
                    description={reward.description}
                    image={reward.thumbnailImage}
                    vuiPoints={reward.issueVUIPoint}
                    brandPoints={reward.issueBrandCurrencyPoint}
                    brandLogo={reward.merchant?.brandCurrencyLogo}
                    originalPrice={reward.retailPrice}
                    discountPercent={reward.discount}
                    isFlashSale={reward.isFlashSale}
                    merchant={reward.merchant}
                    className="w-[278px]"
                    onClick={() => {}}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* General Rewards Section */}
      {rewardList.length > 0 && vuiPointsNumber > 0 && (
        <div>
          <SectionHeader 
            title="Đổi nhiều nhất"
            actionText="Tất cả"
            onActionClick={onAction}
            className="px-4"
          />
          <div className="px-4">
            <div className="flex gap-3 overflow-x-auto pb-2">
              {rewardList.map((reward) => (
                <div key={reward.id} className="flex-shrink-0">
                  <RewardThumbnailCard
                    id={reward.id}
                    name={reward.name}
                    description={reward.description}
                    image={reward.thumbnailImage}
                    vuiPoints={reward.issueVUIPoint}
                    brandPoints={reward.issueBrandCurrencyPoint}
                    brandLogo={reward.merchant?.brandCurrencyLogo}
                    originalPrice={reward.retailPrice}
                    discountPercent={reward.discount}
                    isFlashSale={reward.isFlashSale}
                    merchant={reward.merchant}
                    className="w-[278px]"
                    onClick={() => {}}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="px-4">
          <div className="flex gap-3 overflow-x-auto">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex-shrink-0 w-[278px] h-[200px] bg-gray-200 rounded-lg animate-pulse"></div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};