import React from 'react';
import { Button } from '../Button';
import { WebView } from '../WebView';
import { resizeImage } from '../../../utils';
import { InboxDetailContentProps } from './types';

interface ExtendedCampaignContentProps extends InboxDetailContentProps {
  webviewUrl?: string;
  showActionButton?: boolean;
  customActionText?: string;
}

export const CampaignDetailContent: React.FC<ExtendedCampaignContentProps> = ({ 
  data, 
  onAction,
  webviewUrl,
  showActionButton = true,
  customActionText,
}) => {
  // If webview URL is provided, render WebView
  if (webviewUrl) {
    return (
      <div className="h-full">
        <WebView 
          src={webviewUrl}
          className="w-full h-full min-h-[600px]"
        />
      </div>
    );
  }

  return (
    <>
      {/* Background/Thumbnail Section */}
      {data.backgroundImage ? (
        <div 
          className="h-[221px] bg-gradient-to-r from-[#EAB494] to-[#D4A574] flex items-center justify-center"
          style={{
            backgroundImage: `url(${resizeImage(data.backgroundImage, { width: 828, height: 442, quality: 85, fit: 'cover' })})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        >
          {/* Overlay for better text readability */}
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/10" />
        </div>
      ) : (
        // Default background when no image provided
        <div className="h-[221px] bg-gradient-to-r from-[#EAB494] to-[#D4A574] flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" className="text-white">
                <path 
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" 
                  fill="currentColor"
                />
              </svg>
            </div>
            <h2 className="text-white text-lg font-semibold">Thông báo đặc biệt</h2>
          </div>
        </div>
      )}

      {/* Content Section */}
      <div className="px-4 py-6">
        {/* Title */}
        <h1 className="text-[24px] font-[700] text-[#1A1818] text-center leading-[32px] mb-4" 
            style={{ fontFamily: 'Archia' }}>
          {data.title}
        </h1>

        {/* Message Content */}
        <div className="text-[14px] font-[400] text-[#1A1818] leading-[22px] whitespace-pre-line" 
             style={{ fontFamily: 'Archia' }}>
          {data.mainMessage}
        </div>

        {/* Additional details if provided */}
        {data.subMessage && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <p className="text-[14px] font-[400] text-[#666] leading-[22px]" 
               style={{ fontFamily: 'Archia' }}>
              {data.subMessage}
            </p>
          </div>
        )}

        {/* Campaign specific details */}
        {data.campaignDetails && (
          <div className="mt-4">
            <p className="text-[14px] font-[400] text-[#1A1818] leading-[22px] whitespace-pre-line" 
               style={{ fontFamily: 'Archia' }}>
              {data.campaignDetails}
            </p>
          </div>
        )}

        {/* Location info if provided */}
        {data.location && (
          <div className="mt-4 flex items-center gap-2 text-[14px] text-[#666]">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-[#F65D79]">
              <path 
                d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" 
                stroke="currentColor" 
                strokeWidth="2"
              />
              <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2" />
            </svg>
            <span style={{ fontFamily: 'Archia' }}>{data.location}</span>
          </div>
        )}

        {/* Bottom spacing for fixed button */}
        {showActionButton && <div className="pb-20" />}
      </div>

      {/* Bottom CTA Panel */}
      {showActionButton && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-[#ECECEC] px-4 py-4 shadow-lg">
          <Button 
            onClick={onAction}
            className="w-full h-11"
          >
            {customActionText || data.actionText || 'Xem ngay'}
          </Button>
        </div>
      )}
    </>
  );
};