import React from 'react';
import { But<PERSON> } from '../Button';
import { InboxDetailContentProps } from './types';

export const SurveyDetailContent: React.FC<InboxDetailContentProps> = ({ 
  data, 
  onAction 
}) => (
  <>
    {/* Title and Message */}
    <div className="px-4 py-8 text-center">
      <h1 className="text-[24px] font-[700] text-[#1A1818] mb-4 leading-[32px]" 
          style={{ fontFamily: 'Archia' }}>
        {data.title}
      </h1>
      <p className="text-[16px] font-[400] text-[#1A1818] leading-[24px] mb-6" 
         style={{ fontFamily: 'Archia' }}>
        {data.mainMessage}
      </p>
    </div>

    {/* Survey Gifts Section */}
    {data.surveyGifts && data.surveyGifts.length > 0 && (
      <div className="mx-4 mb-8 bg-white rounded-xl p-4 shadow-sm">
        <h2 className="text-[16px] font-[700] text-[#1A1818] mb-4 text-center" 
            style={{ fontFamily: 'Archia' }}>
          🎁 Phần thưởng khảo sát
        </h2>
        <div className="space-y-3">
          {data.surveyGifts.slice(0, 3).map((gift, index) => (
            <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
              <div className="w-12 h-12 rounded-full overflow-hidden bg-white flex-shrink-0">
                <img 
                  src={gift.imageUrl} 
                  alt={gift.giftTab}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="ml-3 flex-1">
                <p className="text-[14px] font-[600] text-[#0DC98B] leading-[20px]" 
                   style={{ fontFamily: 'Archia' }}>
                  {gift.giftTab}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}

    {/* Bottom CTA Panel */}
    <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-[#ECECEC] px-4 py-4 shadow-lg">
      <Button 
        onClick={onAction}
        className="w-full h-11"
      >
        Bắt đầu khảo sát
      </Button>
    </div>
  </>
);