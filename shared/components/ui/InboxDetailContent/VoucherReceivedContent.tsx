import React, { useEffect, useState } from "react";
import { Button } from "../Button";
import { MyRewardCard } from "../MyRewardCard";
import { LoadingSkeleton } from "../LoadingSkeleton";
import { InboxDetailContentProps } from "./types";
import {
  myRewardsAPI,
  type MyRewardItemType,
} from "../../../services/api/myrewards";

interface ExtendedVoucherContentProps extends InboxDetailContentProps {
  voucherCodeIds?: string[];
  hideQuantity?: boolean;
  onVoucherClick?: (voucher: MyRewardItemType) => void;
  onViewAllVouchers?: () => void;
}

export const VoucherReceivedContent: React.FC<ExtendedVoucherContentProps> = ({
  data,
  onAction,
  voucherCodeIds = [],
  hideQuantity = false,
  onVoucherClick,
  onViewAllVouchers,
}) => {
  const [voucherList, setVoucherList] = useState<MyRewardItemType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchVouchers = async () => {
      if (!voucherCodeIds || voucherCodeIds.length === 0) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await myRewardsAPI.getMyRewardsByCodeIds({
          codeIds: voucherCodeIds,
        });

        if (response?.data) {
          setVoucherList(response.data);
        }
      } catch (error) {
        console.error("Failed to fetch vouchers:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchVouchers();
  }, [voucherCodeIds]);

  const handleVoucherClick = (voucher: MyRewardItemType) => {
    if (onVoucherClick) {
      onVoucherClick(voucher);
    }
  };

  const handleActionClick = () => {
    if (onViewAllVouchers) {
      onViewAllVouchers();
    } else {
      onAction();
    }
  };

  // Don't render if no vouchers and not loading
  if (!loading && (!voucherList || voucherList.length === 0)) {
    return null;
  }

  return (
    <div className="pb-4">
      {/* Title and Message */}
      <div className="px-4 py-6 text-center">
        <h1
          className="text-[24px] font-[700] text-[#1A1818] mb-1 leading-[32px]"
          style={{ fontFamily: "Archia" }}
        >
          {data.title || "Nhận voucher"}
        </h1>
        <p
          className="text-[16px] font-[400] text-[#1A1818] leading-[24px] mb-6"
          style={{ fontFamily: "Archia" }}
        >
          {data.mainMessage}
        </p>
      </div>

      {/* Voucher Cards Container */}
      <div className="px-4 mb-6">
        <div className="bg-white rounded-xl p-6 shadow-lg">
          {loading ? (
            // Loading State
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className={index < 2 ? "mb-3" : ""}>
                  <LoadingSkeleton className="h-[80px] w-full rounded-lg" />
                </div>
              ))}
            </div>
          ) : (
            <>
              {/* Voucher Count */}
              {!hideQuantity && voucherList.length > 0 && (
                <div className="text-center mb-3">
                  <p className="text-[14px] font-[600] text-[#1A1818]">
                    <span className="font-bold text-green-600">
                      +{voucherList.length}
                    </span>{" "}
                    voucher
                  </p>
                </div>
              )}

              {/* Voucher List */}
              <div className="space-y-3">
                {voucherList.map((voucher, index) => (
                  <div key={voucher.codeId}>
                    <MyRewardCard
                      id={voucher.codeId}
                      name={voucher.name}
                      description={voucher.description}
                      image={voucher.thumbnailImage}
                      merchantName={voucher.merchantName}
                      merchantLogo={voucher.merchantLogo}
                      expiryDate={voucher.expiredDate}
                      status={voucher.status}
                      type="short"
                      onClick={() => handleVoucherClick(voucher)}
                    />
                    {index < voucherList.length - 1 && <div className="h-3" />}
                  </div>
                ))}
              </div>

              {/* View All Button */}
              {voucherList.length > 0 && (
                <div className="mt-6 flex justify-center">
                  <Button
                    onClick={handleActionClick}
                    className="w-[85px] h-8 text-sm"
                    size="sm"
                  >
                    Xem ngay
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
