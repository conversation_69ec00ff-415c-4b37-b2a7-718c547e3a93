import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import ChallengeSectionNotReceiveGift from './ChallengeSectionNotReceiveGift';

const meta: Meta<typeof ChallengeSectionNotReceiveGift> = {
  title: 'Components/Challenge/ChallengeSectionNotReceiveGift',
  component: ChallengeSectionNotReceiveGift,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    onChallengeClick: { action: 'challenge clicked' },
    onReceiveGiftSuccess: { action: 'receive gift success' },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const mockChallenges = [
  {
    _id: '1',
    displayChallengeName: 'Hoàn thành mua sắm tuần này',
    challengeImage: 'https://via.placeholder.com/60x60/F59E0B/FFFFFF?text=🎁',
    userProgress: {
      state: 'NOT_RECEIVED_YET',
    },
  },
  {
    _id: '2',
    displayChallengeName: '<PERSON><PERSON><PERSON> thách khám phá thương hiệu mới',
    challengeImage: 'https://via.placeholder.com/60x60/EF4444/FFFFFF?text=🏆',
    userProgress: {
      state: 'NOT_RECEIVED_YET',
    },
  },
  {
    _id: '3',
    displayChallengeName: 'Nhiệm vụ hàng ngày - Tuần 1',
    challengeImage: 'https://via.placeholder.com/60x60/8B5CF6/FFFFFF?text=⭐',
    userProgress: {
      state: 'NOT_RECEIVED_YET',
    },
  },
];

export const Default: Story = {
  args: {
    data: mockChallenges,
  },
};

export const SingleChallenge: Story = {
  args: {
    data: [mockChallenges[0]],
  },
};

export const Empty: Story = {
  args: {
    data: [],
  },
};

export const WithoutImages: Story = {
  args: {
    data: mockChallenges.map(challenge => ({
      ...challenge,
      challengeImage: undefined,
    })),
  },
};

export const LongNames: Story = {
  args: {
    data: [
      {
        _id: '1',
        displayChallengeName: 'Hoàn thành mua sắm tại tất cả các thương hiệu đối tác trong tuần này để nhận phần quà cực kỳ hấp dẫn',
        challengeImage: 'https://via.placeholder.com/60x60/F59E0B/FFFFFF?text=🎁',
        userProgress: {
          state: 'NOT_RECEIVED_YET',
        },
      },
      {
        _id: '2',
        displayChallengeName: 'Thử thách khám phá thương hiệu mới và trải nghiệm dịch vụ tuyệt vời',
        challengeImage: 'https://via.placeholder.com/60x60/EF4444/FFFFFF?text=🏆',
        userProgress: {
          state: 'NOT_RECEIVED_YET',
        },
      },
    ],
  },
};