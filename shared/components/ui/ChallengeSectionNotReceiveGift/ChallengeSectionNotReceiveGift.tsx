import React from 'react';
import { SectionHeader } from '../SectionHeader';

interface Challenge {
  _id: string;
  displayChallengeName: string;
  challengeImage?: string;
  userProgress?: {
    state: string;
  };
  [key: string]: any;
}

interface ChallengeSectionNotReceiveGiftProps {
  data: Challenge[];
  onChallengeClick?: (challengeId: string, challenge: Challenge, index: number) => void;
  onReceiveGiftSuccess?: () => void;
}

const ChallengeSectionNotReceiveGift: React.FC<ChallengeSectionNotReceiveGiftProps> = ({
  data,
  onChallengeClick,
  onReceiveGiftSuccess,
}) => {
  if (data.length === 0) return null;

  const handleChallengePress = (challenge: Challenge, index: number) => {
    onChallengeClick?.(challenge._id, challenge, index);
  };

  return (
    <div className="bg-white pb-1.5">
      <SectionHeader
        title="Nhận quà"
        className="px-4 pb-0"
      />
      <div className="px-4 space-y-2.5">
        {data.map((challenge, index) => (
          <div key={challenge._id} data-testid="challenge-card">
            <button
              onClick={() => handleChallengePress(challenge, index)}
              className="w-full bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4 text-left border border-orange-100"
            >
              <div className="flex items-center space-x-3">
                {challenge.challengeImage && (
                  <img
                    src={challenge.challengeImage}
                    alt={challenge.displayChallengeName}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                )}
                <div className="flex-1">
                  <h3 className="font-medium text-sm text-gray-900">
                    {challenge.displayChallengeName}
                  </h3>
                  <p className="text-xs text-orange-600 mt-1 font-medium">
                    Đã hoàn thành - Nhận quà ngay
                  </p>
                </div>
                <div className="text-orange-500">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChallengeSectionNotReceiveGift;