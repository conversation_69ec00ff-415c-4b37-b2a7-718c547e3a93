import React from 'react';

// Types matching mobile implementation
export enum EVoucherStatus {
  ACTIVE = 1,
  INACTIVE = 2,
  DONE = 3,
}

export interface MyRewardType {
  [key: string]: any; // Simplified for web version
}

interface SectionAutoRedeemProps {
  timeRedeemAuto: number;
  codeId: string;
  code: string;
  executeAutoRedeemDate: string | null | undefined;
  setDetail: (val: MyRewardType) => void;
  paramsTracking: {
    merchant_code: string;
    reward_name: string;
  };
  status: EVoucherStatus | undefined;
  endTime: string | undefined;
  rewardName: string;
  className?: string;
  onCopySuccess?: (message: string) => void;
  onCopyError?: (message: string) => void;
}

const SectionAutoRedeem: React.FC<SectionAutoRedeemProps> = ({
  timeRedeemAuto,
  codeId,
  code,
  executeAutoRedeemDate,
  paramsTracking,
  setDetail,
  status,
  endTime,
  rewardName,
  className = '',
  onCopySuccess,
  onCopyError,
}) => {
  return (
    <div className={className}>
      {executeAutoRedeemDate ? (
        <SectionCountDownReward
          codeId={codeId}
          code={code}
          status={status}
          executeAutoRedeemDate={executeAutoRedeemDate}
          rewardName={rewardName}
          onCopySuccess={onCopySuccess}
          onCopyError={onCopyError}
        />
      ) : (
        <SectionWarningUseVoucher
          timeRedeemAuto={timeRedeemAuto}
          codeId={codeId}
          setRewardDetail={setDetail}
          paramsTracking={paramsTracking}
          endTime={endTime}
        />
      )}
    </div>
  );
};

// SectionWarningUseVoucher Component
interface SectionWarningUseVoucherProps {
  timeRedeemAuto: number;
  codeId: string;
  setRewardDetail: (reward: MyRewardType) => void;
  paramsTracking: {
    merchant_code: string;
    reward_name: string;
  };
  endTime: string | undefined;
}

const SectionWarningUseVoucher: React.FC<SectionWarningUseVoucherProps> = ({
  timeRedeemAuto,
  codeId,
  paramsTracking,
  endTime,
  setRewardDetail,
}) => {
  const isExpire = endTime ? new Date() > new Date(endTime) : false;

  const showBottomWarning = () => {
    // For web, we'll use a simple confirm dialog instead of bottom modal
    // In a real app, you'd want to use a proper modal component
    const confirmed = window.confirm(
      `Cần dùng ưu đãi ngay?\n\nLưu ý:\n1. Chỉ nên chọn "Dùng ngay" khi bạn đang ở bước thanh toán.\n2. Mã ưu đãi chỉ hiển thị trong ${timeRedeemAuto} phút. Sau đó, ưu đãi sẽ được đánh dấu "Đã dùng" và không thể khôi phục lại.\n\nBạn có muốn tiếp tục không?`
    );
    
    if (confirmed) {
      // Here you would call the API to activate auto redeem
      // For now, just simulate the response
      console.log('Auto redeem activated for codeId:', codeId);
      // setRewardDetail with updated data
    }
  };

  return (
    <>
      <div className="mb-4">
        <p className="text-sm text-gray-600 mb-4">
          Chọn "Dùng ngay" để hiện mã ưu đãi.
        </p>
        
        {/* Warning Container */}
        <div className="bg-orange-50 rounded-lg p-3 flex items-start space-x-3 mb-4">
          {/* Warning Icon */}
          <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none">
              <path
                d="M10 6V10M10 14H10.01M19 10C19 14.9706 14.9706 19 10 19C5.02944 19 1 14.9706 1 10C1 5.02944 5.02944 1 10 1C14.9706 1 19 5.02944 19 10Z"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          
          {/* Warning Text */}
          <div className="flex-1">
            <p className="text-sm text-gray-700">
              Ưu đãi sẽ được đánh dấu{' '}
              <span className="font-semibold">"Đã dùng"</span>{' '}
              sau{' '}
              <span className="font-semibold">
                {timeRedeemAuto} phút
              </span>{' '}
              kể từ khi chọn:
            </p>
          </div>
        </div>
        
        {/* Use Now Button */}
        <button
          onClick={showBottomWarning}
          disabled={isExpire}
          className={`w-full px-4 py-3 rounded-lg font-medium text-sm transition-colors ${
            isExpire
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-[#F65D79] text-white hover:bg-[#e54d6a]'
          }`}
        >
          Dùng ngay
        </button>
      </div>
    </>
  );
};

// SectionCountDownReward Component  
interface SectionCountDownRewardProps {
  executeAutoRedeemDate: string;
  codeId: string;
  code: string;
  status: EVoucherStatus | undefined;
  rewardName: string;
  onCopySuccess?: (message: string) => void;
  onCopyError?: (message: string) => void;
}

const SectionCountDownReward: React.FC<SectionCountDownRewardProps> = ({
  executeAutoRedeemDate,
  codeId,
  status,
  code,
  rewardName,
  onCopySuccess,
  onCopyError,
}) => {
  const [secondsRemaining, setSecondsRemaining] = React.useState(0);
  const [isUsed, setIsUsed] = React.useState(false);

  // Countdown hook effect
  React.useEffect(() => {
    const isExpire = new Date() > new Date(executeAutoRedeemDate);
    const isExpireOrUsed = status === EVoucherStatus.DONE || isExpire;
    
    if (isExpireOrUsed) {
      setIsUsed(true);
      return;
    }

    const interval = setInterval(() => {
      const now = new Date().getTime();
      const expireTime = new Date(executeAutoRedeemDate).getTime();
      const diff = Math.max(0, expireTime - now);
      
      if (diff <= 0) {
        setIsUsed(true);
        clearInterval(interval);
      } else {
        setSecondsRemaining(Math.floor(diff / 1000));
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [executeAutoRedeemDate, status]);

  // Format time display
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600).toString().padStart(2, '0');
    const minutes = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return { hours, minutes, secs };
  };

  const { hours, minutes, secs } = formatTime(secondsRemaining);

  const onPressCopy = () => {
    navigator.clipboard.writeText(code)
      .then(() => {
        if (onCopySuccess) {
          onCopySuccess('Đã sao chép mã!');
        } else {
          alert('Đã sao chép mã!');
        }
      })
      .catch(() => {
        if (onCopyError) {
          onCopyError('Không thể sao chép mã');
        } else {
          alert('Không thể sao chép mã');
        }
      });
  };

  // If voucher is used or expired
  if (isUsed || status === EVoucherStatus.DONE) {
    return (
      <div className="py-4">
        {/* Divider */}
        <div className="w-full mb-8">
          <svg width="100%" height="1" className="overflow-visible">
            <line x1="0" x2="100%" y1="0.5" y2="0.5" stroke="#E5E5E5" strokeWidth="1" />
          </svg>
        </div>
        
        {/* Expired Container */}
        <div className="py-12">
          <div className="bg-green-50 rounded-lg h-12 mx-6 flex items-center justify-center">
            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
              <svg width="16" height="16" viewBox="0 0 20 20" fill="none">
                <path
                  d="M7 10L9 12L13 8M19 10C19 14.9706 14.9706 19 10 19C5.02944 19 1 14.9706 1 10C1 5.02944 5.02944 1 10 1C14.9706 1 19 5.02944 19 10Z"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <span className="text-sm font-medium text-gray-700">Đã dùng</span>
          </div>
          
          <p className="text-sm text-gray-600 text-center px-6 mt-3">
            Ưu đãi đã được chuyển sang trạng thái "Đã dùng" và không thể khôi phục.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="py-4">
      {/* Countdown Warning */}
      <div className="bg-orange-50 rounded-lg p-3 flex items-center space-x-3 mb-3">
        <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
          <svg width="16" height="16" viewBox="0 0 20 20" fill="none">
            <path
              d="M10 6V10M10 14H10.01M19 10C19 14.9706 14.9706 19 10 19C5.02944 19 1 14.9706 1 10C1 5.02944 5.02944 1 10 1C14.9706 1 19 5.02944 19 10Z"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
        
        <span className="text-sm font-medium text-gray-700">
          Thời gian sử dụng:
        </span>
        
        <span className="text-orange-600 font-bold text-base">
          {hours}:{minutes}:{secs}
        </span>
      </div>

      {/* Content */}
      <p className="text-sm text-gray-600 text-center py-3">
        *Ưu đãi sẽ được đánh dấu "Đã dùng" và không thể khôi phục sau thời gian trên.
      </p>

      {/* QR Code/Barcode */}
      <div className="bg-white border rounded-lg p-6 text-center mb-4">
        {/* Simple barcode representation */}
        <div className="flex justify-center mb-4">
          <div className="bg-black h-16 w-1 mx-px"></div>
          <div className="bg-black h-16 w-0.5 mx-px"></div>
          <div className="bg-black h-16 w-1.5 mx-px"></div>
          <div className="bg-black h-16 w-0.5 mx-px"></div>
          <div className="bg-black h-16 w-2 mx-px"></div>
          <div className="bg-black h-16 w-1 mx-px"></div>
          <div className="bg-black h-16 w-0.5 mx-px"></div>
          <div className="bg-black h-16 w-1.5 mx-px"></div>
          <div className="bg-black h-16 w-1 mx-px"></div>
          <div className="bg-black h-16 w-2 mx-px"></div>
          <div className="bg-black h-16 w-0.5 mx-px"></div>
          <div className="bg-black h-16 w-1 mx-px"></div>
        </div>
        
        <p className="text-lg font-bold tracking-wider mb-2">{code}</p>
      </div>

      {/* Copy Button */}
      <div className="flex justify-center">
        <button
          onClick={onPressCopy}
          className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
        >
          Sao chép mã
        </button>
      </div>
    </div>
  );
};

export default SectionAutoRedeem;