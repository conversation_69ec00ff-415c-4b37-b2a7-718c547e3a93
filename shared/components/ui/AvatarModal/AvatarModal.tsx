import React, { useState, useEffect } from 'react';
import { avatarAPI, type AvatarCategory, type Avatar } from '../../../services/api/avatar';
import { AvatarList } from '../AvatarList';
import { useAuthStore } from '../../../stores/authStore';
import { resizeImage } from '../../../utils';
import { Dialog } from '../Dialog';
import './styles.css';

interface AvatarModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAvatarChanged: (avatar: string) => void;
  onError?: (message: string) => void;
}

export const AvatarModal: React.FC<AvatarModalProps> = ({
  isOpen,
  onClose,
  onAvatarChanged,
  onError
}) => {
  const [categories, setCategories] = useState<AvatarCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<AvatarCategory | null>(null);
  const [selectedAvatar, setSelectedAvatar] = useState<Avatar | null>(null);
  const [avatars, setAvatars] = useState<Avatar[]>([]);
  const [loading, setLoading] = useState(false);
  const [categoryLoading, setCategoryLoading] = useState(false);
  const [error, setError] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const { profile } = useAuthStore();

  useEffect(() => {
    if (isOpen) {
      fetchCategories();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedCategory) {
      fetchAvatars(selectedCategory.id, selectedCategory.isDefault);
    }
  }, [selectedCategory]);

  const fetchCategories = async () => {
    try {
      setCategoryLoading(true);
      setError(false);
      const response = await avatarAPI.getAvatarCategories();
      const data = response?.data || [];
      setCategories(data);
      if (data.length > 0) {
        setSelectedCategory(data[0]);
      }
    } catch (err) {
      console.error('Failed to fetch avatar categories:', err);
      setError(true);
    } finally {
      setCategoryLoading(false);
    }
  };

  const fetchAvatars = async (categoryId: string, isMyAvatar?: boolean) => {
    try {
      setLoading(true);
      const mobile = profile?.mobile;
      const response = await avatarAPI.getAvatarsByCategory(categoryId, isMyAvatar, mobile);
      const data = response?.data || [];
      setAvatars(data);
    } catch (err) {
      console.error('Failed to fetch avatars:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAvatar = (avatar: Avatar) => {
    setSelectedAvatar(avatar);
  };

  const handleConfirm = async () => {
    if (!selectedAvatar) return;

    const loyaltyPoint = profile?.loyaltyPoint || 0;
    
    // Check if need to buy
    if (!selectedAvatar.enable && selectedAvatar.point > 0) {
      if (loyaltyPoint < selectedAvatar.point) {
        if (onError) {
          onError('Bạn không đủ điểm VUI để đổi avatar này');
        } else {
          alert('Bạn không đủ điểm VUI để đổi avatar này');
        }
        return;
      }
      
      setShowConfirmDialog(true);
      return;
    }

    // Direct use/change for enabled avatars
    await performAvatarChange();
  };

  const performAvatarChange = async () => {
    if (!selectedAvatar) return;
    
    try {
      setLoading(true);
      const response = await avatarAPI.buyOrUseAvatar(selectedAvatar.id);
      const result = response?.data;
      if (result) {
        onAvatarChanged(selectedAvatar.avatarImage);
        onClose();
      }
    } catch (err) {
      console.error('Failed to change avatar:', err);
      if (onError) {
        onError('Có lỗi xảy ra khi đổi avatar');
      } else {
        alert('Có lỗi xảy ra khi đổi avatar');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmPurchase = async () => {
    setShowConfirmDialog(false);
    await performAvatarChange();
  };

  const handleCancelPurchase = () => {
    setShowConfirmDialog(false);
  };

  if (!isOpen) return null;

  const getButtonText = () => {
    if (!selectedAvatar) return 'Chọn avatar';
    if (selectedAvatar.enable || selectedAvatar.point === 0) {
      return 'Đổi avatar';
    }
    return `Đổi với ${selectedAvatar.point} VUI`;
  };

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 z-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md z-50 bg-gray-100 rounded-t-[30px] animate-slide-up max-h-[80vh] flex flex-col">
        {/* Close area */}
        <div 
          className="h-[20vh] absolute -top-[20vh] w-full"
          onClick={onClose}
        />

        {/* Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Avatar List */}
          <div className="flex-1 overflow-y-auto bg-gray-100 px-5 pt-6">
            {error ? (
              <div className="flex flex-col items-center justify-center py-16">
                <div className="w-20 h-32 mb-3 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-4xl">😔</span>
                </div>
                <p className="text-gray-600 text-sm">Có lỗi xảy ra</p>
              </div>
            ) : (
              <AvatarList
                avatars={avatars}
                selectedAvatar={selectedAvatar}
                onSelectAvatar={handleSelectAvatar}
                loading={loading}
                isMyAvatar={selectedCategory?.isDefault}
              />
            )}
          </div>

          {/* Bottom section with tabs and buttons */}
          <div className="bg-white shadow-[0_-2px_8px_rgba(0,0,0,0.1)] px-5 pt-4 pb-4">
            {/* Category tabs */}
            <div className="flex gap-7 overflow-x-auto pb-4 mb-4 scrollbar-hide">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category)}
                  className="flex-shrink-0"
                >
                  <div className="w-12 h-12 flex items-center justify-center">
                    <img
                      src={resizeImage(category.image, { width: 96, height: 96, quality: 85, fit: 'cover' })}
                      alt={category.name}
                      className={`rounded-full transition-all ${
                        selectedCategory?.id === category.id
                          ? 'w-12 h-12 ring-1 ring-gray-800 bg-gray-200'
                          : 'w-10 h-10'
                      }`}
                    />
                  </div>
                </button>
              ))}
            </div>

            {/* Action buttons */}
            <div className="flex gap-4">
              <button
                onClick={handleConfirm}
                disabled={!selectedAvatar || loading}
                className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
                  selectedAvatar && !loading
                    ? 'bg-[#F65D79] text-white hover:bg-[#F65D79]/90'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {getButtonText()}
              </button>
              <button
                onClick={onClose}
                className="flex-1 py-3 px-4 rounded-lg font-medium border-2 border-[#F65D79] text-[#F65D79] hover:bg-[#F65D79]/10"
              >
                Hủy
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Dialog
        isOpen={showConfirmDialog}
        onClose={handleCancelPurchase}
        title="Xác nhận đổi avatar"
        content={`Bạn có chắc chắn muốn đổi avatar này với ${selectedAvatar?.point || 0} điểm VUI?`}
        confirmText="Đồng ý"
        cancelText="Hủy"
        onConfirm={handleConfirmPurchase}
        confirmLoading={loading}
      />
    </>
  );
};