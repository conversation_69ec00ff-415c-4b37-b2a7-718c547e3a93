# HowToEarn Component

Component web độc lập để hiển thị các phương thức kiếm điểm từ merchant. Component này được chuyển đổi từ React Native sang React web và có thể sử dụng ở bất kỳ dự án web nào.

## Tính năng

- ✅ Hiển thị danh sách các phương thức kiếm điểm
- ✅ Hỗ trợ nhiều loại phương thức: quét mã vạch, chụp hóa đơn, mua online
- ✅ Hiển thị thông tin cửa hàng gần đây (khi có)
- ✅ Responsive design
- ✅ Không phụ thuộc vào thư viện bên ngoài
- ✅ Có thể tái sử dụng ở bất kỳ dự án nào

## Cài đặt

Component này không cần cài đặt dependencies. Chỉ cần copy file `HowToEarn.tsx` vào dự án của bạn.

## Sử dụng

### Import component

```tsx
import HowToEarn from './HowToEarn';
```

### Sử dụng cơ bản

```tsx
import React from 'react';
import HowToEarn from './HowToEarn';

const App = () => {
  const methodList = [
    {
      id: '1',
      code: 'BARCODE',
      name: 'Quét mã vạch',
      description: 'Quét mã vạch sản phẩm để kiếm điểm',
      image: 'https://example.com/barcode.jpg',
      enable: true,
      status: true,
      directButton: {
        image: 'https://example.com/icon.png',
        label: 'Quét mã vạch',
        pageCode: 'barcode_scanner',
      },
    },
  ];

  const earnModels = [
    {
      earnMethodCode: 'BARCODE',
      earnMethodName: 'Quét mã vạch',
      description: '<p>Mô tả chi tiết về phương thức</p>',
      image: 'https://example.com/method.jpg',
    },
  ];

  return (
    <HowToEarn
      methodList={methodList}
      earnModels={earnModels}
      merchantCode="MERCHANT_001"
      isUsingBC={false}
      logo="https://example.com/logo.png"
      numberOfStores={10}
      onLayout={e => console.log('Layout:', e)}
      openSnapConditions={() => alert('Mở điều kiện')}
    />
  );
};

export default App;
```

## Props

### SectionEarnProps

| Prop                 | Type               | Required | Description                          |
| -------------------- | ------------------ | -------- | ------------------------------------ |
| `methodList`         | `EarnMethodType[]` | ✅       | Danh sách các phương thức kiếm điểm  |
| `earnModels`         | `EarnModel[]`      | ✅       | Mô hình kiếm điểm chi tiết           |
| `merchantCode`       | `string`           | ✅       | Mã merchant                          |
| `isUsingBC`          | `boolean`          | ✅       | Có đang sử dụng brand currency không |
| `logo`               | `string`           | ✅       | Logo của merchant                    |
| `numberOfStores`     | `number`           | ✅       | Số lượng cửa hàng                    |
| `onLayout`           | `(e: any) => void` | ❌       | Callback khi layout thay đổi         |
| `openSnapConditions` | `() => void`       | ❌       | Callback mở điều kiện chụp hóa đơn   |

### EarnMethodType

```tsx
interface EarnMethodType {
  id: string;
  code: string;
  name: string;
  description: string;
  index: number;
  image: string;
  enable: boolean;
  status: boolean;
  steps: {
    description: string;
    image: string;
    index: number;
    title: string;
  }[];
  directButton: {
    deepLink?: string;
    image: string;
    label: string;
    pageCode: string;
    sections?: Record<string, unknown>;
  };
  subMerchants: any[];
}
```

### EarnModel

```tsx
interface EarnModel {
  additionRate?: string;
  description?: string;
  earnMethodCode: string;
  earnMethodName?: string;
  image: string;
}
```

## Các phương thức được hỗ trợ

- **BARCODE**: Quét mã vạch sản phẩm
- **BILL_SCAN**: Chụp hóa đơn mua hàng
- **ONLINE**: Mua hàng online
- **GIFT_CARD**: Quét thẻ quà tặng
- **SCAN_QR**: Quét mã QR
- **INPUT_CODE**: Nhập mã thủ công
- **POINT_CONVERSION**: Chuyển đổi điểm

## Tùy chỉnh

Component sử dụng inline styles và có thể dễ dàng tùy chỉnh thông qua CSS hoặc thay đổi trực tiếp trong code.

### Thay đổi màu sắc

```tsx
// Trong component Button
const getButtonStyles = () => {
  const baseStyles = {
    // ... base styles
  };

  switch (variant) {
    case 'primary':
      return {
        ...baseStyles,
        backgroundColor: '#your-color', // Thay đổi màu ở đây
        color: '#ffffff',
      };
    // ...
  }
};
```

### Thay đổi layout

```tsx
// Trong component Section
<div
  style={{
    backgroundColor,
    marginTop,
    paddingBottom,
    borderRadius: 8, // Thay đổi border radius
    padding: 16,    // Thay đổi padding
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)', // Thay đổi shadow
  }}
>
```

## Demo

Mở file `demo.html` trong trình duyệt để xem component hoạt động với dữ liệu mẫu.

## Chuyển đổi từ React Native

Component này được chuyển đổi từ React Native với những thay đổi chính:

- `View` → `div`
- `StyleSheet` → inline styles
- `TouchableOpacity` → `button`
- `Text` → `div` với styles
- `Image` → `img`
- `ScrollView` → `div` với overflow
- `Linking.openURL` → `window.open`

## Lưu ý

- Component sử dụng `dangerouslySetInnerHTML` để render HTML content, hãy đảm bảo content được sanitize
- Các callback như `onPress`, `openSnapConditions` cần được implement theo logic của dự án
- Component có thể cần điều chỉnh để phù hợp với design system của dự án

## Hỗ trợ

Nếu có vấn đề hoặc cần hỗ trợ, hãy kiểm tra:

1. Props được truyền đúng format
2. Dữ liệu `methodList` và `earnModels` có đúng cấu trúc
3. Console log để debug các callback functions
