import React, { FC, ReactNode } from "react";
import { convertDeeplinkToUrl } from "../../../utils/deeplink";
import { StoreNearby } from "../StoreNearby";

// Types
export interface EarnMethodType {
  id: string;
  code: string;
  name: string;
  description: string;
  index: number;
  image: string;
  enable: boolean;
  status: boolean;
  steps: {
    description: string;
    image: string;
    index: number;
    title: string;
  }[];
  directButton: {
    deepLink?: string;
    image: string;
    label: string;
    pageCode: string;
    sections?: Record<string, unknown>;
  };
  subMerchants: any[];
}

export interface EarnModel {
  additionRate?: string;
  description?: string;
  earnMethodCode: string;
  earnMethodName?: string;
  image: string;
}

export interface SectionEarnProps {
  methodList: EarnMethodType[];
  earnModels: EarnModel[];
  merchantCode: string;
  isUsingBC: boolean;
  openSnapConditions?: () => void;
  logo: string;
  numberOfStores: number;
  onNavigate?: (url: string) => void;
}

// Constants
const METHOD_CODES = {
  BARCODE: "barcode",
  BILL_SCAN: "bill_scan",
  ONLINE: "online",
} as const;

// Utility functions
const isArray = (value: any): value is any[] => {
  return Array.isArray(value);
};

const isEmpty = (value: any): boolean => {
  if (value == null) return true;
  if (typeof value === "string") return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === "object") return Object.keys(value).length === 0;
  return false;
};

// Section Component
interface SectionProps {
  title: string;
  backgroundColor?: string;
  marginTop?: number;
  paddingBottom?: number;
  children: ReactNode;
}

const Section: FC<SectionProps> = ({
  title,
  backgroundColor = "#ffffff",
  marginTop = 0,
  paddingBottom = 16,
  children,
}) => {
  return (
    <div
      style={{
        backgroundColor,
        marginTop,
        paddingBottom,
        borderRadius: 0,
        padding: 16,
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
      }}
    >
      {title && (
        <h3
          style={{
            margin: "0 0 16px 0",
            fontSize: "18px",
            fontWeight: 600,
            color: "#333",
          }}
        >
          {title}
        </h3>
      )}
      {children}
    </div>
  );
};

// Button Component
interface ButtonProps {
  children: ReactNode;
  onPress: () => void;
  containerStyle?: React.CSSProperties;
  variant?: "primary" | "secondary" | "tertiary";
}

const Button: FC<ButtonProps> = ({
  children,
  onPress,
  containerStyle,
  variant = "primary",
}) => {
  const getButtonStyles = () => {
    const baseStyles: React.CSSProperties = {
      border: "none",
      borderRadius: 8,
      padding: "12px 24px",
      fontSize: "14px",
      fontWeight: 600,
      cursor: "pointer",
      width: "100%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      gap: "8px",
      transition: "all 0.2s ease",
      ...containerStyle,
    };

    switch (variant) {
      case "primary":
        return {
          ...baseStyles,
          backgroundColor: "#F65D79",
          color: "#ffffff",
        };
      case "secondary":
        return {
          ...baseStyles,
          backgroundColor: "#6c757d",
          color: "#ffffff",
        };
      case "tertiary":
        return {
          ...baseStyles,
          backgroundColor: "transparent",
          color: "#F65D79",
          border: "1px solid #F65D79",
        };
      default:
        return baseStyles;
    }
  };

  return (
    <button style={getButtonStyles()} onClick={onPress}>
      {children}
    </button>
  );
};

// Icon Components
const Icon20px = {
  OutlineWebsiteLink: ({ fill }: { fill: string }) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M10 2C5.58 2 2 5.58 2 10C2 14.42 5.58 18 10 18C14.42 18 18 14.42 18 10C18 5.58 14.42 2 10 2ZM10 16C6.69 16 4 13.31 4 10C4 6.69 6.69 4 10 4C13.31 4 16 6.69 16 10C16 13.31 13.31 16 10 16Z"
        fill={fill}
      />
      <path
        d="M10 6C7.79 6 6 7.79 6 10C6 12.21 7.79 14 10 14C12.21 14 14 12.21 14 10C14 7.79 12.21 6 10 6ZM10 12C8.9 12 8 11.1 8 10C8 8.9 8.9 8 10 8C11.1 8 12 8.9 12 10C12 11.1 11.1 12 10 12Z"
        fill={fill}
      />
    </svg>
  ),
  OutlineCall: ({ fill }: { fill: string }) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M20 15.5C18.75 15.5 17.55 15.3 16.43 14.93C16.08 14.82 15.69 14.9 15.41 15.18L13.21 17.38C10.38 15.94 8.06 13.62 6.62 10.79L8.82 8.59C9.1 8.31 9.18 7.92 9.07 7.57C8.7 6.45 8.5 5.25 8.5 4C8.5 3.45 8.05 3 7.5 3H4C3.45 3 3 3.45 3 4C3 11.55 8.45 17 16 17C16.55 17 17 16.55 17 16V12.5C17 11.95 16.55 11.5 16 11.5H20Z"
        fill={fill}
      />
    </svg>
  ),
  OutlineMap: () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M18.5 2L12.5 6L6.5 2L1.5 4V18L6.5 20L12.5 16L18.5 20V6L18.5 2Z"
        stroke="#333"
        strokeWidth="2"
        fill="none"
      />
    </svg>
  ),
  SolidLocation: () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        d="M10 2C6.13 2 3 5.13 3 9C3 14.25 10 22 10 22C10 22 17 14.25 17 9C17 5.13 13.87 2 10 2ZM10 11.5C8.62 11.5 7.5 10.38 7.5 9C7.5 7.62 8.62 6.5 10 6.5C11.38 6.5 12.5 7.62 12.5 9C12.5 10.38 11.38 11.5 10 11.5Z"
        fill="#333"
      />
    </svg>
  ),
};

const Icon24px = {
  OutlineDocumentRed: () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z"
        fill="#ff4757"
      />
    </svg>
  ),
};

// Text Component
interface BasedTextProps {
  children: ReactNode;
  style?: React.CSSProperties;
  color?: string;
  numberOfLines?: number;
  fontWeight?: string;
}

const BasedText: FC<BasedTextProps> = ({
  children,
  style,
  color = "#333",
  numberOfLines,
  fontWeight,
}) => {
  const textStyle: React.CSSProperties = {
    color,
    fontWeight,
    margin: 0,
    ...style,
  };

  if (numberOfLines) {
    return (
      <div
        style={{
          ...textStyle,
          overflow: "hidden",
          textOverflow: "ellipsis",
          display: "-webkit-box",
          WebkitLineClamp: numberOfLines,
          WebkitBoxOrient: "vertical",
        }}
      >
        {children}
      </div>
    );
  }

  return <div style={textStyle}>{children}</div>;
};

// Image Component
interface ProgressiveImageProps {
  source: { uri: string };
  style?: React.CSSProperties;
}

const ProgressiveImage: FC<ProgressiveImageProps> = ({ source, style }) => {
  return (
    <img
      src={source.uri}
      alt=""
      style={{
        width: "100%",
        height: "auto",
        borderRadius: 8,
        ...style,
      }}
      onError={(e) => {
        const target = e.target as HTMLImageElement;
        target.style.display = "none";
      }}
    />
  );
};

// HTML Reader Component
interface HtmlReaderProps {
  content: string;
  containerStyle?: React.CSSProperties;
}

const HtmlReader: FC<HtmlReaderProps> = ({ content, containerStyle }) => {
  return (
    <div style={containerStyle} dangerouslySetInnerHTML={{ __html: content }} />
  );
};

// (removed unused Plain button helper)

// SectionMethod Component
interface SectionMethodProps {
  earnModels?: EarnModel[];
  method?: EarnMethodType;
  isUsingBC: boolean;
  merchantCode: string;
  openSnapConditions?: () => void;
  onNavigate?: (url: string) => void;
}

const SectionMethod: FC<SectionMethodProps> = ({
  earnModels,
  method,
  isUsingBC,
  merchantCode: _merchantCode,
  openSnapConditions,
  onNavigate,
}) => {
  const renderButton = () => {
    return (
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        {method?.directButton?.image ? (
          <img
            src={method.directButton.image}
            alt=""
            style={{
              width: 18,
              height: 18,
              objectFit: "contain",
            }}
          />
        ) : null}
        <BasedText style={{ color: "#ffffff" }}>
          {method?.directButton?.label}
        </BasedText>
      </div>
    );
  };

  const model = earnModels?.find((m) => m.earnMethodCode === method?.code);
  const methodName = model?.earnMethodName || method?.name;

  const adjustedMethodName = !isUsingBC
    ? methodName
    : methodName?.replace("VUI ", "");

  const renderOnlineButton = () => {
    return (
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <div style={{ width: 20, height: 20 }}>
          <Icon20px.OutlineWebsiteLink fill="#ffffff" />
        </div>
        <BasedText style={{ color: "#ffffff" }}>Đi đến website</BasedText>
      </div>
    );
  };

  const onPress = async () => {
    if (method?.code === METHOD_CODES.ONLINE) {
      if (model?.additionRate) {
        window.open(model.additionRate, "_blank");
      }
      return;
    }
    if (method?.directButton?.deepLink) {
      const deeplink = convertDeeplinkToUrl(method?.directButton?.deepLink);
      const targetUrl = deeplink?.webUrl || "/";
      if (onNavigate) {
        onNavigate(targetUrl);
      } else if (typeof window !== "undefined") {
        window.location.href = targetUrl;
      }
      return;
    }
    // Handle navigation if needed
    console.log("Navigate to:", method?.directButton.pageCode);
  };

  return (
    <Section
      title={adjustedMethodName || ""}
      backgroundColor="#ffffff"
      marginTop={12}
    >
      {!isEmpty(model?.image) ? (
        <ProgressiveImage
          source={{ uri: model?.image || "" }}
          style={{
            width: "100%",
            aspectRatio: "343 / 171",
            borderRadius: 12,
          }}
        />
      ) : null}
      <HtmlReader
        content={model?.description || method?.description || "<div/>"}
        containerStyle={{ marginTop: 16 }}
      />
      {model?.earnMethodCode === METHOD_CODES.BILL_SCAN ? (
        <>
          <BasedText
            style={{
              marginTop: 8,
              fontWeight: "600",
            }}
          >
            Điểm sẽ được cộng
          </BasedText>
          <button
            style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              marginTop: 8,
              background: "none",
              border: "none",
              cursor: "pointer",
              color: "#ff4757",
            }}
            onClick={openSnapConditions}
          >
            <Icon24px.OutlineDocumentRed />
            <BasedText style={{ color: "#ff4757" }}>
              Điều kiện chụp hóa đơn
            </BasedText>
          </button>
        </>
      ) : null}
      {!isEmpty(method?.directButton) &&
      model?.earnMethodCode !== METHOD_CODES.ONLINE ? (
        <Button onPress={onPress} containerStyle={{ marginTop: 24 }}>
          {renderButton()}
        </Button>
      ) : null}
      {model?.earnMethodCode === METHOD_CODES.ONLINE ? (
        <Button onPress={onPress} containerStyle={{ marginTop: 24 }}>
          {renderOnlineButton()}
        </Button>
      ) : null}
    </Section>
  );
};

// StoreNearby props interface removed (unused)

// const StoreNearby: FC<StoreNearbyProps> = ({
//   numberOfStores,
//   merchantCode,
//   logo,
//   event,
// }) => {
//   const onCall = () => {
//     // Handle call action
//     console.log("Call action");
//   };

//   const onShowMap = () => {
//     // Handle show map action
//     console.log("Show map action");
//   };

//   const goToStoreList = () => {
//     // Handle navigation to store list
//     console.log("Go to store list");
//   };

//   return (
//     <Section title="Cửa hàng gần đây" backgroundColor="#ffffff" marginTop={12}>
//       <div style={{ minHeight: 220 }}>
//         <div
//           style={{
//             display: "flex",
//             alignItems: "center",
//             marginBottom: 8,
//           }}
//         >
//           <BasedText
//             style={{
//               fontSize: "16px",
//               fontWeight: "600",
//               flex: 1,
//             }}
//             numberOfLines={1}
//           >
//             Cửa hàng gần nhất
//           </BasedText>
//           <div
//             style={{
//               display: "flex",
//               alignItems: "center",
//               gap: "4px",
//             }}
//           >
//             <Icon20px.SolidLocation />
//             <BasedText style={{ fontSize: "14px" }}>500m</BasedText>
//           </div>
//         </div>
//         <div style={{ minHeight: 44, marginBottom: 12 }}>
//           <BasedText
//             style={{
//               fontSize: "14px",
//               color: "#666",
//             }}
//             numberOfLines={2}
//           >
//             123 Đường ABC, Quận 1, TP.HCM
//           </BasedText>
//         </div>
//         <div
//           style={{
//             display: "flex",
//             alignItems: "center",
//             justifyContent: "space-between",
//             marginBottom: 32,
//           }}
//         >
//           <Plain
//             text="0123456789"
//             onPress={onCall}
//             IconCP={<Icon20px.OutlineCall fill="#ff4757" />}
//           />
//           <Plain
//             text="Xem bản đồ"
//             onPress={onShowMap}
//             IconCP={<Icon20px.OutlineMap />}
//           />
//         </div>
//         <Button variant="tertiary" onPress={goToStoreList}>
//           Xem tất cả cửa hàng ({numberOfStores})
//         </Button>
//       </div>
//     </Section>
//   );
// };

// Main HowToEarn Component
const HowToEarn: FC<SectionEarnProps> = ({
  methodList,
  earnModels,
  merchantCode,
  isUsingBC,
  openSnapConditions,
  logo,
  numberOfStores,
  onNavigate,
}) => {
  if (isArray(methodList) && methodList.length > 0) {
    return (
      <>
        {methodList.map((method) => {
          return (
            <div key={method.code}>
              <SectionMethod
                earnModels={earnModels}
                method={method}
                isUsingBC={isUsingBC}
                merchantCode={merchantCode}
                openSnapConditions={openSnapConditions}
                onNavigate={onNavigate}
              />
              {method.code === METHOD_CODES.BARCODE && numberOfStores > 0 ? (
                <div style={{ marginTop: -12 }}>
                  <StoreNearby
                    numberOfStores={numberOfStores}
                    merchantCode={merchantCode}
                    logo={logo}
                    event="MERCHANT_DETAIL_OFFLINE_STORE_ACTION"
                  />
                </div>
              ) : null}
            </div>
          );
        })}
      </>
    );
  }
  return null;
};

export default HowToEarn;
