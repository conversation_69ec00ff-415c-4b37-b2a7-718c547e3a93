import React from 'react';

interface TierCardProps {
  tierName: string;
  tierLevel: number;
  background?: string;
  labelBackground?: string;
  isMaxRank?: boolean;
  merchantName?: string;
  voucherCode?: string;
  onViewDetails?: () => void;
}

export const TierCard: React.FC<TierCardProps> = ({
  tierName,
  tierLevel,
  background,
  labelBackground,
  isMaxRank = false,
  merchantName,
  voucherCode,
  onViewDetails
}) => {
  return (
    <div 
      className="relative rounded-xl p-4 shadow-md overflow-hidden"
      style={{ 
        background: background || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}
    >
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-white text-lg font-bold">{tierName}</h3>
            {merchantName && (
              <p className="text-white/80 text-sm">{merchantName}</p>
            )}
          </div>
          <div 
            className="px-3 py-1 rounded-full text-sm font-semibold"
            style={{ 
              backgroundColor: labelBackground || 'rgba(255,255,255,0.2)',
              color: 'white'
            }}
          >
            Hạng {tierLevel}
          </div>
        </div>

        {isMaxRank && (
          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 mb-3">
            <p className="text-white text-sm font-medium">
              🎉 Bạn đã đạt hạng cao nhất!
            </p>
          </div>
        )}

        {voucherCode && (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 mb-3">
            <p className="text-white/80 text-xs mb-1">Mã ưu đãi</p>
            <p className="text-white text-sm font-mono font-bold">{voucherCode}</p>
          </div>
        )}

        {onViewDetails && (
          <button
            onClick={onViewDetails}
            className="w-full bg-white/20 backdrop-blur-sm text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-white/30 transition-colors"
          >
            Xem chi tiết
          </button>
        )}
      </div>

      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <pattern id="tier-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
            <circle cx="10" cy="10" r="1" fill="white" />
          </pattern>
          <rect width="100" height="100" fill="url(#tier-pattern)" />
        </svg>
      </div>
    </div>
  );
};