/* HTML Content Styles for SectionInfo */
.section-info-html {
  font-size: 14px;
  line-height: 1.57;
  color: #1A1818;
  font-family: 'Archia', system-ui, sans-serif;
}

.section-info-html h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1A1818;
  margin-top: 12px;
  margin-bottom: 6px;
  font-family: 'Archia', system-ui, sans-serif;
}

.section-info-html h4 {
  font-size: 15px;
  font-weight: 600;
  color: #1A1818;
  margin-top: 10px;
  margin-bottom: 5px;
  font-family: 'Archia', system-ui, sans-serif;
}

.section-info-html h5 {
  font-size: 14px;
  font-weight: 600;
  color: #1A1818;
  margin-top: 8px;
  margin-bottom: 4px;
  font-family: 'Archia', system-ui, sans-serif;
}

.section-info-html p {
  color: #1A1818;
  margin-bottom: 8px;
  font-family: 'Archia', system-ui, sans-serif;
}

.section-info-html strong {
  color: #1A1818;
  font-weight: 600;
  font-family: 'Archia', system-ui, sans-serif;
}

.section-info-html ul, 
.section-info-html ol {
  padding-left: 16px;
  margin-bottom: 8px;
}

.section-info-html li {
  color: #1A1818;
  margin-bottom: 3px;
  font-family: 'Archia', system-ui, sans-serif;
}

.section-info-html ul li {
  list-style-type: disc;
}

.section-info-html ol li {
  list-style-type: decimal;
}

.section-info-html code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 13px;
  color: #F65D79;
  font-family: 'Courier New', monospace;
}

.section-info-html table {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  font-size: 13px;
}

.section-info-html th {
  background-color: #f8f8f8;
  padding: 6px 8px;
  text-align: left;
  font-weight: 600;
  border: 1px solid #e5e5e5;
  color: #1A1818;
  font-family: 'Archia', system-ui, sans-serif;
}

.section-info-html td {
  padding: 6px 8px;
  border: 1px solid #e5e5e5;
  color: #1A1818;
  font-family: 'Archia', system-ui, sans-serif;
}

.section-info-html a {
  color: #F65D79;
  text-decoration: underline;
  cursor: pointer;
}

.section-info-html a:hover {
  color: #e44560;
}

.section-info-html img {
  max-width: 100%;
  height: auto;
  margin: 8px 0;
  border-radius: 6px;
}

.section-info-html blockquote {
  border-left: 3px solid #F65D79;
  padding-left: 12px;
  margin: 12px 0;
  color: #1A1818;
  font-style: italic;
}

.section-info-html hr {
  border: none;
  border-top: 1px solid #e5e5e5;
  margin: 16px 0;
}

.section-info-html pre {
  background-color: #f8f8f8;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  margin: 8px 0;
}

.section-info-html em {
  font-style: italic;
  color: #1A1818;
}

.section-info-html span[style*="text-decoration: underline"] {
  text-decoration: underline;
}

.section-info-html span[style*="font-weight"] {
  font-family: 'Archia', system-ui, sans-serif;
}

/* Responsive adjustments for mobile */
@media (max-width: 640px) {
  .section-info-html {
    font-size: 13px;
  }
  
  .section-info-html h3 {
    font-size: 15px;
  }
  
  .section-info-html h4 {
    font-size: 14px;
  }
  
  .section-info-html table {
    font-size: 12px;
  }
  
  .section-info-html th,
  .section-info-html td {
    padding: 4px 6px;
  }
  
  .section-info-html ul, 
  .section-info-html ol {
    padding-left: 14px;
  }
}