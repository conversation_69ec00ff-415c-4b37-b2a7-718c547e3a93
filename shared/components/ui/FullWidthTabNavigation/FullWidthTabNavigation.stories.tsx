import type { Meta, StoryObj } from '@storybook/react';
import { FullWidthTabNavigation } from './FullWidthTabNavigation';
import { useState } from 'react';

const meta: Meta<typeof FullWidthTabNavigation> = {
  title: 'Components/Navigation/FullWidthTabNavigation',
  component: FullWidthTabNavigation,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A full-width tab navigation component perfect for 2-tab layouts where each tab takes 50% width.',
      },
    },
  },
  argTypes: {
    tabs: {
      description: 'Array of tab items',
    },
    activeTabId: {
      description: 'ID of the currently active tab',
    },
    onTabChange: {
      description: 'Callback function when tab changes',
    },
    className: {
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof FullWidthTabNavigation>;

// Interactive wrapper for stories
const InteractiveWrapper = ({ tabs, initialTab }: { tabs: any[], initialTab: string }) => {
  const [activeTab, setActiveTab] = useState(initialTab);
  
  return (
    <div className="w-full">
      <FullWidthTabNavigation
        tabs={tabs}
        activeTabId={activeTab}
        onTabChange={setActiveTab}
      />
      <div className="p-4 bg-gray-50 min-h-[200px]">
        <p className="text-center text-gray-600">
          Active tab: <span className="font-semibold">{activeTab}</span>
        </p>
      </div>
    </div>
  );
};

export const TwoTabs: Story = {
  render: () => (
    <InteractiveWrapper
      tabs={[
        { id: 'all', label: 'LỊCH SỬ' },
        { id: 'unread', label: 'TIN MỚI' }
      ]}
      initialTab="all"
    />
  ),
};

export const WithBadge: Story = {
  render: () => (
    <InteractiveWrapper
      tabs={[
        { id: 'history', label: 'LỊCH SỬ' },
        { id: 'new', label: 'TIN MỚI', badge: true, badgeText: '5' }
      ]}
      initialTab="history"
    />
  ),
};

export const LongerLabels: Story = {
  render: () => (
    <InteractiveWrapper
      tabs={[
        { id: 'completed', label: 'ĐÃ HOÀN THÀNH' },
        { id: 'pending', label: 'ĐANG CHỜ XỬ LÝ' }
      ]}
      initialTab="completed"
    />
  ),
};

export const WithCustomStyling: Story = {
  render: () => (
    <InteractiveWrapper
      tabs={[
        { id: 'tab1', label: 'TAB 1' },
        { id: 'tab2', label: 'TAB 2', badge: true, badgeText: '99+' }
      ]}
      initialTab="tab1"
    />
  ),
};