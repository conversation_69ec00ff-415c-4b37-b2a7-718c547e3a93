import React from 'react';
import { cn } from '../../../utils';

export interface TabItem {
  id: string;
  label: string;
  badge?: boolean;
  badgeText?: string;
}

export interface FullWidthTabNavigationProps {
  tabs: TabItem[];
  activeTabId: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export const FullWidthTabNavigation: React.FC<FullWidthTabNavigationProps> = ({
  tabs,
  activeTabId,
  onTabChange,
  className = '',
}) => {
  return (
    <div className={cn('w-full bg-white', className)}>
      <div className="flex">
        {tabs.map((tab) => {
          const isActive = tab.id === activeTabId;
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={cn(
                'flex-1 flex items-center justify-center gap-1 px-4 py-3 relative',
                'border-b-2 transition-all duration-200',
                isActive 
                  ? 'border-[#F65D79]' 
                  : 'border-[#F8F8F8] hover:border-gray-200'
              )}
            >
              {/* Tab Label */}
              <span 
                className={cn(
                  'text-sm font-semibold leading-[22px] text-center',
                  isActive 
                    ? 'text-[#1A1818] font-bold' 
                    : 'text-[#9A9A9A] font-semibold'
                )}
              >
                {tab.label || `Tab ${tab.id}`}
              </span>

              {/* Badge */}
              {tab.badge && (
                <div className="ml-1 px-1.5 py-0.5 bg-[#F65D79] rounded-full min-w-[16px] h-4 flex items-center justify-center">
                  {tab.badgeText && (
                    <span className="text-white text-xs font-medium leading-none whitespace-nowrap">
                      {tab.badgeText}
                    </span>
                  )}
                </div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};