import React, { useState } from 'react';
import { ActionCallModal } from '../ActionCallModal';

interface TierNoRankProps {
  merchantName?: string;
  onRegister?: () => void;
  loading?: boolean;
  backgroundColor?: string;
}

export const TierNoRank: React.FC<TierNoRankProps> = ({
  merchantName,
  onRegister,
  loading = false,
  backgroundColor
}) => {
  const [modalState, setModalState] = useState<'success' | 'failed' | null>(null);

  const handleRegister = async () => {
    if (onRegister) {
      try {
        await onRegister();
        setModalState('success');
      } catch (error) {
        setModalState('failed');
      }
    }
  };

  const closeModal = () => {
    setModalState(null);
  };

  return (
    <>
    <div 
      className="rounded-xl p-6 text-center"
      style={{ 
        background: backgroundColor || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}
    >
      <div className="mb-4">
        <svg 
          className="w-16 h-16 mx-auto text-white/80"
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
          />
        </svg>
      </div>

      <h3 className="text-white text-lg font-bold mb-2">
        Chưa tham gia chương trình thành viên
      </h3>
      
      {merchantName && (
        <p className="text-white/90 text-sm mb-4">
          Tham gia ngay để nhận nhiều ưu đãi từ {merchantName}
        </p>
      )}

      {onRegister && (
        <button
          onClick={handleRegister}
          disabled={loading}
          className="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <span className="flex items-center gap-2">
              <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                <circle 
                  className="opacity-25" 
                  cx="12" 
                  cy="12" 
                  r="10" 
                  stroke="currentColor" 
                  strokeWidth="4"
                />
                <path 
                  className="opacity-75" 
                  fill="currentColor" 
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              Đang đăng ký...
            </span>
          ) : (
            'Tham gia ngay'
          )}
        </button>
      )}

      <div className="mt-6 p-4 bg-white/10 backdrop-blur-sm rounded-lg">
        <h4 className="text-white font-semibold text-sm mb-2">
          Quyền lợi khi tham gia:
        </h4>
        <ul className="text-white/90 text-sm space-y-1 text-left">
          <li className="flex items-start gap-2">
            <span>✓</span>
            <span>Tích điểm cho mỗi giao dịch</span>
          </li>
          <li className="flex items-start gap-2">
            <span>✓</span>
            <span>Nhận ưu đãi độc quyền theo hạng thành viên</span>
          </li>
          <li className="flex items-start gap-2">
            <span>✓</span>
            <span>Quà tặng sinh nhật và sự kiện đặc biệt</span>
          </li>
        </ul>
      </div>
    </div>

    {/* Success Modal */}
    <ActionCallModal
      isOpen={modalState === 'success'}
      onClose={closeModal}
      title="Đăng ký thành công!"
      content={`Chúc mừng bạn đã trở thành thành viên của ${merchantName || 'chương trình'}. Hãy tích điểm và tận hưởng các ưu đãi hấp dẫn!`}
      mascotName="congrat"
      primaryButtonText="Đóng"
    />

    {/* Failed Modal */}
    <ActionCallModal
      isOpen={modalState === 'failed'}
      onClose={closeModal}
      title="Đăng ký thất bại"
      content="Có lỗi xảy ra trong quá trình đăng ký. Vui lòng thử lại sau."
      mascotName="error"
      primaryButtonText="Đóng"
    />
    </>
  );
};