import React from "react";
import { Dialog } from "../Dialog/Dialog";
import congratImage from "../../../assets/images/mascot/Congrat.png";

export interface SuccessDialogProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
  confirmText?: string;
  image?: string;
}

export const SuccessDialog: React.FC<SuccessDialogProps> = ({
  open,
  onClose,
  title = "Thành công",
  message = "Đã thực hiện thành công.",
  confirmText = "Xong",
  image = congratImage,
}) => {
  return (
    <Dialog
      variant="bottomSheet"
      isOpen={open}
      onClose={onClose}
      title={title}
      confirmText={confirmText}
      onConfirm={onClose}
    >
      <div className="flex flex-col items-center bg-white px-4 pb-6">
        {/* Success icon */}
        <div className="rounded-full flex items-center justify-center mt-6 mb-4">
          <img
            src={image}
            alt="Success"
            className="object-contain"
          />
        </div>

        {/* Success text */}
        <div className="text-center mb-6">
          <p className="text-sm text-[#666666]">
            {message}
          </p>
        </div>
      </div>
    </Dialog>
  );
};

export default SuccessDialog;