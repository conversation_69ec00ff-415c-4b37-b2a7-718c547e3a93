import React, { useCallback, useEffect, useMemo, useRef } from "react";
import Button from "../Button";

export type StoreOnlineData = {
  title?: string;
  description?: string;
  websiteURL?: string;
};

export type StoreOnlineLabels = {
  onlineStore?: string;
  goToWebsite?: string;
};

export type StoreOnlineProps = {
  onLayout?: (rect: { width: number; height: number }) => void;
  data?: StoreOnlineData;
  merchantCode?: string;
  earnModels?: unknown;
  method?: unknown;
  isUsingBC?: boolean;
  labels?: StoreOnlineLabels;
};

function isEmptyValue(value: unknown): boolean {
  if (value == null) return true;
  if (typeof value === "string") return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === "object")
    return Object.keys(value as Record<string, unknown>).length === 0;
  return false;
}

const containerStyle: React.CSSProperties = {
  backgroundColor: "#FFFFFF",
  padding: 16,
  borderRadius: 8,
  boxShadow: "0 1px 2px rgba(0,0,0,0.06)",
};

const sectionTitleStyle: React.CSSProperties = {
  fontSize: 16,
  fontWeight: 600,
  marginTop: 12,
  marginBottom: 12,
};

const titleStyle: React.CSSProperties = {
  fontSize: 18,
  fontWeight: 600,
  marginTop: 8,
  marginBottom: 12,
};

const descriptionStyle: React.CSSProperties = {
  fontSize: 14,
  fontWeight: 400,
  color: "#333333",
  marginBottom: 16,
  lineHeight: 1.5,
};

const buttonStyle: React.CSSProperties = {
  display: "inline-flex",
  flexDirection: "row",
  alignItems: "center",
  gap: 8,
  padding: "10px 14px",
  backgroundColor: "#FFE6EE",
  border: "1px solid #FFD1DF",
  color: "#E91E63",
  borderRadius: 8,
  cursor: "pointer",
  textDecoration: "none",
  fontWeight: 600,
};

const iconStyle: React.CSSProperties = {
  display: "inline-block",
  width: 20,
  height: 20,
};

const methodNoteStyle: React.CSSProperties = {
  background: "#F8FAFC",
  border: "1px dashed #CBD5E1",
  color: "#334155",
  padding: 12,
  borderRadius: 8,
  marginBottom: 12,
  fontSize: 12,
};

const OnlineLinkIcon = () => (
  <svg
    style={iconStyle}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      d="M14 3h7v7"
      stroke="#E91E63"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 14L21 3"
      stroke="#E91E63"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21 14v7h-7"
      stroke="#E91E63"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3 10v11h11"
      stroke="#E91E63"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const StoreOnline: React.FC<StoreOnlineProps> = ({
  onLayout,
  data,
  merchantCode,
  earnModels,
  method,
  isUsingBC,
  labels,
}) => {
  const { title = "", description = "", websiteURL = "" } = data || {};

  const hasOnlineMethod = useMemo(() => !isEmptyValue(method), [method]);
  const containerRef = useRef<any>(null);

  const resolvedLabels: Required<StoreOnlineLabels> = {
    onlineStore: labels?.onlineStore ?? "Đặt hàng online",
    goToWebsite: labels?.goToWebsite ?? "Đặt ngay",
  };

  const handleOpenWebsite = useCallback(() => {
    if (!websiteURL) return;
    try {
      const g = (
        typeof globalThis !== "undefined" ? (globalThis as any) : undefined
      ) as any;
      if (g && typeof g.open === "function") {
        g.open(websiteURL, "_blank", "noopener");
      }
    } catch (_) {
      // noop
    }
  }, [websiteURL]);

  useEffect(() => {
    if (!onLayout) return;
    const el = containerRef.current as any;
    if (!el) return;
    const report = () =>
      onLayout({
        width: el?.offsetWidth ?? 0,
        height: el?.offsetHeight ?? 0,
      });
    report();
    const RO = (
      typeof globalThis !== "undefined"
        ? (globalThis as any).ResizeObserver
        : undefined
    ) as any;
    if (RO) {
      const ro = new RO(report);
      try {
        ro.observe(el);
      } catch (_) {
        /* noop */
      }
      return () => {
        try {
          ro.disconnect();
        } catch (_) {
          /* noop */
        }
      };
    }
    return;
  }, [onLayout]);

  if (isEmptyValue(data)) return null;

  return (
    <div
      ref={containerRef}
      style={containerStyle}
      data-merchant-code={merchantCode}
      data-using-bc={Boolean(isUsingBC)}
    >
      <div style={sectionTitleStyle}>{resolvedLabels.onlineStore}</div>

      {hasOnlineMethod ? (
        <div style={methodNoteStyle}>Online method info is available.</div>
      ) : null}

      {title ? <div style={titleStyle}>{title}</div> : null}
      {description ? <div style={descriptionStyle}>{description}</div> : null}

      {websiteURL ? (
        <Button
          onClick={handleOpenWebsite}
          aria-label={resolvedLabels.goToWebsite}
          variant="outline"
          className="w-full"
        >
          <OnlineLinkIcon />
          <span className="ml-2">{resolvedLabels.goToWebsite}</span>
        </Button>
      ) : null}
    </div>
  );
};

export default StoreOnline;
