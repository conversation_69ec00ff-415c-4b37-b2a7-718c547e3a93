import React from 'react';
import { Button } from '../Button';
import { CarouselDots } from '../CarouselDots';

export interface CameraPreviewStepProps {
  capturedPhoto: string;
  remainingSnaps: number;
  userStatus: {
    maxSnapPerDay: number;
    currentSnapCount: number;
    isAllowedToSnap: boolean;
  } | null;
  onBack: () => void;
  onComplete: () => void;
}

export const CameraPreviewStep: React.FC<CameraPreviewStepProps> = ({
  capturedPhoto,
  remainingSnaps,
  userStatus,
  onBack,
  onComplete
}) => {
  return (
    <div className="w-full h-full bg-black relative">
      {/* Captured Image Preview */}
      <div className="absolute inset-0 flex items-center justify-center p-4">
        <div className="relative w-full max-w-[327px] h-[586px]">
          <img 
            src={capturedPhoto}
            alt="Captured receipt"
            className="w-full h-full object-contain rounded"
          />
          
          {/* Frame overlay */}
          <div className="absolute inset-0 pointer-events-none">
            {/* Top Left Corner */}
            <div className="absolute top-0 left-0 w-4 h-4">
              <div className="absolute top-0 left-0 w-4 h-1 bg-[#F65D79]"></div>
              <div className="absolute top-0 left-0 w-1 h-4 bg-[#F65D79]"></div>
            </div>
            
            {/* Top Right Corner */}
            <div className="absolute top-0 right-0 w-4 h-4">
              <div className="absolute top-0 right-0 w-4 h-1 bg-[#F65D79]"></div>
              <div className="absolute top-0 right-0 w-1 h-4 bg-[#F65D79]"></div>
            </div>
            
            {/* Bottom Left Corner */}
            <div className="absolute bottom-0 left-0 w-4 h-4">
              <div className="absolute bottom-0 left-0 w-4 h-1 bg-[#F65D79]"></div>
              <div className="absolute bottom-0 left-0 w-1 h-4 bg-[#F65D79]"></div>
            </div>
            
            {/* Bottom Right Corner */}
            <div className="absolute bottom-0 right-0 w-4 h-4">
              <div className="absolute bottom-0 right-0 w-4 h-1 bg-[#F65D79]"></div>
              <div className="absolute bottom-0 right-0 w-1 h-4 bg-[#F65D79]"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Carousel Dots */}
      <div className="absolute top-[108px] left-1/2 transform -translate-x-1/2">
        <CarouselDots
          totalSteps={2}
          currentStep={2}
          activeColor="#0DC98B"
          inactiveColor="#FFFFFF"
          lineColor="#0DC98B"
        />
      </div>

      {/* Tooltip for remaining snaps - positioned above Complete button */}
      {userStatus && remainingSnaps > 0 && (
        <div className="absolute bottom-[80px] right-[51px] z-10">
          <div className="relative">
            <div className="bg-white rounded-lg px-4 py-3 shadow-lg">
              <span className="text-[#1A1818] text-sm font-semibold">
                Còn {remainingSnaps} lượt hôm nay
              </span>
            </div>
            <div className="absolute -bottom-2 right-[20px]">
              <svg width="16" height="10" viewBox="0 0 16 10" fill="none">
                <path d="M8 10L0 0H16L8 10Z" fill="white"/>
              </svg>
            </div>
          </div>
        </div>
      )}

      {/* Bottom Action Buttons */}
      <div className="absolute bottom-0 left-0 right-0 p-4 flex gap-[59px] justify-center">
        <Button
          variant="outline"
          size="medium"
          onClick={onBack}
          className="w-[142px] h-11 border border-[#CACACA] bg-white rounded-lg"
        >
          <span className="text-[#1A1818] text-sm font-semibold">Quay lại</span>
        </Button>
        <Button
          variant="primary"
          size="medium"
          onClick={onComplete}
          className="w-[142px] h-11 bg-[#F65D79] rounded-lg"
        >
          <span className="text-white text-sm font-semibold">Hoàn tất</span>
        </Button>
      </div>
    </div>
  );
};

export default CameraPreviewStep;