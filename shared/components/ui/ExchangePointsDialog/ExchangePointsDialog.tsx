import React, { useState, useCallback } from "react";
import { Dialog } from "../Dialog/Dialog";
import coinIcon from "../../../assets/icons/coin-24px.svg";
import minusIcon from "../../../assets/icons/minus-circle-grey.svg";
import plusIcon from "../../../assets/icons/plus-circle-grey.svg";

export interface ExchangePointsDialogProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  currentPoints: number;
  pointsPerItem: number;
  itemName?: string;
  onConfirm?: (quantity: number) => Promise<void> | void;
  minQuantity?: number;
  maxQuantity?: number;
}

export const ExchangePointsDialog: React.FC<ExchangePointsDialogProps> = ({
  open,
  onClose,
  title = "Thêm lượt quay",
  currentPoints,
  pointsPerItem,
  itemName = "lượt quay",
  onConfirm,
  minQuantity = 1,
  maxQuantity = 99,
}) => {
  const [quantity, setQuantity] = useState(minQuantity);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const totalPoints = quantity * pointsPerItem;
  const canConfirm = currentPoints >= totalPoints;

  const handleIncrease = useCallback(() => {
    setQuantity((prev) => Math.min(prev + 1, maxQuantity));
  }, [maxQuantity]);

  const handleDecrease = useCallback(() => {
    setQuantity((prev) => Math.max(prev - 1, minQuantity));
  }, [minQuantity]);

  const handleConfirm = useCallback(async () => {
    if (!canConfirm || !onConfirm) return;

    setConfirmLoading(true);
    try {
      await onConfirm(quantity);
      onClose();
    } catch (error) {
      console.error("Error confirming exchange:", error);
    } finally {
      setConfirmLoading(false);
    }
  }, [canConfirm, onConfirm, quantity, onClose]);

  return (
    <Dialog
      variant="bottomSheet"
      isOpen={open}
      onClose={onClose}
      title={title}
      confirmText="Xác nhận"
      onConfirm={canConfirm ? handleConfirm : undefined}
      confirmLoading={confirmLoading}
    >
      <div className="flex flex-col gap-8 py-2 justify-center items-center bg-white">
        {/* Current balance info */}
        <div className="w-full">
          <div className="text-sm text-[#1A1818]">
            Bạn đang có {currentPoints.toLocaleString()} POINTS
          </div>
        </div>

        {/* Quantity selector */}
        <div className="bg-[#F8F8F8] rounded-lg p-6 w-full">
          <div className="text-sm font-semibold text-[#1A1818] mb-4 text-center">
            Số {itemName} muốn đổi
          </div>

          <div className="flex items-center justify-center gap-5">
            {/* Decrease button */}
            <button
              onClick={handleDecrease}
              disabled={quantity <= minQuantity}
              className={`w-8 h-8 rounded-full flex items-center justify-center transition-all ${
                quantity <= minQuantity
                  ? "bg-gray-200 cursor-not-allowed"
                  : "bg-[#F65D79] hover:bg-[#F65D79]/90"
              }`}
              aria-label={`Giảm số ${itemName}`}
            >
              <img src={minusIcon} alt="Giảm" className="w-full h-full" />
            </button>

            {/* Quantity display */}
            <div className="text-lg font-semibold text-[#1A1818] min-w-[21px] text-center">
              {quantity}
            </div>

            {/* Increase button */}
            <button
              onClick={handleIncrease}
              disabled={quantity >= maxQuantity}
              className={`w-8 h-8 rounded-full flex items-center justify-center transition-all ${
                quantity >= maxQuantity
                  ? "bg-gray-200 cursor-not-allowed"
                  : "bg-[#F65D79] hover:bg-[#F65D79]/90"
              }`}
              aria-label={`Tăng số ${itemName}`}
            >
              <img src={plusIcon} alt="Tăng" className="w-full h-full" />
            </button>
          </div>
        </div>

        {/* Total calculation section */}
        <div className="w-full border-t border-[#ECECEC] pt-4">
          <div className="flex items-center justify-between">
            <div className="text-sm font-semibold text-[#1A1818]">
              Tổng cộng
            </div>

            <div className="flex items-center gap-1.5">
              <img src={coinIcon} alt="Points" className="w-6 h-6" />
              <span className="text-lg font-semibold text-[#1A1818]">
                {totalPoints.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Insufficient points warning */}
          {!canConfirm && (
            <div className="mt-4 text-sm text-[#FF4C4D] text-center">
              Cần thêm {(totalPoints - currentPoints).toLocaleString()} POINTS
            </div>
          )}
        </div>
      </div>
    </Dialog>
  );
};

export default ExchangePointsDialog;