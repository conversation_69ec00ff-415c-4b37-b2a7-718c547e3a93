import React, { useState } from "react";
import { ExchangePointsDialog } from "./ExchangePointsDialog";

/**
 * Example usage of ExchangePointsDialog component
 * This example shows how to use the dialog for different exchange scenarios
 */
export const ExchangePointsDialogExample: React.FC = () => {
  const [dialogType, setDialogType] = useState<string | null>(null);
  const [userPoints] = useState(500); // Simulated user points

  const handleConfirm = async (quantity: number) => {
    console.log(`User wants to exchange ${quantity} items`);
    
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    // After successful exchange
    alert(`Successfully exchanged ${quantity} items!`);
    setDialogType(null);
  };

  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold mb-4">Exchange Points Dialog Examples</h2>
      
      <div className="space-y-4">
        {/* Example 1: Exchange for spins */}
        <button
          onClick={() => setDialogType("spins")}
          className="px-4 py-2 bg-[#F65D79] text-white rounded-lg hover:bg-[#F65D79]/90"
        >
          Exchange Points for Spins
        </button>

        {/* Example 2: Exchange for rewards */}
        <button
          onClick={() => setDialogType("rewards")}
          className="px-4 py-2 bg-[#F65D79] text-white rounded-lg hover:bg-[#F65D79]/90"
        >
          Exchange Points for Rewards
        </button>

        {/* Example 3: Exchange for vouchers */}
        <button
          onClick={() => setDialogType("vouchers")}
          className="px-4 py-2 bg-[#F65D79] text-white rounded-lg hover:bg-[#F65D79]/90"
        >
          Exchange Points for Vouchers
        </button>
      </div>

      {/* Dialog for spins */}
      <ExchangePointsDialog
        open={dialogType === "spins"}
        onClose={() => setDialogType(null)}
        title="Thêm lượt quay"
        currentPoints={userPoints}
        pointsPerItem={50}
        itemName="lượt quay"
        onConfirm={handleConfirm}
        minQuantity={1}
        maxQuantity={10}
      />

      {/* Dialog for rewards */}
      <ExchangePointsDialog
        open={dialogType === "rewards"}
        onClose={() => setDialogType(null)}
        title="Đổi phần thưởng"
        currentPoints={userPoints}
        pointsPerItem={100}
        itemName="phần thưởng"
        onConfirm={handleConfirm}
        minQuantity={1}
        maxQuantity={5}
      />

      {/* Dialog for vouchers */}
      <ExchangePointsDialog
        open={dialogType === "vouchers"}
        onClose={() => setDialogType(null)}
        title="Đổi voucher"
        currentPoints={userPoints}
        pointsPerItem={200}
        itemName="voucher"
        onConfirm={handleConfirm}
        minQuantity={1}
        maxQuantity={2}
      />

      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <p className="text-sm text-gray-600">
          Current user points: <strong>{userPoints}</strong>
        </p>
      </div>
    </div>
  );
};

export default ExchangePointsDialogExample;