import type { <PERSON>a, StoryObj } from "@storybook/react";
import { ExchangePointsDialog } from "./ExchangePointsDialog";
import { useState } from "react";

const meta: Meta<typeof ExchangePointsDialog> = {
  title: "Components/UI/ExchangePointsDialog",
  component: ExchangePointsDialog,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof ExchangePointsDialog>;

const ExchangePointsDialogWrapper = (args: any) => {
  const [open, setOpen] = useState(true);

  const handleConfirm = async (quantity: number) => {
    console.log(`Exchanging ${quantity} items`);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setOpen(false);
  };

  return (
    <>
      <button
        onClick={() => setOpen(true)}
        className="px-4 py-2 bg-[#F65D79] text-white rounded-lg"
      >
        Open Dialog
      </button>
      <ExchangePointsDialog
        {...args}
        open={open}
        onClose={() => setOpen(false)}
        onConfirm={handleConfirm}
      />
    </>
  );
};

export const Default: Story = {
  render: (args) => <ExchangePointsDialogWrapper {...args} />,
  args: {
    title: "Thêm lượt quay",
    currentPoints: 100,
    pointsPerItem: 10,
    itemName: "lượt quay",
    minQuantity: 1,
    maxQuantity: 10,
  },
};

export const InsufficientPoints: Story = {
  render: (args) => <ExchangePointsDialogWrapper {...args} />,
  args: {
    title: "Đổi ưu đãi",
    currentPoints: 50,
    pointsPerItem: 100,
    itemName: "ưu đãi",
    minQuantity: 1,
    maxQuantity: 5,
  },
};

export const CustomItemName: Story = {
  render: (args) => <ExchangePointsDialogWrapper {...args} />,
  args: {
    title: "Đổi quà tặng",
    currentPoints: 500,
    pointsPerItem: 50,
    itemName: "quà tặng",
    minQuantity: 1,
    maxQuantity: 10,
  },
};

export const HighQuantityLimit: Story = {
  render: (args) => <ExchangePointsDialogWrapper {...args} />,
  args: {
    title: "Mua vật phẩm",
    currentPoints: 10000,
    pointsPerItem: 5,
    itemName: "vật phẩm",
    minQuantity: 1,
    maxQuantity: 999,
  },
};