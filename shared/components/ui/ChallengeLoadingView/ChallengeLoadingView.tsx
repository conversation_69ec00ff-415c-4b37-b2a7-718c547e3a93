import React from 'react';
import { LoadingSpinner } from '../Loading';

interface ErrorMessage {
  title: string;
  content: string;
}

interface ChallengeLoadingViewProps {
  isLoading: boolean;
  errorMessage?: ErrorMessage;
}

const ChallengeLoadingView: React.FC<ChallengeLoadingViewProps> = ({
  isLoading,
  errorMessage,
}) => {
  if (!isLoading && !errorMessage) return null;

  return (
    <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
      {errorMessage ? (
        <div className="text-center px-4">
          <div className="text-6xl mb-4">😔</div>
          <h2 className="text-lg font-bold text-gray-900 mb-2">
            {errorMessage.title || 'Lỗi tải dữ liệu'}
          </h2>
          <p className="text-sm text-gray-600">
            {errorMessage.content || 'Không thể tải thông tin thử thách. Vui lòng thử lại sau.'}
          </p>
        </div>
      ) : (
        <div className="text-center">
          <LoadingSpinner size="large" />
          <p className="text-sm text-gray-600 mt-4">Đang tải...</p>
        </div>
      )}
    </div>
  );
};

export default React.memo(ChallengeLoadingView);