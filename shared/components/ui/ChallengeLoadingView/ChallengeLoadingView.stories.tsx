import type { Meta, StoryObj } from '@storybook/react';
import ChallengeLoadingView from './ChallengeLoadingView';

const meta: Meta<typeof ChallengeLoadingView> = {
  title: 'Components/Challenge/ChallengeLoadingView',
  component: ChallengeLoadingView,
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Loading: Story = {
  args: {
    isLoading: true,
    errorMessage: undefined,
  },
};

export const ErrorState: Story = {
  args: {
    isLoading: false,
    errorMessage: {
      title: 'Lỗi tải dữ liệu',
      content: 'Không thể tải thông tin thử thách. Vui lòng thử lại sau.',
    },
  },
};

export const NetworkError: Story = {
  args: {
    isLoading: false,
    errorMessage: {
      title: 'Lỗi kết nối',
      content: 'Kiểm tra kết nối mạng và thử lại.',
    },
  },
};

export const ServerError: Story = {
  args: {
    isLoading: false,
    errorMessage: {
      title: 'Lỗi máy chủ',
      content: '<PERSON><PERSON><PERSON> chủ đang bảo trì. Vui lòng quay lại sau ít phút.',
    },
  },
};

export const CustomError: Story = {
  args: {
    isLoading: false,
    errorMessage: {
      title: 'Thử thách không tồn tại',
      content: 'Thử thách này có thể đã bị xóa hoặc hết hạn.',
    },
  },
};

export const Hidden: Story = {
  args: {
    isLoading: false,
    errorMessage: undefined,
  },
};

export const LongErrorMessage: Story = {
  args: {
    isLoading: false,
    errorMessage: {
      title: 'Đã xảy ra lỗi không mong muốn',
      content: 'Rất tiếc, hệ thống đang gặp sự cố kỹ thuật. Chúng tôi đang khắc phục sự cố này. Vui lòng thử lại sau ít phút hoặc liên hệ bộ phận hỗ trợ khách hàng qua hotline 1900-xxxx để được trợ giúp.',
    },
  },
};