import React from 'react';
import { ChallengeSectionNotReceiveGift } from '../ChallengeSectionNotReceiveGift';
import { SectionBanner } from '../SectionBanner';

interface ChallengeHeaderProps {
  banners: any[];
  notReceiveList: any[];
  onChallengeClick?: (challengeId: string) => void;
  onReceiveGiftSuccess?: () => void;
  onBannerClick?: (banner: any) => void;
}

const ChallengeHeader: React.FC<ChallengeHeaderProps> = ({
  banners,
  notReceiveList,
  onChallengeClick,
  onReceiveGiftSuccess,
  onBannerClick,
}) => {
  return (
    <div className="bg-white">
      {banners.length > 0 && (
        <div className="pt-4 px-4">
          <SectionBanner
            data={banners}
            onBannerClick={onBannerClick}
            entertainmentTab="CHALLENGES"
          />
        </div>
      )}
      <ChallengeSectionNotReceiveGift
        data={notReceiveList}
        onChallengeClick={onChallengeClick}
        onReceiveGiftSuccess={onReceiveGiftSuccess}
      />
    </div>
  );
};

ChallengeHeader.displayName = 'ChallengeHeader';
export default React.memo(ChallengeHeader);