import type { <PERSON>a, StoryObj } from '@storybook/react';
import ChallengeHeader from './ChallengeHeader';

const meta: Meta<typeof ChallengeHeader> = {
  title: 'Components/Challenge/ChallengeHeader',
  component: ChallengeHeader,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    onChallengeClick: { action: 'challenge clicked' },
    onReceiveGiftSuccess: { action: 'receive gift success' },
    onBannerClick: { action: 'banner clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const mockBanners = [
  {
    id: '1',
    image: 'https://via.placeholder.com/400x150/3B82F6/FFFFFF?text=Challenge+Banner+1',
    title: 'Th<PERSON> thách mới nhất',
    description: 'Tham gia ngay để nhận quà hấp dẫn',
  },
  {
    id: '2',
    image: 'https://via.placeholder.com/400x150/10B981/FFFFFF?text=Challenge+Banner+2',
    title: 'Flash Sale đặc biệt',
    description: '<PERSON><PERSON><PERSON><PERSON> gi<PERSON> lên đến 50%',
  },
];

const mockNotReceiveList = [
  {
    _id: '1',
    displayChallengeName: '<PERSON><PERSON><PERSON> thành mua sắm tuần này',
    challengeImage: 'https://via.placeholder.com/60x60/F59E0B/FFFFFF?text=🎁',
    userProgress: {
      state: 'NOT_RECEIVED_YET',
    },
  },
  {
    _id: '2', 
    displayChallengeName: 'Thử thách khám phá thương hiệu',
    challengeImage: 'https://via.placeholder.com/60x60/EF4444/FFFFFF?text=🏆',
    userProgress: {
      state: 'NOT_RECEIVED_YET',
    },
  },
];

export const Default: Story = {
  args: {
    banners: mockBanners,
    notReceiveList: mockNotReceiveList,
  },
};

export const WithoutBanners: Story = {
  args: {
    banners: [],
    notReceiveList: mockNotReceiveList,
  },
};

export const WithoutNotReceiveGifts: Story = {
  args: {
    banners: mockBanners,
    notReceiveList: [],
  },
};

export const Empty: Story = {
  args: {
    banners: [],
    notReceiveList: [],
  },
};

export const SingleBanner: Story = {
  args: {
    banners: [mockBanners[0]],
    notReceiveList: mockNotReceiveList,
  },
};

export const SingleNotReceiveGift: Story = {
  args: {
    banners: mockBanners,
    notReceiveList: [mockNotReceiveList[0]],
  },
};