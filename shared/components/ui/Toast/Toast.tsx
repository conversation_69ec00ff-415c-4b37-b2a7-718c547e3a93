import React, { useEffect, useState } from 'react';

export interface ToastProps {
  message: string;
  type?: 'success' | 'error' | 'info';
  duration?: number;
  isVisible: boolean;
  onClose: () => void;
  position?: 'top' | 'bottom';
}

const Toast: React.FC<ToastProps> = ({
  message,
  type = 'success',
  duration = 2000,
  isVisible,
  onClose,
  position
}) => {
  // Auto-determine position based on type if not explicitly set
  const finalPosition = position ?? (type === 'error' ? 'top' : 'bottom');
  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  if (!isVisible) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-[#0DC98B] text-white';
      case 'error':
        return 'bg-red-500 text-white';
      case 'info':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-[#0DC98B] text-white';
    }
  };

  const getPositionStyles = () => {
    switch (finalPosition) {
      case 'top':
        return 'top-20';
      case 'bottom':
        return 'bottom-20';
      default:
        return 'bottom-20';
    }
  };

  const getAnimationClass = () => {
    return finalPosition === 'top' ? 'animate-fade-in-down' : 'animate-fade-in-up';
  };

  return (
    <div
      className={`
        fixed left-1/2 transform -translate-x-1/2 z-notification
        px-4 py-3 rounded-lg shadow-lg
        ${getAnimationClass()}
        ${getTypeStyles()}
        ${getPositionStyles()}
      `}
    >
      <div className="flex items-center gap-2">
        {type === 'success' && (
          <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          </svg>
        )}
        {type === 'error' && (
          <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
        )}
        {type === 'info' && (
          <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          </svg>
        )}
        <span className="font-medium text-sm">{message}</span>
      </div>
    </div>
  );
};

export default Toast;