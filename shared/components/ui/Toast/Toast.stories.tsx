import type { Meta, StoryObj } from '@storybook/react';
import React, { useState } from 'react';
import { Toast } from './Toast';

const meta = {
  title: 'UI/Toast',
  component: Toast,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['success', 'error', 'info'],
    },
    position: {
      control: 'select',
      options: ['top', 'bottom'],
    },
  },
} satisfies Meta<typeof Toast>;

export default meta;
type Story = StoryObj<typeof meta>;

// Interactive story with button to trigger toast
const ToastDemo: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');
  const [message, setMessage] = useState('Mã đã được sao chép!');

  const showToast = (type: 'success' | 'error' | 'info', msg: string) => {
    setToastType(type);
    setMessage(msg);
    setIsVisible(true);
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600 mb-4">
        <p>📍 <strong>Auto-positioning:</strong></p>
        <ul className="ml-4 mt-2">
          <li>• Error toasts → Top (red)</li>
          <li>• Success/Info toasts → Bottom (green/blue)</li>
        </ul>
      </div>
      <div className="flex gap-4">
        <button
          onClick={() => showToast('success', 'Voucher đã được kích hoạt thành công!')}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Success (Bottom)
        </button>
        <button
          onClick={() => showToast('error', 'Hãy chờ đến 2025-12-10 13:46:19 để dùng ưu đãi này')}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Error (Top)
        </button>
        <button
          onClick={() => showToast('info', 'Thông tin đã được cập nhật')}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Info (Bottom)
        </button>
      </div>
      
      <Toast
        message={message}
        type={toastType}
        isVisible={isVisible}
        onClose={() => setIsVisible(false)}
        // No position prop - let it auto-determine based on type
      />
    </div>
  );
};

export const Interactive: Story = {
  render: () => <ToastDemo />,
};

export const Success: Story = {
  args: {
    message: 'Mã đã được sao chép!',
    type: 'success',
    isVisible: true,
    onClose: () => {},
    position: 'bottom',
  },
};

export const Error: Story = {
  args: {
    message: 'Hãy chờ đến 2025-12-10 13:46:19 để dùng ưu đãi này',
    type: 'error',
    isVisible: true,
    onClose: () => {},
    // No position prop - will auto-position at top
  },
};

export const Info: Story = {
  args: {
    message: 'Thông tin đã được cập nhật',
    type: 'info',
    isVisible: true,
    onClose: () => {},
    position: 'bottom',
  },
};

export const TopPosition: Story = {
  args: {
    message: 'Toast hiển thị ở trên',
    type: 'success',
    isVisible: true,
    onClose: () => {},
    position: 'top',
  },
};