import React from 'react';
import { Dialog } from '../Dialog';

export type MascotType = 'congrat' | 'error' | 'warning' | 'info';

export interface ActionCallModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  content: string;
  mascotName?: MascotType;
  primaryButtonText?: string;
  secondaryButtonText?: string;
  onPrimaryAction?: () => void;
  onSecondaryAction?: () => void;
  showCloseButton?: boolean;
}

const getMascotImage = (mascotName?: MascotType) => {
  switch (mascotName) {
    case 'congrat':
      return (
        <div className="w-24 h-24 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
          <svg className="w-12 h-12 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
      );
    case 'error':
      return (
        <div className="w-24 h-24 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
          <svg className="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
      );
    case 'warning':
      return (
        <div className="w-24 h-24 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
          <svg className="w-12 h-12 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
      );
    case 'info':
      return (
        <div className="w-24 h-24 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
          <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
      );
    default:
      return null;
  }
};

export const ActionCallModal: React.FC<ActionCallModalProps> = ({
  isOpen,
  onClose,
  title,
  content,
  mascotName,
  primaryButtonText = 'Đóng',
  secondaryButtonText,
  onPrimaryAction,
  onSecondaryAction,
  showCloseButton = true,
}) => {
  const handlePrimaryAction = () => {
    if (onPrimaryAction) {
      onPrimaryAction();
    } else {
      onClose();
    }
  };

  const handleSecondaryAction = () => {
    if (onSecondaryAction) {
      onSecondaryAction();
    }
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      showCloseButton={showCloseButton}
      className="max-w-sm"
    >
      <div className="text-center">
        {getMascotImage(mascotName)}
        
        <h2 className="text-xl font-bold text-gray-900 mb-3">
          {title}
        </h2>
        
        <p className="text-sm text-gray-600 mb-6">
          {content}
        </p>
        
        <div className="flex flex-col gap-3">
          <button
            onClick={handlePrimaryAction}
            className="w-full px-4 py-3 bg-primary text-white rounded-lg font-semibold hover:bg-primary-dark transition-colors"
          >
            {primaryButtonText}
          </button>
          
          {secondaryButtonText && (
            <button
              onClick={handleSecondaryAction}
              className="w-full px-4 py-3 bg-gray-100 text-gray-700 rounded-lg font-semibold hover:bg-gray-200 transition-colors"
            >
              {secondaryButtonText}
            </button>
          )}
        </div>
      </div>
    </Dialog>
  );
};