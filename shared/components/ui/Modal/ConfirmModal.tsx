import React from 'react';
import { Modal, ModalProps } from './Modal';

export interface ConfirmModalProps extends Omit<ModalProps, 'variant'> {
  // Confirm specific props
  confirmButtonText?: string;
  cancelButtonText?: string;
  confirmButtonVariant?: 'danger' | 'primary' | 'success';
  onConfirm?: () => void;
  onCancel?: () => void;
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  confirmButtonText = 'Xác nhận',
  cancelButtonText = 'Hủy',
  confirmButtonVariant = 'primary',
  onConfirm,
  onCancel,
  title = 'Xác nhận',
  description = 'Bạn có chắc chắn muốn thực hiện thao tác này?',
  ...rest
}) => {
  const getButtonClass = () => {
    switch (confirmButtonVariant) {
      case 'danger':
        return 'bg-red-500 hover:bg-red-600';
      case 'success':
        return 'bg-green-500 hover:bg-green-600';
      default:
        return 'bg-[#F65D79] hover:bg-[#F65D79]/90';
    }
  };

  return (
    <Modal
      {...rest}
      variant="default"
      title={title}
      description={description}
      primaryButtonText={confirmButtonText}
      secondaryButtonText={cancelButtonText}
      onPrimaryAction={onConfirm}
      onSecondaryAction={onCancel}
      className={rest.className}
    />
  );
};

export default ConfirmModal;