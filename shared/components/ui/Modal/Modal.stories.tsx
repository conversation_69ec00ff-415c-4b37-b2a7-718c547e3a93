import React, { useState } from 'react';
import { <PERSON>a, StoryObj } from '@storybook/react';
import { Modal } from './Modal';
import { SuccessModal } from './SuccessModal';
import { WarningModal } from './WarningModal';
import { ConfirmModal } from './ConfirmModal';
import { GiftModal } from './GiftModal';
import { InfoModal } from './InfoModal';

const meta: Meta<typeof Modal> = {
  title: 'UI/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Hệ thống Modal/Popup component với nhiều variants khác nhau.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    isOpen: {
      control: 'boolean',
      description: 'Trạng thái hiển thị của modal',
    },
    title: {
      control: 'text',
      description: 'Tiêu đề của modal',
    },
    description: {
      control: 'text',
      description: '<PERSON><PERSON> tả/nội dung của modal',
    },
    variant: {
      control: 'select',
      options: ['default', 'success', 'warning', 'error', 'info', 'gift'],
      description: 'Variant của modal',
    },
    primaryButtonText: {
      control: 'text',
      description: 'Text cho nút primary',
    },
    secondaryButtonText: {
      control: 'text',
      description: 'Text cho nút secondary',
    },
    showCloseButton: {
      control: 'boolean',
      description: 'Hiển thị nút close',
    },
    closeOnBackdropClick: {
      control: 'boolean',
      description: 'Đóng modal khi click backdrop',
    },
    maxWidth: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Chiều rộng tối đa của modal',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Modal>;

// Base Modal Story
export const Default: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(true);
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-pink-500 text-white rounded-lg"
        >
          Open Modal
        </button>
        <Modal
          {...args}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Default Modal"
          description="This is a default modal with basic styling and functionality."
          primaryButtonText="Xác nhận"
          secondaryButtonText="Hủy"
        />
      </>
    );
  },
};

// Success Modal Story
export const Success: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-green-500 text-white rounded-lg"
        >
          Open Success Modal
        </button>
        <SuccessModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Chúc mừng bạn!"
          points={100}
          rewardName="Voucher giảm 20%"
          showImage={true}
        />
      </>
    );
  },
};

// Warning Modal Story
export const Warning: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-yellow-500 text-white rounded-lg"
        >
          Open Warning Modal
        </button>
        <WarningModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Sử dụng voucher ngay"
          warningList={[
            'Hệ thống sẽ kích hoạt sử dụng voucher ngay sau khi bạn xác nhận.',
            'Voucher sẽ hết hạn sử dụng sau'
          ]}
          timeLimit={30}
          primaryButtonText="Xác nhận"
          secondaryButtonText="Để sau"
          showMascot={true}
        />
      </>
    );
  },
};

// Confirm Modal Story
export const Confirm: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-red-500 text-white rounded-lg"
        >
          Open Confirm Modal
        </button>
        <ConfirmModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Xác nhận xóa"
          description="Bạn có chắc chắn muốn xóa mục này? Hành động này không thể hoàn tác."
          confirmButtonText="Xóa"
          cancelButtonText="Hủy"
          confirmButtonVariant="danger"
          onConfirm={() => {
            console.log('Confirmed!');
            setIsOpen(false);
          }}
        />
      </>
    );
  },
};

// Gift Modal Story
export const Gift: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-purple-500 text-white rounded-lg"
        >
          Open Gift Modal
        </button>
        <GiftModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Yay, trúng rồi!"
          giftAmount={50}
          giftType="point"
          showCoinIcon={true}
          primaryButtonText="Quay tiếp"
        />
      </>
    );
  },
};

// Info Modal Story
export const Info: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg"
        >
          Open Info Modal
        </button>
        <InfoModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Thông tin quan trọng"
          description="Đây là một số thông tin bạn cần biết:"
          infoList={[
            'Voucher có hiệu lực trong 30 ngày',
            'Không áp dụng cùng các chương trình khuyến mãi khác',
            'Áp dụng cho đơn hàng từ 500.000đ',
            'Mỗi khách hàng chỉ được sử dụng 1 lần'
          ]}
          showIcon={true}
        />
      </>
    );
  },
};

// Multiple Modals Example
export const MultipleModals: Story = {
  render: () => {
    const [successOpen, setSuccessOpen] = useState(false);
    const [warningOpen, setWarningOpen] = useState(false);
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [giftOpen, setGiftOpen] = useState(false);
    const [infoOpen, setInfoOpen] = useState(false);

    return (
      <div className="flex flex-wrap gap-4">
        <button
          onClick={() => setSuccessOpen(true)}
          className="px-4 py-2 bg-green-500 text-white rounded-lg"
        >
          Success Modal
        </button>
        <button
          onClick={() => setWarningOpen(true)}
          className="px-4 py-2 bg-yellow-500 text-white rounded-lg"
        >
          Warning Modal
        </button>
        <button
          onClick={() => setConfirmOpen(true)}
          className="px-4 py-2 bg-red-500 text-white rounded-lg"
        >
          Confirm Modal
        </button>
        <button
          onClick={() => setGiftOpen(true)}
          className="px-4 py-2 bg-purple-500 text-white rounded-lg"
        >
          Gift Modal
        </button>
        <button
          onClick={() => setInfoOpen(true)}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg"
        >
          Info Modal
        </button>

        <SuccessModal
          isOpen={successOpen}
          onClose={() => setSuccessOpen(false)}
          title="Thành công!"
          points={100}
        />

        <WarningModal
          isOpen={warningOpen}
          onClose={() => setWarningOpen(false)}
          title="Cảnh báo"
          warningList={['Lưu ý 1', 'Lưu ý 2']}
          timeLimit={15}
        />

        <ConfirmModal
          isOpen={confirmOpen}
          onClose={() => setConfirmOpen(false)}
          title="Xác nhận"
          description="Bạn có chắc chắn?"
        />

        <GiftModal
          isOpen={giftOpen}
          onClose={() => setGiftOpen(false)}
          giftAmount="10%"
          giftType="voucher"
          giftName="Voucher giảm giá"
        />

        <InfoModal
          isOpen={infoOpen}
          onClose={() => setInfoOpen(false)}
          title="Thông tin"
          infoList={['Thông tin 1', 'Thông tin 2', 'Thông tin 3']}
        />
      </div>
    );
  },
};

// Custom Content Modal
export const CustomContent: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-indigo-500 text-white rounded-lg"
        >
          Open Custom Modal
        </button>
        <Modal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Custom Content Modal"
          primaryButtonText="Đồng ý"
          secondaryButtonText="Hủy"
        >
          <div className="space-y-4">
            <div className="p-4 bg-gray-100 rounded-lg">
              <h3 className="font-semibold mb-2">Custom Section 1</h3>
              <p className="text-sm text-gray-600">
                This is custom content that can be anything you want.
              </p>
            </div>
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold mb-2 text-blue-800">Custom Section 2</h3>
              <ul className="list-disc list-inside text-sm text-blue-600">
                <li>Custom list item 1</li>
                <li>Custom list item 2</li>
                <li>Custom list item 3</li>
              </ul>
            </div>
          </div>
        </Modal>
      </>
    );
  },
};

// Loading State Modal
export const LoadingState: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    const handleConfirm = () => {
      setLoading(true);
      setTimeout(() => {
        setLoading(false);
        setIsOpen(false);
      }, 2000);
    };

    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-gray-500 text-white rounded-lg"
        >
          Open Loading Modal
        </button>
        <Modal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Processing Request"
          description="Click confirm to see loading state"
          primaryButtonText={loading ? "Processing..." : "Confirm"}
          secondaryButtonText="Cancel"
          primaryButtonLoading={loading}
          primaryButtonDisabled={loading}
          onPrimaryAction={handleConfirm}
          closeOnBackdropClick={!loading}
        />
      </>
    );
  },
};