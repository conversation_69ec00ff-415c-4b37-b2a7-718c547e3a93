import React from 'react';
import { Modal, ModalProps } from './Modal';
import warningMascot from '@shared/assets/images/mascotV3/warning.png';

export interface WarningModalProps extends Omit<ModalProps, 'variant' | 'iconType'> {
  // Warning specific props
  warningList?: string[];
  showMascot?: boolean;
  highlightText?: string;
  timeLimit?: number; // in minutes
}

export const WarningModal: React.FC<WarningModalProps> = ({
  showMascot = true,
  warningList,
  highlightText,
  timeLimit,
  title = 'Lưu ý',
  description,
  primaryButtonText = 'Xác nhận',
  secondaryButtonText = 'Để sau',
  children,
  ...rest
}) => {
  return (
    <Modal
      {...rest}
      variant="warning"
      title={title}
      description={description}
      primaryButtonText={primaryButtonText}
      secondaryButtonText={secondaryButtonText}
      customIcon={showMascot ? warningMascot : undefined}
      iconType={!showMascot ? 'warning' : undefined}
    >
      {/* Warning list content */}
      {warningList && warningList.length > 0 && (
        <div className="space-y-2 mb-4">
          <p className="font-semibold text-sm text-[#1A1818]">
            Lưu ý khi sử dụng:
          </p>
          <div className="space-y-2">
            {warningList.map((warning, index) => (
              <div key={index} className="flex gap-2">
                <span className="text-sm text-[#1A1818]">{index + 1}.</span>
                <p className="text-sm text-gray-700 flex-1">
                  {warning}
                  {index === 1 && timeLimit && (
                    <>
                      {' '}
                      <span className="text-red-500 font-semibold">
                        {timeLimit} phút
                      </span>
                      .{' '}
                      <span className="text-red-500 font-semibold">
                        Không thể hoàn tác
                      </span>
                      .
                    </>
                  )}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Highlight text */}
      {highlightText && (
        <div 
          className="px-3 py-2 rounded-md bg-orange-50 border border-orange-200"
        >
          <p className="text-sm text-orange-700 font-medium">
            {highlightText}
          </p>
        </div>
      )}

      {children}
    </Modal>
  );
};

export default WarningModal;