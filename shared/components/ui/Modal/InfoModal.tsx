import React from 'react';
import { Modal, ModalProps } from './Modal';

export interface InfoModalProps extends Omit<ModalProps, 'variant' | 'iconType'> {
  // Info specific props
  infoList?: string[];
  showIcon?: boolean;
}

export const InfoModal: React.FC<InfoModalProps> = ({
  infoList,
  showIcon = true,
  title = 'Thông báo',
  description,
  primaryButtonText = 'Đã hiểu',
  children,
  ...rest
}) => {
  return (
    <Modal
      {...rest}
      variant="info"
      title={title}
      description={description}
      primaryButtonText={primaryButtonText}
      iconType={showIcon ? 'info' : undefined}
    >
      {/* Info list content */}
      {infoList && infoList.length > 0 && (
        <div className="space-y-2 text-left">
          {infoList.map((info, index) => (
            <div key={index} className="flex gap-2">
              <span className="text-blue-500">•</span>
              <p className="text-sm text-gray-700 flex-1">{info}</p>
            </div>
          ))}
        </div>
      )}
      
      {children}
    </Modal>
  );
};

export default InfoModal;