import React from 'react';
import { Modal, ModalProps } from './Modal';

export interface GiftModalProps extends Omit<ModalProps, 'variant'> {
  // Gift specific props
  giftAmount?: number | string;
  giftType?: 'point' | 'voucher' | 'reward';
  giftName?: string;
  showCoinIcon?: boolean;
}

export const GiftModal: React.FC<GiftModalProps> = ({
  giftAmount,
  giftType = 'point',
  giftName,
  showCoinIcon = true,
  title = 'Ya<PERSON>, trúng rồi!',
  description,
  primaryButtonText = 'Quay tiếp',
  children,
  ...rest
}) => {
  // Build description based on gift type
  const buildDescription = () => {
    if (description) return description;
    
    switch (giftType) {
      case 'point':
        return `Bạn đã nhận được ${giftAmount} POINT. Quà sẽ được thêm vào kho điểm của bạn.`;
      case 'voucher':
        return giftName 
          ? `Đã trúng ${giftName}`
          : `Đã trúng Voucher giảm ${giftAmount}`;
      case 'reward':
        return giftName 
          ? `Chúc mừng bạn đã nhận được ${giftName}`
          : 'Chúc mừng bạn đã nhận được phần thưởng!';
      default:
        return 'Chúc mừng bạn!';
    }
  };

  return (
    <Modal
      {...rest}
      variant="gift"
      title={title}
      description={buildDescription()}
      primaryButtonText={primaryButtonText}
    >
      {/* Custom gift content with coin icon and amount */}
      {showCoinIcon && giftAmount && giftType === 'point' && (
        <div className="flex justify-center items-center gap-2 mb-4">
          <div className="relative">
            {/* Coin icon placeholder */}
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-xl">₫</span>
            </div>
            {/* Amount badge */}
            <div className="absolute -right-2 -bottom-2 w-9 h-9 bg-[#F65D79] rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-xs">
                +{giftAmount}
              </span>
            </div>
          </div>
        </div>
      )}
      
      {children}
    </Modal>
  );
};

export default GiftModal;