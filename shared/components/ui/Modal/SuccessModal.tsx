import React from 'react';
import { Modal, ModalProps } from './Modal';
import successImage from '../../../assets/images/modals/success.png';

export interface SuccessModalProps extends Omit<ModalProps, 'variant' | 'iconType' | 'customIcon'> {
  // Success specific props
  showImage?: boolean;
  points?: number;
  rewardName?: string;
}

export const SuccessModal: React.FC<SuccessModalProps> = ({
  showImage = true,
  points,
  rewardName,
  title = 'Chúc mừng bạn',
  description,
  primaryButtonText = 'Xác nhận',
  ...rest
}) => {
  // Build description with points if provided
  const buildDescription = () => {
    if (description) return description;
    
    if (points && rewardName) {
      return `Bạn đã nhận được ${points} POINT từ ${rewardName}`;
    } else if (points) {
      return `Bạn đã nhận được ${points} POINT. Quà sẽ được thêm vào kho điểm của bạn.`;
    } else if (rewardName) {
      return `Đã trúng ${rewardName}`;
    }
    
    return 'Thao tác thành công!';
  };

  return (
    <Modal
      {...rest}
      variant="success"
      title={title}
      description={buildDescription()}
      primaryButtonText={primaryButtonText}
      customIcon={showImage ? successImage : undefined}
      iconType={!showImage ? 'success' : undefined}
    />
  );
};

export default SuccessModal;