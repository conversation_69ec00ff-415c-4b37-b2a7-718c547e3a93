import React, { useEffect, useState } from 'react';
import { cn } from '../../../utils';
import { Button } from '../Button/Button';

// Base Modal Props
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  
  // Content
  title?: string;
  description?: string;
  children?: React.ReactNode;
  
  // Icon/Image
  icon?: React.ReactNode;
  iconType?: 'success' | 'warning' | 'error' | 'info' | 'custom';
  customIcon?: string;
  
  // Buttons
  primaryButtonText?: string;
  secondaryButtonText?: string;
  onPrimaryAction?: () => void;
  onSecondaryAction?: () => void;
  primaryButtonLoading?: boolean;
  primaryButtonDisabled?: boolean;
  
  // Styling
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info' | 'gift';
  showCloseButton?: boolean;
  closeOnBackdropClick?: boolean;
  maxWidth?: 'sm' | 'md' | 'lg';
}

// Icon components for different states
const SuccessIcon = () => (
  <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
    <svg className="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
  </div>
);

const WarningIcon = () => (
  <div className="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center">
    <svg className="w-10 h-10 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
    </svg>
  </div>
);

const ErrorIcon = () => (
  <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
    <svg className="w-10 h-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  </div>
);

const InfoIcon = () => (
  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
    <svg className="w-10 h-10 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  </div>
);

const GiftIcon = () => (
  <div className="w-20 h-20 bg-pink-100 rounded-full flex items-center justify-center">
    <svg className="w-10 h-10 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
    </svg>
  </div>
);

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  icon,
  iconType,
  customIcon,
  primaryButtonText,
  secondaryButtonText,
  onPrimaryAction,
  onSecondaryAction,
  primaryButtonLoading = false,
  primaryButtonDisabled = false,
  className,
  variant = 'default',
  showCloseButton = true,
  closeOnBackdropClick = true,
  maxWidth = 'sm',
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const handleBackdropClick = () => {
    if (closeOnBackdropClick) {
      handleClose();
    }
  };

  const handlePrimaryAction = () => {
    if (onPrimaryAction) {
      onPrimaryAction();
    } else {
      handleClose();
    }
  };

  const handleSecondaryAction = () => {
    if (onSecondaryAction) {
      onSecondaryAction();
    } else {
      handleClose();
    }
  };

  // Get icon based on type
  const getIcon = () => {
    if (icon) return icon;
    if (customIcon) {
      return (
        <img 
          src={customIcon} 
          alt="Modal icon" 
          className="w-20 h-20 object-contain"
        />
      );
    }
    
    switch (iconType || variant) {
      case 'success':
        return <SuccessIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'error':
        return <ErrorIcon />;
      case 'info':
        return <InfoIcon />;
      case 'gift':
        return <GiftIcon />;
      default:
        return null;
    }
  };

  // Get max width class
  const getMaxWidthClass = () => {
    switch (maxWidth) {
      case 'sm':
        return 'max-w-[343px]';
      case 'md':
        return 'max-w-md';
      case 'lg':
        return 'max-w-lg';
      default:
        return 'max-w-[343px]';
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center transition-all duration-300',
        isAnimating ? 'bg-black bg-opacity-50' : 'bg-transparent'
      )}
      onClick={handleBackdropClick}
    >
      <div
        className={cn(
          'bg-white rounded-xl mx-4 transition-all duration-300 ease-out',
          getMaxWidthClass(),
          isAnimating 
            ? 'scale-100 opacity-100' 
            : 'scale-95 opacity-0',
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        {showCloseButton && (
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        )}

        {/* Content */}
        <div className="p-6">
          {/* Icon */}
          {getIcon() && (
            <div className="flex justify-center mb-6">
              {getIcon()}
            </div>
          )}

          {/* Title */}
          {title && (
            <h2 
              className="text-2xl font-bold text-[#1A1818] text-center mb-2"
              style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
            >
              {title}
            </h2>
          )}

          {/* Description */}
          {description && (
            <p 
              className="text-sm text-[#1A1818] text-center mb-6"
              style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
            >
              {description}
            </p>
          )}

          {/* Custom content */}
          {children && (
            <div className="mb-6">
              {children}
            </div>
          )}

          {/* Buttons */}
          {(primaryButtonText || secondaryButtonText) && (
            <div className="flex flex-col gap-4">
              {primaryButtonText && (
                <Button
                  onClick={handlePrimaryAction}
                  disabled={primaryButtonDisabled || primaryButtonLoading}
                  loading={primaryButtonLoading}
                  variant="primary"
                  fullWidth
                  className="h-11"
                >
                  {primaryButtonText}
                </Button>
              )}
              
              {secondaryButtonText && (
                <button
                  onClick={handleSecondaryAction}
                  className="w-full h-11 text-[#F65D79] font-semibold text-sm hover:bg-gray-50 transition-colors rounded-lg"
                  style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
                >
                  {secondaryButtonText}
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Modal;