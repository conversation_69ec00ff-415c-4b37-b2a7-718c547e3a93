import React from 'react';
import vuiLogo from '../../../assets/images/vui-logo.png';

interface PointHistoryItemProps {
  item: {
    id: string;
    adjustedPoint: number;
    adjustedType: 'REDEEM' | 'ISSUE' | 'EXPIRE' | 'REFUND';
    description: {
      note: string | null;
    };
    createdAt: string;
    logo: string | null;
    currencyCode: string;
    brandCode: string;
  };
  index: number;
  isLast: boolean;
}

export const PointHistoryItem: React.FC<PointHistoryItemProps> = ({ 
  item, 
  index, 
  isLast 
}) => {
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }) + ' • ' + date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const colorLabel = item.adjustedType === 'ISSUE' ? 'text-[#0DC98B]' : 'text-[#BF0301]';
  const pointText = item.adjustedType === 'ISSUE' 
    ? `+ ${formatNumber(item.adjustedPoint)}`
    : `- ${formatNumber(item.adjustedPoint)}`;

  return (
    <div 
      className={`flex items-center justify-between mx-4 py-4 ${!isLast ? 'border-b border-[#ECECEC]' : ''}`}
    >
      <div className="flex-1 mr-6">
        <div 
          className="text-xs text-[#9A9A9A] mb-1"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          {formatDateTime(item.createdAt)}
        </div>
        <div 
          className="text-sm text-[#1A1818] line-clamp-2"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          {item.description?.note || ''}
        </div>
      </div>
      <div className="flex items-center gap-2">
        <span 
          className={`text-sm font-bold ${colorLabel}`}
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          {pointText}
        </span>
        <img 
          src={vuiLogo} 
          alt="VUI" 
          className="w-6 h-6 rounded-full object-cover"
        />
      </div>
    </div>
  );
};

export default PointHistoryItem;