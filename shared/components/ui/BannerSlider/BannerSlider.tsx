import React from "react";
import { cn } from "../../../utils";
import { BannerType } from "../../../services/api/entertainmentService";
import { BannerCarousel } from "../BannerCarousel/BannerCarousel";

export interface BannerSliderProps {
  banners: BannerType[];
  onBannerClick?: (banner: BannerType) => void;
  className?: string;
  loading?: boolean;
  error?: string | null;
  onRetry?: () => void;
}

export const BannerSlider: React.FC<BannerSliderProps> = ({
  banners,
  onBannerClick,
  className,
  loading = false,
  error = null,
  onRetry,
}) => {
  // Convert BannerType to BannerItem format for BannerCarousel
  const bannerItems = banners.map((banner) => ({
    id: banner._id,
    imageUrl: banner.imageUrl,
    title: banner.title,
    subtitle: banner.description,
    link: banner.linkUrl,
  }));

  const handleBannerClick = (bannerItem: any) => {
    if (onBannerClick) {
      // Find the original banner data
      const originalBanner = banners.find((b) => b._id === bannerItem.id);
      if (originalBanner) {
        onBannerClick(originalBanner);
      }
    }
  };

  if (error) {
    return (
      <div className={cn("w-full px-4 py-4", className)}>
        <div className="flex flex-col items-center justify-center py-8">
          <p className="text-red-500 text-sm mb-4">{error}</p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Thử lại
            </button>
          )}
        </div>
      </div>
    );
  }

  if (loading && banners.length === 0) {
    return (
      <div className={cn("w-full px-4 py-4", className)}>
        <div className="w-full h-[156px] rounded-xl bg-gray-200 animate-pulse" />
      </div>
    );
  }

  if (banners.length === 0) {
    return null; // Don't render anything if no banners
  }

  return (
    <div className={cn("w-full", className)}>
      <BannerCarousel
        banners={bannerItems}
        onBannerClick={handleBannerClick}
        className="mb-6"
      />
    </div>
  );
};

export default BannerSlider;
