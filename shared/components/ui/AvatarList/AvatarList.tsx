import React from 'react';
import { Lock } from 'lucide-react';
import type { Avatar } from '../../../services/api/avatar';
import { resizeImage } from '../../../utils';

interface AvatarListProps {
  avatars: Avatar[];
  selectedAvatar: Avatar | null;
  onSelectAvatar: (avatar: Avatar) => void;
  loading?: boolean;
  isMyAvatar?: boolean;
}

export const AvatarList: React.FC<AvatarListProps> = ({
  avatars,
  selectedAvatar,
  onSelectAvatar,
  loading,
  isMyAvatar
}) => {
  if (loading) {
    return (
      <div className="grid grid-cols-3 gap-6">
        {[...Array(9)].map((_, i) => (
          <div key={i} className="flex flex-col items-center">
            <div className="w-[60px] h-[60px] rounded-full bg-gray-200 animate-pulse" />
            <div className="w-16 h-3 bg-gray-200 rounded mt-2 animate-pulse" />
          </div>
        ))}
      </div>
    );
  }

  if (avatars.length === 0) {
    const message = isMyAvatar
      ? 'Bạn chưa có avatar nào'
      : 'Có lỗi xảy ra';
    
    return (
      <div className="flex flex-col items-center justify-center py-16">
  
        <p className="text-gray-600 text-sm text-center px-12">
          {message}
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-3 gap-y-6 pb-8">
      {avatars.map((avatar) => (
        <AvatarItem
          key={avatar.id}
          avatar={avatar}
          isSelected={selectedAvatar?.id === avatar.id}
          onSelect={() => onSelectAvatar(avatar)}
        />
      ))}
    </div>
  );
};

interface AvatarItemProps {
  avatar: Avatar;
  isSelected: boolean;
  onSelect: () => void;
}

const AvatarItem: React.FC<AvatarItemProps> = ({
  avatar,
  isSelected,
  onSelect
}) => {
  const containerSize = isSelected ? 80 : 60;
  const imageSize = avatar.isDefault ? 80 : (isSelected ? 80 : 60);
  
  return (
    <button
      onClick={onSelect}
      className="flex flex-col items-center w-full gap-1"
      style={{ height: '106px' }}
    >
      <div 
        className={`relative rounded-full bg-white flex items-center justify-center transition-all ${
          isSelected ? 'mt-0' : 'mt-2.5'
        } ${
          avatar.isDefault ? 'border-[2px] border-[#DAA520]' : ''
        } ${
          isSelected ? 'border border-gray-800 bg-gray-200' : ''
        }`}
        style={{ 
          width: `${containerSize}px`, 
          height: `${containerSize}px` 
        }}
      >
        <div className="rounded-full overflow-hidden">
          <img
            src={resizeImage(avatar.avatarImage, { width: imageSize * 2, height: imageSize * 2, quality: 85, fit: 'cover' })}
            alt={avatar.avatarName}
            className="rounded-full object-cover"
            style={{
              width: `${imageSize}px`,
              height: `${imageSize}px`
            }}
          />
        </div>
        
        {!avatar.enable && (
          <>
            <div className="absolute -right-3 top-0 z-10">
              <div className="w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center">
                <Lock size={12} className="text-white" />
              </div>
            </div>
            <div className="absolute inset-0 bg-black/25 rounded-full" />
          </>
        )}
      </div>
      
      <span 
        className={`text-[11px] leading-4 text-center max-w-[84px] px-1 truncate ${
          isSelected ? 'font-bold text-black' : 'font-normal text-black'
        }`}
      >
        {avatar.avatarName}
      </span>
    </button>
  );
};