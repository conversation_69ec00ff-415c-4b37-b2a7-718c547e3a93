import React from 'react';
import { formatPoints } from '../../../utils/formatNumber';

interface BrandCurrencyType {
  currencyCode: string;
  logo: string;
  name: string;
  totalBalance: number;
  merchantCode: string;
}

interface CardBrandPointProps {
  data: BrandCurrencyType;
  index: number;
  onPress: () => void;
}

export const CardBrandPoint: React.FC<CardBrandPointProps> = ({ 
  data, 
  index, 
  onPress 
}) => {

  const marginRight = (index + 1) % 3 === 0 ? '' : 'mr-3';

  return (
    <button
      onClick={onPress}
      className={`bg-[#F7CC15] w-[calc((100%-24px)/3)] h-[124px] rounded-lg p-3 flex flex-col items-center justify-between mt-3 ${marginRight}`}
      style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
    >
      <div 
        className="text-2xs font-semibold text-[#1A1818] text-center flex items-center justify-center mb-3 px-1"
        style={{ 
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          lineHeight: '1.3',
          wordBreak: 'break-word',

        }}
      >
        {data.name}
      </div>
      <div className="bg-[#ffefb3] rounded-full px-2 py-1.5 flex items-center justify-between w-full">
        <img
          src={data.logo}
          alt={data.name}
          className="w-6 h-6 rounded-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
          }}
        />
        <div className="text-sm font-semibold text-[#1A1818] flex-1 text-center ml-1">
          {formatPoints(data.totalBalance)}
        </div>
      </div>
    </button>
  );
};

export const CardBrandPointSkeleton: React.FC<{ index: number }> = ({ index }) => {
  const marginRight = (index + 1) % 3 === 0 ? '' : 'mr-3';

  return (
    <div
      className={`bg-[#ECECEC] w-[calc((100%-24px)/3)] h-[124px] rounded-lg p-3 flex flex-col justify-between mt-3 ${marginRight} animate-pulse`}
    >
      <div className="bg-[#F0F0F0] h-[22px] w-[58px] rounded mx-auto"></div>
      <div className="relative">
        <div className="bg-[#F0F0F0] h-9 rounded-full w-full"></div>
        <div className="absolute left-2 top-1.5 w-6 h-6 bg-[#ECECEC] rounded-full"></div>
      </div>
    </div>
  );
};

export default CardBrandPoint;