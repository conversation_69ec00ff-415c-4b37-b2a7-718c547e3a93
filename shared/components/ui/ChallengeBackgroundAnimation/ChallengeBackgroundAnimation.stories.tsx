import type { Meta, StoryObj } from '@storybook/react';
import React, { useState, useEffect } from 'react';
import ChallengeBackgroundAnimation from './ChallengeBackgroundAnimation';

const meta: Meta<typeof ChallengeBackgroundAnimation> = {
  title: 'Components/Challenge/ChallengeBackgroundAnimation',
  component: ChallengeBackgroundAnimation,
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Component wrapper to simulate scroll behavior
const ScrollSimulator: React.FC<{ 
  backgroundHeader: string;
  autoScroll?: boolean;
}> = ({ backgroundHeader, autoScroll = false }) => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    if (!autoScroll) return;
    
    const interval = setInterval(() => {
      setScrollY(prev => (prev >= 300 ? 0 : prev + 5));
    }, 50);

    return () => clearInterval(interval);
  }, [autoScroll]);

  const handleScroll = (e: React.ChangeEvent<HTMLInputElement>) => {
    setScrollY(Number(e.target.value));
  };

  return (
    <div className="w-full">
      {/* Control Panel */}
      <div className="bg-gray-100 p-4 mb-4">
        <label className="block text-sm font-medium mb-2">
          Scroll Position: {scrollY}px
        </label>
        <input
          type="range"
          min="0"
          max="400"
          value={scrollY}
          onChange={handleScroll}
          className="w-full"
          disabled={autoScroll}
        />
        <div className="text-xs text-gray-600 mt-1">
          Drag to simulate scroll or enable auto-scroll below
        </div>
      </div>

      {/* Animation Container */}
      <div className="relative w-full h-96 overflow-hidden border border-gray-300 rounded-lg">
        <ChallengeBackgroundAnimation
          positionY={scrollY}
          backgroundHeader={backgroundHeader}
        />
        
        {/* Content overlay */}
        <div className="relative z-10 p-8 text-white text-center">
          <h1 className="text-2xl font-bold mb-4">Challenge Title</h1>
          <p className="text-lg">Scroll to see the parallax effect</p>
          <div className="mt-8 text-sm opacity-80">
            Scroll Y: {scrollY}px
          </div>
        </div>
      </div>
    </div>
  );
};

export const BlueBackground: Story = {
  render: () => (
    <ScrollSimulator backgroundHeader="#3B82F6" />
  ),
};

export const GreenBackground: Story = {
  render: () => (
    <ScrollSimulator backgroundHeader="#10B981" />
  ),
};

export const PurpleBackground: Story = {
  render: () => (
    <ScrollSimulator backgroundHeader="#8B5CF6" />
  ),
};

export const RedBackground: Story = {
  render: () => (
    <ScrollSimulator backgroundHeader="#EF4444" />
  ),
};

export const GradientBackground: Story = {
  render: () => (
    <ScrollSimulator backgroundHeader="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" />
  ),
};

export const AutoScrollDemo: Story = {
  render: () => (
    <ScrollSimulator backgroundHeader="#F59E0B" autoScroll={true} />
  ),
};

// Simple static stories for basic testing
export const StaticBlue: Story = {
  args: {
    positionY: 0,
    backgroundHeader: '#3B82F6',
  },
  decorators: [
    (Story) => (
      <div className="relative w-full h-96 overflow-hidden">
        <Story />
        <div className="relative z-10 p-8 text-white text-center">
          <h1 className="text-2xl font-bold">Static Background</h1>
        </div>
      </div>
    ),
  ],
};

export const StaticScaled: Story = {
  args: {
    positionY: 200,
    backgroundHeader: '#10B981',
  },
  decorators: [
    (Story) => (
      <div className="relative w-full h-96 overflow-hidden">
        <Story />
        <div className="relative z-10 p-8 text-white text-center">
          <h1 className="text-2xl font-bold">Scaled Background</h1>
          <p>positionY = 200px</p>
        </div>
      </div>
    ),
  ],
};