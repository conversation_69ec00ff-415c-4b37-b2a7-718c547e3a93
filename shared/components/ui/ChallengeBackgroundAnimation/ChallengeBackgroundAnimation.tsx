import React, { useEffect, useState } from 'react';

interface ChallengeBackgroundAnimationProps {
  positionY?: number;
  backgroundHeader: string;
}

const ChallengeBackgroundAnimation: React.FC<ChallengeBackgroundAnimationProps> = ({
  positionY = 0,
  backgroundHeader,
}) => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    if (positionY !== 0) {
      setScrollY(positionY);
    }
  }, [positionY]);

  const THUMBNAIL_HEIGHT = 500;
  
  const scale = Math.max(1, 3 - (scrollY / THUMBNAIL_HEIGHT) * 2);

  return (
    <div
      className="absolute inset-0 transition-transform duration-100 ease-out"
      style={{
        height: `${THUMBNAIL_HEIGHT}px`,
        backgroundColor: backgroundHeader,
        transform: `scale(${scale})`,
      }}
    />
  );
};

export default ChallengeBackgroundAnimation;