import React from "react";
import { cn } from "../../../utils";
import { iconsAssets } from "../../../assets/icons";

export interface VUIPointsIconProps {
  className?: string;
}

export const VUIPointsIcon: React.FC<VUIPointsIconProps> = ({
  className = "",
}) => {
  return (
    <div
      className={cn(
        "w-6 h-6 rounded-full flex items-center justify-center",
        className
      )}
    >
      <img
        src={iconsAssets.VUIPointsIcon}
        alt="VUI Points"
        className={cn("w-full h-full")}
      />
    </div>
  );
};

VUIPointsIcon.displayName = "VUIPointsIcon";

export default VUIPointsIcon;
