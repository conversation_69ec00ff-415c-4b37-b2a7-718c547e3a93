import React from 'react';
import { Button } from '../Button';
import billScanSuccessStars from './assets/bill-scan-success-stars.svg?url';
import billScanSuccessReceipt from './assets/bill-scan-success-receipt-5c03ac.png?url';
import checkIconBg from './assets/check-icon-bg.svg?url';
import checkIcon from './assets/check-icon.svg?url';

export interface CameraSuccessStateProps {
  onGoHome: () => void;
  onViewCapturedReceipts: () => void;
}

export const CameraSuccessState: React.FC<CameraSuccessStateProps> = ({ 
  onGoHome, 
  onViewCapturedReceipts 
}) => {
  return (
    <div className="w-full h-full bg-[#EFF3F6] relative overflow-hidden">
      {/* Background Stars */}
      <div className="absolute top-[81px] left-[19px] w-[336px] h-[411px]">
        <img 
          src={billScanSuccessStars} 
          alt="" 
          className="w-full h-full object-cover"
        />
      </div>

      {/* Main Content */}
      <div className="absolute top-[129px] left-[24px] right-[24px] flex flex-col items-center gap-6">
        {/* Receipt Image with Check Icon */}
        <div className="flex flex-col items-center">
          <div className="relative">
            <img 
              src={billScanSuccessReceipt}
              alt="Captured receipt"
              className="w-[119px] h-[170px] object-cover rounded border-2 border-white"
            />
          </div>
          <div className="relative w-10 h-10 -mt-5">
            <img 
              src={checkIconBg}
              alt=""
              className="absolute inset-0 w-full h-full"
            />
            <img 
              src={checkIcon}
              alt="Success"
              className="absolute top-[15px] left-[13.75px] w-[13.75px] h-[9.38px]"
            />
          </div>
        </div>

        {/* Success Message */}
        <div className="flex flex-col items-center gap-2">
          <h2 className="text-[#1A1818] text-lg font-bold text-center leading-[24px] w-[327px]" style={{ fontFamily: 'Archia' }}>
            Chụp hóa đơn thành công
          </h2>
          <p className="text-[#1A1818] text-sm text-center leading-[22px] w-[311px]" style={{ fontFamily: 'Archia' }}>
            Chúng tôi sẽ xử lý hoá đơn của bạn trong tối đa 48h tới. Hãy giữ lại hoá đơn và theo dõi thông báo từ app nha.
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="absolute bottom-[16px] left-[16px] right-[16px] flex flex-col items-center gap-4">
        <Button
          variant="primary"
          size="large"
          onClick={onGoHome}
          className="w-full h-11 bg-[#F65D79] rounded-lg"
        >
          <span className="text-white text-sm font-semibold text-center leading-[22px]" style={{ fontFamily: 'Archia' }}>
            Về trang chủ
          </span>
        </Button>
        
        <button
          onClick={onViewCapturedReceipts}
          className="h-[22px]"
        >
          <span className="text-[#F65D79] text-sm font-semibold text-center leading-[22px]" style={{ fontFamily: 'Archia' }}>
            Xem hóa đơn đã chụp
          </span>
        </button>
      </div>
    </div>
  );
};

export default CameraSuccessState;