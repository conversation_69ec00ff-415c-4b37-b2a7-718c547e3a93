import React, { useEffect, useState, useCallback } from 'react';
import { cn } from '../../../utils';

interface TimeLeft {
  hours: number;
  minutes: number;
  seconds: number;
  total: number;
}

export interface AutoRedeemCountdownProps {
  executeAutoRedeemDate: string;
  onExpired?: () => void;
  className?: string;
}

export const AutoRedeemCountdown: React.FC<AutoRedeemCountdownProps> = ({
  executeAutoRedeemDate,
  onExpired,
  className,
}) => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ hours: 0, minutes: 0, seconds: 0, total: 0 });
  const [isExpired, setIsExpired] = useState(false);

  const calculateTimeLeft = useCallback(() => {
    const now = new Date().getTime();
    const targetTime = new Date(executeAutoRedeemDate).getTime();
    const difference = targetTime - now;

    if (difference <= 0) {
      setIsExpired(true);
      if (onExpired) {
        onExpired();
      }
      return { hours: 0, minutes: 0, seconds: 0, total: 0 };
    }

    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { hours, minutes, seconds, total: difference };
  }, [executeAutoRedeemDate, onExpired]);

  useEffect(() => {
    const timer = setInterval(() => {
      const newTimeLeft = calculateTimeLeft();
      setTimeLeft(newTimeLeft);
      
      if (newTimeLeft.total <= 0) {
        clearInterval(timer);
      }
    }, 1000);

    // Calculate immediately on mount
    setTimeLeft(calculateTimeLeft());

    return () => clearInterval(timer);
  }, [calculateTimeLeft]);

  if (isExpired) {
    return (
      <div className={cn("flex flex-col items-center", className)}>
        {/* Expired state - matching mobile style */}
        <div className="w-full bg-[#E8F5E9] rounded-lg p-3 mb-3">
          <div className="flex items-center justify-center gap-2">
            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <svg 
                width="14" 
                height="14" 
                viewBox="0 0 14 14" 
                fill="none"
              >
                <path 
                  d="M3 7L6 10L11 4" 
                  stroke="white" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <span className="text-sm font-semibold text-[#1A1818]">
              Đã dùng
            </span>
          </div>
        </div>
        <p className="text-xs text-center px-2" style={{ color: 'rgb(128, 128, 128)' }}>
          Ưu đãi đã được chuyển sang trạng thái "Đã dùng" và không thể khôi phục.
        </p>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col items-center", className)}>
      {/* Countdown warning - matching mobile style */}
      <div 
        className="w-full rounded-md px-3 py-3 flex items-center gap-2 mb-3"
        style={{ backgroundColor: 'rgba(255, 116, 37, 0.15)' }}
      >
        <div 
          className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0"
          style={{ backgroundColor: '#FF7425' }}
        >
          <svg 
            width="12" 
            height="12" 
            viewBox="0 0 12 12" 
            fill="none"
          >
            <path 
              d="M6 2V6.5M6 9.5V9.505" 
              stroke="white" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </div>
        
        <span className="text-sm font-semibold text-[#1A1818] pr-1">
          Thời gian sử dụng:
        </span>
        
        <span className="text-base font-bold text-[#FF7425]">
          {timeLeft.hours.toString().padStart(2, '0')}:
          {timeLeft.minutes.toString().padStart(2, '0')}:
          {timeLeft.seconds.toString().padStart(2, '0')}
        </span>
      </div>

      {/* Info text */}
      <p className="text-xs text-center px-2" style={{ color: 'rgb(128, 128, 128)' }}>
        *Ưu đãi sẽ được đánh dấu "Đã dùng" và không thể khôi phục sau thời gian trên.
      </p>
    </div>
  );
};

export default AutoRedeemCountdown;