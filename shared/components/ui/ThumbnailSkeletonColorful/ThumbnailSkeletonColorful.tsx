import React from 'react';
import { Skeleton } from '../Skeleton';

export interface ThumbnailSkeletonColorfulProps {
  className?: string;
}

export const ThumbnailSkeletonColorful: React.FC<ThumbnailSkeletonColorfulProps> = ({
  className = '',
}) => {
  return (
    <Skeleton.Container className={`relative ${className}`}>
      <div className="w-[262px] h-[197px] bg-white rounded-lg shadow-sm overflow-hidden">
        {/* Main content area */}
        <div className="flex-1 p-4 flex items-center gap-4 h-[149px]">
          {/* Left side - text lines */}
          <div className="flex-1 flex flex-col justify-around h-full">
            {Array.from({ length: 3 }).map((_, index) => (
              <Skeleton.Rect
                key={index}
                width="112px"
                height="16px"
                className="rounded"
              />
            ))}
          </div>
          
          {/* Right side - circle */}
          <Skeleton.Circle size="90px" />
        </div>
        
        {/* Dashed line separator */}
        <div className="absolute bottom-12 left-1 right-0 h-1">
          <div className="w-full h-full border-b-2 border-dashed border-white" />
        </div>
      </div>
      
      {/* Bottom colorful section */}
      <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 rounded-b-lg p-4 flex items-center justify-between shadow-sm">
        <Skeleton.Rect width="112px" height="16px" className="rounded bg-white/30" />
        <Skeleton.Circle size="24px" className="bg-white/30" />
      </div>
    </Skeleton.Container>
  );
};

export default ThumbnailSkeletonColorful;