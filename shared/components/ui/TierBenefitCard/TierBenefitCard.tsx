import React from 'react';

interface Benefit {
  image: string;
  description: string;
}

interface TierBenefitCardProps {
  level: number;
  name: string;
  background?: string;
  logo?: string;
  conditions?: Array<{
    value: number;
    code: string;
    name: string;
  }>;
  benefits?: Benefit[];
  offerName?: string;
  description?: string;
  isCurrentTier?: boolean;
  isAchieved?: boolean;
}

export const TierBenefitCard: React.FC<TierBenefitCardProps> = ({
  level,
  name,
  background,
  logo,
  conditions,
  benefits,
  offerName,
  description,
  isCurrentTier = false,
  isAchieved = false
}) => {
  return (
    <div 
      className={`relative rounded-xl overflow-hidden transition-all ${
        isCurrentTier ? 'ring-2 ring-primary shadow-lg' : 'shadow-md'
      }`}
    >
      <div 
        className="p-4"
        style={{ 
          background: background || 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
        }}
      >
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            {logo && (
              <img 
                src={logo} 
                alt={name}
                className="w-12 h-12 rounded-lg object-cover"
              />
            )}
            <div>
              <h3 className="text-lg font-bold text-gray-900">
                {name}
              </h3>
              <p className="text-sm text-gray-600">
                Hạng {level}
              </p>
            </div>
          </div>
          {(isCurrentTier || isAchieved) && (
            <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
              isCurrentTier 
                ? 'bg-primary text-white' 
                : 'bg-green-100 text-green-800'
            }`}>
              {isCurrentTier ? 'Hạng hiện tại' : 'Đã đạt'}
            </span>
          )}
        </div>

        {conditions && conditions.length > 0 && (
          <div className="mb-4 p-3 bg-white/50 rounded-lg">
            <p className="text-sm font-medium text-gray-700 mb-1">Điều kiện:</p>
            {conditions.map((condition, index) => (
              <p key={index} className="text-sm text-gray-600">
                {condition.value} {condition.name}
              </p>
            ))}
          </div>
        )}

        {offerName && (
          <div className="mb-3">
            <p className="text-sm font-medium text-gray-700 mb-1">Ưu đãi:</p>
            <p className="text-sm text-gray-900 font-semibold">{offerName}</p>
          </div>
        )}

        {description && (
          <p className="text-sm text-gray-600 mb-3">{description}</p>
        )}

        {benefits && benefits.length > 0 && (
          <div>
            <p className="text-sm font-medium text-gray-700 mb-2">Quyền lợi:</p>
            <div className="space-y-2">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-start gap-2">
                  {benefit.image && (
                    <img 
                      src={benefit.image} 
                      alt=""
                      className="w-5 h-5 rounded object-cover flex-shrink-0"
                    />
                  )}
                  <p className="text-sm text-gray-600">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};