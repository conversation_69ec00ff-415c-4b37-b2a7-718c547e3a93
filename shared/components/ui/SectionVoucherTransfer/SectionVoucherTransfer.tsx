import React from 'react';

interface SectionVoucherTransferProps {
  transferName: string;
  avatarImage?: string;
  phoneNumber: string;
  ownershipStatus: 'OWNED' | 'RECEIVED' | 'TRANSFERRED';
  receivedDate: string;
  className?: string;
}

const SectionVoucherTransfer: React.FC<SectionVoucherTransferProps> = ({
  transferName,
  avatarImage = '',
  phoneNumber,
  ownershipStatus,
  receivedDate,
  className = '',
}) => {
  const voucherTransferInfo = { title: '', name: '' };

  switch (ownershipStatus) {
    case 'RECEIVED':
      Object.assign(voucherTransferInfo, {
        title: 'Thông tin người tặng',
        name: '<PERSON><PERSON>ờ<PERSON> tặng',
      });
      break;
    case 'TRANSFERRED':
      Object.assign(voucherTransferInfo, {
        title: 'Thông tin người nhận',
        name: 'Người nhận',
      });
      break;
    default:
      break;
  }

  // Random background colors for avatar (similar to getRandomBGColor from mobile)
  const getRandomBGColor = () => {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  // Format date similar to parseDateTimeCenterDot from mobile
  const formatDateTime = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      return `${day}/${month}/${year} • ${hours}:${minutes}`;
    } catch {
      return dateString;
    }
  };

  return (
    <div className={`bg-white mx-4 my-4 rounded-lg p-4 ${className}`}>
      {/* Section Title */}
      <h3 className="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-100 pb-2">
        {voucherTransferInfo.title}
      </h3>

      {/* Content Wrapper with gap similar to mobile */}
      <div className="space-y-3">
        {/* Name Row */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600 text-sm">
            {voucherTransferInfo.name}
          </span>
          <div className="flex items-center">
            {/* Avatar with random background color */}
            <div
              className="w-6 h-6 rounded-full mr-1 flex items-center justify-center overflow-hidden"
              style={{ backgroundColor: getRandomBGColor() }}
            >
              {avatarImage ? (
                <img
                  src={avatarImage}
                  alt="Avatar"
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full" />
              )}
            </div>
            <span className="text-gray-900 font-medium text-sm">
              {transferName}
            </span>
          </div>
        </div>

        {/* Phone Number Row */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600 text-sm">
            Số điện thoại
          </span>
          <span className="text-gray-900 font-medium text-sm">
            {phoneNumber}
          </span>
        </div>

        {/* Transfer Date Row */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600 text-sm">
            Ngày tặng
          </span>
          <span className="text-gray-900 font-medium text-sm">
            {formatDateTime(receivedDate)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default React.memo(SectionVoucherTransfer);