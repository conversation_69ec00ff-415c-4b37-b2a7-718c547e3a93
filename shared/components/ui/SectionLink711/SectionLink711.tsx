import React, { useCallback, useState } from 'react';

interface SectionLink711Props {
  merchantCode: string;
  rewardName: string;
  className?: string;
  onLinkSuccess?: (message: string) => void;
  onLinkError?: (message: string) => void;
}

const SectionLink711: React.FC<SectionLink711Props> = ({
  rewardName,
  merchantCode,
  className = '',
  onLinkSuccess,
  onLinkError,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [isLinking, setIsLinking] = useState(false);

  const paramsTracking = {
    merchant_code: merchantCode,
    reward_name: rewardName || '',
  };

  const hideBottomSheetLink711 = useCallback(() => {
    setShowModal(false);
  }, []);

  const openBottomSheetLink711 = useCallback(() => {
    console.log('7-Eleven link account tracking:', paramsTracking);
    setShowModal(true);
  }, [paramsTracking]);

  const handleConfirmLink = async () => {
    setIsLinking(true);
    
    try {
      // Simulate API call for 7-Eleven linking
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In real implementation, you would call the actual API
      console.log('7-Eleven account linked successfully');
      setShowModal(false);
      
      // Show success message
      if (onLinkSuccess) {
        onLinkSuccess('Đã liên kết xong.');
      } else {
        alert('Đã liên kết xong.');
      }
      
    } catch (error) {
      console.error('7-Eleven linking failed:', error);
      if (onLinkError) {
        onLinkError('Chưa liên kết được. Uống miếng nước và thử lại nha.');
      } else {
        alert('Chưa liên kết được. Uống miếng nước và thử lại nha.');
      }
    } finally {
      setIsLinking(false);
    }
  };

  return (
    <div className={`bg-white mx-4 my-4 rounded-lg p-4 ${className}`}>
      {/* Blurred Barcode Background */}
      <div className="relative h-24 w-80 mx-auto mt-1 mb-4 rounded-lg overflow-hidden bg-gradient-to-r from-gray-300 to-gray-400 flex items-center justify-center">
        {/* Simulated blurred barcode pattern */}
        <div className="absolute inset-0 opacity-30">
          <div className="h-full flex items-center justify-center space-x-1">
            {[...Array(20)].map((_, i) => (
              <div
                key={i}
                className="bg-black h-12"
                style={{
                  width: Math.random() * 4 + 1 + 'px',
                  filter: 'blur(2px)'
                }}
              />
            ))}
          </div>
        </div>
        
        {/* Overlay Text */}
        <div className="relative z-10 px-8">
          <p className="text-center font-bold text-gray-800 text-sm leading-tight">
            Liên kết với 7-Eleven để hiện mã ưu đãi
          </p>
        </div>
      </div>

      {/* Link Now Button */}
      <div className="flex justify-center">
        <button
          onClick={openBottomSheetLink711}
          className="px-6 py-2 bg-[#F65D79] text-white text-sm font-medium rounded-lg hover:bg-[#e54d6a] transition-colors"
        >
          Liên kết ngay
        </button>
      </div>

      {/* Modal/Bottom Sheet */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-end justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-t-3xl w-full max-w-md mx-4 mb-0 animate-slide-up">
            <div className="p-6">
              {/* Handle */}
              <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>
              
              {/* Title */}
              <h3 className="text-lg font-bold text-center mb-6">
                Liên kết thông tin với 7-Eleven
              </h3>
              
              {/* Content */}
              <div className="mb-8">
                <p className="text-sm text-gray-700 leading-relaxed">
                  Khi bấm xác nhận, bạn đã:
                </p>
                <div className="mt-3 space-y-2">
                  <p className="text-sm text-gray-700">
                    1. Chắc rằng bạn đủ 18 tuổi trở lên.
                  </p>
                  <p className="text-sm text-gray-700">
                    2. Cho phép 7-Eleven ghi nhận tên và số điện thoại của bạn.
                  </p>
                </div>
              </div>
              
              {/* Buttons */}
              <div className="space-y-3">
                <button
                  onClick={handleConfirmLink}
                  disabled={isLinking}
                  className={`w-full py-3 rounded-lg font-medium text-sm transition-colors ${
                    isLinking
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-[#F65D79] text-white hover:bg-[#e54d6a]'
                  }`}
                >
                  {isLinking ? 'Đang liên kết...' : 'Xác nhận'}
                </button>
                
                <button
                  onClick={hideBottomSheetLink711}
                  disabled={isLinking}
                  className={`w-full py-3 rounded-lg font-medium text-sm transition-colors ${
                    isLinking
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  Để sau
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes slide-up {
          from {
            transform: translateY(100%);
          }
          to {
            transform: translateY(0);
          }
        }
        .animate-slide-up {
          animation: slide-up 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default React.memo(SectionLink711);