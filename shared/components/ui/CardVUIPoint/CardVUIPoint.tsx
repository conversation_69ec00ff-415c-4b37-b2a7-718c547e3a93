import React from 'react';
import { formatPoints } from '../../../utils/formatNumber';
import vuiCoinIcon from '../../../assets/icons/vui.png';

interface CardVUIPointProps {
  point: number;
  onPress: () => void;
}

export const CardVUIPoint: React.FC<CardVUIPointProps> = ({ 
  point, 
  onPress 
}) => {

  return (
    <button
      onClick={onPress}
      className="bg-[#F7CC15] w-full  h-[134px] flex flex-col items-center justify-center rounded-lg p-5 mx-auto"
      style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
    >
      <div className="text-lg font-bold text-[#1A1818] mb-4">
        VUI
      </div>
      <div className="bg-[#ffefb3] rounded-full px-3 py-2 flex items-center gap-2">
        <img 
          src={vuiCoinIcon} 
          alt="VUI Coin" 
          className="w-10 h-10"
        />
        <div className="text-lg font-semibold text-[#1A1818] min-w-0">
          {formatPoints(point)}
        </div>
      </div>
    </button>
  );
};

export default CardVUIPoint;