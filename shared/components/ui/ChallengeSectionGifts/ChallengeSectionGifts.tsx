import React from 'react';

interface Gift {
  giftContent: string;
  giftImage?: string;
  quantity?: number;
  [key: string]: any;
}

interface PackageGift {
  content?: string;
  remain?: number;
  gifts?: Gift[];
}

interface ChallengeSectionGiftsProps {
  packageGift?: PackageGift;
  isExpired: boolean;
  isReceived: boolean;
  state?: string;
}

const ChallengeSectionGifts: React.FC<ChallengeSectionGiftsProps> = ({
  packageGift,
  isExpired,
  state,
}) => {
  const { content = '', remain = 0, gifts } = packageGift || {};

  if (!gifts || gifts.length === 0) return null;

  const renderGiftStatus = () => {
    if (['SUCCESSFULLY', 'RELEASING', 'FAILED'].includes(state || '')) {
      const statusText = state === 'SUCCESSFULLY' ? 'Đã nhận' : 
                        state === 'RELEASING' ? 'Đang xử lý' : 'Thất bại';
      const statusColor = state === 'SUCCESSFULLY' ? 'text-green-600 bg-green-50' :
                         state === 'RELEASING' ? 'text-yellow-600 bg-yellow-50' : 'text-red-600 bg-red-50';
      
      return (
        <div className={`inline-block px-2 py-1 rounded-md text-xs font-medium ${statusColor}`}>
          {statusText}
        </div>
      );
    }

    if (!isExpired && remain > 0) {
      return (
        <div className="inline-block px-2 py-1 rounded-md text-xs font-medium text-blue-600 bg-blue-50">
          Còn lại {remain} phần quà
        </div>
      );
    }

    return null;
  };

  return (
    <div className="bg-white pt-4 pb-2 mb-2 px-4">
      <div className="flex items-center mb-3">
        <div 
          className={`w-5 h-6 mr-3 ${isExpired ? 'grayscale' : ''}`}
        >
          🎁
        </div>
        <h3 className="font-bold text-base">Phần quà của thử thách</h3>
      </div>

      <div className="ml-8 mb-4">
        {renderGiftStatus()}
        {content && (
          <p className="text-sm text-gray-700 mt-2">{content}</p>
        )}
      </div>

      {/* Gift List */}
      <div className="space-y-3">
        {gifts.map((gift, index) => (
          <div 
            key={gift.giftContent}
            className={`flex items-center p-3 bg-gray-50 rounded-lg ${
              isExpired ? 'grayscale opacity-60' : ''
            } ${index === gifts.length - 1 ? '' : 'border-b border-gray-200'}`}
          >
            {gift.giftImage && (
              <img
                src={gift.giftImage}
                alt={gift.giftContent}
                className="w-10 h-10 rounded-lg object-cover mr-3"
              />
            )}
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">
                {gift.giftContent}
              </p>
              {gift.quantity && (
                <p className="text-xs text-gray-500 mt-1">
                  Số lượng: {gift.quantity}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default React.memo(ChallengeSectionGifts);