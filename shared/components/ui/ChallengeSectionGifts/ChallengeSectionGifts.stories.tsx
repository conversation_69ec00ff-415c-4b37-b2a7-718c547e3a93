import type { Meta, StoryObj } from '@storybook/react';
import ChallengeSectionGifts from './ChallengeSectionGifts';

const meta: Meta<typeof ChallengeSectionGifts> = {
  title: 'Components/Challenge/ChallengeSectionGifts',
  component: ChallengeSectionGifts,
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const mockGifts = [
  {
    giftContent: '1.000 điểm VUI',
    giftImage: 'https://via.placeholder.com/40x40/3B82F6/FFFFFF?text=💎',
    quantity: 1,
  },
  {
    giftContent: 'Voucher giảm giá 50.000đ',
    giftImage: 'https://via.placeholder.com/40x40/10B981/FFFFFF?text=🎟️',
    quantity: 1,
  },
  {
    giftContent: 'Cơ hội quay thưởng đặc biệt',
    giftImage: 'https://via.placeholder.com/40x40/F59E0B/FFFFFF?text=🎰',
    quantity: 1,
  },
];

const mockPackageGift = {
  content: '<PERSON><PERSON><PERSON> thành thử thách để nhận ngay những phần quà hấp dẫn này!',
  remain: 10,
  gifts: mockGifts,
};

export const Available: Story = {
  args: {
    packageGift: mockPackageGift,
    isExpired: false,
    isReceived: false,
    state: 'RUNNING',
  },
};

export const Successfully: Story = {
  args: {
    packageGift: mockPackageGift,
    isExpired: false,
    isReceived: true,
    state: 'SUCCESSFULLY',
  },
};

export const Releasing: Story = {
  args: {
    packageGift: mockPackageGift,
    isExpired: false,
    isReceived: false,
    state: 'RELEASING',
  },
};

export const Failed: Story = {
  args: {
    packageGift: mockPackageGift,
    isExpired: false,
    isReceived: false,
    state: 'FAILED',
  },
};

export const Expired: Story = {
  args: {
    packageGift: mockPackageGift,
    isExpired: true,
    isReceived: false,
    state: 'RUNNING',
  },
};

export const NoGiftsRemaining: Story = {
  args: {
    packageGift: {
      ...mockPackageGift,
      remain: 0,
    },
    isExpired: false,
    isReceived: false,
    state: 'RUNNING',
  },
};

export const SingleGift: Story = {
  args: {
    packageGift: {
      content: 'Phần quà duy nhất cho thử thách này',
      remain: 5,
      gifts: [mockGifts[0]],
    },
    isExpired: false,
    isReceived: false,
    state: 'RUNNING',
  },
};

export const WithoutImages: Story = {
  args: {
    packageGift: {
      content: 'Các phần quà không có hình ảnh',
      remain: 8,
      gifts: mockGifts.map(gift => ({
        ...gift,
        giftImage: undefined,
      })),
    },
    isExpired: false,
    isReceived: false,
    state: 'RUNNING',
  },
};

export const LongDescription: Story = {
  args: {
    packageGift: {
      content: 'Hoàn thành thử thách mua sắm tuần này để nhận ngay những phần quà cực kỳ hấp dẫn và giá trị. Các phần quà được chọn lọc kỹ càng để mang lại trải nghiệm tốt nhất cho khách hàng.',
      remain: 15,
      gifts: [
        {
          giftContent: '1.500 điểm VUI - Điểm thưởng cao cấp dành cho thành viên VIP',
          giftImage: 'https://via.placeholder.com/40x40/8B5CF6/FFFFFF?text=⭐',
          quantity: 1,
        },
        {
          giftContent: 'Voucher giảm giá 100.000đ cho đơn hàng từ 500.000đ',
          giftImage: 'https://via.placeholder.com/40x40/EF4444/FFFFFF?text=🎫',
          quantity: 1,
        },
      ],
    },
    isExpired: false,
    isReceived: false,
    state: 'RUNNING',
  },
};

export const Empty: Story = {
  args: {
    packageGift: {
      content: 'Không có phần quà',
      remain: 0,
      gifts: [],
    },
    isExpired: false,
    isReceived: false,
    state: 'RUNNING',
  },
};

export const UndefinedPackage: Story = {
  args: {
    packageGift: undefined,
    isExpired: false,
    isReceived: false,
    state: 'RUNNING',
  },
};