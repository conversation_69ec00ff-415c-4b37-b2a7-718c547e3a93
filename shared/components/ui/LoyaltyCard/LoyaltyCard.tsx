import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import './LoyaltyCard.css';
import qrScanIcon from '../../../assets/icons/qr-scan-icon.svg';
import voucherIcon from '../../../assets/icons/voucher-icon.svg';
import { resizeImage } from '../../../utils';

// Type-safe Swiper components
const SwiperComponent = Swiper as any;
const SwiperSlideComponent = SwiperSlide as any;

export type LoyaltyCardStatus = 'no-join' | 'without-rank' | 'with-rank' | 'max-rank';
export type TierState = 'noRank' | 'hasRankTier' | 'hasRankAndVoucher' | 'maxTier';
export type LoyaltyCardTheme = 'default' | 'merchant-detail';

export interface MerchantTier {
  tierLevel: number;
  tierId: string;
  tierName: string;
  conditions: Array<{
    value: number;
    code: string;
    name: string;
  }>;
}

export interface CurrentTier {
  value: number;
  code: string;
  name: string;
  currencyLogo?: string;
}

export interface LoyaltyCardData {
  status?: LoyaltyCardStatus;
  brandName: string;
  userTitle?: string;
  pointsToNextLevel?: number;
  currentProgress?: number; // 0-100 percentage
  maxProgress?: number;
  badgeIconSrc?: string;
  illustrationSrc?: string;
  onActionClick?: () => void;
  onVoucherClick?: () => void;
  onCardClick?: () => void;
  brandCode?: string;
  merchantCode?: string;
  // Enhanced data from legacy mobile
  tierLevel?: number;
  tierName?: string;
  current?: CurrentTier[];
  merchantTiers?: MerchantTier[];
  giftStatus?: 'NO_GIFT' | 'ERROR' | 'SUCCESS';
  // Custom tier styling (from mobile app pattern)
  tierBackground?: string;
  tierLabelBackground?: string;
  tierProgressBarColor?: string;
  merchantBackgroundColor?: string;
}

export interface LoyaltyCardProps {
  cards?: LoyaltyCardData[];
  theme?: LoyaltyCardTheme;
  className?: string;
  onNavigate?: (path: string) => void;
  // Legacy single card props for backward compatibility
  status?: LoyaltyCardStatus;
  brandName?: string;
  userTitle?: string;
  pointsToNextLevel?: number;
  currentProgress?: number;
  maxProgress?: number;
  badgeIconSrc?: string;
  illustrationSrc?: string;
  onActionClick?: () => void;
  onVoucherClick?: () => void;
  onCardClick?: () => void;
  // Custom tier styling (legacy single card)
  tierBackground?: string;
  tierLabelBackground?: string;
  tierProgressBarColor?: string;
  merchantBackgroundColor?: string;
}

// Barcode Scan Icon Component
const BarcodeScanIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none">
    <path 
      d="M2.25 4.5L13.5 4.5M13.5 4.5L13.5 9M13.5 4.5L9 9" 
      stroke="currentColor" 
      strokeWidth="1.5" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
  </svg>
);

// Coin Icon Components using currencyLogo from data
const CoinIcon16: React.FC<{ className?: string; src?: string }> = ({ className, src }) => (
  src ? <img src={src} alt="Coin" className={className} /> : <div className={className}></div>
);

const CoinIcon20: React.FC<{ className?: string; src?: string }> = ({ className, src }) => (
  src ? <img src={src} alt="Coin" className={className} /> : <div className={className}></div>
);

// Helper functions from legacy mobile
const getStateTier = (cardData: LoyaltyCardData): TierState => {
  if (!cardData.current || !cardData.merchantTiers || cardData.current.length === 0) {
    return 'noRank';
  }
  
  const isOnlyRankTier = cardData.giftStatus === 'NO_GIFT';
  const isRankAndVoucher = !isOnlyRankTier;
  const maxValueTier = cardData.merchantTiers[cardData.merchantTiers.length - 1]?.conditions?.[0]?.value;
  
  if (!maxValueTier) return 'noRank';
  
  const isMaxLevel = cardData.current[0].value >= maxValueTier;
  
  if (isMaxLevel) return 'maxTier';
  if (isRankAndVoucher) return 'hasRankAndVoucher';
  if (isOnlyRankTier) return 'hasRankTier';
  
  return 'noRank';
};

const calculateProgress = (currentValue: number, merchantTiers: MerchantTier[]): { progress: number; pointsToNext: number } => {
  if (!merchantTiers || merchantTiers.length === 0) {
    return { progress: 0, pointsToNext: 0 };
  }
  
  // Find current tier and next tier
  let currentTierIndex = -1;
  for (let i = 0; i < merchantTiers.length; i++) {
    if (currentValue >= merchantTiers[i].conditions[0].value) {
      currentTierIndex = i;
    }
  }
  
  // If at max level
  if (currentTierIndex === merchantTiers.length - 1) {
    return { progress: 100, pointsToNext: 0 };
  }
  
  const currentTierValue = currentTierIndex >= 0 ? merchantTiers[currentTierIndex].conditions[0].value : 0;
  const nextTierValue = merchantTiers[currentTierIndex + 1]?.conditions[0].value || 0;
  
  const pointsToNext = nextTierValue - currentValue;
  const progressInTier = currentValue - currentTierValue;
  const tierRange = nextTierValue - currentTierValue;
  
  const progress = tierRange > 0 ? Math.min((progressInTier / tierRange) * 100, 100) : 0;
  
  return { progress: Math.max(0, progress), pointsToNext: Math.max(0, pointsToNext) };
};

const mapTierStateToLoyaltyStatus = (tierState: TierState): LoyaltyCardStatus => {
  switch (tierState) {
    case 'noRank':
      return 'no-join';
    case 'hasRankTier':
      return 'without-rank';
    case 'hasRankAndVoucher':
      return 'with-rank';
    case 'maxTier':
      return 'max-rank';
    default:
      return 'no-join';
  }
};

// Single Card Component
const SingleLoyaltyCard: React.FC<LoyaltyCardData & { theme: LoyaltyCardTheme; className?: string; onNavigate?: (path: string) => void }> = (cardData) => {
  
  const {
    status: providedStatus,
    theme,
    brandName,
    userTitle,
    pointsToNextLevel: providedPointsToNext,
    currentProgress: providedProgress,
    maxProgress,
    badgeIconSrc,
    illustrationSrc,
    onActionClick,
    onVoucherClick,
    onCardClick,
    className,
    tierBackground,
    tierLabelBackground,
    tierProgressBarColor,
    merchantBackgroundColor,
    merchantCode,
    current,
    merchantTiers,
    tierLevel,
    tierName,
    giftStatus,
  } = cardData;

  // Calculate tier state and progress using legacy mobile logic
  const tierState = getStateTier(cardData);
  const actualStatus = providedStatus || mapTierStateToLoyaltyStatus(tierState);
  
  const { progress: calculatedProgress, pointsToNext: calculatedPointsToNext } = 
    current && merchantTiers && current.length > 0 
      ? calculateProgress(current[0].value, merchantTiers)
      : { progress: providedProgress || 0, pointsToNext: providedPointsToNext || 0 };
  
  const currentProgress = providedProgress !== undefined ? providedProgress : calculatedProgress;
  const pointsToNextLevel = providedPointsToNext !== undefined ? providedPointsToNext : calculatedPointsToNext;
  const status = actualStatus;

  const getUserTitle = () => {
    switch (status) {
      case 'no-join':
        return 'Chưa đăng ký';
      case 'without-rank':
      case 'with-rank':
      case 'max-rank':
      default:
        return userTitle;
    }
  };

  const getActionIcon = () => {
    if (theme === 'merchant-detail' && status === 'with-rank') {
      return <img src={qrScanIcon} alt="QR Scan" className="w-[18px] h-[18px]" />;
    }
    
    if (status === 'no-join') {
      return (
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
          <g>
            <path d="M6.77 10.64L6.77 17.37" stroke="#1A1818" strokeWidth="1.5"/>
            <path d="M0 0L9.86 10.99" stroke="#1A1818" strokeWidth="1.5"/>
            <path d="M5.05 1.89L8.43 4.58" stroke="#1A1818" strokeWidth="1.5"/>
          </g>
        </svg>
      );
    } else if (status === 'without-rank') {
      return (
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
          <path d="M2.25 4.5L13.5 4.5M13.5 4.5L13.5 9M13.5 4.5L9 9" stroke="#1A1818" strokeWidth="1.5"/>
        </svg>
      );
    } else {
      return (
        <img src={voucherIcon} alt="Voucher" className="w-[18px] h-[18px]" />
      );
    }
  };

  const getBackgroundStyle = () => {
    const background = merchantBackgroundColor || tierBackground;
    console.log(background)
    // Check if it's an image URL
    if (background && (background.startsWith('http') || background.startsWith('https'))) {
      return {
        backgroundImage: `url(${resizeImage(background, { width: 686, height: theme === 'merchant-detail' ? 368 : 416, quality: 85, fit: 'cover' })})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    }
    
    // Otherwise treat as color
    return {
      backgroundColor: background || (theme === 'merchant-detail' ? '#F7CC15' : '#0EA151')
    };
  };

  const getProgressBarColor = () => {
    return tierProgressBarColor;
  };

  const getBadgeBackgroundColor = () => {
    return tierLabelBackground;
  };

  const getActionText = () => {
    if (theme === 'merchant-detail' && status === 'with-rank') {
      return 'Quét mã tích điểm';
    }
    
    switch (status) {
      case 'no-join':
        return 'Đăng ký thành viên ngay';
      case 'without-rank':
        return 'Quét mã tích điểm';
      case 'with-rank':
        return 'Dùng ưu đãi thành viên';
      case 'max-rank':
        return 'Dùng ưu đãi thành viên';
      default:
        return 'Đăng ký thành viên ngay';
    }
  };

  const renderBackgroundPattern = () => {
    if (theme === 'merchant-detail') {
      return (
        <div className="absolute inset-0 overflow-hidden">
          {/* Decorative stars */}
          <div className="absolute w-[166.57px] h-[166.57px] top-[-19px] left-[3.11px] opacity-20">
            <div className="w-full h-full rounded-[10.11px] bg-gradient-to-br from-white/40 to-white/20" 
                 style={{ filter: 'drop-shadow(0px 4px 20px rgba(0, 0, 0, 0.12))' }}></div>
          </div>
          <div className="absolute w-[274.34px] h-[274.34px] top-[-15.18px] right-[98px] opacity-20">
            <div className="w-full h-full rounded-[14.26px] bg-gradient-to-br from-white/40 to-white/20" 
                 style={{ filter: 'drop-shadow(0px 4px 20px rgba(0, 0, 0, 0.12))' }}></div>
          </div>
          {/* Vector patterns */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute w-[271.45px] h-[239.54px] top-[-42.91px] right-[135px] bg-white opacity-5"></div>
            <div className="absolute w-[271.45px] h-[239.54px] top-[-46px] right-[168.39px] bg-white opacity-5"></div>
            <div className="absolute w-[236px] h-[194.5px] top-[33px] left-0 bg-white opacity-10"></div>
            <div className="absolute w-[236px] h-[194.5px] top-[43px] left-[-32px] bg-white opacity-10"></div>
          </div>
        </div>
      );
    }
    
    return (
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/20"></div>
      </div>
    );
  };

  const handleCardClick = () => {
    console.log('[LoyaltyCard] Card clicked', { status, tierState, merchantCode, brandCode: cardData.brandCode });
    
    if (status === 'no-join' || tierState === 'noRank') {
      // For no-join/noRank state, disable card click
      console.log('[LoyaltyCard] Click disabled for no-join/noRank state');
      return;
    }
    
    if (onCardClick) {
      console.log('[LoyaltyCard] Calling custom onCardClick');
      onCardClick();
    } else if (merchantCode) {
      // Navigate to MembershipOverview page with merchantCode
      console.log('Navigate to MembershipOverview:', { merchantCode });
      cardData.onNavigate?.(`/membership/${merchantCode}`);
    } else if (cardData.brandCode) {
      // Fallback to brandCode for legacy compatibility
      console.log('Navigate to MembershipOverview (legacy):', { brandCode: cardData.brandCode });
      cardData.onNavigate?.(`/membership/${cardData.brandCode}`);
    }
  };

  return (
    <div 
      className={`${className?.includes('w-full') ? 'w-full' : 'w-[343px]'} ${theme === 'merchant-detail' ? 'h-[184px]' : 'h-[208px]'} relative rounded-xl overflow-hidden ${tierState !== 'noRank' ? 'cursor-pointer' : ''} ${className}`}
      onClick={handleCardClick}
    >
      {/* Background */}
      <div className="absolute inset-0 rounded-xl" style={getBackgroundStyle()}>
        {/* Pattern overlay */}
        {renderBackgroundPattern()}
      </div>

      {theme === 'merchant-detail' ? (
        <div className="relative z-10 h-full flex flex-col">
          {/* Merchant Detail Layout */}
          <div className="pt-3 pl-4">
            <p 
              className="text-[18px] font-semibold text-white leading-[24px]"
              style={{ fontFamily: 'Archia' }}
            >
              {getUserTitle()}
            </p>
          </div>
          
          {/* Content and Action Button */}
          <div className="flex-1 p-4 pt-2 flex flex-col gap-3">
            {/* Content Area */}
            <div className="flex-1">
              <div className="relative h-[74px] bg-white/60 backdrop-blur-[20px] rounded-xl">
                {status === 'max-rank' ? (
                  /* Max Level Message */
                  <div className="flex items-center justify-center h-full">
                    <p 
                      className="text-sm font-semibold text-[#1A1818] leading-[22px] text-center"
                      style={{ fontFamily: 'Archia' }}
                    >
                      Max level rồi,{'\n'}Tận hưởng ưu đãi thôi
                    </p>
                  </div>
                ) : (
                  <>
                    {/* Progress Text */}
                    <div className="pt-3 pl-[21px] flex items-center gap-1">
                      <span 
                        className="text-xs text-[#1A1818] leading-[18px]"
                        style={{ fontFamily: 'Archia' }}
                      >
                        Tích thêm
                      </span>
                      <span 
                        className="text-sm font-bold text-[#1A1818] leading-[22px]"
                        style={{ fontFamily: 'Archia' }}
                      >
                        {pointsToNextLevel}
                      </span>
                      <CoinIcon16 className="w-4 h-4" src={current?.[0]?.currencyLogo} />
                      <span 
                        className="text-xs text-[#1A1818] leading-[18px]"
                        style={{ fontFamily: 'Archia' }}
                      >
                        để nâng hạng bạn nhé
                      </span>
                    </div>

                    {/* Progress Bar */}
                    <div className="absolute top-[38px] left-3 w-[287px] h-5">
                      <div className="absolute top-1.5 w-full h-[7px] bg-white rounded-[58px]"></div>
                      <div className="absolute top-0 left-0">
                        <CoinIcon20 className="w-5 h-5" src={current?.[0]?.currencyLogo} />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Action Button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                // For merchant-detail theme with with-rank status, navigate to MembershipOverview page
                if (theme === 'merchant-detail' && status === 'with-rank') {
                  if (merchantCode) {
                    cardData.onNavigate?.(`/membership/${merchantCode}`);
                  } else if (cardData.brandCode) {
                    cardData.onNavigate?.(`/membership/${cardData.brandCode}`);
                  }
                } else if (onActionClick) {
                  onActionClick();
                }
              }}
              className="w-full bg-white rounded-lg py-2 px-12 flex items-center justify-center gap-2"
            >
              <div className="w-[18px] h-[18px] flex items-center justify-center">
                {getActionIcon()}
              </div>
              <span 
                className="text-sm font-semibold text-[#1A1818] leading-[22px]"
                style={{ fontFamily: 'Archia' }}
              >
                {getActionText()}
              </span>
            </button>
          </div>
        </div>
      ) : (
        <>
          {/* Default Layout */}
          {/* Brand Name */}
          <div className="absolute top-4 left-4">
            <p 
              className="text-[10px] font-bold text-white leading-[16px] tracking-[0.04em] uppercase"
              style={{ fontFamily: 'Archia, sans-serif' }}
            >
              {brandName}
            </p>
          </div>

          {/* User Title */}
          <div className="absolute top-9 left-4">
            <p 
              className="text-lg font-semibold text-white leading-6"
              style={{ fontFamily: 'Archia, sans-serif' }}
            >
              {getUserTitle()}
            </p>
          </div>

          {/* Default Layout Content */}
          {/* Voucher Button (only for with-rank and max-rank) */}
          {(status === 'with-rank' || status === 'max-rank') && (
            <div className="absolute top-[158px] left-4 right-4">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onVoucherClick?.();
                }}
                className="w-[311px] bg-white/60 backdrop-blur-[20px] rounded-lg px-12 py-2 flex items-center justify-center gap-2.5"
              >
                <div className="w-[18px] h-[18px] flex items-center justify-center">
                  {getActionIcon()}
                </div>
                <span 
                  className="text-sm font-semibold text-[#1A1818] leading-[22px]"
                  style={{ fontFamily: 'Archia, sans-serif' }}
                >
                  {getActionText()}
                </span>
              </button>
            </div>
          )}
          {/* Main Content Card */}
          <div className="absolute top-[72px] left-4 right-4 bottom-4">
            <div className="w-[311px] h-[74px] bg-white/60 backdrop-blur-[20px] rounded-xl relative">
          {status === 'no-join' && (
            <>
              {/* No Join Content */}
              <div className="absolute top-[15px] left-[14px]">
                <p 
                  className="text-sm font-semibold text-[#1A1818] leading-[22px] w-[174px]"
                  style={{ fontFamily: 'Archia, sans-serif' }}
                >
                  Trở thành khách ruột{'\n'}tận hưởng nhiều ưu đãi
                </p>
              </div>
              
              <div className="absolute bottom-0 left-0 right-0">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onActionClick?.();
                  }}
                  className="w-[311px] bg-white rounded-lg px-12 py-2 flex items-center justify-center gap-2"
                >
                  <div className="w-[18px] h-[18px] flex items-center justify-center">
                    {getActionIcon()}
                  </div>
                  <span 
                    className="text-sm font-semibold text-[#1A1818] leading-[22px]"
                    style={{ fontFamily: 'Archia, sans-serif' }}
                  >
                    {getActionText()}
                  </span>
                </button>
              </div>
            </>
          )}

          {status === 'without-rank' && (
            <>
              {/* Progress Text */}
              <div className="absolute top-3 left-[33px] flex items-center gap-1">
                <span 
                  className="text-xs text-[#1A1818] leading-[18px]"
                  style={{ fontFamily: 'Archia, sans-serif' }}
                >
                  Tích thêm
                </span>
                <span 
                  className="text-sm font-bold text-[#1A1818] leading-[22px]"
                  style={{ fontFamily: 'Archia, sans-serif' }}
                >
                  {pointsToNextLevel}
                </span>
                <div 
                  className="w-5 h-5 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: getBadgeBackgroundColor() }}
                >
                  <img src={resizeImage(badgeIconSrc || '', { width: 30, height: 30, quality: 85, fit: 'cover' })} alt="badge" className="w-[15px] h-[15px] rounded-full" />
                </div>
                <span 
                  className="text-xs text-[#1A1818] leading-[18px]"
                  style={{ fontFamily: 'Archia, sans-serif' }}
                >
                  để nâng hạng bạn nhé
                </span>
              </div>

              {/* Progress Bar */}
              <div className="absolute top-[38px] left-3 w-[287px] h-5">
                <div className="absolute top-1.5 w-full h-[7px] bg-white rounded-[58px]"></div>
                <div className="absolute top-0 left-0">
                  <div 
                    className="w-5 h-5 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: getBadgeBackgroundColor() }}
                  >
                    <img src={resizeImage(badgeIconSrc || '', { width: 30, height: 30, quality: 85, fit: 'cover' })} alt="badge" className="w-[15px] h-[15px] rounded-full" />
                  </div>
                </div>
              </div>

              {/* Action Button */}
              <div className="absolute bottom-0 left-0 right-0">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // For "Quét mã tích điểm" status, navigate to MembershipOverview page
                    if (status === 'without-rank') {
                      if (merchantCode) {
                        cardData.onNavigate?.(`/membership/${merchantCode}`);
                      } else if (cardData.brandCode) {
                        cardData.onNavigate?.(`/membership/${cardData.brandCode}`);
                      }
                    } else if (onActionClick) {
                      onActionClick();
                    }
                  }}
                  className="w-[311px] bg-white rounded-lg px-12 py-2 flex items-center justify-center gap-2"
                >
                  <div className="w-[18px] h-[18px] flex items-center justify-center">
                    {getActionIcon()}
                  </div>
                  <span 
                    className="text-sm font-semibold text-[#1A1818] leading-[22px]"
                    style={{ fontFamily: 'Archia, sans-serif' }}
                  >
                    {getActionText()}
                  </span>
                </button>
              </div>
            </>
          )}

          {status === 'with-rank' && (
            <>
              {/* Progress Text */}
              <div className="absolute top-3 left-[33px] flex items-center gap-1">
                <span 
                  className="text-xs text-[#1A1818] leading-[18px]"
                  style={{ fontFamily: 'Archia, sans-serif' }}
                >
                  Tích thêm
                </span>
                <span 
                  className="text-sm font-bold text-[#1A1818] leading-[22px]"
                  style={{ fontFamily: 'Archia, sans-serif' }}
                >
                  {pointsToNextLevel}
                </span>
                <div 
                  className="w-5 h-5 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: getBadgeBackgroundColor() }}
                >
                  <img src={resizeImage(badgeIconSrc || '', { width: 30, height: 30, quality: 85, fit: 'cover' })} alt="badge" className="w-[15px] h-[15px] rounded-full" />
                </div>
                <span 
                  className="text-xs text-[#1A1818] leading-[18px]"
                  style={{ fontFamily: 'Archia, sans-serif' }}
                >
                  để nâng hạng bạn nhé
                </span>
              </div>

              {/* Progress Bar */}
              <div className="absolute top-[38px] left-3 w-[287px] h-5">
                <div className="absolute top-1.5 w-full h-[7px] bg-white rounded-[58px]"></div>
                <div 
                  className="absolute top-1.5 h-[7px] rounded-[58px]"
                  style={{ 
                    width: `${Math.min(((currentProgress || 0) / (maxProgress || 1)) * 100, 20)}%`,
                    backgroundColor: getProgressBarColor()
                  }}
                ></div>
                <div className="absolute top-0" style={{ left: `${Math.min(((currentProgress || 0) / (maxProgress || 1)) * 100, 18)}%` }}>
                  <div 
                    className="w-5 h-5 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: getBadgeBackgroundColor() }}
                  >
                    <img src={resizeImage(badgeIconSrc || '', { width: 30, height: 30, quality: 85, fit: 'cover' })} alt="badge" className="w-[15px] h-[15px] rounded-full" />
                  </div>
                </div>
              </div>
            </>
          )}

          {status === 'max-rank' && (
            <div className="absolute top-[15px] left-[14px]">
              <p 
                className="text-sm font-semibold text-[#1A1818] leading-[22px] w-[174px]"
                style={{ fontFamily: 'Archia, sans-serif' }}
              >
                Max level rồi,{'\n'}Tận hưởng ưu đãi thôi
              </p>
            </div>
          )}

              {/* Illustration */}
              {illustrationSrc && (
                <div className="absolute top-[-60px] right-[25px] w-[121px] h-[134px]">
                  <img 
                    src={resizeImage(illustrationSrc, { width: 242, height: 268, quality: 85, fit: 'cover' })} 
                    alt="illustration" 
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

// Main LoyaltyCard Component with multiple cards support
export const LoyaltyCard: React.FC<LoyaltyCardProps> = ({
  cards,
  theme,
  className,
  onNavigate,
  // Legacy single card props
  status,
  brandName,
  userTitle,
  pointsToNextLevel,
  currentProgress,
  maxProgress,
  badgeIconSrc,
  illustrationSrc,
  onActionClick,
  onVoucherClick,
  onCardClick,
  tierBackground,
  tierLabelBackground,
  tierProgressBarColor,
  merchantBackgroundColor,
}) => {
  // If cards array is provided, render multiple cards with Swiper
  if (cards && cards.length > 0) {
    console.log('Cards:', cards);
    return (
      <div className={`w-full overflow-hidden ${className}`}>
        <SwiperComponent
          spaceBetween={0}
          slidesPerView={1}
          centeredSlides={false}
          pagination={{ 
            clickable: true
          }}
          modules={[Pagination]}
          className="loyalty-card-swiper-fullscreen"
        >
          {cards.map((card, index) => (
            <SwiperSlideComponent key={card.brandCode || index}>
              <div className="w-full flex justify-center">
                <SingleLoyaltyCard
                  {...card}
                  theme={theme || 'default'}
                  className="w-full max-w-full"
                  onNavigate={onNavigate}
                />
              </div>
            </SwiperSlideComponent>
          ))}
        </SwiperComponent>
      </div>
    );
  }

  // Backward compatibility: single card mode
  if (status && brandName) {
    return (
      <SingleLoyaltyCard
        status={status}
        theme={theme || 'default'}
        brandName={brandName}
        userTitle={userTitle}
        pointsToNextLevel={pointsToNextLevel}
        currentProgress={currentProgress}
        maxProgress={maxProgress}
        badgeIconSrc={badgeIconSrc}
        illustrationSrc={illustrationSrc}
        onActionClick={onActionClick}
        onNavigate={onNavigate}
        onVoucherClick={onVoucherClick}
        onCardClick={onCardClick}
        tierBackground={tierBackground}
        tierLabelBackground={tierLabelBackground}
        tierProgressBarColor={tierProgressBarColor}
        merchantBackgroundColor={merchantBackgroundColor}
        className={className}
      />
    );
  }

  // No cards provided - render empty state or default
  return null;
};

export default LoyaltyCard;