/* Custom styles for LoyaltyCard Swiper */
.loyalty-card-swiper .swiper-pagination {
  position: relative !important;
  margin-top: 16px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.loyalty-card-swiper .swiper-pagination-bullet {
  background-color: #D1D5DB !important;
  width: 8px !important;
  height: 8px !important;
  margin: 0 4px !important;
  opacity: 1 !important;
}

.loyalty-card-swiper .swiper-pagination-bullet-active {
  background-color: #3B82F6 !important;
}

/* Make sure the swiper container allows proper centering */
.loyalty-card-swiper {
  width: 100%;
  overflow: visible;
}

.loyalty-card-swiper .swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Full screen mode for LoyaltyCard Swiper */
.loyalty-card-swiper-fullscreen {
  width: 100%;
  overflow: hidden;
}

.loyalty-card-swiper-fullscreen .swiper-wrapper {
  align-items: center;
}

.loyalty-card-swiper-fullscreen .swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.loyalty-card-swiper-fullscreen .swiper-pagination {
  position: relative !important;
  margin-top: 16px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.loyalty-card-swiper-fullscreen .swiper-pagination-bullet {
  background-color: #D1D5DB !important;
  width: 8px !important;
  height: 8px !important;
  margin: 0 4px !important;
  opacity: 1 !important;
}

.loyalty-card-swiper-fullscreen .swiper-pagination-bullet-active {
  background-color: #3B82F6 !important;
}