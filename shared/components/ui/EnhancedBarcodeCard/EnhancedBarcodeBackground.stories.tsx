import type { Meta, StoryObj } from '@storybook/react';
import EnhancedBarcodeBackground from './EnhancedBarcodeBackground';

const meta: Meta<typeof EnhancedBarcodeBackground> = {
  title: 'Components/EnhancedBarcodeBackground',
  component: EnhancedBarcodeBackground,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'SVG background component for EnhancedBarcodeCard with customizable fill color'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    fill: {
      control: 'color',
      description: 'Background fill color',
      defaultValue: '#ffffff'
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    fill: '#ffffff'
  },
  render: (args) => (
    <div className="w-[90%] max-w-[343px] h-[385px] flex items-center justify-center">
      <EnhancedBarcodeBackground {...args} />
    </div>
  )
};

export const WithCustomColor: Story = {
  args: {
    fill: '#5f6d75'
  },
  render: (args) => (
    <div className="w-[90%] max-w-[343px] h-[385px]">
      <EnhancedBarcodeBackground {...args} />
    </div>
  )
};

export const WithBrandColors: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4">
      <div className="w-[343px] h-[385px]">
        <div className="mb-2 text-sm font-semibold">Pink (#F65D79)</div>
        <EnhancedBarcodeBackground fill="#F65D79" />
      </div>
      <div className="w-[343px] h-[385px]">
        <div className="mb-2 text-sm font-semibold">Green (#0DC98B)</div>
        <EnhancedBarcodeBackground fill="#0DC98B" />
      </div>
      <div className="w-[343px] h-[385px]">
        <div className="mb-2 text-sm font-semibold">Blue (#3B82F6)</div>
        <EnhancedBarcodeBackground fill="#3B82F6" />
      </div>
      <div className="w-[343px] h-[385px]">
        <div className="mb-2 text-sm font-semibold">Gray (#5f6d75)</div>
        <EnhancedBarcodeBackground fill="#5f6d75" />
      </div>
    </div>
  )
};