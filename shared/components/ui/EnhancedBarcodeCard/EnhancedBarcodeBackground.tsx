import * as React from "react";

interface EnhancedBarcodeBackgroundProps {
  className?: string;
  fill?: string;
}

const EnhancedBarcodeBackground: React.FC<EnhancedBarcodeBackgroundProps> = ({ 
  className, 
  fill = "#ffffff",
  ...props 
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="100%"
    height="100%"
    fill="none"
    className={className}
    viewBox="0 0 343 385"
    preserveAspectRatio="none"
    {...props}
  >
    <path
      fill={fill}
      d="M16 321.32c0-8.875-7.163-16.07-16-16.07V8a8 8 0 0 1 8-8h327a8 8 0 0 1 8 8v297.25c-8.837 0-16 7.195-16 16.07s7.163 16.07 16 16.07v39.575c0 4.438-3.582 8.035-8 8.035H8c-4.418 0-8-3.597-8-8.035V337.39c8.837 0 16-7.195 16-16.07Z"
    />
    <path stroke="#CACACA" strokeDasharray="3 4" d="M24 320.5h295" />
  </svg>
);

export default EnhancedBarcodeBackground;