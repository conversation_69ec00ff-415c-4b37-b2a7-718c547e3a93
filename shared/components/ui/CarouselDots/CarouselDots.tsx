import React from 'react';
import { cn } from '../../../utils';
import billScanLineIcon from '../../../assets/icons/bill-scan-line.svg';

export interface CarouselDotsProps {
  totalSteps: number;
  currentStep: number;
  className?: string;
  activeColor?: string;
  inactiveColor?: string;
  lineColor?: string;
}

export const CarouselDots: React.FC<CarouselDotsProps> = ({
  totalSteps = 2,
  currentStep = 1,
  className,
  activeColor = '#0DC98B',
  inactiveColor = '#FFFFFF',
  lineColor = '#0DC98B',
}) => {
  return (
    <div className={cn('flex items-center', className)}>
      {Array.from({ length: totalSteps }, (_, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber <= currentStep;
        const isLastStep = stepNumber === totalSteps;

        console.log('currentStep',currentStep)

        return (
          <React.Fragment key={stepNumber}>
            {/* Dot */}
            <div
              className="w-[10px] h-[10px] rounded-full transition-colors duration-300"
              style={{
                backgroundColor: isActive ? activeColor : inactiveColor,
              }}
            />
            
            {/* Line connector (not shown after last dot) */}
            {!isLastStep && (
              currentStep === 1 ? (
                <img 
                  src={billScanLineIcon} 
                  alt="" 
                  className="w-5 h-px"
                />
              ) : (
                <div
                  className="w-5 h-px transition-colors duration-300"
                  style={{
                    backgroundColor: lineColor,
                  }}
                />
              )
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default CarouselDots;