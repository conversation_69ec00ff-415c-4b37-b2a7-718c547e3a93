import React, { useState, forwardRef, useEffect } from "react";
import TaptapEmptyIcon from "../../../assets/icons/icon-40px-outline-taptap-empty.svg";

export interface ImageSource {
  uri: string;
  width?: number;
  height?: number;
  cache?: "immutable" | "web" | "cacheOnly";
}

export interface BasedImageProps
  extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, "src"> {
  source?: ImageSource | string | number;
  defaultSource?: ImageSource | string;
  resizeMode?: "contain" | "cover" | "fill" | "none" | "scale-down";
  fallbackSrc?: string;
  onLoad?: (event: React.SyntheticEvent<HTMLImageElement>) => void;
  onError?: (event: React.SyntheticEvent<HTMLImageElement>) => void;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  priority?: "high" | "low" | "auto";
}

const DEFAULT_IMAGE = TaptapEmptyIcon;

const BasedImage = forwardRef<HTMLImageElement, BasedImageProps>(
  (props, ref) => {
    const {
      source,
      defaultSource,
      resizeMode = "contain",
      fallbackSrc,
      onLoad,
      onError,
      onLoadStart,
      onLoadEnd,
      priority = "auto",
      style,
      className = "",
      ...restProps
    } = props;

    const [imageError, setImageError] = useState(false);
    const initialPlaceholder = (defaultSource as string) || DEFAULT_IMAGE;
    const [displayedSrc, setDisplayedSrc] =
      useState<string>(initialPlaceholder);

    const getImageUrl = (
      src: ImageSource | string | number | undefined
    ): string => {
      if (!src)
        return (defaultSource as string) || fallbackSrc || DEFAULT_IMAGE;
      if (typeof src === "string") return src;
      if (typeof src === "number") return src.toString();
      if (typeof src === "object" && src.uri) return src.uri;
      return (defaultSource as string) || fallbackSrc || DEFAULT_IMAGE;
    };

    // Preload the real source; only swap to it after it loads successfully.
    useEffect(() => {
      const realUrl = getImageUrl(source);
      setImageError(false);

      // If there is no real URL or it's the same as placeholder, just show placeholder
      if (!realUrl || realUrl === initialPlaceholder) {
        setDisplayedSrc(initialPlaceholder);
        return;
      }

      onLoadStart?.();

      let cancelled = false;
      const img = new Image();
      img.onload = () => {
        if (cancelled) return;
        setDisplayedSrc(realUrl);
        // Let the actual <img> onLoad handler fire to call onLoad/onLoadEnd
      };
      img.onerror = (e) => {
        if (cancelled) return;
        setImageError(true);
        setDisplayedSrc(initialPlaceholder);
        onError?.(e as unknown as React.SyntheticEvent<HTMLImageElement>);
        onLoadEnd?.();
      };

      img.src = realUrl;

      return () => {
        cancelled = true;
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [source]);

    const handleLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
      setImageError(false);
      onLoadEnd?.();
      onLoad?.(event);
    };

    const handleError = (event: React.SyntheticEvent<HTMLImageElement>) => {
      if (!imageError) {
        setImageError(true);
        onError?.(event);
        onLoadEnd?.();
      }
    };

    const combinedStyle: React.CSSProperties = {
      objectFit: resizeMode,
      objectPosition: "center",
      display: "block",
      maxWidth: "100%",
      height: "auto",
      ...style,
    };

    return (
      <img
        ref={ref}
        src={displayedSrc}
        onLoad={handleLoad}
        onError={handleError}
        style={combinedStyle}
        className={`based-image ${className}`}
        loading={priority === "high" ? "eager" : "lazy"}
        {...restProps}
      />
    );
  }
);

BasedImage.displayName = "BasedImage";

export default BasedImage;
