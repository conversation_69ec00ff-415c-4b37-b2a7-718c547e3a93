# SectionGame Component

Component hiển thị section game với khả năng scroll ngang ở trang home.

## Tính năng

- Hiển thị danh sách game cards với scroll ngang
- Tự động fetch game data từ API với pagination
- Loading state với skeleton
- Pull-to-refresh functionality
- Navigation đến trang game detail khi click
- Responsive design cho mobile

## Props

```typescript
interface SectionGameProps {
  data: any; // Section configuration data
  sectionIndex: number; // Index của section
}
```

## Sử dụng

```tsx
import { SectionGame } from '@taptap/shared';

// Trong HomePage
case HomeSectionCode.GAME:
  return (
    <SectionGame
      key={`game-${index}`}
      data={section}
      sectionIndex={sectionIndex}
    />
  );
```

## Styling

Component sử dụng CSS classes:

- `.section-game-scroll`: Container cho scroll ngang
- `.section-game-card`: Style cho từng game card

## API Integration

Component sử dụng `useFetchListGame` hook để fetch game data từ `gameCardService.getGameCards()` với:

- `offset: 0`
- `limit: 8` (có thể load more)
- Support cho `collectionCode` để filter theo brand
- Pagination và refresh functionality

## Navigation

Khi click vào game card:

- `branded_wheel` type: Navigate đến `/game/wheel/{id}`
- `game` type: Navigate đến `/game/detail/{id}`

## Dependencies

- `BrandedWheelCard`: Component hiển thị game card
- `SectionHeader`: Header với title và action button
- `useFetchListGame`: Custom hook để fetch và quản lý game data
- `gameCardService`: Service để fetch game data
- `useNavigate`: React Router hook cho navigation
