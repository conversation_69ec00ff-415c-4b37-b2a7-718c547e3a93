import React from "react";
import { useNavigate } from "react-router-dom";
import { appPath, BrandedWheelCard, SectionHeader } from "@taptap/shared";
import type { IGameCard } from "@taptap/shared";
import useFetchListGame from "./useFetchListGame";
import "./SectionGame.css";

interface SectionGameProps {
  data: any;
  sectionIndex: number;
}

const SectionGame: React.FC<SectionGameProps> = ({
  data,
  sectionIndex: _sectionIndex,
}) => {
  const navigate = useNavigate();
  const {
    data: gameCards,
    isFirstLoad,
    refreshing,
    onRefresh,
  } = useFetchListGame(data?.collectionCode);

  // Add pull-to-refresh functionality
  React.useEffect(() => {
    let startY = 0;
    let isPulling = false;

    const handleTouchStart = (e: TouchEvent) => {
      const scrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
      if (scrollTop === 0) {
        startY = e.touches[0].clientY;
        isPulling = true;
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isPulling) return;

      const currentY = e.touches[0].clientY;
      const diff = currentY - startY;

      if (diff > 100 && !refreshing) {
        onRefresh();
        isPulling = false;
      }
    };

    const handleTouchEnd = () => {
      isPulling = false;
    };

    document.addEventListener("touchstart", handleTouchStart);
    document.addEventListener("touchmove", handleTouchMove);
    document.addEventListener("touchend", handleTouchEnd);

    return () => {
      document.removeEventListener("touchstart", handleTouchStart);
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("touchend", handleTouchEnd);
    };
  }, [refreshing, onRefresh]);

  const handleGamePress = (gameCard: IGameCard, _index: number) => {
    if (gameCard.type === "branded_wheel") {
      navigate(appPath.gameWheel(gameCard.data?._id));
    } else if (gameCard.type === "game") {
      navigate(appPath.gameDetail(gameCard._id));
    } else {
      console.log("Unknown game type:", gameCard.type);
    }
  };

  if (isFirstLoad) {
    return (
      <div className="bg-white rounded-lg mx-4 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Kho game</h3>
        </div>
        <div className="flex space-x-4 overflow-x-auto pb-2">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="flex-shrink-0">
              <div className="w-40 h-56 bg-gray-200 rounded-lg animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (gameCards.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg">
      <div className="flex items-center justify-between mb-4 px-4">
        <SectionHeader
          title="Kho game"
          actionText="Xem tất cả"
          onActionClick={() => navigate("/games")}
          className="text-lg font-semibold text-gray-900"
        />
      </div>

      <div className="flex overflow-x-auto pb-2 section-game-scroll pt-[35px] px-4 pb-4">
        {gameCards.map((card, _index) => (
          <div key={card._id} className="section-game-card">
            <BrandedWheelCard
              gameCard={card}
              onClick={() => handleGamePress(card, _index)}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default SectionGame;
