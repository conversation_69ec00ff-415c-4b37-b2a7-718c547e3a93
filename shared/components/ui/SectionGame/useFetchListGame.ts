import { useEffect, useState, useRef, useCallback } from 'react';
import type { IGameCard, IRequestGameCard } from '@taptap/shared';
import { gameCardService } from '@taptap/shared';

interface IReturnType {
  isFirstLoad: boolean;
  refreshing: boolean;
  loadmore: boolean;
  onRefresh: () => void;
  onLoadMore: () => void;
  data: IGameCard[];
}

enum ListStatus {
  FIRST_LOADING = 1,
  REFRESHING,
  LOAD_MORE,
  NONE,
}

const LIMIT = 8;

const useFetchListGame = (collectionCode?: string): IReturnType => {
  const [isFirstLoad, setFirstLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [loadmore, setLoadmore] = useState<boolean>(false);
  const endOfList = useRef<boolean>(false);
  const offset = useRef<number>(0);
  const [data, setData] = useState<IGameCard[]>([]);
  const listStatus = useRef<ListStatus>(ListStatus.FIRST_LOADING);
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchData = useCallback(async (_offset: number) => {
    try {
      // Create new AbortController for each request
      abortControllerRef.current = new AbortController();
      
      const params: IRequestGameCard = {
        offset: _offset,
        limit: LIMIT,
      };

      // Add brandCode if collectionCode is provided
      if (collectionCode) {
        params.brandCode = collectionCode;
      }

      const result = await gameCardService.getGameCards(params);
      
      if (result && Array.isArray(result)) {
        if (result.length < LIMIT) {
          endOfList.current = true;
        }

        if (_offset === 0) {
          setData([...result]);
          offset.current = result.length;
        } else {
          setData(currentList => [
            ...currentList,
            ...result,
          ]);
          offset.current += result.length;

          if (result.length < LIMIT) {
            endOfList.current = true;
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch game cards:', error);
      // Only abort if it's not a cancellation error
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Error fetching game cards:', error);
      }
    } finally {
      switch (listStatus.current) {
        case ListStatus.FIRST_LOADING:
          setFirstLoading(false);
          break;
        case ListStatus.REFRESHING:
          setRefreshing(false);
          break;
        case ListStatus.LOAD_MORE:
          setLoadmore(false);
          break;
        default:
          setFirstLoading(false);
      }
    }
  }, [collectionCode]);

  useEffect(() => {
    fetchData(0);
    
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchData]);

  const onLoadMore = useCallback(() => {
    if (loadmore || endOfList.current || refreshing) return;
    
    listStatus.current = ListStatus.LOAD_MORE;
    setLoadmore(true);
    fetchData(offset.current);
  }, [loadmore, refreshing, fetchData]);

  const onRefresh = useCallback(() => {
    if (refreshing || loadmore) return;
    
    setRefreshing(true);
    endOfList.current = false;
    offset.current = 0;
    listStatus.current = ListStatus.REFRESHING;
    fetchData(0);
  }, [refreshing, loadmore, fetchData]);

  return {
    isFirstLoad,
    refreshing,
    loadmore,
    onRefresh,
    onLoadMore,
    data,
  };
};

export default useFetchListGame;
