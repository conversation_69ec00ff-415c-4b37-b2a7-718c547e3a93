/* SectionGame styles */
.section-game-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.section-game-scroll::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}

/* Smooth scrolling for better UX */
.section-game-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */
}

/* Ensure proper spacing between cards */
.section-game-card {
  flex-shrink: 0;
  margin-right: 1rem; /* 16px */
}

.section-game-card:last-child {
  margin-right: 0;
}

/* Pull to refresh styles */
.section-game-refresh-indicator {
  display: flex;
  justify-content: center;
  padding: 0.5rem 0;
}

.section-game-refresh-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
