import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import SectionG<PERSON> from "./SectionGame";

const meta: Meta<typeof SectionGame> = {
  title: "Components/SectionGame",
  component: SectionGame,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
  argTypes: {
    data: {
      control: "object",
      description: "Section configuration data",
    },
    sectionIndex: {
      control: "number",
      description: "Index of the section",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    data: {
      code: "game",
      name: "Game Section",
      isDisplayName: true,
    },
    sectionIndex: 1,
  },
};

export const WithCustomData: Story = {
  args: {
    data: {
      code: "game",
      name: "Custom Game Section",
      isDisplayName: false,
      collectionCode: "games",
    },
    sectionIndex: 2,
  },
};
