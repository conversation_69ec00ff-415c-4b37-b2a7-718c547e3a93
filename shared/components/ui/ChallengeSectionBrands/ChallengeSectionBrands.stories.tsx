import type { Meta, StoryObj } from '@storybook/react';
import ChallengeSectionBrands from './ChallengeSectionBrands';

const meta: Meta<typeof ChallengeSectionBrands> = {
  title: 'Components/Challenge/ChallengeSectionBrands',
  component: ChallengeSectionBrands,
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const mockCondition = {
  conditionType: 'BRAND',
  transactionsWithinBrands: 3,
  brands: [
    { brandId: '1', logoLink: 'https://via.placeholder.com/48x48/3B82F6/FFFFFF?text=A' },
    { brandId: '2', logoLink: 'https://via.placeholder.com/48x48/10B981/FFFFFF?text=B' },
    { brandId: '3', logoLink: 'https://via.placeholder.com/48x48/F59E0B/FFFFFF?text=C' },
  ],
};

const mockChallengeWithProgress = [
  {
    validTransactions: [{ id: '1' }, { id: '2' }],
    logoLink: 'https://via.placeholder.com/48x48/3B82F6/FFFFFF?text=A',
  },
  {
    validTransactions: [{ id: '3' }],
    logoLink: 'https://via.placeholder.com/48x48/10B981/FFFFFF?text=B',
  },
];

const mockChallengeNoProgress = [
  {
    validTransactions: [],
    logoLink: 'https://via.placeholder.com/48x48/3B82F6/FFFFFF?text=A',
  },
];

export const WithProgress: Story = {
  args: {
    condition: mockCondition,
    challengeBrands: mockChallengeWithProgress,
    isExpired: false,
    conditionChallengeType: 'BRAND',
  },
};

export const NoProgress: Story = {
  args: {
    condition: mockCondition,
    challengeBrands: mockChallengeNoProgress,
    isExpired: false,
    conditionChallengeType: 'BRAND',
  },
};

export const Expired: Story = {
  args: {
    condition: mockCondition,
    challengeBrands: mockChallengeWithProgress,
    isExpired: true,
    conditionChallengeType: 'BRAND',
  },
};

export const TransactionEachBrand: Story = {
  args: {
    condition: {
      conditionType: 'BRAND',
      transactionEachBrand: 1,
      brands: [
        { brandId: '1', logoLink: 'https://via.placeholder.com/48x48/3B82F6/FFFFFF?text=A' },
        { brandId: '2', logoLink: 'https://via.placeholder.com/48x48/10B981/FFFFFF?text=B' },
        { brandId: '3', logoLink: 'https://via.placeholder.com/48x48/F59E0B/FFFFFF?text=C' },
        { brandId: '4', logoLink: 'https://via.placeholder.com/48x48/EF4444/FFFFFF?text=D' },
        { brandId: '5', logoLink: 'https://via.placeholder.com/48x48/8B5CF6/FFFFFF?text=E' },
      ],
    },
    challengeBrands: [
      {
        validTransactions: [{ id: '1' }],
        logoLink: 'https://via.placeholder.com/48x48/3B82F6/FFFFFF?text=A',
      },
      {
        validTransactions: [{ id: '2' }],
        logoLink: 'https://via.placeholder.com/48x48/10B981/FFFFFF?text=B',
      },
    ],
    isExpired: false,
    conditionChallengeType: 'BRAND',
  },
};

export const ManyBrands: Story = {
  args: {
    condition: {
      conditionType: 'BRAND',
      transactionsWithinBrands: 8,
    },
    challengeBrands: [
      {
        validTransactions: [{ id: '1' }, { id: '2' }, { id: '3' }],
        logoLink: 'https://via.placeholder.com/48x48/3B82F6/FFFFFF?text=A',
      },
      {
        validTransactions: [{ id: '4' }, { id: '5' }],
        logoLink: 'https://via.placeholder.com/48x48/10B981/FFFFFF?text=B',
      },
    ],
    isExpired: false,
    conditionChallengeType: 'BRAND',
  },
};

export const NonBrandCondition: Story = {
  args: {
    condition: {
      conditionType: 'OPEN_LINK',
      buttonLabel: 'Mở liên kết',
      directBtnLink: 'https://example.com',
    },
    challengeBrands: [],
    isExpired: false,
    conditionChallengeType: 'BRAND',
  },
};

export const EarnPointType: Story = {
  args: {
    condition: mockCondition,
    challengeBrands: mockChallengeWithProgress,
    isExpired: false,
    conditionChallengeType: 'EARN_POINT',
  },
};

export const RedeemPointType: Story = {
  args: {
    condition: mockCondition,
    challengeBrands: mockChallengeWithProgress,
    isExpired: false,
    conditionChallengeType: 'REDEEM_POINT',
  },
};

export const EmptyCondition: Story = {
  args: {
    condition: undefined,
    challengeBrands: [],
    isExpired: false,
    conditionChallengeType: 'BRAND',
  },
};