import React from 'react';
import { SectionHeader } from '../SectionHeader';

interface Brand {
  brandId: string;
  logoLink?: string;
  validTransactions?: any[];
}

interface Condition {
  conditionType?: string;
  transactionsWithinBrands?: number;
  transactionEachBrand?: number;
  brands?: Brand[];
}

interface UserProgress {
  validTransactions?: any[];
  [key: string]: any;
}

interface ChallengeSectionBrandsProps {
  condition?: Condition;
  challengeBrands?: UserProgress[];
  isExpired: boolean;
  conditionChallengeType?: string;
}

interface BrandLogoProps {
  item: Brand;
  isExpiredItem: boolean;
}

const BrandLogo: React.FC<BrandLogoProps> = ({ item, isExpiredItem }) => {
  const hasValidTransactions = item?.validTransactions && item.validTransactions.length > 0;
  
  return (
    <div className={`
      relative w-12 h-12 rounded-full mx-1.5 mb-3 bg-gray-100
      border-2 border-dashed border-pink-400 flex items-center justify-center overflow-hidden
      ${hasValidTransactions 
        ? `border-solid ${isExpiredItem ? 'border-gray-400' : 'border-green-500'}` 
        : isExpiredItem ? 'border-gray-400' : 'border-pink-400'
      }
      ${isExpiredItem ? 'grayscale opacity-60' : ''}
    `}>
      {hasValidTransactions && item.logoLink ? (
        <img
          src={item.logoLink}
          alt="Brand logo"
          className="w-full h-full rounded-full object-cover"
        />
      ) : (
        <div className="text-lg">🏆</div>
      )}
    </div>
  );
};

const ChallengeSectionBrands: React.FC<ChallengeSectionBrandsProps> = ({
  condition,
  challengeBrands,
  isExpired,
  conditionChallengeType,
}) => {
  if (
    !condition ||
    condition?.conditionType !== 'BRAND' ||
    conditionChallengeType === 'EARN_POINT' ||
    conditionChallengeType === 'REDEEM_POINT'
  ) {
    return null;
  }

  const getNumberOfAccumulating = () => {
    const isValidNumber = (num: any) => typeof num === 'number' && !isNaN(num);
    
    if (isValidNumber(condition.transactionsWithinBrands)) {
      return condition.transactionsWithinBrands!;
    }

    if (isValidNumber(condition.transactionEachBrand) && condition.brands) {
      return condition.brands.length;
    }

    return 0;
  };

  const numberOfAccumulating = getNumberOfAccumulating();

  if (!numberOfAccumulating) return null;

  const getAccumulatedBrands = (): Brand[] => {
    const brands: Brand[] = [];

    // Add accumulated brands first
    if (challengeBrands && challengeBrands.length > 0) {
      challengeBrands.forEach(userProgress => {
        if (userProgress.validTransactions && userProgress.validTransactions.length > 0) {
          for (let i = 0; i < userProgress.validTransactions.length; i++) {
            if (brands.length < numberOfAccumulating) {
              brands.push({ 
                brandId: `${brands.length}`, 
                logoLink: (userProgress as any).logoLink || '',
                validTransactions: userProgress.validTransactions 
              });
              
              const isValidNumber = (num: any) => typeof num === 'number' && !isNaN(num);
              if (isValidNumber(condition.transactionEachBrand)) {
                break;
              }
            }
          }
        }
      });
    }

    // Add empty brand slots
    const emptyBrands = numberOfAccumulating - brands.length;
    if (emptyBrands > 0) {
      for (let i = 0; i < emptyBrands; i++) {
        brands.push({
          brandId: `${brands.length}`,
          logoLink: '',
        });
      }
    }

    return brands;
  };

  const accumulatedBrands = getAccumulatedBrands();

  return (
    <div className="bg-white pt-3 pb-3 mb-2">
      <div className="px-2.5">
        <SectionHeader title="Tích lũy tại" />
        <div className="flex flex-wrap mt-3">
          {accumulatedBrands.map((item, index) => (
            <BrandLogo
              key={`${item.brandId || ''}${index}`}
              item={item}
              isExpiredItem={isExpired}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default React.memo(ChallengeSectionBrands);