import React from 'react';
import { cn } from '../../../utils';
import loaderGif from '../../../assets/images/loader.gif';
import taptapLoadingGif from '../../../assets/images/taptap_loading_2.gif';

interface LoadingGifProps {
  type?: 'loader' | 'taptap';
  size?: number | string;
  className?: string;
  alt?: string;
}

export const LoadingGif: React.FC<LoadingGifProps> = ({
  type = 'loader',
  size = 60,
  className,
  alt = 'Loading...',
}) => {
  const gifSrc = type === 'taptap' ? taptapLoadingGif : loaderGif;
  const sizeValue = typeof size === 'number' ? `${size}px` : size;

  return (
    <img
      src={gifSrc}
      alt={alt}
      className={cn('object-contain', className)}
      style={{
        width: sizeValue,
        height: sizeValue,
      }}
    />
  );
};