import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { LoadingSpinner } from './LoadingSpinner';
import { LoadingGif } from './LoadingGif';

const meta: Meta<typeof LoadingSpinner> = {
  title: 'Components/Loading',
  component: LoadingSpinner,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Spinner: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Loading Spinner Sizes</h3>
        <div className="flex items-center gap-8">
          <div className="text-center">
            <LoadingSpinner size="sm" />
            <p className="mt-2 text-sm text-gray-600">Small</p>
          </div>
          <div className="text-center">
            <LoadingSpinner size="md" />
            <p className="mt-2 text-sm text-gray-600">Medium</p>
          </div>
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-2 text-sm text-gray-600">Large</p>
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Loading Spinner Colors</h3>
        <div className="flex items-center gap-8">
          <div className="text-center">
            <LoadingSpinner size="md" color="#FF69B4" />
            <p className="mt-2 text-sm text-gray-600">Pink</p>
          </div>
          <div className="text-center">
            <LoadingSpinner size="md" color="#00CED1" />
            <p className="mt-2 text-sm text-gray-600">Turquoise</p>
          </div>
          <div className="text-center">
            <LoadingSpinner size="md" color="#32CD32" />
            <p className="mt-2 text-sm text-gray-600">Green</p>
          </div>
        </div>
      </div>
    </div>
  ),
};

export const AnimatedGif: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Loading GIF Types</h3>
        <div className="flex items-center gap-8">
          <div className="text-center">
            <LoadingGif type="loader" size={60} />
            <p className="mt-2 text-sm text-gray-600">Loader</p>
          </div>
          <div className="text-center">
            <LoadingGif type="taptap" size={60} />
            <p className="mt-2 text-sm text-gray-600">TapTap</p>
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Loading GIF Sizes</h3>
        <div className="flex items-center gap-8">
          <div className="text-center">
            <LoadingGif type="loader" size={30} />
            <p className="mt-2 text-sm text-gray-600">30px</p>
          </div>
          <div className="text-center">
            <LoadingGif type="loader" size={60} />
            <p className="mt-2 text-sm text-gray-600">60px</p>
          </div>
          <div className="text-center">
            <LoadingGif type="loader" size={90} />
            <p className="mt-2 text-sm text-gray-600">90px</p>
          </div>
        </div>
      </div>
    </div>
  ),
};

export const InContext: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="border rounded-lg p-8 bg-gray-50">
        <h3 className="text-lg font-semibold mb-4">Loading Card</h3>
        <div className="bg-white rounded-lg shadow-md p-6 max-w-sm">
          <div className="flex flex-col items-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-gray-600">Loading your data...</p>
          </div>
        </div>
      </div>
      
      <div className="border rounded-lg p-8 bg-gray-50">
        <h3 className="text-lg font-semibold mb-4">Loading Button</h3>
        <button className="bg-blue-500 text-white px-6 py-3 rounded-lg flex items-center gap-3">
          <LoadingSpinner size="sm" color="#FFFFFF" />
          <span>Processing...</span>
        </button>
      </div>
      
      <div className="border rounded-lg p-8 bg-gray-50">
        <h3 className="text-lg font-semibold mb-4">Full Page Loading</h3>
        <div className="relative h-64 bg-white rounded-lg">
          <div className="absolute inset-0 flex items-center justify-center bg-white/80">
            <div className="text-center">
              <LoadingGif type="taptap" size={80} />
              <p className="mt-4 text-lg font-medium text-gray-700">Loading TapTap...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
};