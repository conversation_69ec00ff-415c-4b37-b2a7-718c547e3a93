import React from 'react';
import { cn } from '../../../utils';
import { getLoaderGif } from '../../../assets';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  color?: string;
  useGif?: boolean; // New prop to use gif or CSS spinner
  full?: boolean; // Take full width/height of container
  center?: boolean; // Center the spinner in its container
  width?: string; // Custom width (e.g., '100px', '50%')
  height?: string; // Custom height (e.g., '100px', '50%')
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6', 
  lg: 'w-8 h-8',
};

const gifSizeClasses = {
  sm: 'w-24 h-24',
  md: 'w-36 h-36', 
  lg: 'w-48 h-48',
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className,
  color = '#3B82F6',
  useGif = true, // Default to using gif
  full = false,
  center = true,
  width,
  height,
}) => {
  const getContainerClasses = () => {
    const baseClasses = [];
    
    if (center) {
      baseClasses.push('flex items-center justify-center');
      baseClasses.push('min-h-[calc(100vh-40px)]');
    }
    
    if (full) {
      baseClasses.push('w-full h-full');
    }
    
    return cn(baseClasses, className);
  };

  const getSpinnerStyle = () => {
    const style: React.CSSProperties = {};
    if (width) style.width = width;
    if (height) style.height = height;
    return style;
  };

  const getSpinnerClasses = () => {
    // Don't apply size classes if custom width/height is provided
    if (width || height || full) {
      return '';
    }
    return useGif ? gifSizeClasses[size] : sizeClasses[size];
  };
  if (useGif) {
    return (
      <div className={getContainerClasses()}>
        <img
          src={getLoaderGif()}
          alt="Loading..."
          className={cn(
            'object-contain',
            getSpinnerClasses(),
            full && 'w-full h-full'
          )}
          style={getSpinnerStyle()}
          onError={(e) => {
            // Fallback to CSS spinner if gif fails to load
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const fallback = target.parentNode?.querySelector('.fallback-spinner') as HTMLElement;
            if (fallback) {
              fallback.style.display = 'block';
            }
          }}
        />
        <div
          className={cn(
            'fallback-spinner hidden animate-spin rounded-full border-2 border-transparent absolute inset-0',
            getSpinnerClasses(),
            full && 'w-full h-full'
          )}
          style={{
            borderTopColor: color,
            borderRightColor: color,
            ...getSpinnerStyle()
          }}
        />
      </div>
    );
  }

  return (
    <div className={getContainerClasses()}>
      <div
        className={cn(
          'animate-spin rounded-full border-2 border-transparent',
          getSpinnerClasses(),
          full && 'w-full h-full'
        )}
        style={{
          borderTopColor: color,
          borderRightColor: color,
          ...getSpinnerStyle()
        }}
      />
    </div>
  );
};