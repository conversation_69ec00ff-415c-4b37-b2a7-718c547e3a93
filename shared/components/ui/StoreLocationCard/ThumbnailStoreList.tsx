import React, { useCallback } from "react";
import { OfflineStoreType } from "../../../stores";

export type ThumbnailStoreListProps = {
  item: OfflineStoreType;
  merchantCode: string;
};

const containerStyle: React.CSSProperties = {
  padding: "24px 16px",
  backgroundColor: "#FFFFFF",
  height: "206px",
  display: "flex",
  flexDirection: "column",
};

const headerStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "row",
  paddingBottom: "12px",
  borderBottom: "1px solid #E5E7EB",
  alignItems: "center",
  marginBottom: "16px",
};

const storeNameStyle: React.CSSProperties = {
  fontSize: "16px",
  fontWeight: 600,
  marginLeft: "16px",
  flex: 1,
};

const addressStyle: React.CSSProperties = {
  minHeight: "44px",
  justifyContent: "space-between",
  display: "flex",
  flexDirection: "row",
};

const addressTextStyle: React.CSSProperties = {
  fontSize: "14px",
  flex: 1,
  lineHeight: 1.4,
  color: "#6B7280",
};

const distanceWrapperStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "row",
  height: "20px",
  alignItems: "center",
  marginLeft: "8px",
};

const bottomStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginTop: "16px",
};

const distanceStyle: React.CSSProperties = {
  fontSize: "14px",
  fontWeight: 600,
  marginLeft: "4px",
};

const logoStyle: React.CSSProperties = {
  width: "48px",
  height: "48px",
  borderRadius: "50%",
  objectFit: "cover",
};

const buttonStyle: React.CSSProperties = {
  display: "inline-flex",
  alignItems: "center",
  gap: "4px",
  background: "transparent",
  border: "none",
  padding: 0,
  cursor: "pointer",
  color: "#E91E63",
  fontSize: "14px",
};

const phoneButtonStyle: React.CSSProperties = {
  ...buttonStyle,
  color: "#E91E63",
};

const iconStyle: React.CSSProperties = {
  width: "20px",
  height: "20px",
  flex: "0 0 20px",
};

const IconLocation: React.VFC = () => (
  <svg
    style={iconStyle}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
      stroke="#6B7280"
      strokeWidth="2"
    />
    <circle cx="12" cy="10" r="3" stroke="#6B7280" strokeWidth="2" />
  </svg>
);

const IconCall: React.VFC = () => (
  <svg
    style={iconStyle}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.86 19.86 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6A19.86 19.86 0 0 1 2.08 4.18 2 2 0 0 1 4.06 2h3a2 2 0 0 1 2 1.72c.12.9.33 1.77.62 2.6a2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.48-1.14a2 2 0 0 1 2.11-.45c.83.29 1.7.5 2.6.62A2 2 0 0 1 22 16.92Z"
      stroke="#E91E63"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const IconMap: React.VFC = () => (
  <svg
    style={iconStyle}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path
      d="M1 6v16l7-4 8 4 6-4V2l-7 4-8-4-6 4Z"
      stroke="#E91E63"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 2v16"
      stroke="#E91E63"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16 6v16"
      stroke="#E91E63"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const formatDistance = (distance: number): string => {
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`;
  }
  return `${distance.toFixed(1)}km`;
};

const ThumbnailStoreList: React.VFC<ThumbnailStoreListProps> = ({
  item,
  merchantCode,
}) => {
  const {
    name = "",
    address,
    distance = 0,
    lat = 0,
    lng = 0,
    code = "",
    merchantLogo = "",
    phone = "",
  } = item || {};

  const handleCall = useCallback(() => {
    if (!phone) return;
    try {
      const g = (
        typeof globalThis !== "undefined" ? (globalThis as any) : undefined
      ) as any;
      if (g && typeof g.open === "function") {
        g.open(`tel:${phone}`);
      }
    } catch (_) {
      // noop
    }
  }, [phone]);

  const handleShowMap = useCallback(() => {
    try {
      const g = (
        typeof globalThis !== "undefined" ? (globalThis as any) : undefined
      ) as any;
      if (g && typeof g.open === "function") {
        // Try to open Google Maps or fallback to coordinates
        const mapUrl = `https://www.google.com/maps?q=${lat},${lng}`;
        g.open(mapUrl, "_blank", "noopener");
      }
    } catch (_) {
      // noop
    }
  }, [lat, lng]);

  const hasPhone = phone && phone.length > 0;

  if (hasPhone) {
    return (
      <div
        style={containerStyle}
        data-store-code={code}
        data-merchant-code={merchantCode}
      >
        <div style={headerStyle}>
          <img src={merchantLogo} alt="merchant-logo" style={logoStyle} />
          <div style={storeNameStyle}>{name}</div>
        </div>
        <div style={addressStyle}>
          <div style={addressTextStyle}>{address?.address}</div>
          <div style={distanceWrapperStyle}>
            <IconLocation />
            <div style={distanceStyle}>{formatDistance(distance)}</div>
          </div>
        </div>
        <div style={bottomStyle}>
          <button style={phoneButtonStyle} onClick={handleCall}>
            <IconCall />
            <span>{phone}</span>
          </button>
          <button style={buttonStyle} onClick={handleShowMap}>
            <IconMap />
            <span>Xem bản đồ</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      style={containerStyle}
      data-store-code={code}
      data-merchant-code={merchantCode}
    >
      <div style={headerStyle}>
        <img src={merchantLogo} alt="merchant-logo" style={logoStyle} />
        <div style={storeNameStyle}>{name}</div>
      </div>
      <div style={addressStyle}>
        <div style={addressTextStyle}>{address?.address}</div>
      </div>
      <div style={bottomStyle}>
        <button style={buttonStyle} onClick={handleShowMap}>
          <IconMap />
          <span>Xem bản đồ</span>
        </button>
        <div style={distanceWrapperStyle}>
          <IconLocation />
          <div style={distanceStyle}>{formatDistance(distance)}</div>
        </div>
      </div>
    </div>
  );
};

export default ThumbnailStoreList;
