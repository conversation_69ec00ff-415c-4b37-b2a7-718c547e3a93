import React from 'react';

export interface MarkAsUsedToggleProps {
  isMarkedAsUsed: boolean;
  markingAsUsed: boolean;
  onToggle: () => void;
  title?: string;
  description?: string;
  className?: string;
}

export const MarkAsUsedToggle: React.FC<MarkAsUsedToggleProps> = ({
  isMarkedAsUsed,
  markingAsUsed,
  onToggle,
  title = "Đánh dấu đã sử dụng",
  description = "Giúp bạn nhớ voucher này đã được sử dụng",
  className = "",
}) => (
  <div className={`px-4 py-3 bg-white ${className}`}>
    <div className="flex justify-between items-center">
      <div className="flex-1">
        <p className="font-semibold text-base">{title}</p>
        <p className="text-sm text-gray-500 mt-1">
          {description}
        </p>
      </div>
      <button
        onClick={onToggle}
        disabled={markingAsUsed}
        className={`
          w-14 h-8 rounded-full transition-colors duration-200 relative
          ${isMarkedAsUsed ? 'bg-[#0DC98B]' : 'bg-gray-300'}
          ${markingAsUsed ? 'opacity-50' : ''}
        `}
      >
        <span
          className={`
            absolute top-1 left-0 transition-transform duration-200
            w-6 h-6 bg-white rounded-full shadow-md
            ${isMarkedAsUsed ? 'translate-x-6' : 'translate-x-1'}
          `}
        />
      </button>
    </div>
  </div>
);