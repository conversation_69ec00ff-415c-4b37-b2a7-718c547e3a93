import React, { useCallback, useState } from "react";
import { useAuthStore, VUIPointsIcon } from "@taptap/shared";

export type BrandCurrencyDetail = {
  date: string;
  point: number;
};

export type BrandCurrencyType = {
  name?: string;
  currencyCode?: string;
  totalBalance?: number;
  logo?: string;
  description?: string;
  note?: string;
  details?: BrandCurrencyDetail[];
};

export type BrandCurrencyHeaderProps = {
  merchantCode: string;
  isUsingBC: boolean;
  balanceData: BrandCurrencyType[];
};

const containerStyle: React.CSSProperties = {
  backgroundColor: "#FFFFFF",
  padding: "4px 8px",
  borderRadius: "8px",
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  gap: "4px",
  cursor: "pointer",
};

const containerBalanceStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  gap: "8px",
};

const balanceStyle: React.CSSProperties = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  gap: "4px",
};

const smallLogoStyle: React.CSSProperties = {
  width: "16px",
  height: "16px",
  borderRadius: "50%",
  objectFit: "cover",
};

const modalOverlayStyle: React.CSSProperties = {
  position: "fixed",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  zIndex: 1000,
};

const modalContentStyle: React.CSSProperties = {
  height: "538px",
  backgroundColor: "#FFFFFF",
  borderRadius: "12px",
  padding: "32px 24px",
  display: "flex",
  flexDirection: "column",
  maxWidth: "90vw",
  maxHeight: "90vh",
};

const modalTitleStyle: React.CSSProperties = {
  fontSize: "24px",
  fontWeight: "bold",
  textAlign: "center",
  marginBottom: "24px",
  color: "#000000",
};

const scrollContentStyle: React.CSSProperties = {
  flex: 1,
  overflowY: "auto",
};

const userCurrencyStyle: React.CSSProperties = {
  width: "100%",
  borderRadius: "16px",
  backgroundColor: "#F3F4F6",
  alignItems: "center",
  padding: "16px",
  marginBottom: "16px",
};

const largeLogoStyle: React.CSSProperties = {
  width: "72px",
  height: "72px",
  marginBottom: "8px",
  borderRadius: "50%",
  objectFit: "cover",
};

const currentCurrencyStyle: React.CSSProperties = {
  fontSize: "14px",
  color: "#000000",
  textAlign: "center",
};

const detailsContainerStyle: React.CSSProperties = {
  marginTop: "8px",
};

const detailTextStyle: React.CSSProperties = {
  fontSize: "12px",
  color: "#6B7280",
  textAlign: "center",
  marginBottom: "4px",
};

const currencyInfoStyle: React.CSSProperties = {
  marginBottom: "24px",
};

const sectionTitleStyle: React.CSSProperties = {
  fontSize: "14px",
  fontWeight: "bold",
  color: "#000000",
  marginTop: "16px",
  marginBottom: "8px",
};

const sectionContentStyle: React.CSSProperties = {
  fontSize: "14px",
  lineHeight: 1.5,
  color: "#374151",
  marginBottom: "16px",
};

const closeButtonStyle: React.CSSProperties = {
  backgroundColor: "#F3F4F6",
  border: "1px solid #D1D5DB",
  borderRadius: "8px",
  padding: "12px 24px",
  cursor: "pointer",
  fontSize: "14px",
  fontWeight: "600",
  color: "#374151",
  marginTop: "12px",
};

const formatCurrencyBalance = (amount: number): string => {
  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M`;
  }
  if (amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}K`;
  }
  return amount.toString();
};

const formatNumber = (num: number): string => {
  return num.toLocaleString();
};

const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  } catch {
    return dateString;
  }
};

const BrandCurrencyHeader: React.VFC<BrandCurrencyHeaderProps> = ({
  merchantCode,
  isUsingBC,
  balanceData,
}) => {
  const profile = useAuthStore((state) => state.profile);

  const [showModal, setShowModal] = useState(false);
  const [modalContent, setModalContent] = useState<BrandCurrencyType | null>(
    null
  );

  const currentBalance = balanceData[0];
  const isDisabled = !isUsingBC || !currentBalance;

  const handlePress = useCallback(() => {
    if (isDisabled) return;
    // Show modal with current balance data
    setModalContent(currentBalance);
    setShowModal(true);
  }, [isDisabled, currentBalance]);

  const closeModal = useCallback(() => {
    setShowModal(false);
    setModalContent(null);
  }, []);

  const renderModalContent = (content: BrandCurrencyType) => {
    return (
      <div style={modalContentStyle}>
        <div style={modalTitleStyle}>
          {`Thông tin ${content?.name || "tiền tệ"}`}
        </div>

        <div style={scrollContentStyle}>
          <div style={userCurrencyStyle}>
            <img
              src={currentBalance?.logo}
              alt="currency-logo"
              style={largeLogoStyle}
            />
            <div style={currentCurrencyStyle}>
              {`Bạn có ${formatNumber(currentBalance?.totalBalance || 0)}`}
            </div>

            {content.details && content.details.length > 0 ? (
              <div style={detailsContainerStyle}>
                {content.details.map(({ date, point }) => (
                  <div key={date} style={detailTextStyle}>
                    {`${formatNumber(point)} hết hạn ngày ${formatDate(date)}`}
                  </div>
                ))}
              </div>
            ) : null}
          </div>

          <div style={currencyInfoStyle}>
            {content.description ? (
              <>
                <div style={sectionTitleStyle}>Giới thiệu</div>
                <div
                  style={sectionContentStyle}
                  dangerouslySetInnerHTML={{
                    __html: content.description,
                  }}
                />
              </>
            ) : null}

            {content.note ? (
              <>
                <div style={sectionTitleStyle}>Cách sử dụng</div>
                <div
                  style={sectionContentStyle}
                  dangerouslySetInnerHTML={{ __html: content.note }}
                />
              </>
            ) : null}
          </div>
        </div>

        <button style={closeButtonStyle} onClick={closeModal}>
          Đóng
        </button>
      </div>
    );
  };

  return (
    <>
      <div
        style={{
          ...containerStyle,
          cursor: isDisabled ? "default" : "pointer",
        }}
        onClick={handlePress}
        data-merchant-code={merchantCode}
        data-using-bc={isUsingBC}
      >
        <span style={{ fontSize: "12px", color: "#6B7280" }}>Bạn có</span>

        <div style={containerBalanceStyle}>
          {currentBalance ? (
            <div style={balanceStyle}>
              <span
                style={{
                  fontSize: "14px",
                  fontWeight: "600",
                  color: "#000000",
                }}
              >
                {formatCurrencyBalance(currentBalance.totalBalance || 0)}
              </span>
              <img
                src={currentBalance.logo}
                alt="currency-logo"
                style={smallLogoStyle}
              />
            </div>
          ) : null}

          <div style={balanceStyle}>
            <span
              style={{
                fontSize: "14px",
                fontWeight: "600",
                color: "#000000",
              }}
            >
              {formatCurrencyBalance(profile.loyaltyPoint || 0)}
            </span>
            <div>
              {/* Placeholder for loyalty point icon */}
              <VUIPointsIcon />
            </div>
          </div>
        </div>
      </div>

      {showModal && modalContent && (
        <div style={modalOverlayStyle} onClick={closeModal}>
          <div onClick={(e) => e.stopPropagation()}>
            {renderModalContent(modalContent)}
          </div>
        </div>
      )}
    </>
  );
};

export default BrandCurrencyHeader;
