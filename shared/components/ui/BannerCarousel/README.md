# BannerCarousel Component

Component hiển thị banner carousel vớ<PERSON> khả năng xử lý image loading và error states.

## Tính năng

- **Image Loading States**: Hiển thị loading spinner khi đang tải image
- **Error Handling**: Hiển thị skeleton placeholder khi không thể load image
- **Responsive Design**: Tương thích với các kích thước màn hình
- **Pagination**: Hỗ trợ pagination dots cho multiple banners
- **Lazy Loading**: Sử dụng lazy loading cho images

## Props

```tsx
interface BannerCarouselProps {
  banners: BannerItem[];
  onBannerClick?: (banner: BannerItem) => void;
  className?: string;
}

interface BannerItem {
  id: string;
  imageUrl: string;
  title?: string;
  subtitle?: string;
  link?: string;
}
```

## Cách sử dụng

### Cơ bản

```tsx
import { BannerCarousel } from "./BannerCarousel";

const banners = [
  {
    id: "1",
    imageUrl: "https://example.com/banner1.jpg",
    title: "Banner 1",
    subtitle: "Mô tả banner 1",
  },
  {
    id: "2",
    imageUrl: "https://example.com/banner2.jpg",
    title: "Banner 2",
    subtitle: "Mô tả banner 2",
  },
];

<BannerCarousel
  banners={banners}
  onBannerClick={(banner) => {
    console.log("Banner clicked:", banner);
    // Xử lý khi click banner
  }}
/>;
```

### Với custom styling

```tsx
<BannerCarousel
  banners={banners}
  className="my-custom-class"
  onBannerClick={handleBannerClick}
/>
```

## Image Loading & Error Handling

Component tự động xử lý các trường hợp:

### 1. Loading State

- Hiển thị loading spinner với animation pulse
- Background màu xám nhạt với spinner xanh

### 2. Error State

- Khi image không thể load được
- Hiển thị skeleton placeholder từ `imagesAssets.mascot.skeleton`
- Background màu xám nhạt với opacity 50%

### 3. Success State

- Image được load thành công
- Hiển thị image với transition mượt mà

## Styling

### CSS Classes

- `.banner-slide`: Container cho mỗi banner slide
- `.banner-slide-image`: Image element
- `.banner-carousel-pagination`: Pagination container
- `.banner-carousel-bullet`: Pagination dots

### Default Dimensions

- **Height**: 156px
- **Width**: 100% (responsive)
- **Border Radius**: 12px (rounded-xl)

## Dependencies

- `ScrollableCarousel`: Component carousel cơ bản
- `imagesAssets.mascot.skeleton`: Skeleton placeholder image
- Tailwind CSS classes cho styling

## Browser Support

- **Image Loading**: Sử dụng `loading="lazy"` cho lazy loading
- **Error Handling**: `onError` event handler
- **Loading States**: `onLoad` event handler
- **CSS Animations**: Tailwind CSS animations

## Performance

- **Lazy Loading**: Images chỉ được load khi cần thiết
- **State Management**: Sử dụng React useState để track loading/error states
- **Memory Efficient**: Không tạo unnecessary re-renders
- **Smooth Transitions**: CSS transitions cho loading states
