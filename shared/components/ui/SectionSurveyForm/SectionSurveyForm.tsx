import React, { useState, useCallback } from 'react';

// Survey types matching mobile implementation
export enum ESurveyType {
  MERCHANDISE = 'merch',
  FORM_ACCESS_TRADE = 'formAccesstrade',
}

interface SectionSurveyFormProps {
  code: string;
  type: ESurveyType;
  link?: string;
  submitted: boolean;
  merchantName: string;
  resetMyRewardData: () => void;
  endTime?: string;
  merchantCode: string;
  rewardName: string;
  className?: string;
}

const SectionSurveyForm: React.FC<SectionSurveyFormProps> = ({
  code,
  type,
  link,
  submitted,
  merchantName,
  resetMyRewardData,
  endTime,
  merchantCode,
  rewardName,
  className = '',
}) => {
  const [loading, setLoading] = useState(false);

  // Check if voucher is expired
  const expired = !submitted && endTime ? new Date() > new Date(endTime) : false;

  // Get survey form link for merchandise type
  const getLinkMerchandise = async (): Promise<string> => {
    try {
      setLoading(true);
      // In web version, you would call actual API
      // For now, simulate API call
      const mockResult = {
        status: { code: 200 },
        data: 'https://example.com/merchandise-form'
      };
      
      if (mockResult?.status?.code === 200 && mockResult?.data) {
        return mockResult.data;
      }
      return '';
    } catch (e) {
      console.error('getLinkMerchandise error', e);
      return '';
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission based on type
  const onPressForm = useCallback(async () => {
    let linkForm = link || '';

    switch (type) {
      case ESurveyType.MERCHANDISE:
        linkForm = await getLinkMerchandise();
        break;
      case ESurveyType.FORM_ACCESS_TRADE:
        // Add UTM parameters for tracking
        try {
          const url = new URL(link || '');
          url.searchParams.set('utm_source', 'taptap_citibank');
          url.searchParams.set('utm_campaign', 'voucher');
          url.searchParams.set('utm_medium', code);
          linkForm = url.toString();
        } catch {
          linkForm = link || '';
        }
        break;
      default:
        break;
    }

    // Validate URL and open
    if (linkForm && isValidUrl(linkForm)) {
      window.open(linkForm, '_blank');
      // Update status after form submission
      if (!submitted) {
        setTimeout(() => {
          resetMyRewardData();
        }, 1000); // Simulate delay for form submission
      }
    }
  }, [type, link, code, submitted, resetMyRewardData]);

  // URL validation helper
  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const onOpenForm = async () => {
    console.log('Survey form opened:', {
      merchant_code: merchantCode,
      reward_name: rewardName,
      campaign: type,
    });
    await onPressForm();
  };

  const onCheckInfo = async () => {
    console.log('Check info clicked:', {
      merchant_code: merchantCode,
      reward_name: rewardName,
    });
    await onPressForm();
  };

  // Get description text based on type
  const getDescriptionText = () => {
    switch (type) {
      case ESurveyType.MERCHANDISE:
        return 'Điền thông tin nhận hàng để TAPTAP gửi phần quà này đến bạn.';
      case ESurveyType.FORM_ACCESS_TRADE:
        return 'Điền thông tin để Citibank hỗ trợ bạn mở thẻ nhận quà.';
      default:
        return 'Điền thông tin để nhận ưu đãi.';
    }
  };

  // Get submitted description based on type
  const getSubmittedDescription = () => {
    switch (type) {
      case ESurveyType.MERCHANDISE:
        return 'TAPTAP sẽ chuyển quà theo thông tin mà bạn đã điền.';
      case ESurveyType.FORM_ACCESS_TRADE:
        return 'Citibank sẽ liên hệ hỗ trợ bạn nếu thoả điều kiện mở thẻ.';
      default:
        return 'Thông tin đã được ghi nhận thành công.';
    }
  };

  if (submitted) {
    return (
      <div className={`bg-white mx-4 my-4 rounded-lg p-4 ${className}`}>
        {/* Content Center */}
        <div className="flex flex-col items-center text-center">
          {/* Status Submitted Badge */}
          <div className="flex items-center bg-gray-200 rounded-full px-3 py-1.5 mb-3">
            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-1.5">
              <svg width="12" height="12" viewBox="0 0 20 20" fill="none">
                <path
                  d="M7 10L9 12L13 8M19 10C19 14.9706 14.9706 19 10 19C5.02944 19 1 14.9706 1 10C1 5.02944 5.02944 1 10 1C14.9706 1 19 5.02944 19 10Z"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <span className="text-sm font-medium text-gray-700">
              Đã điền thông tin
            </span>
          </div>

          {/* Submitted Description */}
          <p className="text-sm font-medium text-gray-700 mb-4">
            {getSubmittedDescription()}
          </p>

          {/* View Gift Info Button (only for merchandise) */}
          {type === ESurveyType.MERCHANDISE && (
            <button
              onClick={onCheckInfo}
              disabled={loading}
              className={`px-6 py-2 text-sm font-medium rounded-lg transition-colors ${
                loading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {loading ? 'Đang tải...' : 'Kiểm tra thông tin'}
            </button>
          )}
        </div>

        {/* Voucher Code Label */}
        <div className="mt-4 pt-4 border-t border-gray-100">
          <p className="text-xs text-gray-500 text-center">
            Mã ưu đãi: {code}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white mx-4 my-4 rounded-lg p-4 ${className}`}>
      {/* Content */}
      <div className="flex flex-col items-center text-center">
        {/* Description */}
        <p className="text-sm text-gray-700 mb-6">
          {getDescriptionText()}
        </p>

        {/* Fill Info Button */}
        <div className="w-52">
          <button
            onClick={onOpenForm}
            disabled={loading || expired}
            className={`w-full px-4 py-3 rounded-lg font-medium text-sm transition-colors ${
              loading || expired
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-[#F65D79] text-white hover:bg-[#e54d6a]'
            }`}
          >
            {loading ? 'Đang tải...' : 'Điền thông tin'}
          </button>
        </div>
      </div>

      {/* Voucher Code Label */}
      <div className="mt-4 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-500 text-center">
          Mã ưu đãi: {code}
        </p>
      </div>
    </div>
  );
};

export default React.memo(SectionSurveyForm);