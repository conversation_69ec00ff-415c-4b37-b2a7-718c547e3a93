import React from 'react';
import { cn } from '../../../utils/cn';

// Import mascot images from mascotV3 (following the same pattern as EmptyBillState)
import meditateImg from '../../../assets/images/mascotV3/meditate.png';
import errorImg from '../../../assets/images/mascotV3/error.png';
import lostConnectionImg from '../../../assets/images/mascotV3/lostConnection.png';
import sleepImg from '../../../assets/images/mascotV3/sleep.png';
import cryImg from '../../../assets/images/mascotV3/cry.png';
import warningImg from '../../../assets/images/mascotV3/warning.png';
import inboxImg from '../../../assets/images/mascotV3/inbox.png';
import congratImg from '../../../assets/images/mascotV3/congrat.png';
import cuteImg from '../../../assets/images/mascotV3/cute.png';
import loveImg from '../../../assets/images/mascotV3/love.png';

export type MascotNameType = 'meditate' | 'error' | 'lostConnection' | 'sleep' | 'cry' | 'warning' | 'inbox' | 'congrat' | 'cute' | 'love';

export interface StatusScreenProps {
  type?: 'loading_error' | 'empty' | 'dynamic' | 'success' | 'warning';
  mascotName?: MascotNameType;
  title?: string;
  description?: string;
  actionButton?: {
    text: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary';
  };
  className?: string;
  children?: React.ReactNode;
}

// Map mascot names to imported images
const getMascotImage = (name: MascotNameType): string => {
  switch (name) {
    case 'meditate':
      return meditateImg;
    case 'error':
      return errorImg;
    case 'lostConnection':
      return lostConnectionImg;
    case 'sleep':
      return sleepImg;
    case 'cry':
      return cryImg;
    case 'warning':
      return warningImg;
    case 'inbox':
      return inboxImg;
    case 'congrat':
      return congratImg;
    case 'cute':
      return cuteImg;
    case 'love':
      return loveImg;
    default:
      return meditateImg;
  }
};

// Status type to mascot mapping for better defaults
const statusToMascotMap: Record<StatusScreenProps['type'], MascotNameType> = {
  loading_error: 'error',
  empty: 'meditate',
  dynamic: 'cute',
  success: 'congrat',
  warning: 'warning'
} as const;

// Status type to color mapping
const statusColors: Record<StatusScreenProps['type'], string> = {
  loading_error: 'text-red-500',
  empty: 'text-gray-500',
  dynamic: 'text-blue-500',
  success: 'text-green-500',
  warning: 'text-orange-500'
} as const;

export const StatusScreen: React.FC<StatusScreenProps> = ({
  type = 'empty',
  mascotName,
  title = 'No items',
  description,
  actionButton,
  className,
  children
}) => {
  // Use provided mascotName or default based on status type
  const selectedMascot = mascotName || statusToMascotMap[type];
  const statusColor = statusColors[type];

  return (
    <div className={cn(
      'flex flex-col items-center justify-center py-16 px-8',
      className
    )}>
      {/* Mascot Image */}
      <div className="w-[180px] h-[130px] flex items-center justify-center mb-6">
        <img 
          src={getMascotImage(selectedMascot)} 
          alt={`Mascot ${selectedMascot}`}
          className="w-full h-full object-contain"
        />
      </div>

      {/* Title */}
      <h2 className={cn(
        'text-lg font-bold text-center mb-2',
        statusColor
      )}
      style={{ fontFamily: 'Archia' }}
      >
        {title}
      </h2>

      {/* Description */}
      {description && (
        <p className="text-sm text-[#5A5A5A] text-center leading-relaxed mb-6 max-w-[280px]" 
           style={{ fontFamily: 'Archia' }}>
          {description}
        </p>
      )}

      {/* Action Button */}
      {actionButton && (
        <button
          onClick={actionButton.onClick}
          className={cn(
            'px-6 py-3 rounded-lg text-sm font-semibold transition-colors',
            actionButton.variant === 'secondary'
              ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              : 'bg-[#4391F7] text-white hover:bg-[#3580E6]'
          )}
          style={{ fontFamily: 'Archia' }}
        >
          {actionButton.text}
        </button>
      )}

      {/* Custom children content */}
      {children && (
        <div className="mt-4">
          {children}
        </div>
      )}
    </div>
  );
};

export default StatusScreen;