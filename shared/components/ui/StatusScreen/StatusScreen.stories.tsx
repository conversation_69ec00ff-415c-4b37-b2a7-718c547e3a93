import type { Meta, StoryObj } from '@storybook/react';
import { StatusScreen } from './StatusScreen';

const meta: Meta<typeof StatusScreen> = {
  title: 'UI/StatusScreen',
  component: StatusScreen,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    type: {
      control: 'select',
      options: ['loading_error', 'empty', 'dynamic', 'success', 'warning'],
    },
    mascotName: {
      control: 'select',
      options: ['meditate', 'error', 'lostConnection', 'sleep', 'cry', 'warning', 'inbox', 'congrat', 'cute', 'love'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    type: 'empty',
    mascotName: 'meditate',
    title: 'No items found',
    description: 'There are no items to display at the moment.',
  },
};

export const LoadingError: Story = {
  args: {
    type: 'loading_error',
    mascotName: 'error',
    title: 'Failed to load',
    description: 'Something went wrong while loading the content. Please try again.',
    actionButton: {
      text: 'Try Again',
      onClick: () => console.log('Retry clicked'),
      variant: 'primary',
    },
  },
};

export const EmptyList: Story = {
  args: {
    type: 'empty',
    mascotName: 'meditate',
    title: 'Empty List',
    description: 'The list is currently empty. Add some items to get started.',
  },
};

export const Success: Story = {
  args: {
    type: 'success',
    mascotName: 'congrat',
    title: 'Operation Complete',
    description: 'Your action has been completed successfully!',
    actionButton: {
      text: 'Continue',
      onClick: () => console.log('Continue clicked'),
      variant: 'primary',
    },
  },
};

export const Warning: Story = {
  args: {
    type: 'warning',
    mascotName: 'warning',
    title: 'Attention Required',
    description: 'Please review the information before proceeding.',
    actionButton: {
      text: 'Review',
      onClick: () => console.log('Review clicked'),
      variant: 'secondary',
    },
  },
};

export const NetworkError: Story = {
  args: {
    type: 'loading_error',
    mascotName: 'lostConnection',
    title: 'No Connection',
    description: 'Please check your internet connection and try again.',
    actionButton: {
      text: 'Retry',
      onClick: () => console.log('Retry clicked'),
      variant: 'primary',
    },
  },
};

export const WithCustomContent: Story = {
  args: {
    type: 'dynamic',
    mascotName: 'cute',
    title: 'Custom Status',
    description: 'This status screen has custom content below.',
  },
  render: (args) => (
    <StatusScreen {...args}>
      <div className="space-y-2">
        <p className="text-sm text-gray-500">Custom content goes here:</p>
        <div className="flex gap-2">
          <button className="px-3 py-1 bg-gray-200 rounded text-sm">
            Action 1
          </button>
          <button className="px-3 py-1 bg-gray-200 rounded text-sm">
            Action 2
          </button>
        </div>
      </div>
    </StatusScreen>
  ),
};