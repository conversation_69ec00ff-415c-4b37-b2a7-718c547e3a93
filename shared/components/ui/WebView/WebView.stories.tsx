import type { Meta, StoryObj } from '@storybook/react';
import { WebView } from './WebView';

const meta = {
  title: 'UI/WebView',
  component: WebView,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'WebView component for displaying external content via iframe. Supports URL validation, sandbox security, and responsive sizing.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    url: {
      control: 'text',
      description: 'URL to display in the iframe',
    },
    title: {
      control: 'text',
      description: 'Title for the iframe (accessibility)',
    },
    width: {
      control: 'text',
      description: 'Width of the iframe (CSS value or number in px)',
    },
    height: {
      control: 'text',
      description: 'Height of the iframe (CSS value or number in px)',
    },
    allowFullScreen: {
      control: 'boolean',
      description: 'Allow fullscreen mode',
    },
    sandbox: {
      control: 'text',
      description: 'Sandbox restrictions for security',
    },
    loading: {
      control: 'select',
      options: ['eager', 'lazy'],
      description: 'Loading strategy',
    },
  },
} satisfies Meta<typeof WebView>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    url: 'https://example.com',
    title: 'Example Website',
    width: 400,
    height: 300,
  },
};

export const YouTube: Story = {
  args: {
    url: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    title: 'YouTube Video',
    width: 560,
    height: 315,
    allowFullScreen: true,
  },
};

export const Google: Story = {
  args: {
    url: 'https://www.google.com/search?q=example&igu=1',
    title: 'Google Search',
    width: '100%',
    height: 400,
  },
};

export const ResponsiveSize: Story = {
  args: {
    url: 'https://tailwindcss.com',
    title: 'Tailwind CSS',
    width: '100%',
    height: '500px',
    className: 'max-w-4xl',
  },
};

export const WithCustomSandbox: Story = {
  args: {
    url: 'https://codepen.io/pen/KKBQbQb',
    title: 'CodePen Example',
    width: 600,
    height: 400,
    sandbox: 'allow-scripts allow-same-origin allow-forms',
  },
};

export const InvalidURL: Story = {
  args: {
    url: 'invalid-url',
    title: 'Invalid URL Test',
    width: 400,
    height: 200,
  },
};

export const EmptyURL: Story = {
  args: {
    url: '',
    title: 'Empty URL Test',
    width: 400,
    height: 200,
  },
};

export const MobileSize: Story = {
  args: {
    url: 'https://m.facebook.com',
    title: 'Mobile Facebook',
    width: 375,
    height: 667,
    className: 'border border-gray-300 rounded-xl shadow-lg',
  },
};

export const FullScreenCapable: Story = {
  args: {
    url: 'https://player.vimeo.com/video/148751763',
    title: 'Vimeo Video',
    width: 640,
    height: 360,
    allowFullScreen: true,
    sandbox: 'allow-scripts allow-same-origin allow-fullscreen',
  },
};