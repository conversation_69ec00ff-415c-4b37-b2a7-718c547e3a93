import React from 'react';
import { cn } from '../../../utils';

export interface WebViewProps {
  url: string;
  title?: string;
  className?: string;
  width?: string | number;
  height?: string | number;
  allowFullScreen?: boolean;
  sandbox?: string;
  loading?: 'eager' | 'lazy';
  onLoad?: () => void;
  onError?: () => void;
}

export const WebView: React.FC<WebViewProps> = ({
  url,
  title,
  className,
  width = '100%',
  height = '100%',
  allowFullScreen = false,
  sandbox = 'allow-scripts allow-same-origin allow-forms allow-popups',
  loading = 'lazy',
  onLoad,
  onError,
}) => {
  // Validate URL
  const isValidUrl = (urlString: string) => {
    try {
      const urlObj = new URL(urlString);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  };

  if (!url || !isValidUrl(url)) {
    return (
      <div 
        className={cn(
          'flex items-center justify-center bg-gray-100 border border-gray-300 rounded-lg',
          className
        )}
        style={{ width, height }}
      >
        <div className="text-center text-gray-500">
          <p className="text-sm font-medium">Invalid URL</p>
          <p className="text-xs">Please provide a valid HTTP or HTTPS URL</p>
        </div>
      </div>
    );
  }

  return (
    <iframe
      src={url}
      title={title || 'WebView'}
      className={cn('border-0 rounded-lg', className)}
      width={width}
      height={height}
      allowFullScreen={allowFullScreen}
      sandbox={sandbox}
      loading={loading}
      onLoad={onLoad}
      onError={onError}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
      }}
    />
  );
};

export default WebView;