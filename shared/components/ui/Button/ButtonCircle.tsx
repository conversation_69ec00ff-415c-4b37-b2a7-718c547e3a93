import React from "react";

/**
 * Props cho ButtonCircle component
 */
interface ButtonCircleProps {
  children?: React.ReactNode;
  variant?: "primary" | "secondary" | "grey" | "disabled";
  size?: "24px" | "32px" | "40px" | "48px";
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
  type?: "button" | "submit" | "reset";
  ariaLabel?: string;
}

/**
 * ButtonCircle Component theo TapTap Design System
 * Hỗ trợ các kích thước circle button khác nhau
 */
export const ButtonCircle: React.FC<ButtonCircleProps> = ({
  children,
  variant = "primary",
  size = "32px",
  disabled = false,
  onClick,
  className = "",
  type = "button",
  ariaLabel,
}) => {
  // Base classes cho tất cả circle buttons
  const baseClasses =
    "rounded-full flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";

  // Variant classes theo design system
  const variantClasses = {
    primary: "bg-[#F65D79] hover:bg-[#E54B6B] text-white focus:ring-[#F65D79]",
    secondary:
      "bg-[#F8F8F8] hover:bg-[#ECECEC] text-[#1A1818] focus:ring-[#F65D79]",
    grey: "bg-[#ECECEC] hover:bg-[#D1D5DB] text-[#9CA3AF] focus:ring-[#9CA3AF]",
    disabled: "bg-[#ECECEC] cursor-not-allowed text-[#9CA3AF]",
  };

  // Size classes theo design system
  const sizeClasses = {
    "24px": "w-6 h-6",
    "32px": "w-8 h-8",
    "40px": "w-10 h-10",
    "48px": "w-12 h-12",
  };

  // Combine all classes
  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled}
      aria-label={ariaLabel}
    >
      {children}
    </button>
  );
};

export default ButtonCircle;
