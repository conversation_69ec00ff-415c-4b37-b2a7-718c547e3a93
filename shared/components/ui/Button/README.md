# Button Component - Plain Variant

## ButtonPlainLargeNormal

Button component với variant "plain", size "large", và state "normal" theo thiết kế Figma.

### Tính năng

- **Variant**: Plain - button trong suốt với text màu primary pink
- **Size**: Large - padding lớn và font size 18px
- **State**: Normal - trạng thái bình thường, có thể click
- **Typography**: Sử dụng font Archia DemiBold với line-height 22px
- **Color**: Text màu #F65D79 (primary pink)

### Cách sử dụng

```tsx
import { ButtonPlainLargeNormal } from './ButtonPlainLargeNormal';

// Basic usage
<ButtonPlainLargeNormal onClick={handleClick}>
  Action
</ButtonPlainLargeNormal>

// With custom text
<ButtonPlainLargeNormal onClick={handleClick}>
  Custom Button Text
</ButtonPlainLargeNormal>

// Disabled state
<ButtonPlainLargeNormal disabled>
  Disabled Button
</ButtonPlainLargeNormal>

// With custom className
<ButtonPlainLargeNormal
  onClick={handleClick}
  className="border border-gray-300 rounded-lg"
>
  Custom Styled
</ButtonPlainLargeNormal>
```

### Props

| Prop        | Type              | Default     | Description              |
| ----------- | ----------------- | ----------- | ------------------------ |
| `children`  | `React.ReactNode` | `'Action'`  | Nội dung button          |
| `onClick`   | `() => void`      | `undefined` | Function xử lý khi click |
| `disabled`  | `boolean`         | `false`     | Trạng thái disabled      |
| `className` | `string`          | `''`        | CSS class tùy chỉnh      |

### Design Specs

- **Font Family**: Archia DemiBold
- **Font Size**: 14px (trong Figma), 18px (trong implementation)
- **Line Height**: 22px
- **Color**: #F65D79 (Primary Pink)
- **Background**: Transparent
- **Hover Effect**: Background với opacity thấp
- **Focus**: Ring focus với màu primary pink

### Demo

Sử dụng `ButtonPlainLargeNormalDemo` component để xem các ví dụ:

```tsx
import { ButtonPlainLargeNormalDemo } from "./ButtonPlainLargeNormal.demo";

<ButtonPlainLargeNormalDemo />;
```

### Tương thích

- ✅ Web (React)
- ✅ Zalo Mini App
- ✅ Responsive design
- ✅ Accessibility support
- ✅ TypeScript support
