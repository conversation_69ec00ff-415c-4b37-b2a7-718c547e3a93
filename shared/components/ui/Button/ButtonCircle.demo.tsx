import React from "react";
import { ButtonCircle } from "./ButtonCircle";
import { MinusIcon, PlusIcon } from "../../../assets";

export const ButtonCircleDemo: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-bold mb-4">ButtonCircle Demo</h2>

      {/* Size variants */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Sizes</h3>
        <div className="flex items-center gap-4">
          <ButtonCircle size="24px" variant="primary">
            <img src={MinusIcon} alt="Minus" className="w-3 h-3" />
          </ButtonCircle>
          <ButtonCircle size="32px" variant="primary">
            <img src={MinusIcon} alt="Minus" className="w-4 h-4" />
          </ButtonCircle>
          <ButtonCircle size="40px" variant="primary">
            <img src={MinusIcon} alt="Minus" className="w-5 h-5" />
          </ButtonCircle>
          <ButtonCircle size="48px" variant="primary">
            <img src={MinusIcon} alt="Minus" className="w-6 h-6" />
          </ButtonCircle>
        </div>
      </div>

      {/* Variant variants */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Variants</h3>
        <div className="flex items-center gap-4">
          <ButtonCircle size="32px" variant="primary">
            <img src={PlusIcon} alt="Plus" className="w-4 h-4" />
          </ButtonCircle>
          <ButtonCircle size="32px" variant="secondary">
            <img src={PlusIcon} alt="Plus" className="w-4 h-4" />
          </ButtonCircle>
          <ButtonCircle size="32px" variant="grey">
            <img src={PlusIcon} alt="Plus" className="w-4 h-4" />
          </ButtonCircle>
          <ButtonCircle size="32px" variant="disabled">
            <img src={PlusIcon} alt="Plus" className="w-4 h-4" />
          </ButtonCircle>
        </div>
      </div>

      {/* Interactive demo */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Interactive Demo</h3>
        <div className="flex items-center gap-4">
          <ButtonCircle
            size="32px"
            variant="primary"
            onClick={() => alert("Decrease clicked!")}
            ariaLabel="Decrease"
          >
            <img src={MinusIcon} alt="Decrease" className="w-4 h-4" />
          </ButtonCircle>

          <span className="text-lg font-semibold">1</span>

          <ButtonCircle
            size="32px"
            variant="primary"
            onClick={() => alert("Increase clicked!")}
            ariaLabel="Increase"
          >
            <img src={PlusIcon} alt="Increase" className="w-4 h-4" />
          </ButtonCircle>
        </div>
      </div>
    </div>
  );
};

export default ButtonCircleDemo;
