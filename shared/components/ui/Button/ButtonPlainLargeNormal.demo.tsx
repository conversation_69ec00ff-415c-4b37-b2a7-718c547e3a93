import React from "react";
import { ButtonPlainLargeNormal } from "./ButtonPlainLargeNormal";

/**
 * Demo component cho ButtonPlainLargeNormal
 * Hiển thị các trạng thái khác nhau của button
 */
export const ButtonPlainLargeNormalDemo: React.FC = () => {
  const handleClick = () => {
    console.log("Button clicked!");
  };

  return (
    <div className="p-8 space-y-6 bg-white">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">
        Button Plain Large Normal Demo
      </h2>

      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            Normal State
          </h3>
          <ButtonPlainLargeNormal onClick={handleClick}>
            Action
          </ButtonPlainLargeNormal>
        </div>

        <div>
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            Custom Text
          </h3>
          <ButtonPlainLargeNormal onClick={handleClick}>
            Custom Button Text
          </ButtonPlainLargeNormal>
        </div>

        <div>
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            Disabled State
          </h3>
          <ButtonPlainLargeNormal disabled>
            Disabled Button
          </ButtonPlainLargeNormal>
        </div>

        <div>
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            With Custom Class
          </h3>
          <ButtonPlainLargeNormal
            onClick={handleClick}
            className="border border-gray-300 rounded-lg"
          >
            Custom Styled
          </ButtonPlainLargeNormal>
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold text-gray-700 mb-2">Usage:</h4>
        <pre className="text-sm text-gray-600 bg-white p-3 rounded border">
          {`import { ButtonPlainLargeNormal } from './ButtonPlainLargeNormal';

<ButtonPlainLargeNormal onClick={handleClick}>
  Action
</ButtonPlainLargeNormal>`}
        </pre>
      </div>
    </div>
  );
};

export default ButtonPlainLargeNormalDemo;
