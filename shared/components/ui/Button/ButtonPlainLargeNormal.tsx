import React from "react";
import { But<PERSON> } from "./Button";

/**
 * ButtonPlainLargeNormal Component
 * Demo button với variant 'plain', size 'large', và state 'normal'
 * <PERSON> thiết kế <PERSON>gma: Button/Plain/Large/Normal
 */
export const ButtonPlainLargeNormal: React.FC<{
  children?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}> = ({ children = "Action", onClick, disabled = false, className = "" }) => {
  return (
    <Button
      variant="plain"
      size="large"
      disabled={disabled}
      onClick={onClick}
      className={className}
    >
      {children}
    </Button>
  );
};

export default ButtonPlainLargeNormal;
