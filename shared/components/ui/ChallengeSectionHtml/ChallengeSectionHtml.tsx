import React from 'react';
import { SectionHeader } from '../SectionHeader';

interface ChallengeSectionHtmlProps {
  title: string;
  html?: string;
  isExpired?: boolean;
}

const ChallengeSectionHtml: React.FC<ChallengeSectionHtmlProps> = ({
  title,
  html,
  isExpired = false,
}) => {
  if (!html) return null;

  return (
    <div className="bg-white pt-4 pb-4 mb-2">
      <div className="px-4">
        <SectionHeader title={title} />
        <div 
          className={`mt-3 text-sm leading-relaxed ${
            isExpired ? 'text-gray-400' : 'text-gray-900'
          }`}
          style={{ filter: isExpired ? 'grayscale(1)' : 'none' }}
          dangerouslySetInnerHTML={{ __html: html }}
        />
      </div>
    </div>
  );
};

export default React.memo(ChallengeSectionHtml);