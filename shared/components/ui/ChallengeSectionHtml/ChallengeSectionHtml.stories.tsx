import type { Met<PERSON>, StoryObj } from '@storybook/react';
import ChallengeSectionHtml from './ChallengeSectionHtml';

const meta: Meta<typeof ChallengeSectionHtml> = {
  title: 'Components/Challenge/ChallengeSectionHtml',
  component: ChallengeSectionHtml,
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const simpleHtml = `
  <p>Đ<PERSON> hoàn thành thử thách này, bạn cần:</p>
  <ul>
    <li>Mua sắm tại ít nhất 3 thương hiệu đối tác</li>
    <li>Tổng giá trị đơn hàng tối thiểu 500.000đ</li>
    <li>Hoàn thành trong vòng 7 ngày</li>
  </ul>
  <p><strong>Lưu ý:</strong> Chỉ tính các giao dịch hợp lệ đư<PERSON>c xác nhận bởi hệ thống.</p>
`;

const richHtml = `
  <div>
    <h3 style="color: #3B82F6; margin-bottom: 16px;">🎯 Hướng dẫn chi tiết</h3>
    <p style="margin-bottom: 12px;">Chào mừng bạn đến với <strong>Thử thách mua sắm tuần này</strong>!</p>
    
    <h4 style="color: #10B981; margin: 16px 0 8px 0;">📋 Yêu cầu:</h4>
    <ol style="margin-left: 20px;">
      <li style="margin-bottom: 8px;">Mua sắm tại <strong>ít nhất 3 thương hiệu</strong> khác nhau</li>
      <li style="margin-bottom: 8px;">Tổng giá trị đơn hàng: <strong style="color: #EF4444;">≥ 500.000đ</strong></li>
      <li style="margin-bottom: 8px;">Hoàn thành trong vòng <strong>7 ngày</strong></li>
    </ol>
    
    <h4 style="color: #F59E0B; margin: 16px 0 8px 0;">🎁 Phần thưởng:</h4>
    <ul style="margin-left: 20px;">
      <li style="margin-bottom: 8px;">1.000 điểm VUI</li>
      <li style="margin-bottom: 8px;">Voucher giảm giá 50.000đ</li>
      <li style="margin-bottom: 8px;">Cơ hội quay thưởng đặc biệt</li>
    </ul>
    
    <div style="background: #FEF3C7; padding: 12px; border-radius: 8px; margin-top: 16px;">
      <p style="color: #92400E; margin: 0;"><strong>💡 Mẹo:</strong> Hãy mua sắm tại các thương hiệu yêu thích để tích điểm nhanh chóng!</p>
    </div>
  </div>
`;

const expiredContentHtml = `
  <p>Thử thách này đã hết hạn. Bạn có thể xem thông tin để tham khảo cho các thử thách tương lai.</p>
  <p><em>Cảm ơn bạn đã tham gia!</em></p>
`;

export const Guide: Story = {
  args: {
    title: 'Hướng dẫn',
    html: simpleHtml,
    isExpired: false,
  },
};

export const RichContent: Story = {
  args: {
    title: 'Hướng dẫn chi tiết',
    html: richHtml,
    isExpired: false,
  },
};

export const Note: Story = {
  args: {
    title: 'Lưu ý',
    html: `
      <div>
        <p style="color: #EF4444;"><strong>⚠️ Quan trọng:</strong></p>
        <ul>
          <li>Chỉ áp dụng cho giao dịch trong thời gian diễn ra thử thách</li>
          <li>Không áp dụng đồng thời với các chương trình khuyến mãi khác</li>
          <li>Phần thưởng sẽ được cộng vào tài khoản trong vòng 24h</li>
        </ul>
        <p style="margin-top: 16px;"><em>Mọi thắc mắc xin liên hệ hotline: 1900-xxxx</em></p>
      </div>
    `,
    isExpired: false,
  },
};

export const ExpiredChallenge: Story = {
  args: {
    title: 'Thông tin thử thách',
    html: expiredContentHtml,
    isExpired: true,
  },
};

export const Empty: Story = {
  args: {
    title: 'Hướng dẫn',
    html: '',
    isExpired: false,
  },
};

export const UndefinedContent: Story = {
  args: {
    title: 'Hướng dẫn',
    html: undefined,
    isExpired: false,
  },
};

export const SimpleText: Story = {
  args: {
    title: 'Mô tả ngắn',
    html: '<p>Hoàn thành mua sắm để nhận điểm thưởng hấp dẫn!</p>',
    isExpired: false,
  },
};