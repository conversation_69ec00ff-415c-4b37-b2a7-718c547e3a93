import React from 'react';
import { Skeleton } from '../Skeleton';

export interface ThumbnailSkeletonNormalProps {
  className?: string;
}

export const ThumbnailSkeletonNormal: React.FC<ThumbnailSkeletonNormalProps> = ({
  className = '',
}) => {
  return (
    <Skeleton.Container className={`relative ${className}`}>
      <div className="w-[262px] h-[197px] bg-white rounded-lg shadow-md overflow-hidden">
        <div className="flex p-1.5 h-full">
          {/* Left section */}
          <div className="flex-1 ml-3 mt-1.5 flex flex-col gap-6">
            <Skeleton.Circle size="24px" />
            <div className="space-y-4">
              <Skeleton.Rect width="134px" height="16px" className="rounded" />
              <Skeleton.Rect width="134px" height="16px" className="rounded" />
              <Skeleton.Rect width="134px" height="16px" className="rounded" />
            </div>
          </div>
          
          {/* Right section - large image skeleton */}
          <Skeleton.Rect 
            width="119px" 
            height="159px" 
            className="rounded-lg"
          />
        </div>
      </div>
    </Skeleton.Container>
  );
};

export default ThumbnailSkeletonNormal;