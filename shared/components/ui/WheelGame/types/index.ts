import { WheelDetail } from "services/api/entertainmentService";

// Wheel Game Types
export interface WheelGift {
  name: string;
  image: string;
}

export interface SpinResult {
  giftIndex: number;
  state: "gift" | "no-gift";
  name: string;
  image?: string;
  data?: unknown;
  type?: "VOUCHER" | "WISHES";
}

export interface WheelGameProps {
  wheelData: WheelDetail;
  gifts: WheelGift[];
  onSpin: () => Promise<SpinResult | null>;
  onSpinComplete?: (result: unknown) => void;
  onNavigateBack?: () => void;
  onNavigateHome?: () => void;
  logoUrl?: string;
  urlCDN?: string;
  className?: string;
  remainingSpins: number
  containerClassName?: string;
  showCheckInMessage?: boolean;
  remainingCheckIns?: number;
  isSpinning?: boolean;
}

// Add Spin Sheet Types
export interface AddSpinSheetProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (spinCount: number) => void;
  currentVui?: number;
  currentPepsiPoints?: number;
  vuiPerSpin?: number;
  pepsiPointsPerSpin?: number;
}

// Modal Types
export interface WheelGameModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  className?: string;
}

export interface WheelSuccessContentProps {
  name: string;
  image: string;
  message?: string;
}

export interface WheelFailContentProps {
  remainingCheckIns?: number;
}

// Game Engine Types
export interface WOFMetadata {
  id: string;
  logoCenter: string;
  urlCDN: string;
  parts: Array<{
    name: string;
    imageURL?: string;
  }>;
}

export interface WOFConfig {
  ID: string;
  MarginLR: number;
  WheelBorderWidth: number;
  CenterRadius: number;
  CenterBorderWidth: number;
  LogoSize: number;
  IconWidth: number;
  WheelRadius: number;
  DiskRadius: number;
  WheelRadiusInside: number;
  Metadata: WOFMetadata;
}

// Import from existing types
