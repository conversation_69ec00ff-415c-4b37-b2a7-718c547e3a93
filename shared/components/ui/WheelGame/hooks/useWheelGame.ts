import { useWheelGameStore } from "../store/wheelGameStore";

export const useWheelGame = () => {
  // Sử dụng selector để chỉ lấy những gì cần thiết
  const spinCount = useWheelGameStore((state) => state.spinCount);
  const currentVui = useWheelGameStore((state) => state.currentVui);
  const currentPepsiPoints = useWheelGameStore(
    (state) => state.currentPepsiPoints
  );
  const increaseSpinCount = useWheelGameStore(
    (state) => state.increaseSpinCount
  );
  const decreaseSpinCount = useWheelGameStore(
    (state) => state.decreaseSpinCount
  );
  const resetSpinCount = useWheelGameStore((state) => state.resetSpinCount);
  const getTotalVuiNeeded = useWheelGameStore(
    (state) => state.getTotalVuiNeeded
  );
  const getTotalPepsiPointsNeeded = useWheelGameStore(
    (state) => state.getTotalPepsiPointsNeeded
  );
  const getAdditionalVuiNeeded = useWheelGameStore(
    (state) => state.getAdditionalVuiNeeded
  );
  const getAdditionalPepsiPointsNeeded = useWheelGameStore(
    (state) => state.getAdditionalPepsiPointsNeeded
  );
  const getCanConfirm = useWheelGameStore((state) => state.getCanConfirm);
  const setInitialValues = useWheelGameStore((state) => state.setInitialValues);

  return {
    // State
    spinCount,
    currentVui,
    currentPepsiPoints,

    // Actions
    increaseSpinCount,
    decreaseSpinCount,
    resetSpinCount,

    // Computed values
    totalVuiNeeded: getTotalVuiNeeded(),
    totalPepsiPointsNeeded: getTotalPepsiPointsNeeded(),
    additionalVuiNeeded: getAdditionalVuiNeeded(),
    additionalPepsiPointsNeeded: getAdditionalPepsiPointsNeeded(),
    canConfirm: getCanConfirm(),

    // Utilities
    setInitialValues,
  };
};
