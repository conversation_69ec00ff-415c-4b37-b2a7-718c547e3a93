# Performance Optimization Guide - WheelGame Module

## 🚀 Tối ưu hóa đã thực hiện

### 1. **Tách Component thành các phần nhỏ (Component Splitting)**

- `AddSpinSheet` → `BalanceInfo` + `SpinCounter` + `PointsDisplay`
- Mỗi component chỉ re-render khi cần thiết
- Sử dụng `React.memo()` để tránh re-render không cần thiết

### 2. **Sử dụng Zustand Store thay vì Prop Drilling**

```tsx
// Trước: Props được truyền từ cha xuống con
<AddSpinSheet
  currentVui={10}
  currentPepsiPoints={30}
  vuiPerSpin={2}
  pepsiPointsPerSpin={1}
/>;

// Sau: Mỗi component lấy data trực tiếp từ store
const { currentVui, currentPepsiPoints } = useWheelGameStore();
```

### 3. **Memoization với React.memo() + Selector Pattern**

```tsx
export const SpinCounter = memo(() => {
  // Component chỉ re-render khi spinCount thay đổi
  const spinCount = useWheelGameStore((state) => state.spinCount);
  const increaseSpinCount = useWheelGameStore(
    (state) => state.increaseSpinCount
  );
  const decreaseSpinCount = useWheelGameStore(
    (state) => state.decreaseSpinCount
  );
  // ...
});
```

### 4. **Computed Values trong Store**

```tsx
// Các giá trị được tính toán trong store, không phải trong component
getTotalVuiNeeded: () => {
  const { spinCount, vuiPerSpin } = get();
  return spinCount * vuiPerSpin;
},
```

### 5. **Selector Pattern để tối ưu Re-render**

```tsx
// ❌ Không tốt: Component sẽ re-render khi bất kỳ state nào thay đổi
const { spinCount, currentVui, currentPepsiPoints } = useWheelGameStore();

// ✅ Tốt: Component chỉ re-render khi state cụ thể thay đổi
const spinCount = useWheelGameStore((state) => state.spinCount);
const currentVui = useWheelGameStore((state) => state.currentVui);
const currentPepsiPoints = useWheelGameStore(
  (state) => state.currentPepsiPoints
);
```

### 6. **Custom Hook để quản lý Store**

```tsx
export const useWheelGame = () => {
  // Sử dụng selector để chỉ lấy những gì cần thiết
  const spinCount = useWheelGameStore((state) => state.spinCount);
  const totalVuiNeeded = useWheelGameStore((state) =>
    state.getTotalVuiNeeded()
  );
  const canConfirm = useWheelGameStore((state) => state.getCanConfirm());
  // ...
};
```

## 📊 So sánh Performance

### **Trước khi tối ưu:**

- ❌ Component re-render mỗi khi state thay đổi
- ❌ Functions được tạo lại mỗi lần render
- ❌ Props được truyền qua nhiều cấp component
- ❌ Calculations được thực hiện lại mỗi lần render

### **Sau khi tối ưu:**

- ✅ Chỉ component cần thiết mới re-render
- ✅ Functions được cache với useCallback
- ✅ Data được lấy trực tiếp từ store
- ✅ Calculations được cache trong store

## 🎯 Lợi ích của việc tối ưu

1. **Performance tốt hơn**: Ít re-render, ít calculations
2. **Maintainability**: Code dễ đọc, dễ sửa
3. **Scalability**: Dễ thêm tính năng mới
4. **Testing**: Mỗi component có thể test độc lập
5. **Reusability**: Components có thể tái sử dụng

## 🔧 Cách sử dụng

### **Trong component cha:**

```tsx
import { AddSpinSheet } from "@taptap/shared";

function ParentComponent() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <AddSpinSheet
      open={isOpen}
      onClose={() => setIsOpen(false)}
      onConfirm={(spinCount) => console.log(spinCount)}
    />
  );
}
```

### **Trong component con (nếu cần):**

```tsx
import { useWheelGame } from "@taptap/shared";

function CustomComponent() {
  const { spinCount, increaseSpinCount } = useWheelGame();

  return (
    <button onClick={increaseSpinCount}>
      Tăng lượt quay (Hiện tại: {spinCount})
    </button>
  );
}
```

## 📝 Best Practices

1. **Luôn sử dụng `memo()` cho components con**
2. **Sử dụng store thay vì props cho shared state**
3. **Tách logic phức tạp vào store**
4. **Sử dụng computed values thay vì tính toán trong component**
5. **Tránh tạo functions mới trong render**
6. **Luôn sử dụng selector pattern: `useWheelGameStore(state => state.specificValue)`**
7. **Không destructure toàn bộ store: `const store = useWheelGameStore()`**

## 🚨 Lưu ý

- Zustand store sẽ tự động trigger re-render cho components sử dụng nó
- **Chỉ những components subscribe vào store mới re-render**
- **Sử dụng selector pattern để chỉ subscribe vào state cần thiết**
- Sử dụng `useCallback` và `useMemo` khi cần thiết
- Test performance với React DevTools Profiler
- **Selector pattern giúp tránh re-render không cần thiết khi state khác thay đổi**
