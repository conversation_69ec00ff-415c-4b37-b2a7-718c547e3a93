import React, { memo } from "react";
import { useWheelGameStore } from "../store/wheelGameStore";
import { ButtonCircle } from "../../Button";
import { MinusIcon, PlusIcon } from "../../../../assets";

export const SpinCounter = memo(() => {
  const spinCount = useWheelGameStore((state) => state.spinCount);
  const increaseSpinCount = useWheelGameStore(
    (state) => state.increaseSpinCount
  );
  const decreaseSpinCount = useWheelGameStore(
    (state) => state.decreaseSpinCount
  );

  return (
    <div className="bg-[#F8F8F8] rounded-lg p-6 w-full">
      <div className="text-sm font-semibold text-[#1A1818] leading-[22px] mb-4 w-full text-center">
        Số lượt quay muốn đổi
      </div>

      <div className="flex items-center justify-center gap-5">
        {/* Decrease button */}
        <ButtonCircle
          variant={spinCount <= 1 ? "disabled" : "primary"}
          size="32px"
          onClick={decreaseSpinCount}
          disabled={spinCount <= 1}
          ariaLabel="Giảm số lượt quay"
        >
          <img src={MinusIcon} alt="Giảm" className="w-4 h-4" />
        </ButtonCircle>

        {/* Spin count display */}
        <div className="text-lg font-semibold text-[#1A1818] leading-[24px] w-[21px] text-center">
          {spinCount}
        </div>

        {/* Increase button */}
        <ButtonCircle
          variant="primary"
          size="32px"
          onClick={increaseSpinCount}
          ariaLabel="Tăng số lượt quay"
        >
          <img src={PlusIcon} alt="Tăng" className="w-4 h-4" />
        </ButtonCircle>
      </div>
    </div>
  );
});

SpinCounter.displayName = "SpinCounter";
