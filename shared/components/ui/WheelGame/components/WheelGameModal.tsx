import React from "react";
import { cn } from "../../../../utils";
import BannerDetailReward from "../../../../assets/images/wheel/BannerDetailRe.png";
import type {
  WheelGameModalProps,
  WheelSuccessContentProps,
  WheelFailContentProps,
} from "../types";

export const WheelGameModal: React.FC<WheelGameModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  className = "",
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />

      {/* Modal */}
      <div
        className={cn(
          "relative bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-xl",
          className
        )}
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100"
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path
              d="M15 5L5 15M5 5L15 15"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        {/* Title */}
        <h3 className="text-xl font-bold text-center mb-4 text-[#13b2ed]">
          {title}
        </h3>

        {/* Content */}
        <div>{children}</div>
      </div>
    </div>
  );
};

export const WheelSuccessContent: React.FC<WheelSuccessContentProps> = ({
  name,
  image,
  message,
}) => (
  <div className="flex flex-col justify-center items-center w-full">
    <p className="text-xl font-bold text-center p-5">Bạn đã nhận được {name}</p>
    <img
      src={image}
      alt="Reward"
      className="w-[200px] h-[200px] object-contain mx-auto"
    />
    {message && <p className="text-center mt-4 text-gray-600">{message}</p>}
  </div>
);

export const WheelFailContent: React.FC<WheelFailContentProps> = ({
  remainingCheckIns,
}) => (
  <div className="flex flex-col justify-center">
    <p className="text-lg font-bold text-center">
      Chúc bạn may mắn lần sau!
      <br />
      ✨<br />
      {remainingCheckIns && remainingCheckIns > 0 && (
        <>
          Đừng lo, bạn vẫn còn cơ hội nhận thêm phần quà hấp dẫn từ chương trình
          khi hoàn thành ít nhất{" "}
          <span className="text-xl text-[#18478d]">{remainingCheckIns}</span>{" "}
          lượt điểm danh hợp lệ.
        </>
      )}
    </p>
    <div
      className="relative bg-contain bg-no-repeat bg-center w-full h-auto flex items-center justify-center mb-5 mt-5"
      style={{ backgroundImage: `url(${BannerDetailReward})` }}
    >
      <div className="text-[#296996] font-bold text-lg w-full text-center p-6">
        Hãy tiếp tục cố gắng nhé! 🎁
      </div>
    </div>
  </div>
);
