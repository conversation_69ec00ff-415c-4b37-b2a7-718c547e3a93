/* eslint-disable react/prop-types */
import { memo, useState, useCallback } from "react";
import { Dialog } from "../../Dialog/Dialog";
import { BalanceInfo } from "./BalanceInfo";
import { SpinCounter } from "./SpinCounter";
import { PointsDisplay } from "./PointsDisplay";
import { useWheelGameStore } from "../store/wheelGameStore";
import { entertainmentService } from "../../../../services/api/entertainmentService";
import congratImage from "../../../../assets/images/mascot/Congrat.png";

export interface AddSpinSheetProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (spinCount: number) => void;
}

export const AddSpinSheet = memo<AddSpinSheetProps>((props) => {
  const { open, onClose } = props;
  // const getCanConfirm = useWheelGameStore((state) => state.getCanConfirm);
  const resetSpinCount = useWheelGameStore((state) => state.resetSpinCount);
  const spinCount = useWheelGameStore((state) => state.spinCount);
  const wheelDetail = useWheelGameStore((state) => state.wheelDetail);
  const setWheelDetail = useWheelGameStore((state) => state.setWheelDetail);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [status, setStatus] = useState<"purchase" | "success">("purchase");

  const handleConfirm = useCallback(() => {
    const user = wheelDetail?.user;
    const wheel = wheelDetail?.wheel;
    const wheelId = wheel?._id;
    const pricePerTurn = wheel?.purchaseByVUIPoint;

    const canConfirm = Boolean(
      user &&
        wheelId &&
        pricePerTurn &&
        user.vuiPoints >= spinCount * pricePerTurn
    );

    if (!canConfirm || !wheelId) {
      return;
    }

    setConfirmLoading(true);
    entertainmentService
      .purchaseTurn({ wheelId, turns: spinCount })
      .then((response) => {
        const updatedUser = response?.data;
        console.log("updatedUser", updatedUser);
        if (updatedUser && wheelDetail) {
          setWheelDetail({ ...wheelDetail, user: { ...updatedUser } });
          resetSpinCount();
          setStatus("success");
        } else {
          alert(response?.status?.message);
        }
      })
      .finally(() => setConfirmLoading(false));
  }, [resetSpinCount, setWheelDetail, spinCount, wheelDetail]);

  const handleClose = () => {
    setStatus("purchase");
    onClose();
  };

  // const canConfirm = getCanConfirm();

  const renderPurchaseByVUIPoint = () => {
    return (
      <Dialog
        variant="bottomSheet"
        isOpen={open}
        onClose={handleClose}
        title="Thêm lượt quay"
        confirmText="Xác nhận"
        onConfirm={
          wheelDetail?.user &&
          wheelDetail?.wheel?.purchaseByVUIPoint &&
          wheelDetail?.user.vuiPoints >=
            spinCount * (wheelDetail?.wheel?.purchaseByVUIPoint || 0)
            ? handleConfirm
            : undefined
        }
        confirmLoading={confirmLoading}
      >
        <div className="flex flex-col gap-8 py-2 justify-center items-center bg-white">
          {/* Current balance info */}
          <BalanceInfo />

          {/* Spin counter section */}
          <SpinCounter />

          {/* Total calculation section */}
          <PointsDisplay />
        </div>
      </Dialog>
    );
  };

  const renderSuccess = () => {
    return (
      <Dialog
        variant="bottomSheet"
        isOpen={open}
        onClose={handleClose}
        title="Thêm lượt quay"
        confirmText="Xong"
        onConfirm={handleClose}
      >
        <div className="flex flex-col items-center bg-white px-4 pb-6">
          {/* Success icon */}
          <div className="rounded-full flex items-center justify-center mt-6 mb-4">
            <img
              src={congratImage}
              alt="Congratulations"
              className=" object-contain"
            />
          </div>

          {/* Success text */}
          <div className="text-center mb-6">
            <p className="text-sm text-[#666666] bodyRegular">
              Đã đổi thành công.
            </p>
          </div>
        </div>
      </Dialog>
    );
  };

  return (
    <>{status === "purchase" ? renderPurchaseByVUIPoint() : renderSuccess()}</>
  );
});

AddSpinSheet.displayName = "AddSpinSheet";

export default AddSpinSheet;
