import type { <PERSON>a, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { WheelGame, WheelGameModal, WheelSuccessContent, WheelFailContent } from './';

const meta = {
  title: 'UI/WheelGame',
  component: WheelGame,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof WheelGame>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockGifts = [
  { name: 'Voucher 50K', image: 'https://via.placeholder.com/100x100/FF6B6B/FFFFFF?text=50K' },
  { name: 'Voucher 100K', image: 'https://via.placeholder.com/100x100/4ECDC4/FFFFFF?text=100K' },
  { name: '<PERSON>ú<PERSON> may mắn lần sau', image: '' },
  { name: 'Voucher 20K', image: 'https://via.placeholder.com/100x100/45B7D1/FFFFFF?text=20K' },
  { name: 'Free Ship', image: 'https://via.placeholder.com/100x100/F7B731/FFFFFF?text=Ship' },
  { name: 'Voucher 200K', image: 'https://via.placeholder.com/100x100/5F27CD/FFFFFF?text=200K' },
];

const WheelGameWithState = () => {
  const [remainingSpins, setRemainingSpins] = useState(3);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState<{
    title: string;
    content: React.ReactNode;
  }>({ title: '', content: null });

  const handleSpin = async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Random result
    const giftIndex = Math.floor(Math.random() * mockGifts.length);
    const gift = mockGifts[giftIndex];
    const isWin = gift.name !== 'Chúc may mắn lần sau';
    
    // Update remaining spins
    setRemainingSpins(prev => Math.max(0, prev - 1));
    
    return {
      giftIndex,
      state: isWin ? 'gift' : 'no-gift' as 'gift' | 'no-gift',
      name: gift.name,
      image: gift.image,
    };
  };

  const handleSpinComplete = (result: any) => {
    const gift = mockGifts.find(g => g.name === result?.name);
    const isWin = gift?.name !== 'Chúc may mắn lần sau';
    
    setModalContent({
      title: isWin ? 'XIN CHÚC MỪNG' : 'TIẾC QUÁ',
      content: isWin && gift ? (
        <WheelSuccessContent
          name={gift.name}
          image={gift.image}
          message="Phần thưởng đã được thêm vào tài khoản của bạn!"
        />
      ) : (
        <WheelFailContent remainingCheckIns={5} />
      ),
    });
    setIsModalOpen(true);
  };

  return (
    <>
      <WheelGame
        gifts={mockGifts}
        remainingSpins={remainingSpins}
        onSpin={handleSpin}
        onSpinComplete={handleSpinComplete}
        onNavigateBack={() => console.log('Navigate back')}
        onNavigateHome={() => console.log('Navigate home')}
      />
      
      <WheelGameModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={modalContent.title}
      >
        {modalContent.content}
      </WheelGameModal>
    </>
  );
};

export const Default: Story = {
  render: () => <WheelGameWithState />,
};

export const NoSpinsLeft: Story = {
  render: () => (
    <WheelGame
      gifts={mockGifts}
      remainingSpins={0}
      onSpin={async () => ({ giftIndex: 0, state: 'gift', name: '', image: '' })}
      onNavigateHome={() => console.log('Navigate home')}
    />
  ),
};

export const CustomLogoAndCDN: Story = {
  render: () => (
    <WheelGame
      gifts={mockGifts}
      remainingSpins={5}
      onSpin={async () => ({ giftIndex: 0, state: 'gift', name: '', image: '' })}
      logoUrl="https://via.placeholder.com/80x80/FF6B6B/FFFFFF?text=Logo"
      urlCDN="https://custom-cdn.example.com"
    />
  ),
};

export const SuccessModal: Story = {
  render: () => (
    <WheelGameModal
      isOpen={true}
      onClose={() => console.log('Close modal')}
      title="XIN CHÚC MỪNG"
    >
      <WheelSuccessContent
        name="Voucher 100K"
        image="https://via.placeholder.com/200x200/4ECDC4/FFFFFF?text=100K"
        message="Phần thưởng đã được thêm vào tài khoản của bạn!"
      />
    </WheelGameModal>
  ),
};

export const FailModal: Story = {
  render: () => (
    <WheelGameModal
      isOpen={true}
      onClose={() => console.log('Close modal')}
      title="TIẾC QUÁ"
    >
      <WheelFailContent remainingCheckIns={3} />
    </WheelGameModal>
  ),
};