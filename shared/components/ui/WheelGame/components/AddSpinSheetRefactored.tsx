/* eslint-disable react/prop-types */
import { memo, useState, useCallback } from "react";
import { ExchangePointsDialog } from "../../ExchangePointsDialog";
import { Dialog } from "../../Dialog/Dialog";
import { useWheelGameStore } from "../store/wheelGameStore";
import { entertainmentService } from "../../../../services/api/entertainmentService";
import congratImage from "../../../../assets/images/mascot/Congrat.png";

export interface AddSpinSheetProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (spinCount: number) => void;
}

export const AddSpinSheetRefactored = memo<AddSpinSheetProps>((props) => {
  const { open, onClose } = props;
  const wheelDetail = useWheelGameStore((state) => state.wheelDetail);
  const setWheelDetail = useWheelGameStore((state) => state.setWheelDetail);
  const [status, setStatus] = useState<"purchase" | "success">("purchase");

  const handleConfirm = useCallback(async (quantity: number) => {
    const user = wheelDetail?.user;
    const wheel = wheelDetail?.wheel;
    const wheelId = wheel?._id;

    if (!wheelId || !user || !wheel) {
      throw new Error("Missing wheel or user data");
    }

    const response = await entertainmentService.purchaseTurn({ 
      wheelId, 
      turns: quantity 
    });
    
    const updatedUser = response?.data;
    if (updatedUser && wheelDetail) {
      setWheelDetail({ ...wheelDetail, user: { ...updatedUser } });
      setStatus("success");
    } else {
      throw new Error(response?.status?.message || "Purchase failed");
    }
  }, [setWheelDetail, wheelDetail]);

  const handleClose = () => {
    setStatus("purchase");
    onClose();
  };

  const renderSuccess = () => {
    return (
      <Dialog
        variant="bottomSheet"
        isOpen={open}
        onClose={handleClose}
        title="Thêm lượt quay"
        confirmText="Xong"
        onConfirm={handleClose}
      >
        <div className="flex flex-col items-center bg-white px-4 pb-6">
          {/* Success icon */}
          <div className="rounded-full flex items-center justify-center mt-6 mb-4">
            <img
              src={congratImage}
              alt="Congratulations"
              className="object-contain"
            />
          </div>

          {/* Success text */}
          <div className="text-center mb-6">
            <p className="text-sm text-[#666666] bodyRegular">
              Đã đổi thành công.
            </p>
          </div>
        </div>
      </Dialog>
    );
  };

  if (status === "success") {
    return renderSuccess();
  }

  const currentPoints = wheelDetail?.user?.vuiPoints || 0;
  const pointsPerItem = wheelDetail?.wheel?.purchaseByVUIPoint || 0;

  return (
    <ExchangePointsDialog
      open={open}
      onClose={handleClose}
      title="Thêm lượt quay"
      currentPoints={currentPoints}
      pointsPerItem={pointsPerItem}
      itemName="lượt quay"
      onConfirm={handleConfirm}
      minQuantity={1}
      maxQuantity={99}
    />
  );
});

AddSpinSheetRefactored.displayName = "AddSpinSheetRefactored";

export default AddSpinSheetRefactored;