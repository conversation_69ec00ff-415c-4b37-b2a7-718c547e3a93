import { memo } from "react";
import { useNavigate } from "react-router-dom";
import { useWheelGameStore } from "../store/wheelGameStore";
import { VUIPointsIcon } from "../../VUIPointsIcon";
import { formatPoints } from "../../../../utils/formatNumber";

export const PointsDisplay = memo(() => {
  const navigate = useNavigate();
  const user = useWheelGameStore((state) => state.wheelDetail?.user);
  const wheel = useWheelGameStore((state) => state.wheelDetail?.wheel);
  const spinCount = useWheelGameStore((state) => state.spinCount);

  if (!user || !wheel || !wheel.purchaseByVUIPoint) return null;

  const totalVuiNeeded = wheel.purchaseByVUIPoint * spinCount;

  const handleNavigateToMemberCode = () => {
    navigate("/member-code");
  };

  return (
    <div className="flex flex-col gap-4 w-full">
      <div className="flex items-center justify-between">
        <div className="text-sm font-semibold text-[#1A1818] leading-[22px]">
          Tổng cộng
        </div>

        <div className="flex gap-3">
          {/* VUI points */}
          <div className="flex items-center gap-1.5">
            <VUIPointsIcon className="flex-shrink-0" />
            <span className="text-lg font-semibold text-[#1A1818] leading-[24px]">
              {formatPoints(totalVuiNeeded)}
            </span>
          </div>
        </div>
      </div>

      {/* Additional points needed */}
      {user.vuiPoints < totalVuiNeeded && (
        <div className="flex flex-col gap-2">
          <div className="text-sm font-semibold text-[#FF4C4D] leading-[22px] text-center">
            Cần thêm {formatPoints(Math.abs(totalVuiNeeded - user?.vuiPoints))} VUI
          </div>

          <button
            className="text-sm font-semibold text-[#1A1818] leading-[22px] underline hover:no-underline transition-all"
            onClick={handleNavigateToMemberCode}
          >
            Làm sao tích thêm?
          </button>
        </div>
      )}
    </div>
  );
});

PointsDisplay.displayName = "PointsDisplay";
