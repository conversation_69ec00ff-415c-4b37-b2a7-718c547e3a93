export interface WOFMetadata {
  id: string;
  logoCenter: string;
  urlCDN: string;
  parts: Array<{
    name: string;
    imageURL?: string;
  }>;
}

export class WOFConfig {
  constructor(id: string, screenWidth: number);
  ID: string;
  MarginLR: number;
  WheelBorderWidth: number;
  CenterRadius: number;
  CenterBorderWidth: number;
  LogoSize: number;
  IconWidth: number;
  WheelRadius: number;
  DiskRadius: number;
  WheelRadiusInside: number;
  Metadata: WOFMetadata;
}

export class GameWOFPixi {
  constructor(
    metadata: WOFMetadata,
    containerRef: { current: HTMLElement | null }
  );

  load(): Promise<void>;
  playAndStopAt(index: number): void;
  enableTouchToPlay(enable: boolean): void;
  onCompleted(callback: () => void): void;
  destroy(): void;
  getPrize(): any;

  flagEnableTouchToPlay: boolean;
  flagIsRunning: boolean;
  index: number;
  eventCallback: ((event: any) => void) | null;
}
