import * as PIXI from "pixi.js";
import { Disk } from "./wof-disk";
import { ArrowBase64Image } from "./assets";
const { Application, Sprite, Graphics, Container, Rectangle, Texture } = PIXI;
import { gsap } from "gsap";

export class WOFConfig {
  MarginLR = 15;
  WheelBorderWidth = 14;
  CenterRadius = 35;
  CenterBorderWidth = 10;
  LogoSize = 80; //// 60
  IconWidth = 32;
  Metadata = { logoCenter: "", parts: [], urlCDN: null };
  constructor(id, screenWidth) {
    this.ID = id;
    this.WheelRadius = (screenWidth - this.MarginLR * 2) / 2;
    this.DiskRadius = this.WheelRadius - this.WheelBorderWidth / 2;
    this.WheelRadiusInside = this.DiskRadius - this.WheelBorderWidth * 0.5;
  }
}

export class GameWOFPixi {
  //// radians
  flagEnableTouchToPlay = false;
  flagIsRunning = false;
  index = 0;
  eventCallback = null;

  constructor(metadata, containerRef) {
    if (!metadata) {
      throw new Error("Metadata is missing");
    }

    if (metadata.parts === undefined || metadata.parts === null) {
      throw new Error("Metadata.parts is missing");
    }

    if (metadata.id === undefined || metadata.id === null) {
      throw new Error("Metadata.id is missing");
    }

    let gameContainer = containerRef.current;
    if (!gameContainer) {
      throw new Error("Container reference is missing");
    }

    this.width = gameContainer.offsetWidth || 300;
    this.height = gameContainer.offsetHeight || 300;

    this.app = new Application({
      antialias: true, // default: false
      transparent: true, // default: false,
      backgroundAlpha: 0,
      autoDensity: true,
      width: this.width,
      height: this.height,
      // resolution: window.devicePixelRatio || 1,
      resolution: 2,
      resizeTo: gameContainer,
    });

    // Expose PIXI app to global scope for debugging
    globalThis.__PIXI_APP__ = this.app;

    let wofSize = this.width;
    if (this.width > this.height) {
      wofSize = this.height;
    }

    this.WOF = new WOFConfig(metadata.id, wofSize);
    this.WOF.Metadata = metadata;
    this.containerSize = this.width;
    console.log(`WHEEL id: ${this.WOF.ID}, size : ${wofSize}`);
    this.center = new PIXI.Point(
      this.containerSize / 2,
      this.containerSize / 2
    );

    this.stage = this.app.stage;
    // Add the canvas that Pixi automatically created for you to the HTML document
    if (this.app.view) {
      this.app.view.id = "game";
      gameContainer.appendChild(this.app.view);
    } else {
      throw new Error("Pixi application view is not initialized");
    }
    this.app.stop();

    // load resources
    this.bundle = {};
    this.listOfAsset = {};

    metadata.parts.forEach((element) => {
      if (element.name && element.imageURL) {
        this.listOfAsset[element.name] = element.imageURL;
      }
    });

    if (metadata.logoCenter && metadata.logoCenter !== "") {
      this.listOfAsset["logoCenter"] = metadata.logoCenter;
    }

    this.setupScene = this.setupScene.bind(this);
    this.setupRender = this.setupRender.bind(this);
    this.update = this.update.bind(this);
    this.onAssetsLoaded = this.onAssetsLoaded.bind(this);
    this.renderQueue = [];
    this.isRendering = false;
  }

  bindingEvents() {
    if (this.app.stage) {
      this.app.stage.eventMode = "static";
    }
    ////
    //// this.onClick = this.onClick.bind(this);
    //// this.app.stage.on("pointerdown", this.onClick);
  }

  setupRender(renderer) {
    if (!renderer) {
      return;
    }

    this.renderer = renderer;
    renderer.view.style.position = "absolute";
    renderer.view.style.display = "block";
    //// renderer.view.style.width = `${this.width}px`;
    //// renderer.view.style.height = `${this.height}px`;
    //Start the loop
    this.app.ticker.add(this.update);
  }

  setupScene() {
    this.container = new Container();
    this.container.hitArea = new Rectangle(0, 0, this.width, this.height);
    //
    this.diskContainer = new Container();

    //
    this.graphics = new Graphics();
    this.app.stage?.addChild(this.container);
    this.container?.addChild(this.graphics);
    //// set pivot (tam quay)
    this.container.pivot.x = this.containerSize / 2;
    this.container.pivot.y = this.containerSize / 2;
    const cx = this.width / 2;
    const cy = this.height / 2;
    this.container.position.set(cx, cy);
    //
    this.disk = new Disk(this.WOF, this.bundle || null);
    this.disk.position.set(this.containerSize / 2, this.containerSize / 2);
    this.container?.addChild(this.disk);
    //// this.disk.position.set(cx, cy);
    //// this.stage.addChildAt(this.disk, 0);
    this.blurFilter1 = new PIXI.BlurFilter();
    this.blurFilter1.blur = 0;
    ////
    const Rc = this.WOF.CenterRadius;
    this.mask = new Graphics();
    this.mask.lineStyle(0);
    this.mask.beginFill(0xff0000, 1);
    this.mask.position.set(this.containerSize / 2, this.containerSize / 2);
    this.mask.drawCircle(0, 0, Rc - 2);
    this.mask.endFill();
    this.container?.addChild(this.mask);

    // Add AssetImage
    // const assetTexture = PIXI.Texture.from(BackGroundBase64Image);
    // const assetSprite = new PIXI.Sprite(assetTexture);
    // assetSprite.anchor.set(0.5);
    // assetSprite.position.set(this.containerSize / 2, this.containerSize / 2);
    // assetSprite.scale.set(1.45);
    // this.container.addChildAt(assetSprite, 0);

    // Add background circle (aligned) instead of offset shadow
    const backgroundCircle = new Graphics();
    backgroundCircle.beginFill(0x000000, 0.2);
    backgroundCircle.drawCircle(
      this.containerSize / 2,
      this.containerSize / 2,
      this.WOF.WheelRadius + 6
    );
    backgroundCircle.endFill();
    this.container?.addChildAt(backgroundCircle, 0);
  }

  async load() {
    const keys = Object.keys(this.listOfAsset);
    if (keys.length) {
      // Đợi tất cả assets load xong
      const loadPromises = keys.map(async (k) => {
        if (!this.listOfAsset[k]) return;

        try {
          PIXI.Assets.add(k, this.listOfAsset[k]);
          this.bundle[k] = await PIXI.Assets.load(this.listOfAsset[k]);
          console.log(`Asset loaded: ${k}`);
        } catch (error) {
          console.warn(`Failed to load asset: ${k}`, error);
        }
      });

      // Đợi tất cả assets load xong
      await Promise.all(loadPromises);
    }

    // Đợi thêm 1 frame để đảm bảo GPU sẵn sàng
    await new Promise((resolve) => requestAnimationFrame(resolve));

    this.onAssetsLoaded();
  }

  destroy() {
    try {
      if (this.container) {
        this.disk.destroy(true);
        this.container.destroy(true);
      }

      this.app.destroy(true);
      if (this.listOfAsset) {
        this.listOfAsset = null;
      }

      console.log("Destroy WHEEL");
    } catch (er) {
      console.log(er);
    }
  }

  paint() {
    // const R = this.WOF.DiskRadius;
    // const Ri = this.WOF.WheelRadiusInside;
    const Rc = this.WOF.CenterRadius;
    this.graphics.clear();
    //// draw background rect
    this.graphics.beginFill(0xf5f6f8, 0);
    this.graphics.drawRect(0, 0, this.containerSize, this.containerSize);
    this.graphics.endFill();

    //// shadow border
    this.graphics.lineStyle(this.WOF.WheelBorderWidth / 2, 0xffffff);
    this.graphics.drawCircle(
      this.center.x,
      this.center.y,
      this.WOF.WheelRadius - this.WOF.WheelBorderWidth / 3
    );

    this.disk.paint();
    //// draw circle inside with color #d8b328
    this.graphics.lineStyle(6, 0xffffff);
    this.graphics.drawCircle(
      this.center.x,
      this.center.y,
      this.WOF.DiskRadius - 4
    );
    //// draw additional border inside with color #b7cbdc
    // this.graphics.lineStyle(7, 0xfff000);
    // this.graphics.drawCircle(
    //   this.center.x,
    //   this.center.y,
    //   this.WOF.DiskRadius - 10
    // );
    //// draw circle center with color #ffffff
    this.graphics.lineStyle(0);
    this.graphics.beginFill(0xffffff);
    this.graphics.drawCircle(this.center.x, this.center.y, Rc - 10);
    this.graphics.endFill();
    ////
    try {
      this.paintLogoAtCenter();
    } catch {
      // ignore center logo rendering errors
    }

    //
    this.paintArrow();
  }

  paintArrow() {
    // Load the texture from the base64 data
    const texture = Texture.from(ArrowBase64Image);
    // Create a sprite using the texture
    this.arrow = new Sprite(texture);
    this.arrow.scale.set(0.5);
    this.arrow.anchor.set(0.5, 0.2);
    this.arrow.position.set(
      this.center.x,
      this.center.y - this.WOF.WheelRadius - 5 // Di chuyển arrow lên trên cao hơn (từ -5 thành -20)
    );
    this.container.addChild(this.arrow);
  }

  paintLogoAtCenter() {
    const Rc = this.WOF.CenterRadius;
    ////
    let name = this.WOF.Metadata.logoCenter;
    let centerColor = 0xffffff; // Default white color

    // Check if logoCenter is a color hex string (e.g., "#FF0000", "FF0000", "red")
    if (name) {
      // If it's a hex color (starts with # or is 6-8 character hex)
      if (
        typeof name === "string" &&
        (name.startsWith("#") || /^[0-9A-Fa-f]{6,8}$/.test(name))
      ) {
        // Convert hex to number (remove # if present)
        const hexColor = name.startsWith("#") ? name.substring(1) : name;
        centerColor = parseInt(hexColor, 16);
        console.log(`Using color: ${name} -> 0x${hexColor}`);
      } else {
        // Check if it's an image texture
        let texture = this.listOfAsset["logoCenter"];
        if (texture) {
          let cx = this.containerSize / 2;
          let cy = this.containerSize / 2;
          let logo = PIXI.Sprite.from(texture);
          if (logo.width > 1) {
            logo.anchor.set(0.5, 0.5);
            console.log(`logo.width = ${logo.width}`);
            //// tính toán tỉ lệ scale theo kích thước hình.
            const lz = this.WOF.LogoSize || 60;
            let scaleFactor = lz / logo.width;
            logo.scale.set(scaleFactor);
            logo.position.set(cx, cy);
            if (this.mask) {
              logo.mask = this.mask;
            }
            //
            this.container.addChild(logo);
            //
            console.log(
              `scale factor = ${scaleFactor}, logo.width = ${logo.width}`
            );
            return; // Exit early if image is rendered
          }
        }
      }
    }

    //// draw circle center with the determined color
    let gBg = new Graphics();
    this.container.addChild(gBg);
    gBg.lineStyle(0);
    gBg.beginFill(centerColor);
    gBg.drawCircle(this.center.x, this.center.y, Rc);
    gBg.endFill();
    // Draw border of circle center with color #42b2f1
    let gBorder = new Graphics();
    this.container.addChild(gBorder);
    gBorder.lineStyle(6, 0x42b2f1);
    gBorder.drawCircle(this.center.x, this.center.y, Rc);
    // Draw larger upward arrow touching the border
    // let arrow = new Graphics();
    // this.container.addChild(arrow);
    // arrow.beginFill(0x42b2f1);
    // arrow.moveTo(this.center.x, this.center.y - Rc - 30);
    // arrow.lineTo(this.center.x - 10, this.center.y - Rc);
    // arrow.lineTo(this.center.x + 10, this.center.y - Rc);
    // arrow.lineTo(this.center.x, this.center.y - Rc - 30);
    // arrow.endFill();
  }

  onAssetsLoaded() {
    this.setupRender(this.app.renderer);
    this.setupScene();
    this.bindingEvents();

    if (!this.app.stage) {
      return;
    }

    const app = this.app;

    // Đợi 1 frame để đảm bảo GPU sẵn sàng
    requestAnimationFrame(() => {
      console.log("onAssetsLoaded", app);
      app?.start();

      // Đợi thêm 1 frame nữa để đảm bảo render hoàn tất
      requestAnimationFrame(() => {
        this.paint();
        this.resetAtIndex(0);

        // callback sau khi mọi thứ đã sẵn sàng
        if (this.onReadyEvent) {
          this.onReadyEvent();
        }
      });
    });
  }

  onClick(_event) {
    if (!this.flagEnableTouchToPlay) {
      return;
    }

    //// this.playAndStopForTesting();
  }

  update(_delta) {}

  enableTouchToPlay(value) {
    this.flagEnableTouchToPlay = value;
  }

  getPrize() {
    return this.disk.diskMap[this.index];
  }

  onCompleted(callback) {
    this.eventCallback = callback;
  }

  playAndStopForTesting() {
    this.playAndStopAt(this.index);
    let N = this.disk.diskMap.length || 1;

    setInterval(() => {
      this.index++;
      this.playAndStopAt(this.index % N);
    }, 1000 * 10);
  }

  resetAtIndex(index) {
    let part = this.disk.diskMap[index];
    // Đảm bảo rotation luôn nhất quán
    this.disk.rotation = Math.PI * 1.5 - part.median.radian;
    this.disk.filters = [];
    // Không reset blur ở đây, để playAndStopAt quản lý
    this.flagIsRunning = false;

    // Không force render ở đây, để PIXI tự quản lý
    // Chỉ force render khi thực sự cần thiết
  }

  playAndStopAt(index) {
    this.index = index;
    if (this.flagIsRunning) {
      console.log(`isRunning : ${this.flagIsRunning}, skip it`);
      return;
    }

    let part = this.disk.diskMap[index];
    //// console.log(part);
    const loop = 20;

    // Tính toán vị trí mục tiêu
    const targetRotation = Math.PI * 1.5 - part.median.radian;

    // Tính toán số vòng quay (để tạo hiệu ứng quay nhiều vòng)
    let playAround = loop * (Math.PI * 2) + targetRotation;

    // Đảm bảo wheel quay theo chiều thuận (clockwise)
    if (playAround < this.disk.rotation) {
      playAround += Math.PI * 2;
    }
    this.disk.filters = [this.blurFilter1];
    this.blurFilter1.blur = 25;
    // Tạo một TimelineMax để xây dựng chuỗi hiệu ứng
    const timeline = new gsap.timeline();

    // Không cần reset về 0, bắt đầu quay từ vị trí hiện tại
    // Điều này sẽ làm cho wheel quay mượt mà hơn và nhất quán

    // Tạo một TimelineMax con để thực hiện các hiệu ứng đồng thời
    const parallelTimeline = new gsap.timeline();
    // Tạo một TimelineMax con để thực hiện các hiệu ứng đồng thời
    const arrowTimeline = new gsap.timeline();

    parallelTimeline.to(
      this.disk,
      {
        rotation: playAround,
        duration: 5.0,
        yoyo: false,
        ease: "easeOutQuint",
      },
      0
    );

    //// hieu ung quay disk
    parallelTimeline.to(
      this.disk,
      {
        rotation: playAround,
        duration: 5.0,
        yoyo: false,
        ease: "easeOutQuint",
        onComplete: () => {
          arrowTimeline.kill();
          gsap.to(this.arrow, {
            rotation: 0,
            duration: 0.5,
            yoyo: false,
            ease: "easeOutQuint",
          });
        },
      },
      0
    );

    parallelTimeline.to(
      this.blurFilter1,
      {
        blur: 0,
        duration: 1.5,
        yoyo: false,
      },
      1
    );

    timeline.add(parallelTimeline);

    //// hieu ung quay mui ten
    arrowTimeline.fromTo(
      this.arrow,
      {
        rotation: 0,
      },
      {
        rotation: -Math.PI / 4,
        duration: 0.2,
        yoyo: true,
        repeat: -1,
        ease: "easeInCirc",
      },
      0
    );

    // Thiết lập callback khi timeline hoàn thành
    timeline.eventCallback("onComplete", () => {
      console.log("Wheel is done.");
      this.disk.filters = [];
      this.flagIsRunning = false;

      // Đảm bảo wheel dừng đúng vị trí mục tiêu
      const finalRotation = Math.PI * 1.5 - part.median.radian;
      this.disk.rotation = finalRotation;

      console.log(
        `Wheel stopped at index ${index}, rotation: ${finalRotation}`
      );

      // Force render để đảm bảo vị trí chính xác
      if (this.app && this.app.renderer) {
        this.app.renderer.render(this.app.stage);
      }

      const cb = this.eventCallback;
      if (cb) {
        setTimeout(() => {
          try {
            cb();
          } catch (err) {
            console.log(err);
          }
        }, 100);
      }
    });

    // Bắt đầu hoạt hình
    this.flagIsRunning = true;
    timeline.play();
    arrowTimeline.play();
  }

  /**
   * min and max included
   * @param min
   * @param max
   */
  randomBetween(min, max) {
    return Math.floor(Math.random() * (max - min + 1) + min);
  }

  randomIndex() {
    if (this.disk && this.disk.diskMap) {
      return this.randomBetween(0, this.disk.diskMap.length - 1);
    }
    return 0;
  }

  resoleURLWithCDN(url) {
    if (this.WOF.Metadata.urlCDN && this.WOF.Metadata.urlCDN !== "") {
      if (url && !url.startsWith("http")) {
        return `${this.WOF.Metadata.urlCDN}/${url}`;
      }
    }

    return url;
  }

  async queueRender() {
    if (this.isRendering) {
      this.renderQueue.push(() => this.paint());
      return;
    }

    this.isRendering = true;
    await new Promise((resolve) => requestAnimationFrame(resolve));
    this.paint();
    this.isRendering = false;

    // Process queue
    if (this.renderQueue.length > 0) {
      const nextRender = this.renderQueue.shift();
      if (nextRender) nextRender();
    }
  }
}
