import * as PIXI from "pixi.js";
const { Container, Graphics, Point, Text } = PIXI;

const StyleTitle = new PIXI.TextStyle({
  fontFamily: "RoyceLavie",
  fontSize: 14,
  fontWeight: "Bold",
  fill: "#F65D79", // Changed from #50B9F2 to primaryPink
  align: "center",
  antialias: true,
  // stroke: '#795349',
  // strokeThickness: 1,
  // dropShadow: true,
  // dropShadowColor: '#795349',
  // dropShadowBlur: 1,
  // dropShadowAngle: Math.PI / 6,
  // dropShadowDistance: 6,
  wordWrap: true,
  wordWrapWidth: 50,
  lineJoin: "round",
});

export class Disk extends Container {
  constructor(wofConfig, bundle) {
    super();
    this.bundle = bundle;
    this.graphics = new Graphics();
    this.WOF = wofConfig;
    this.addChild(this.graphics);
    // set pivot (tam quay)
    this.pivot.x = this.WOF.WheelRadiusInside;
    this.pivot.y = this.WOF.WheelRadiusInside;
    const cx = this.WOF.WheelRadiusInside;
    const cy = this.WOF.WheelRadiusInside;
    this.center = new Point(cx, cy);
    //
    this.setup(wofConfig.Metadata.parts || []);
  }

  setup(listGift) {
    let N = listGift.length || 1;
    // Đặt màu cố định: trắng và xanh
    let colors = [0xfef5bf, 0xf7cd14, 0xffffff];
    const angle = (2 * Math.PI) / N;
    const angle2 = angle / 2;
    const radius = this.WOF.WheelRadiusInside;

    this.diskMap = listGift.map((element, i) => {
      // Tính tọa độ (x, y) trên đường tròn
      let medianRadian = (i + 1) * angle - angle2;
      const x = radius * Math.cos(medianRadian);
      const y = radius * Math.sin(medianRadian);

      const colorIndex = i % 3;

      return {
        index: i,
        name: element.name || `item ${i}`,
        radius: radius,
        color: colors[colorIndex],
        angleInRadian: (2 * Math.PI) / N,
        fromAngle: angle * i,
        toAngle: (i + 1) * angle,
        median: { radian: medianRadian, x: x, y: y },
      };
    });

    // Đảm bảo màu cạnh nhau không trùng nhau
    if (N > 3) {
      if (this.diskMap[0].color === this.diskMap[N - 1].color) {
        this.diskMap[N - 1].color = colors[(N - 1 + 1) % 2];
      }
    }
  }

  paint() {
    const Ri = this.WOF.WheelRadiusInside;
    this.graphics.beginFill(0xffffff, 0);
    this.graphics.drawCircle(this.center.x, this.center.y, Ri);
    this.graphics.endFill();
    //// draw pecies
    for (const part of this.diskMap) {
      this.paintArc(part);
    }

    this.paintDots();
  }

  paintArc(part) {
    const { fromAngle, toAngle, radius, color } = part;
    //// draw a wing of WHEEL
    this.graphics.lineStyle(1, 0xb7cbdc);
    this.graphics.beginFill(color);
    this.graphics.moveTo(this.center.x, this.center.y);
    this.graphics.arc(this.center.x, this.center.y, radius, fromAngle, toAngle);
    this.graphics.lineTo(this.center.x, this.center.y);
    this.graphics.endFill();
    //// draw medianLine
    //// this.graphics.lineStyle(5, 0xffff, 1);
    //// this.graphics.moveTo(this.center.x, this.center.y);
    //// this.graphics.lineTo(this.center.x + median.x, this.center.y + median.y);

    this.paintIcon(part);
  }

  paintIcon(part) {
    //// draw icon
    const { median, name } = part;
    const angleInRadian = part.angleInRadian;

    // Position for icon - place it closer to center (was at 80%, now at 40-50%)
    const r = this.WOF.WheelRadiusInside * 0.45; // Position at 45% of radius (closer to center)
    const x = r * Math.cos(median.radian);
    const y = r * Math.sin(median.radian);

    let texture = this.bundle[name];
    if (texture) {
      try {
        let icon = PIXI.Sprite.from(texture);
        if (icon && icon.width > 1) {
          icon.anchor.set(0.5, 0.5);

          // Calculate maximum width available in the sector at this radius
          const sectorWidthAtRadius = 2 * r * Math.sin(angleInRadian / 2) * 0.5; // 50% of max width for padding (reduced from 70%)

          // Calculate scale factor based on available width
          let scaleFactor = Math.min(
            sectorWidthAtRadius / icon.width,
            (this.WOF.WheelRadiusInside * 0.15) / icon.height // Limit height to 15% of radius (reduced from 20%)
          );

          if (name.length < 20) {
            scaleFactor *= 1.2; // giảm từ 1.5 xuống 1.2 để image nhỏ hơn
          }

          icon.scale.set(scaleFactor);
          icon.rotation = median.radian + Math.PI / 2; // Align with sector
          icon.position.set(this.center.x + x, this.center.y + y);
          this.addChild(icon);

          // Add text further from center (was at 40-50%, now at 80%)
          this.renderText(part, name, icon.height * scaleFactor, true);
        }
      } catch (err) {
        console.error("Error rendering icon:", err);
        this.renderText(part, name, 0, false);
      }
    } else {
      this.renderText(part, name, 0, false);
    }
  }

  renderText(part, name, iconHeight, hasIcon) {
    const { median } = part;

    let textDistance;

    if (hasIcon) {
      // Text is now further from center (at 80% radius)
      textDistance = this.WOF.WheelRadiusInside * 0.8;

      if (textDistance < this.WOF.WheelRadiusInside * 0.75) {
        StyleTitle.fontSize = Math.max(10, StyleTitle.fontSize - 2);
      }
    } else {
      textDistance = this.WOF.WheelRadiusInside * 0.8;
    }

    if (hasIcon) {
      if (name.length < 30) {
        textDistance = this.WOF.WheelRadiusInside * 0.8; // Keep text at 80% for consistency
      } else {
        textDistance = Math.max(
          textDistance,
          this.WOF.WheelRadiusInside * 0.8 // Keep text at 80% for consistency
        );
      }
    }

    const x = textDistance * Math.cos(median.radian);
    const y = textDistance * Math.sin(median.radian);

    const text = new Text(name, StyleTitle);
    text.anchor.set(0.5, 0.5);

    const sectorAngle = part.angleInRadian;
    const availableWidth = 2 * textDistance * Math.sin(sectorAngle / 2) * 1.2;

    if (text.width > availableWidth) {
      const scaleFactor = availableWidth / text.width;
      text.scale.set(scaleFactor);
    }

    text.position.set(this.center.x + x, this.center.y + y);
    text.rotation = median.radian + Math.PI / 2;

    this.addChild(text);
  }

  paintDots() {
    let distance = this.WOF.WheelRadius - this.WOF.WheelBorderWidth / 2;
    let R = this.WOF.WheelBorderWidth / 2 - 3;

    for (const part of this.diskMap) {
      this.graphics.lineStyle(0);
      // const dotColor = part.color === 0xffffff ? 0x41b4f1 : 0xffffff
      const dotColor = 0x79b4d8;
      // ;
      this.graphics.beginFill(dotColor);

      let x = this.center.x + distance * Math.cos(part.fromAngle);
      let y = this.center.y + distance * Math.sin(part.fromAngle);

      // let x =
      //   this.center.x +
      //   distance * Math.cos(part.fromAngle + part.angleInRadian / 2);
      // let y =
      //   this.center.y +
      //   distance * Math.sin(part.fromAngle + part.angleInRadian / 2);

      this.graphics.drawCircle(x, y, R);
      this.graphics.endFill();
    }
  }
}
