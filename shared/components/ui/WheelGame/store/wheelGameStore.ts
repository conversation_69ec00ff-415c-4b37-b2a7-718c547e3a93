import { WheelDetail } from "../../../../services/api/entertainmentService";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface WheelGameState {
  // Add Spin Sheet State
  spinCount: number;
  currentVui: number;
  currentPepsiPoints: number;
  vuiPerSpin: number;
  pepsiPointsPerSpin: number;
  wheelDetail: WheelDetail | null;

  // Actions
  setSpinCount: (count: number) => void;
  increaseSpinCount: () => void;
  decreaseSpinCount: () => void;
  resetSpinCount: () => void;
  setInitialValues: (
    currentVui: number,
    currentPepsiPoints: number,
    vuiPerSpin?: number,
    pepsiPointsPerSpin?: number
  ) => void;
  setWheelDetail: (wheelDetail: WheelDetail) => void;
  // Computed values
  getTotalVuiNeeded: () => number;
  getTotalPepsiPointsNeeded: () => number;
  getAdditionalVuiNeeded: () => number;
  getAdditionalPepsiPointsNeeded: () => number;
  getCanConfirm: () => boolean;
}

export const useWheelGameStore = create<WheelGameState>()(
  devtools(
    (set, get) => ({
      // Initial state
      spinCount: 1,
      currentVui: 10,
      currentPepsiPoints: 30,
      vuiPerSpin: 2,
      pepsiPointsPerSpin: 1,
      wheelDetail: null,

      setWheelDetail: (wheelDetail: WheelDetail) => set({ wheelDetail }),

      // Actions
      setSpinCount: (count: number) => set({ spinCount: Math.max(1, count) }),

      increaseSpinCount: () =>
        set((state) => ({
          spinCount: state.spinCount + 1,
        })),

      decreaseSpinCount: () =>
        set((state) => ({
          spinCount: Math.max(1, state.spinCount - 1),
        })),

      resetSpinCount: () => set({ spinCount: 1 }),

      setInitialValues: (
        currentVui: number,
        currentPepsiPoints: number,
        vuiPerSpin: number = 2,
        pepsiPointsPerSpin: number = 1
      ) =>
        set({ currentVui, currentPepsiPoints, vuiPerSpin, pepsiPointsPerSpin }),

      // Computed values (getters)
      getTotalVuiNeeded: () => {
        const { spinCount, vuiPerSpin } = get();
        return spinCount * vuiPerSpin;
      },

      getTotalPepsiPointsNeeded: () => {
        const { spinCount, pepsiPointsPerSpin } = get();
        return spinCount * pepsiPointsPerSpin;
      },

      getAdditionalVuiNeeded: () => {
        const { currentVui } = get();
        const totalVui = get().getTotalVuiNeeded();
        return Math.max(0, totalVui - currentVui);
      },

      getAdditionalPepsiPointsNeeded: () => {
        const { currentPepsiPoints } = get();
        const totalPepsi = get().getTotalPepsiPointsNeeded();
        return Math.max(0, totalPepsi - currentPepsiPoints);
      },

      getCanConfirm: () => {
        const additionalVui = get().getAdditionalVuiNeeded();
        const additionalPepsi = get().getAdditionalPepsiPointsNeeded();
        return additionalVui === 0 && additionalPepsi === 0;
      },
    }),
    {
      name: "wheel-game-store",
    }
  )
);
