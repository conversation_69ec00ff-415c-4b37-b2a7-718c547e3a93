import React from 'react';
import type { Challenge } from '../../../hooks/useChallengeList';

export interface ChallengeCardProps {
  challenge: Challenge;
  onClick: (challengeId: string) => void;
  className?: string;
}

const ChallengeCard: React.FC<ChallengeCardProps> = ({ 
  challenge,
  onClick,
  className = ""
}) => {
  return (
    <button
      onClick={() => onClick(challenge._id)}
      className={`w-full bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 text-left border border-blue-100 ${className}`}
    >
      <div className="flex items-center space-x-3">
        {challenge.challengeImage && (
          <img
            src={challenge.challengeImage}
            alt={challenge.displayChallengeName}
            className="w-16 h-16 rounded-lg object-cover"
          />
        )}
        <div className="flex-1">
          <h3 className="font-medium text-sm text-gray-900 mb-1">
            {challenge.displayChallengeName}
          </h3>
          {challenge.userProgress && (
            <div>
              <p className="text-xs text-blue-600 mb-2">
                {challenge.userProgress.currentProgress}/{challenge.userProgress.maxProgress}
              </p>
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <div
                  className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                  style={{
                    width: `${challenge.userProgress.percentProgress || 0}%`,
                  }}
                />
              </div>
            </div>
          )}
        </div>
        <div className="text-blue-500">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      </div>
    </button>
  );
};

export default ChallengeCard;