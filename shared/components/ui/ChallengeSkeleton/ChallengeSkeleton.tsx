import React from 'react';

export interface ChallengeSkeletonProps {
  count?: number;
  className?: string;
}

const ChallengeSkeleton: React.FC<ChallengeSkeletonProps> = ({ 
  count = 3,
  className = ""
}) => {
  return (
    <div className={`p-4 space-y-2.5 ${className}`}>
      {Array.from({ length: count }, (_, i) => (
        <div key={i} className="animate-pulse">
          <div className="bg-gray-200 rounded-lg h-32 w-full"></div>
        </div>
      ))}
    </div>
  );
};

export default ChallengeSkeleton;