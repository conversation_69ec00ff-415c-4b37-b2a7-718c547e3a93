import React from 'react';
import { Button } from '../Button';
import illustrationSrc from '../../../assets/images/data-hub/entrance-illustration-4f3f27.png';

export interface ZaloDataHubEntranceProps {
  onLinkPhone?: () => void;
  onDecline?: () => void;
}

/**
 * Zalo Data Hub Entrance Component
 * Login/phone linking screen for Zalo Mini App Data Hub feature
 * Features gradient background with stars, illustration, and call-to-action buttons
 */
export const ZaloDataHubEntrance: React.FC<ZaloDataHubEntranceProps> = ({
  onLinkPhone,
  onDecline,
}) => {
  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Pink gradient background with stars */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#FBB9C5] to-[#FBB9C5]">
        {/* Star decorations */}
        <div 
          className="absolute w-[303px] h-[303px] opacity-60"
          style={{
            left: '-163px',
            top: '42px',
            background: 'linear-gradient(182deg, rgba(254, 239, 242, 1) 0%, rgba(254, 239, 242, 0) 79%)',
            borderRadius: '16.45px',
            filter: 'drop-shadow(0px 0px 56.4px rgba(255, 140, 163, 0.4))',
            clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',
          }}
        />
        <div 
          className="absolute w-[367px] h-[367px]"
          style={{
            right: '-184px',
            top: '-106px',
            background: 'linear-gradient(182deg, rgba(254, 239, 242, 1) 0%, rgba(254, 239, 242, 0) 79%)',
            borderRadius: '16.45px',
            filter: 'drop-shadow(0px 0px 56.4px rgba(255, 140, 163, 0.4))',
            clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 flex flex-col min-h-screen">
        {/* Main content area */}
        <div className="flex-1 flex flex-col items-center justify-center px-10 pt-24 pb-8">
          {/* Illustration container */}
          <div className="mb-6">
            <div className="w-[104px] h-[104px] relative">
              <img 
                src={illustrationSrc} 
                alt="Phone illustration" 
                className="w-full h-full object-contain"
              />
            </div>
          </div>

          {/* Content section */}
          <div className="flex flex-col items-center gap-6 w-full max-w-[295px]">
            {/* Text content */}
            <div className="flex flex-col items-center gap-2 text-center">
              {/* Main title */}
              <h1 className="font-archia font-bold text-[24px] leading-[32px] text-[#1A1818]">
                Bạn cần đăng nhập{'\n'}để trải nghiệm tính năng
              </h1>

              {/* Subtitle */}
              <p className="font-archia font-normal text-[14px] leading-[22px] text-[#1A1818] w-full">
                Số điện thoại của bạn sẽ được sử dụng để:
              </p>

              {/* Features list */}
              <div className="font-archia font-normal text-[14px] leading-[22px] text-[#1A1818] w-full text-left">
                <p>Tích điểm ở thương hiệu liên kết</p>
                <p>Đổi điểm để nhận ưu đãi</p>
                <p>Quản lý điểm và ưu đãi</p>
              </div>

              {/* Bottom message */}
              <p className="font-archia font-normal text-[14px] leading-[22px] text-[#1A1818] w-full">
                Bạn vui lòng đồng ý chia sẻ số điện thoại để tiếp tục sử dụng ứng dụng nhé!
              </p>
            </div>

            {/* Action buttons */}
            <div className="flex flex-col items-center gap-4 w-full">
              <Button
                variant="primary"
                size="large"
                fullWidth
                onClick={onLinkPhone}
                className="h-11 text-[14px] font-semibold rounded-lg"
              >
                Liên kết số điện thoại
              </Button>
              
              <Button
                variant="plain"
                size="large"
                onClick={onDecline}
                className="h-auto p-0 text-[14px] font-semibold text-primary-pink"
              >
                Từ chối
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZaloDataHubEntrance;