import React, { CSSProperties, FC, ReactElement } from "react";

type BrandCurrencyType = {
  name?: string;
};

export interface EarnRateProps {
  rate: number;
  description?: string;
  isUsingBC?: boolean;
  balanceData?: BrandCurrencyType[];
  earnValue?: number;
  baseValue?: number;
  iconUrl?: string;
  className?: string;
  style?: CSSProperties;
  contentStyle?: CSSProperties;
}

const containerStyle: CSSProperties = {
  padding: 8,
  backgroundColor: "#FFF8E1",
  borderRadius: 8,
};

const contentTextStyle: CSSProperties = {
  fontSize: 14,
  lineHeight: "20px",
  color: "#1A1A1A",
  margin: 0,
};

const subContentTextStyle: CSSProperties = {
  color: "#666666",
};

const rateContainerStyle: CSSProperties = {
  display: "flex",
  alignItems: "center",
  gap: 4,
};

function formatCurrencyEarnRate(value: number): string {
  if (Number.isNaN(value)) return "0";
  try {
    return new Intl.NumberFormat(undefined, {
      maximumFractionDigits: 2,
    }).format(value);
  } catch {
    return String(value);
  }
}

const EarnRate: FC<EarnRateProps> = ({
  rate,
  description,
  isUsingBC = false,
  balanceData,
  earnValue,
  baseValue,
  iconUrl,
  className,
  style,
  contentStyle,
}): ReactElement => {
  console.log(
    "balanceData",
    rate,
    description,
    isUsingBC,
    earnValue,
    baseValue,
    balanceData
  );

  const renderContent = (): ReactElement => {
    if (rate !== 0) {
      if (isUsingBC) {
        const bcName = balanceData?.[0]?.name ?? "điểm";
        return (
          <p style={{ ...contentTextStyle, ...contentStyle }}>
            Tích VUI và {bcName}
          </p>
        );
      }

      return (
        <div style={rateContainerStyle}>
          <p style={{ ...contentTextStyle, ...contentStyle }}>
            <span>
              Cùng tích điểm với:{" "}
              <span style={subContentTextStyle}>
                {formatCurrencyEarnRate(earnValue || 0)} {" = "}{" "}
                {baseValue || 1}
              </span>
            </span>
          </p>
          {iconUrl ? (
            <img
              src={iconUrl}
              alt="earn-icon"
              width={24}
              height={24}
              style={{ display: "inline-block", borderRadius: "50%" }}
            />
          ) : null}
        </div>
      );
    }

    if (description) {
      return (
        <div
          style={{ ...contentTextStyle, ...contentStyle }}
          // NOTE: Ensure the HTML provided in `description` is sanitized upstream if it may contain user content
          dangerouslySetInnerHTML={{ __html: description }}
        />
      );
    }

    return (
      <p style={{ ...contentTextStyle, ...contentStyle }}>Chưa tích VUI</p>
    );
  };

  return (
    <div className={className} style={{ ...containerStyle, ...style }}>
      {renderContent()}
    </div>
  );
};

export default EarnRate;
