import React, { useMemo, ReactNode } from 'react';

// Types matching mobile implementation
export interface IMyRewardExpiryData {
  redeemStartCalculationType?: number;
  redeemExpireCalculationType?: number;
}

export enum RewardRedeemStartTypeEnum {
  RIGHT_IMMEDIATELY_AFTER_ISSUAL = 1,
  SPECIFIC_DATE = 2,
}

export enum RewardRedeemExpireTypeEnum {
  AFTER_X_HOURS_FROM_ISSUAL = 1,
  AFTER_X_MINUTES_FROM_ISSUAL = 2,
  SPECIFIC_DATE = 3,
  AFTER_X_DAYS_FROM_ISSUAL = 4,
  AFTER_X_MONTHS_FROM_ISSUAL = 5,
}

export interface IPropsSectionTimer {
  redeemPerUser: number;
  remainRedeemCount: number;
  startTime?: string;
  endTime?: string;
  settingRedeemData?: string;
}

interface SectionTimerRewardProps {
  expiryData: IPropsSectionTimer;
  className?: string;
}

const SectionTimerReward: React.FC<SectionTimerRewardProps> = ({
  expiryData,
  className = '',
}) => {
  const {
    redeemPerUser,
    remainRedeemCount,
    startTime,
    endTime,
    settingRedeemData,
  } = expiryData || {};

  // Parse voucher data from encrypted string (simplified for web - no crypto)
  const voucherData = useMemo((): IMyRewardExpiryData | null => {
    let obj: IMyRewardExpiryData = {};
    try {
      if (settingRedeemData && typeof settingRedeemData === 'string' && settingRedeemData.length > 0) {
        // In web version, assume settingRedeemData is already parsed or handle differently
        // For now, we'll use a simplified approach
        obj = JSON.parse(settingRedeemData);
      }
    } catch (_) {
      // Silent fail like mobile version
    }
    return obj;
  }, [settingRedeemData]);

  // Date formatting functions matching mobile
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    } catch {
      return dateString;
    }
  };

  const formatDateTime = (dateString?: string): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${day}/${month}/${year} • ${hours}:${minutes}`;
    } catch {
      return dateString;
    }
  };

  const isBeforeStartTime = startTime ? new Date() < new Date(startTime) : false;
  const startDate = formatDate(startTime);
  const endDate = formatDate(endTime);
  const startDateHour = formatDateTime(startTime);
  const endDateHour = formatDateTime(endTime);
  
  // Check if time is 23:59:59 to hide hour display
  const hideHour = endTime ? new Date(endTime).toTimeString().startsWith('23:59:59') : false;

  // Render highlight note with color styling
  const renderHighlightNote = (
    highlight: string,
    color: string = '#FF8C00' // orange color
  ): ReactNode => {
    return (
      <span className="font-semibold" style={{ color }}>
        {highlight}
      </span>
    );
  };

  // Format string with highlight similar to l10n.formatString
  const formatString = (template: string, replacements: { [key: string]: ReactNode }) => {
    const result: ReactNode[] = [];
    let lastIndex = 0;
    
    // Simple implementation - in real app you'd use a proper i18n library
    Object.entries(replacements).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      const index = template.indexOf(placeholder);
      if (index !== -1) {
        // Add text before placeholder
        if (index > lastIndex) {
          result.push(template.substring(lastIndex, index));
        }
        // Add replacement value
        result.push(value);
        lastIndex = index + placeholder.length;
      }
    });
    
    // Add remaining text
    if (lastIndex < template.length) {
      result.push(template.substring(lastIndex));
    }
    
    return result.length === 0 ? template : result;
  };

  const getNote = (): ReactNode => {
    if (Math.abs(redeemPerUser) === 1) {
      return isBeforeStartTime
        ? formatString('Có thể sử dụng từ {start} đến {end}', {
            start: renderHighlightNote(startDate),
            end: renderHighlightNote(endDate),
          })
        : formatString('Có hiệu lực đến {date}', {
            date: renderHighlightNote(endDate),
          });
    }

    return isBeforeStartTime
      ? formatString('Còn {remain} lần sử dụng từ {start}', {
          remain: renderHighlightNote(`${remainRedeemCount}/${redeemPerUser}`, '#0DC98B'), // green color
          start: renderHighlightNote(startDate),
        })
      : formatString('Còn {remain} lần sử dụng đến {end}', {
          remain: renderHighlightNote(`${remainRedeemCount}/${redeemPerUser}`, '#0DC98B'), // green color
          end: renderHighlightNote(endDate),
        });
  };

  const getTime = (): ReactNode => {
    let time: ReactNode = '';

    if (
      voucherData?.redeemStartCalculationType ===
      RewardRedeemStartTypeEnum.RIGHT_IMMEDIATELY_AFTER_ISSUAL
    ) {
      switch (voucherData?.redeemExpireCalculationType) {
        case RewardRedeemExpireTypeEnum.AFTER_X_HOURS_FROM_ISSUAL:
        case RewardRedeemExpireTypeEnum.AFTER_X_MINUTES_FROM_ISSUAL:
          if (Math.abs(redeemPerUser) === 1) {
            time = formatString('Có hiệu lực đến {date}', {
              date: renderHighlightNote(hideHour ? endDate : endDateHour),
            });
          } else {
            time = formatString('Còn {remain} lần sử dụng đến {end}', {
              remain: renderHighlightNote(`${remainRedeemCount}/${redeemPerUser}`, '#0DC98B'),
              end: renderHighlightNote(hideHour ? endDate : endDateHour),
            });
          }
          break;

        case RewardRedeemExpireTypeEnum.SPECIFIC_DATE:
        case RewardRedeemExpireTypeEnum.AFTER_X_DAYS_FROM_ISSUAL:
        case RewardRedeemExpireTypeEnum.AFTER_X_MONTHS_FROM_ISSUAL:
          time = getNote();
          break;

        default:
          time = getNote();
          break;
      }
    } else if (
      voucherData?.redeemStartCalculationType ===
      RewardRedeemStartTypeEnum.SPECIFIC_DATE
    ) {
      switch (voucherData?.redeemExpireCalculationType) {
        case RewardRedeemExpireTypeEnum.SPECIFIC_DATE:
        case RewardRedeemExpireTypeEnum.AFTER_X_DAYS_FROM_ISSUAL:
        case RewardRedeemExpireTypeEnum.AFTER_X_MONTHS_FROM_ISSUAL:
          if (Math.abs(redeemPerUser) === 1) {
            time = formatString('Có thể sử dụng từ {start} đến {end}', {
              start: renderHighlightNote(hideHour ? startDate : startDateHour),
              end: renderHighlightNote(hideHour ? endDate : endDateHour),
            });
          } else {
            time = isBeforeStartTime
              ? formatString('Còn {remain} lần sử dụng từ {start}', {
                  remain: renderHighlightNote(`${remainRedeemCount}/${redeemPerUser}`, '#0DC98B'),
                  start: renderHighlightNote(hideHour ? startDate : startDateHour),
                })
              : formatString('Còn {remain} lần sử dụng đến {end}', {
                  remain: renderHighlightNote(`${remainRedeemCount}/${redeemPerUser}`, '#0DC98B'),
                  end: renderHighlightNote(hideHour ? endDate : endDateHour),
                });
          }
          break;

        default:
          time = getNote();
          break;
      }
    } else {
      time = getNote();
    }

    return time;
  };

  return (
    <div className={`${className}`}>
      {/* Expiry Label with center alignment and gray color */}
      <div className="text-center text-gray-500 text-sm font-medium pb-4">
        <div>{getTime()}</div>
      </div>
      
      {/* Divider Line */}
      <div className="w-full">
        <svg width="100%" height="1" className="overflow-visible">
          <line x1="0" x2="100%" y1="0.5" y2="0.5" stroke="#E5E5E5" strokeWidth="1" />
        </svg>
      </div>
    </div>
  );
};

export default SectionTimerReward;