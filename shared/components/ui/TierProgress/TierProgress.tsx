import React from 'react';
import { membershipHelpers } from '../../../services/api/membership';

interface TierProgressProps {
  currentValue: number;
  tiers: Array<{
    value: number;
    tierName: string;
  }>;
  progressColor?: string;
  currencyLogo?: string;
  currencyName?: string;
  tierName: string;
  totalEarnNeeded: number;
  merchantName?: string;
  onActionClick?: () => void;
  actionText?: string;
}

export const TierProgress: React.FC<TierProgressProps> = ({
  currentValue,
  tiers,
  progressColor = '#4CAF50',
  currencyLogo,
  currencyName,
  tierName,
  totalEarnNeeded,
  merchantName,
  onActionClick,
  actionText = 'Sử dụng ưu đãi'
}) => {
  const progress = membershipHelpers.calculateProgress(currentValue, tiers);
  
  return (
    <div className="bg-white rounded-xl p-4 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{tierName}</h3>
          {merchantName && (
            <p className="text-sm text-gray-500">{merchantName}</p>
          )}
        </div>
        {onActionClick && (
          <button
            onClick={onActionClick}
            className="px-4 py-2 bg-primary text-white rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors"
          >
            {actionText}
          </button>
        )}
      </div>

      <div className="bg-gray-100 rounded-lg p-3 mb-3">
        <p className="text-sm text-gray-600 mb-2">
          Còn <span className="font-semibold text-gray-900">{totalEarnNeeded}</span>
          {currencyLogo && (
            <img 
              src={currencyLogo} 
              alt={currencyName} 
              className="inline-block w-4 h-4 ml-1 align-middle"
            />
          )}
          {currencyName && <span className="ml-1">{currencyName}</span>} để lên hạng
        </p>
        
        <div className="relative w-full bg-gray-200 rounded-full h-2 overflow-hidden">
          <div 
            className="absolute left-0 top-0 h-full rounded-full transition-all duration-300"
            style={{ 
              width: `${progress}%`,
              backgroundColor: progressColor 
            }}
          />
          {currencyLogo && progress > 0 && (
            <div 
              className="absolute top-1/2 -translate-y-1/2 w-5 h-5 bg-white rounded-full border-2 shadow-sm flex items-center justify-center"
              style={{ 
                left: `calc(${progress}% - 10px)`,
                borderColor: progressColor
              }}
            >
              <img 
                src={currencyLogo} 
                alt="" 
                className="w-3 h-3"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};