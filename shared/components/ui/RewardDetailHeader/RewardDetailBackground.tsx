import * as React from "react";

interface RewardDetailBackgroundProps {
  className?: string;
  fill?: string;
}

const RewardDetailBackground: React.FC<RewardDetailBackgroundProps> = ({ className, fill = "#ffffff" }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="100%"
    height="100%"
    fill="none"
    className={className}
    viewBox="0 0 343 191"
    preserveAspectRatio="none"
  >
    {/* Background fill */}
    <path
      fill={fill}
      fillRule="evenodd"
      d="M8 0h327c4.418 0 8 3.582 8 8v103c0 2.057-.882 3.905-2.262 5.514-1.382 1.611-3.246 2.961-5.15 4.004-5.117 2.801-8.588 8.235-8.588 14.482s3.471 11.681 8.588 14.482c1.904 1.043 3.768 2.393 5.15 4.004C342.118 155.095 343 156.943 343 159v24c0 4.418-3.582 8-8 8H8c-4.418 0-8-3.582-8-8v-24c0-2.057.882-3.905 2.262-5.514 1.382-1.611 3.246-2.961 5.15-4.004C13.53 146.681 17 141.247 17 135s-3.471-11.681-8.588-14.482c-1.904-1.043-3.768-2.393-5.15-4.004C1.882 114.905 1 113.057 1 111V8c0-4.418 3.582-8 8-8Z"
      clipRule="evenodd"
    />
    
    {/* Border outline */}
    <path
      stroke="#ECECEC"
      d="M8 .5h327a7.5 7.5 0 0 1 7.5 7.5v103c0 2.057-.882 3.905-2.262 5.514-1.382 1.611-3.246 2.961-5.15 4.004-5.117 2.801-8.588 8.235-8.588 14.482s3.471 11.681 8.588 14.482c1.904 1.043 3.768 2.393 5.15 4.004 1.38 1.609 2.262 3.457 2.262 5.514v24a7.5 7.5 0 0 1-7.5 7.5H8A7.5 7.5 0 0 1 .5 183v-24c0-2.057.882-3.905 2.262-5.514 1.382-1.611 3.246-2.961 5.15-4.004C13.03 146.681 16.5 141.247 16.5 135s-3.471-11.681-8.588-14.482c-1.904-1.043-3.768-2.393-5.15-4.004C1.382 114.905.5 113.057.5 111V8A7.5 7.5 0 0 1 8 .5Z"
    />
    
    {/* Dashed line */}
    <path stroke="#ECECEC" strokeDasharray="3 4" d="M24 134h295" />
  </svg>
);

export default RewardDetailBackground;