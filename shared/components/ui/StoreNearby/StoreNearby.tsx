import React, { useState, useEffect, useRef } from "react";
import { merchantAPI, OfflineStoreType } from "../../../services/api/merchant";

interface StoreNearbyProps {
  logo: string;
  merchantCode: string;
  numberOfStores: number;
  event: string;
  paramsTracking?: {
    merchant_code?: string;
    reward_name?: string;
    reward_point?: number;
    reward_brandCurrency_point?: number;
  };
  className?: string;
  onLocationInfo?: (message: string) => void;
}

// Skeleton Loading Component
const StoreNearbySkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-4 bg-gray-200 rounded animate-pulse" />
      ))}
      <div className="flex justify-between">
        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse" />
        <div className="h-4 bg-gray-200 rounded w-32 animate-pulse" />
      </div>
      <div className="h-11 bg-gray-200 rounded-lg animate-pulse mt-4" />
    </div>
  );
};

// Empty State Component
const EmptyStoreState: React.FC = () => {
  return (
    <div className="text-center py-8">
      {/* Mascot - simplified meditation pose */}
      <div className="w-32 h-20 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            className="text-gray-500"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
            />
            <path
              d="M8 14s1.5 2 4 2 4-2 4-2"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M9 9h.01M15 9h.01"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
      <p className="text-sm text-gray-600 leading-relaxed">
        Ở đây hơi yên ắng do chưa tìm thấy cửa hàng nào gần đây
      </p>
    </div>
  );
};

// Permission Request Component
const LocationPermissionRequest: React.FC<{
  onRequestPermission: () => void;
  status: string;
}> = ({ onRequestPermission, status }) => {
  const getMessage = () => {
    switch (status) {
      case "denied":
        return "Tìm cửa hàng gần bạn nhất";
      case "blocked":
        return "Bật vị trí trong phần Cài Đặt để bắt đầu tìm";
      default:
        return "Tìm cửa hàng gần bạn nhất";
    }
  };

  const getButtonText = () => {
    switch (status) {
      case "denied":
        return "Tìm ngay";
      case "blocked":
        return "Vào cài đặt ngay";
      default:
        return "Tìm ngay";
    }
  };

  return (
    <div className="text-center py-8">
      {/* Rich Mascot - simplified */}
      <div className="w-32 h-20 mx-auto mb-4 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-lg flex items-center justify-center">
        <div className="w-16 h-16 bg-yellow-300 rounded-full flex items-center justify-center">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            className="text-yellow-700"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
            />
            <path
              d="M8 14s1.5 2 4 2 4-2 4-2"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M9 9h.01M15 9h.01"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>

      <div className="mb-6">
        <p className="text-sm text-gray-700 text-center leading-relaxed">
          {getMessage()}
        </p>
      </div>

      <button
        onClick={onRequestPermission}
        className="px-6 py-2 bg-[#F65D79] text-white text-sm font-medium rounded-lg hover:bg-[#e54d6a] transition-colors"
      >
        {getButtonText()}
      </button>
    </div>
  );
};

const StoreNearby: React.FC<StoreNearbyProps> = ({
  logo,
  merchantCode,
  numberOfStores,
  event: _event,
  paramsTracking,
  className = "",
  onLocationInfo,
}) => {
  const [permissionStatus, setPermissionStatus] = useState<string>("unknown");
  const [nearestStore, setNearestStore] = useState<OfflineStoreType | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [locationError, setLocationError] = useState(false);
  const skipUpdateRef = useRef(false);

  console.log("permissionStatus", permissionStatus);

  // Check geolocation permission
  const checkPermission = async () => {
    console.log("checkPermission", navigator.geolocation);
    if (!navigator.geolocation) {
      setPermissionStatus("unavailable");
      return;
    }

    try {
      // Try to get permission status if available
      const result = await navigator.permissions?.query({
        name: "geolocation" as PermissionName,
      });
      console.log("result", result);
      setPermissionStatus(result.state);

      if (result.state === "granted") {
        getCurrentPosition();
      }
    } catch {
      // Fallback: assume denied initially
      setPermissionStatus("prompt");
    }
  };

  // Fetch nearby stores
  const fetchStoreList = async (latitude: number, longitude: number) => {
    try {
      // Mock API call - in real implementation, call actual API
      console.log("Fetching stores near:", {
        latitude,
        longitude,
        merchantCode,
      });

      const response = await merchantAPI.getMerchantStores({
        action: "NEAR_BY",
        merchantCode,
        latitude,
        longitude,
        offset: 0,
        radius: 9999000,
        limit: 10,
      });
      console.log("response", response);
      if (response.status.code === 200) {
        setNearestStore(response.data[0]);
      }

      console.log("response", response);
    } catch (error) {
      console.error("fetchStoreList error", error);
    } finally {
      setLoading(false);
    }
  };

  // Get current position
  const getCurrentPosition = async () => {
    console.log("getCurrentPosition", permissionStatus);
    const result = await navigator.permissions?.query({
      name: "geolocation" as PermissionName,
    });

    setPermissionStatus(result.state);

    const isGranted = result.state === "granted";
    const isPrompt = result.state === "prompt";

    if (isGranted || isPrompt) {
      setLoading(true);
      setLocationError(false);

      navigator.geolocation.getCurrentPosition(
        (position) => {
          setPermissionStatus("granted");
          const { latitude, longitude } = position.coords;
          fetchStoreList(latitude, longitude);
        },
        (error) => {
          console.error("Geolocation error:", error);
          setLocationError(true);
          setLoading(false);

          // Handle different error codes
          if (error.code === 2) {
            // POSITION_UNAVAILABLE
            setLocationError(true);
          }
        },
        {
          timeout: 10000,
          maximumAge: 10000,
          enableHighAccuracy: true,
        }
      );
    }
  };

  // Track events
  const trackEvent = (action: string) => {
    console.log("Store nearby tracking:", {
      action,
      merchant_code: merchantCode,
      ...paramsTracking,
    });
  };

  // Handle permission request
  const handleRequestPermission = async () => {
    if (permissionStatus === "blocked") {
      trackEvent("goToSetting");
      // For web, we can't directly open settings, but we can show instructions
      const message =
        "Vui lòng vào Settings > Privacy & Security > Location để bật quyền truy cập vị trí cho trang web này.";
      if (onLocationInfo) {
        onLocationInfo(message);
      } else {
        alert(message);
      }
      return;
    }

    try {
      trackEvent("searchNow");
      return getCurrentPosition();
      // Request permission
      const permission = await navigator.permissions?.query({
        name: "geolocation" as PermissionName,
      });

      console.log("permission", permission);

      if (permission) {
        setPermissionStatus(permission.state);

        if (permission.state === "granted") {
          getCurrentPosition();
        }
      } else {
        // Fallback: directly request geolocation
        getCurrentPosition();
      }

      // const response = await merchantAPI.getMerchantStores({
      //   action: "NEAR_BY",
      //   merchantCode,
      //   latitude: 10.823029,
      //   longitude: 106.629699,
      //   offset: 0,
      //   radius: 9999000,
      //   limit: 10,
      // });

      // setNearestStore(response.data[0]);

      // console.log("response", response, response.data[0]);
    } catch (error) {
      console.error("Permission request error:", error);
    }
  };

  // Format distance
  const formatDistance = (distance: number): string => {
    if (distance < 1) {
      return `${(distance * 1000).toFixed(0)}m`;
    }
    return `${distance.toFixed(1)}km`;
  };

  // Handle phone call
  const handleCall = (phone: string) => {
    trackEvent("phone");
    window.location.href = `tel:${phone}`;
  };

  // Handle show map
  const handleShowMap = (lat: number, lng: number) => {
    trackEvent("viewMap");
    const mapUrl = `https://www.google.com/maps?q=${lat},${lng}`;
    window.open(mapUrl, "_blank");
  };

  // Handle view all stores
  const handleViewAllStores = () => {
    trackEvent("viewAll");
    try {
      // Prefer SPA navigation if on web app
      const appOrigin = window.location.origin;
      const path = `/merchants/${merchantCode}/stores`;
      if (window?.history?.pushState) {
        window.history.pushState({}, "", path);
        // Trigger navigation by dispatching a popstate for React Router to update if needed
        window.dispatchEvent(new PopStateEvent("popstate"));
      } else {
        window.location.href = `${appOrigin}${path}`;
      }
    } catch {
      console.log("Navigate to store list:", { merchantCode, logo });
    }
  };

  useEffect(() => {
    checkPermission();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (permissionStatus === "granted" && !skipUpdateRef.current) {
      getCurrentPosition();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [permissionStatus]);

  // Render nearest store
  const renderNearestStore = () => {
    console.log("nearestStore", nearestStore);
    if (loading) return <StoreNearbySkeleton />;
    if (!nearestStore) return <EmptyStoreState />;

    return (
      <>
        {/* Store Info */}
        <div className="flex items-start justify-between mb-2">
          <h4 className="text-base font-medium text-gray-900 flex-1 mr-2">
            {nearestStore.name}
          </h4>
          {nearestStore.distance && nearestStore.distance <= 99 && (
            <div className="flex items-center text-sm text-gray-600">
              <svg
                width="16"
                height="16"
                viewBox="0 0 20 20"
                fill="none"
                className="mr-1"
              >
                <path
                  d="M10 10C11.1046 10 12 9.10457 12 8C12 6.89543 11.1046 6 10 6C8.89543 6 8 6.89543 8 8C8 9.10457 8.89543 10 10 10Z"
                  fill="currentColor"
                />
                <path
                  d="M10 2C7.79086 2 6 3.79086 6 6C6 8.5 10 14 10 14C10 14 14 8.5 14 6C14 3.79086 12.2091 2 10 2Z"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span className="font-medium">
                {formatDistance(nearestStore.distance)}
              </span>
            </div>
          )}
        </div>

        {/* Address */}
        <div className="mb-4">
          <p className="text-sm text-gray-600 leading-relaxed">
            {nearestStore.address.address}
          </p>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between mb-6">
          {nearestStore.phone && (
            <button
              onClick={() => handleCall(nearestStore.phone!)}
              className="flex items-center text-sm text-primary-pink font-medium hover:text-primary-pink-hover transition-colors"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 20 20"
                fill="none"
                className="mr-1"
              >
                <path
                  d="M18.3066 15.1667C18.3066 15.1667 16.9583 16.5 16.2341 17.2242C15.3483 18.11 14.2466 18.0867 13.2891 17.9067C11.0891 17.4717 8.96743 16.2908 7.23076 14.5542C5.49409 12.8175 4.31326 10.6958 3.87826 8.49583C3.69826 7.5375 3.67409 6.43583 4.56076 5.55C5.28493 4.825 6.61826 3.47667 6.61826 3.47667C7.0841 2.92917 7.85326 2.8825 8.37159 3.38417L10.3458 5.2775C10.8941 5.80583 10.9508 6.6225 10.4691 7.22L9.69743 8.1525C9.68409 8.16917 9.68493 8.19167 9.69743 8.20583C10.1183 9.07417 10.7141 9.84417 11.4491 10.4683C12.1841 11.0925 13.0341 11.5408 13.9758 11.7767C13.9908 11.7808 14.0058 11.7792 14.0191 11.7717L14.8258 11.1175C15.4891 10.6208 16.3683 10.6408 17.0091 11.1675L18.9733 13.0417C19.5191 13.5408 19.4841 14.3617 18.9066 14.8158L18.3066 15.1667Z"
                  fill="currentColor"
                />
              </svg>
              {nearestStore.phone}
            </button>
          )}

          <button
            onClick={() =>
              nearestStore.lat &&
              nearestStore.lng &&
              handleShowMap(nearestStore.lat, nearestStore.lng)
            }
            className="flex items-center text-sm text-primary-pink font-medium hover:text-primary-pink-hover transition-colors"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 20 20"
              fill="none"
              className="mr-1"
            >
              <path
                d="M1 6V18L6 15L14 18L19 15V3L14 6L6 3L1 6Z"
                stroke="#F65D79"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M6 3V15M14 6V18"
                stroke="#F65D79"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Xem bản đồ
          </button>
        </div>

        {/* View All Button */}
        <button
          onClick={handleViewAllStores}
          className="w-full h-11 bg-white rounded-lg border border-grey-3 flex items-center justify-center font-bold text-primary-black text-sm"
        >
          Hiển thị tất cả {numberOfStores} cửa hàng
        </button>
      </>
    );
  };

  // Render content based on permission status
  const renderContent = () => {
    if (nearestStore) {
      return renderNearestStore();
    }

    console.log("permissionStatus renderContent", permissionStatus);
    switch (permissionStatus) {
      case "granted":
        if (locationError) {
          return (
            <LocationPermissionRequest
              onRequestPermission={handleRequestPermission}
              status="granted"
            />
          );
        }
        return renderNearestStore();
      case "denied":
      case "prompt":
        return (
          <LocationPermissionRequest
            onRequestPermission={handleRequestPermission}
            status={permissionStatus}
          />
        );
      case "blocked":
        return (
          <LocationPermissionRequest
            onRequestPermission={handleRequestPermission}
            status="blocked"
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={`bg-white my-3 ${className}`}>
      {/* Section Title */}
      <div className="px-4 py-3">
        <h3 className="text-lg font-semibold text-gray-900">
          Cửa hàng gần đây
        </h3>
      </div>

      {/* Content */}
      <div className="p-4">
        <div style={{ minHeight: "220px" }}>{renderContent()}</div>
      </div>
    </div>
  );
};

export default StoreNearby;
