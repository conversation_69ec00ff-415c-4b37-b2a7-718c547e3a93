import React from 'react';

interface Props {
  description: string;
  colorTag?: string;
}

// Constants tương đương với styleGuide từ React Native
const COLORS = {
  darkGreen: '#23B082',
  primaryWhite: '#FFFFFF',
};

const TYPOGRAPHY = {
  statusDemi: {
    fontFamily: 'Arial, sans-serif', // Fallback font cho web
    fontSize: '12px',
    lineHeight: '18px',
    fontWeight: '600',
  },
};

const SPACING = {
  s_8: 8,
  s_12: 12,
};

// Utility function tương đương với resWidth
const resWidth = (size: number): number => {
  // Responsive width calculation for web
  const standardWidth = 375;
  const currentWidth = Math.min(window.innerWidth, 768); // Cap at tablet size
  return Math.round((currentWidth / standardWidth) * size);
};

// CSS-in-JS styles
const styles = {
  container: {
    height: `${resWidth(26)}px`,
    display: 'flex',
    flexDirection: 'row' as const,
    position: 'absolute' as const,
    right: 0,
    top: 0,
  },
  shape: {
    height: `${resWidth(26)}px`,
    aspectRatio: '15 / 26',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentContainer: (colorTag: string) => ({
    backgroundColor: colorTag,
    marginLeft: '-1px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingRight: `${SPACING.s_8}px`,
    borderTopRightRadius: `${SPACING.s_12}px`,
    minHeight: `${resWidth(26)}px`,
  }),
  content: {
    ...TYPOGRAPHY.statusDemi,
    color: COLORS.primaryWhite,
    margin: 0,
    padding: 0,
    whiteSpace: 'nowrap' as const,
  },
  svg: {
    width: '100%',
    height: '100%',
  },
};

// Simple HTML parser for basic HTML content (thay thế cho HtmlReader)
const parseHtmlContent = (htmlString: string): React.ReactNode => {
  // Remove HTML tags and decode basic entities
  const cleanText = htmlString
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&nbsp;/g, ' ');

  return cleanText;
};

const LabelSale: React.FC<Props> = ({
  description,
  colorTag = COLORS.darkGreen,
}) => {
  return (
    <div style={styles.container}>
      <div style={styles.shape}>
        <svg
          style={styles.svg}
          viewBox="0 0 15 26"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path
            d="M14.5 0H2C1.15636 0 0.661077 0.948759 1.14338 1.64094L9.05814 13L1.14338 24.3591C0.661077 25.0512 1.15636 26 2 26H14.5V0Z"
            fill={colorTag}
          />
        </svg>
      </div>
      <div style={styles.contentContainer(colorTag)}>
        <span style={styles.content}>
          {parseHtmlContent(description)}
        </span>
      </div>
    </div>
  );
};

export default LabelSale;
