import React, { FC } from "react";
import moment from "moment";
import { RewardItemType } from "../../../services/api/rewards/types";
import { imagesAssets } from "../../../assets/images";
import { formatPoints, formatPriceNumber as sharedFormatPriceNumber } from "../../../utils/formatNumber";

interface Props {
  reward: RewardItemType;
  color?: string;
  type: "card" | "detail" | "v5";
  isFullPrice?: boolean;
  horizontal?: boolean;
}

// Utility functions - using shared formatPoints for consistency
const formatNumber = (num: number): string => {
  if (typeof num === "number") {
    return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
  }
  return "";
};

const formatFlashSalePrice = (price: number, hasTeasing: boolean): string => {
  if (!hasTeasing) return price.toString();
  return `?${formatNumber(price).slice(1)}`;
};

// Transform helper function
const transformDataDiscount = ({
  campaignType,
  startTime,
  issueVUIPointFls,
  issueBCPointFls,
  issueVUIPointFlsOrPP,
  issueVUIPoint,
  issueBCPointFlsOrPP,
  issueBrandCurrencyPoint,
  type,
}: {
  campaignType?: "flashSale" | "theme";
  startTime: string;
  issueVUIPointFls?: number;
  issueBCPointFls?: number;
  issueVUIPointFlsOrPP?: number;
  issueVUIPoint: number;
  issueBCPointFlsOrPP?: number;
  issueBrandCurrencyPoint?: number;
  type: string;
}) => {
  const isComing = moment().isBefore(startTime);
  const issueVUIPointTheme =
    campaignType === "theme" && type === "detail" && isComing
      ? issueVUIPointFls
      : undefined;
  const issueBCPointTheme =
    campaignType === "theme" && type === "detail" && isComing
      ? issueBCPointFls
      : undefined;

  const currentVUI =
    issueVUIPointFlsOrPP ?? issueVUIPointTheme ?? issueVUIPoint;
  const currentBC =
    issueBCPointFlsOrPP ?? issueBCPointTheme ?? issueBrandCurrencyPoint;

  const hasDiscount =
    (Number.isFinite(issueVUIPointFlsOrPP) &&
      issueVUIPointFlsOrPP !== issueVUIPoint) ||
    (Number.isFinite(issueBCPointFlsOrPP) &&
      issueBCPointFlsOrPP !== issueBrandCurrencyPoint) ||
    // condition to show discount price with Theme campaign
    (Number.isFinite(issueVUIPointTheme) &&
      issueVUIPointTheme !== issueVUIPoint) ||
    (Number.isFinite(issueBCPointTheme) &&
      issueBCPointTheme !== issueBrandCurrencyPoint);

  const percentDiscountVui = Math.round((1 - currentVUI / issueVUIPoint) * 100);
  const percentDiscountBC = Math.round(
    (1 - (currentBC || 0) / (issueBrandCurrencyPoint || 1)) * 100
  );

  return {
    currentVUI,
    currentBC,
    isComing,
    hasDiscount,
    percentDiscountVui,
    percentDiscountBC,
  };
};

// Styles
const styles = {
  container: {
    display: "flex",
    flexDirection: "row" as const,
    alignItems: "center",
    flexWrap: "wrap" as const,
    gap: "2px",
  },
  point: {
    marginLeft: "4px",
  },
  lineThrough: {
    textDecoration: "line-through",
    color: "#9CA3AF", // grey2 equivalent
  },
  rowCenter: {
    display: "flex",
    flexDirection: "row" as const,
    alignItems: "center",
  },
  colBetween: {
    display: "flex",
    flexDirection: "column" as const,
    justifyContent: "space-between",
  },
  rowVCenter: {
    display: "flex",
    flexDirection: "row" as const,
    alignItems: "center",
  },
  logo: {
    borderRadius: "50%",
    objectFit: "cover" as const,
  },
};

// Main component
const PriceWeb: FC<Props> = ({
  reward,
  color = "#000000", // primaryBlack equivalent
  type,
  isFullPrice = true,
  horizontal = false,
}) => {
  const {
    isVuiPoint,
    issueVUIPoint,
    isBrandCurrencyPoint,
    brandCurrency,
    issueBrandCurrencyPoint = 0,
    campaignStartTime,
    campaignCountdownTime,
    displayOnPrice = false,
    campaignType,
    issueBCPointFls,
    issueVUIPointFls,
    issueVUIPointFlsOrPP,
    issueBCPointFlsOrPP,
    startTime,
  } = reward;

  const brandCurrencyLogo = brandCurrency?.logo || "";

  const inCountdownTime =
    campaignCountdownTime && campaignStartTime
      ? moment().isBetween(
          moment(campaignCountdownTime),
          moment(campaignStartTime)
        )
      : false;

  const { currentVUI, currentBC, hasDiscount, isComing } =
    transformDataDiscount({
      campaignType: campaignType as "flashSale" | "theme",
      startTime: startTime || "",
      issueVUIPointFls,
      issueBCPointFls,
      issueVUIPointFlsOrPP,
      issueVUIPoint,
      issueBCPointFlsOrPP,
      issueBrandCurrencyPoint,
      type,
    });

  // Ensure currentBC has a default value
  const safeCurrentBC = currentBC || 0;

  let logoSize = 24; // SPACING.l_24 equivalent
  let marginLeftOldPrice = 4; // SPACING.s_4 equivalent
  let typoPrice: React.CSSProperties = {
    fontWeight: "bold",
    fontSize: "16px",
    lineHeight: "24px",
  }; // TYPOGRAPHY.bodyBold equivalent
  let typoOldPrice: React.CSSProperties = {
    fontWeight: "600",
    fontSize: "14px",
    lineHeight: "20px",
  }; // TYPOGRAPHY.bodyDemi equivalent

  if (type === "detail") {
    logoSize = 24;
    marginLeftOldPrice = 4;
    typoPrice = {
      fontWeight: "bold",
      fontSize: "16px",
      lineHeight: "32px",
    }; // TYPOGRAPHY.header3Bold equivalent
    typoOldPrice = {
      fontWeight: "normal",
      fontSize: "12px",
      lineHeight: "16px",
    }; // TYPOGRAPHY.captionRegular equivalent
  }

  if (type === "v5") {
    typoPrice = {
      fontWeight: "bold",
      fontSize: "16px",
      lineHeight: "24px",
    };
    logoSize = 16; // SPACING.m_16 equivalent
    typoOldPrice = {
      fontWeight: "normal",
      fontSize: "12px",
      lineHeight: "16px",
    };
    marginLeftOldPrice = horizontal
      ? marginLeftOldPrice
      : logoSize + marginLeftOldPrice;
  }

  const priceStyle = { ...styles.point, ...typoPrice };

  const oldPriceOneStyle = {
    ...typoOldPrice,
    ...styles.lineThrough,
    marginLeft: `${marginLeftOldPrice}px`,
  };

  const hasTeasingPrice =
    inCountdownTime &&
    displayOnPrice &&
    (type === "card" ||
      type === "v5" ||
      (type === "detail" && campaignType === "theme" && isComing));

  const getFormattedPrice = ({
    price,
  }: {
    price: number;
    isDiscountPrice?: boolean;
  }) => {
    const fullPrice = isFullPrice && price < 999_999_999;
    return sharedFormatPriceNumber(price, fullPrice);
  };

  const renderVUIPrice = () => {
    if (!isVuiPoint) return null;

    const showVuiBalance = hasTeasingPrice
      ? formatFlashSalePrice(currentVUI, hasTeasingPrice)
      : getFormattedPrice({
          price: currentVUI,
        });

    const showVuidiscount = getFormattedPrice({
      price: issueVUIPoint || 0,
    });

    return (
      <PriceWithDiscount
        logoSource={imagesAssets.vuiLogo} // Replace with actual VUI logo path
        price={showVuiBalance}
        discountPrice={showVuidiscount}
        layoutStyle={type}
        priceStyle={priceStyle}
        oldPriceStyle={oldPriceOneStyle}
        color={color}
        hasDiscount={hasDiscount}
        logoSize={logoSize}
        horizontal={horizontal}
      />
    );
  };

  const renderBCPrice = () => {
    if (!isBrandCurrencyPoint) return null;

    const showBCBalance = hasTeasingPrice
      ? formatFlashSalePrice(safeCurrentBC, hasTeasingPrice)
      : getFormattedPrice({
          price: safeCurrentBC,
        });

    const showBCdiscount = getFormattedPrice({
      price: issueBrandCurrencyPoint,
    });

    return (
      <PriceWithDiscount
        logoSource={brandCurrencyLogo}
        price={showBCBalance}
        discountPrice={showBCdiscount}
        layoutStyle={type}
        priceStyle={priceStyle}
        oldPriceStyle={oldPriceOneStyle}
        color={color}
        hasDiscount={hasDiscount}
        logoSize={logoSize}
        horizontal={horizontal}
      />
    );
  };

  const renderPrice = () => renderVUIPrice() || renderBCPrice();

  return <div style={styles.container}>{renderPrice()}</div>;
};

// Sub-component
const PriceWithDiscount = ({
  logoSource,
  price,
  discountPrice,
  layoutStyle,
  priceStyle,
  oldPriceStyle,
  color,
  hasDiscount,
  logoSize,
  horizontal = false,
}: {
  logoSource: string;
  price: string;
  discountPrice: string;
  layoutStyle: string;
  priceStyle: React.CSSProperties;
  oldPriceStyle: React.CSSProperties;
  color: string;
  hasDiscount: boolean;
  logoSize: number;
  horizontal?: boolean;
}) => {
  const priceComponent = <span style={{ ...priceStyle, color }}>{price}</span>;

  const discountComponent = hasDiscount && (
    <span style={{ ...oldPriceStyle, color }}>{discountPrice}</span>
  );

  if (layoutStyle === "detail" || !!horizontal) {
    return (
      <div style={styles.rowCenter}>
        <img
          src={logoSource}
          alt="logo"
          style={{
            ...styles.logo,
            width: `${logoSize}px`,
            height: `${logoSize}px`,
          }}
        />
        {priceComponent}
        {discountComponent}
      </div>
    );
  }

  return (
    <div style={styles.colBetween}>
      <div style={styles.rowVCenter}>
        <img
          src={logoSource}
          alt="logo"
          style={{
            ...styles.logo,
            width: `${logoSize}px`,
            height: `${logoSize}px`,
          }}
        />
        {priceComponent}
      </div>
      {discountComponent}
    </div>
  );
};

export default PriceWeb;
