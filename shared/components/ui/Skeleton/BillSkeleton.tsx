import React from 'react';
import { Skeleton } from './index';
import { cn } from '../../../utils';

interface BillSkeletonProps {
  className?: string;
}

export const BillSkeleton: React.FC<BillSkeletonProps> = ({ className }) => {
  return (
    <Skeleton.Container>
      <div className={cn(
        'w-full bg-white rounded-lg shadow-[0px_8px_32px_rgba(26,24,24,0.1)] p-2',
        className
      )}>
        <div className="flex gap-3">
          {/* Left Section */}
          <div className="flex-1 flex flex-col p-2">
            {/* Brand and logo row */}
            <div className="flex items-center gap-2 pt-1 mb-4">
              <Skeleton.Circle width={24} height={24} />
              <Skeleton.Rect width={96} height={16} borderRadius={4} />
            </div>

            {/* Info container */}
            <div className="flex-1 flex flex-col justify-end">
              {/* Amount */}
              <Skeleton.Rect width={140} height={16} borderRadius={4} className="mb-2" />
              
              {/* Status/Rewards */}
              <Skeleton.Rect width={140} height={16} borderRadius={4} className="mb-2" />
              
              {/* Date */}
              <Skeleton.Rect width={100} height={12} borderRadius={4} />
            </div>
          </div>

          {/* Right Section - Thumbnail */}
          <div className="w-[100px] h-[130px] ml-4">
            <Skeleton.Rect width="100%" height="100%" borderRadius={8} />
          </div>
        </div>
      </div>
    </Skeleton.Container>
  );
};

interface BillListSkeletonProps {
  count?: number;
  className?: string;
}

export const BillListSkeleton: React.FC<BillListSkeletonProps> = ({ 
  count = 4, 
  className 
}) => {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <BillSkeleton key={index} />
      ))}
    </div>
  );
};

export default BillSkeleton;