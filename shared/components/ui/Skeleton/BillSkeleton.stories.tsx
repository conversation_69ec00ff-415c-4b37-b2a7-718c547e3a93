import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { BillSkeleton, BillListSkeleton } from './BillSkeleton';

const meta: Meta<typeof BillSkeleton> = {
  title: 'UI/Skeleton/BillSkeleton',
  component: BillSkeleton,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'BillSkeleton component mimics the loading state of BillCard components, matching the mobile app structure.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithCustomClass: Story = {
  args: {
    className: 'mx-4',
  },
};

export const List: StoryObj<typeof BillListSkeleton> = {
  render: (args) => <BillListSkeleton {...args} />,
  args: {
    count: 3,
  },
  argTypes: {
    count: {
      control: { type: 'number', min: 1, max: 10 },
      description: 'Number of skeleton items to display',
    },
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes',
    },
  },
};

export const ManyItems: StoryObj<typeof BillListSkeleton> = {
  render: () => <BillListSkeleton count={6} />,
};

export const InMobileContainer: Story = {
  render: () => (
    <div className="max-w-md mx-auto bg-gray-50 p-4">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Loading Bills...</h3>
        <BillListSkeleton count={4} />
      </div>
    </div>
  ),
};

export const TabContainer: Story = {
  render: () => (
    <div className="max-w-md mx-auto bg-[#EFF3F6]">
      {/* Mock tab header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <h2 className="font-semibold text-gray-800">Hóa đơn đã chụp</h2>
        <div className="flex space-x-4 mt-2">
          <div className="px-3 py-1 bg-blue-500 text-white rounded-full text-sm">
            Đang chờ (5)
          </div>
          <div className="px-3 py-1 bg-gray-200 text-gray-700 rounded-full text-sm">
            Thành công
          </div>
          <div className="px-3 py-1 bg-gray-200 text-gray-700 rounded-full text-sm">
            Từ chối
          </div>
        </div>
      </div>
      
      {/* Tab content with skeleton */}
      <div className="p-4">
        <BillListSkeleton count={5} />
      </div>
    </div>
  ),
};

export const SingleVsMultiple: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h4 className="text-md font-semibold mb-4">Single Bill Skeleton</h4>
        <BillSkeleton />
      </div>
      <div>
        <h4 className="text-md font-semibold mb-4">Multiple Bill Skeletons</h4>
        <BillListSkeleton count={3} />
      </div>
    </div>
  ),
};