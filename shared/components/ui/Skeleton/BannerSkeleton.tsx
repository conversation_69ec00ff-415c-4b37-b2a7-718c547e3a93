import React from 'react';
import { SkeletonContainer } from './SkeletonContainer';
import { SkeletonRect } from './SkeletonRect';

interface BannerSkeletonProps {
  className?: string;
  width?: number | string;
  height?: number | string;
  borderRadius?: number | string;
}

export const BannerSkeleton: React.FC<BannerSkeletonProps> = ({
  className,
  width = '100%',
  height = 156, // Default banner height matching BannerCarousel
  borderRadius = 12, // Default rounded-xl
}) => {
  return (
    <SkeletonContainer className={className}>
      <SkeletonRect
        width={width}
        height={height}
        borderRadius={borderRadius}
      />
    </SkeletonContainer>
  );
};

export default BannerSkeleton;