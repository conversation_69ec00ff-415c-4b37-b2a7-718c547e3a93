import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { BannerSkeleton } from './BannerSkeleton';

const meta: Meta<typeof BannerSkeleton> = {
  title: 'UI/Skeleton/BannerSkeleton',
  component: BannerSkeleton,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    width: {
      control: { type: 'text' },
      description: 'Width of the banner skeleton',
    },
    height: {
      control: { type: 'number' },
      description: 'Height of the banner skeleton in pixels',
    },
    borderRadius: {
      control: { type: 'number' },
      description: 'Border radius of the banner skeleton in pixels',
    },
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const CustomSize: Story = {
  args: {
    width: 300,
    height: 120,
  },
};

export const FullWidth: Story = {
  args: {
    width: '100%',
    height: 180,
  },
  decorators: [
    (Story) => (
      <div style={{ width: '400px' }}>
        <Story />
      </div>
    ),
  ],
};

export const RoundedCorners: Story = {
  args: {
    borderRadius: 20,
    height: 140,
  },
};

export const MultipleBanners: Story = {
  render: () => (
    <div className="space-y-4">
      <BannerSkeleton />
      <BannerSkeleton height={120} />
      <BannerSkeleton height={140} borderRadius={8} />
    </div>
  ),
};

export const InContainer: Story = {
  render: () => (
    <div className="max-w-md mx-auto space-y-4 p-4 bg-gray-50 rounded-lg">
      <h3 className="text-lg font-semibold">Loading Banners...</h3>
      <BannerSkeleton />
      <BannerSkeleton />
    </div>
  ),
};