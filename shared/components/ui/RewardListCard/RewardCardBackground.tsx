import * as React from "react";

interface RewardCardBackgroundProps {
  className?: string;
  fill?: string;
}

const RewardCardBackground: React.FC<RewardCardBackgroundProps> = ({ className, fill = "#ffffff" }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="100%"
    height="100%"
    fill="none"
    className={className}
    viewBox="0 0 343 124"
    preserveAspectRatio="none"
  >
    {/* White background fill */}
    <path
      fill={fill}
      fillRule="evenodd"
      d="M12 0C5.373 0 0 5.373 0 12v16c4.956 0 8.974 4.03 8.974 9S4.956 46 0 46v66c0 6.627 5.373 12 12 12h319c6.627 0 12-5.373 12-12V12c0-6.627-5.373-12-12-12H12Z"
      clipRule="evenodd"
    />
    
    {/* Border outline - restored from original */}
    <mask id="border-mask" fill="#fff">
      <path
        fillRule="evenodd"
        d="M12 0C5.373 0 0 5.373 0 12v16c4.956 0 8.974 4.03 8.974 9S4.956 46 0 46v66c0 6.627 5.373 12 12 12h319c6.627 0 12-5.373 12-12V12c0-6.627-5.373-12-12-12H12Z"
        clipRule="evenodd"
      />
    </mask>
    <path
      fill="#ECECEC"
      d="M0 28h-1v1h1v-1Zm0 18v-1h-1v1h1Zm0-34h1C1 5.925 5.925 1 12 1v-2C4.82-1-1 4.82-1 12h1Zm0 16h1V12h-2v16h1Zm0 0v1c4.401 0 7.974 3.579 7.974 8h2c0-5.52-4.463-10-9.974-10v1Zm8.974 9h-1c0 4.421-3.573 8-7.974 8v2c5.511 0 9.974-4.48 9.974-10h-1ZM0 112h1V46h-2v66h1Zm12 12v-1c-6.075 0-11-4.925-11-11h-2c0 7.18 5.82 13 13 13v-1Zm159.5 0v-1H12v2h159.5v-1Zm159.5 0v-1H171.5v2H331v-1Zm12-12h-1c0 6.075-4.925 11-11 11v2c7.18 0 13-5.82 13-13h-1Zm0-100h-1v100h2V12h-1ZM331 0v1c6.075 0 11 4.925 11 11h2c0-7.18-5.82-13-13-13v1ZM12 0v1h319v-2H12v1Z"
      mask="url(#border-mask)"
    />
    <path stroke="#CACACA" strokeDasharray="3 4" d="M10 37.5h323" />
  </svg>
);

export default RewardCardBackground;