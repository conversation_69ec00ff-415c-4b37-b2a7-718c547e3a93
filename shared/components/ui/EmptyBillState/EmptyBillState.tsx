import React from 'react';

// Import mascot images from mascotV3 (copied from mobile project)
import meditateImg from '../../../assets/images/mascotV3/meditate.png';
import errorImg from '../../../assets/images/mascotV3/error.png';
import lostConnectionImg from '../../../assets/images/mascotV3/lostConnection.png';
import sleepImg from '../../../assets/images/mascotV3/sleep.png';
import cryImg from '../../../assets/images/mascotV3/cry.png';
import warningImg from '../../../assets/images/mascotV3/warning.png';
import inboxImg from '../../../assets/images/mascotV3/inbox.png';
import congratImg from '../../../assets/images/mascotV3/congrat.png';
import cuteImg from '../../../assets/images/mascotV3/cute.png';
import loveImg from '../../../assets/images/mascotV3/love.png';

export type MascotNameType = 'meditate' | 'error' | 'lostConnection' | 'sleep' | 'cry' | 'warning' | 'inbox' | 'congrat' | 'cute' | 'love';

export interface EmptyBillStateProps {
  title?: string;
  description?: string;
  mascotName?: MascotNameType;
  onActionClick?: () => void;
  actionText?: string;
  className?: string;
}

const MascotIcon: React.FC<{ name: MascotNameType; className?: string }> = ({ name, className }) => {
  // Map mascot names to imported images from mascotV3
  const getMascotImage = () => {
    switch (name) {
      case 'meditate':
        return meditateImg;
      case 'error':
        return errorImg;
      case 'lostConnection':
        return lostConnectionImg;
      case 'sleep':
        return sleepImg;
      case 'cry':
        return cryImg;
      case 'warning':
        return warningImg;
      case 'inbox':
        return inboxImg;
      case 'congrat':
        return congratImg;
      case 'cute':
        return cuteImg;
      case 'love':
        return loveImg;
      default:
        return meditateImg;
    }
  };

  return (
    <div className={`w-[180px] h-[130px] flex items-center justify-center ${className}`}>
      <img 
        src={getMascotImage()} 
        alt={`Mascot ${name}`}
        className="w-full h-full object-contain"
      />
    </div>
  );
};

export const EmptyBillState: React.FC<EmptyBillStateProps> = ({
  title = 'Chưa có hoá đơn nào',
  description,
  mascotName = 'meditate',
  onActionClick,
  actionText,
  className = '',
}) => {
  return (
    <div className={`flex flex-col items-center justify-center py-16 px-8 ${className}`}>
      {/* Mascot Icon */}
      <MascotIcon name={mascotName} className="mb-6" />
      
      {/* Title */}
      <h2 className="text-lg font-bold text-[#1A1818] text-center mb-2" style={{ fontFamily: 'Archia' }}>
        {title}
      </h2>
      
      {/* Description */}
      {description && (
        <p className="text-sm text-[#5A5A5A] text-center leading-relaxed mb-6 max-w-[280px]" style={{ fontFamily: 'Archia' }}>
          {description}
        </p>
      )}
      
      {/* Action Button */}
      {onActionClick && actionText && (
        <button
          onClick={onActionClick}
          className="px-6 py-3 bg-[#4391F7] text-white rounded-lg text-sm font-semibold hover:bg-[#3580E6] transition-colors"
          style={{ fontFamily: 'Archia' }}
        >
          {actionText}
        </button>
      )}
    </div>
  );
};

export default EmptyBillState;