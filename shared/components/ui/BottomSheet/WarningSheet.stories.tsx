import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WarningSheet } from "./WarningSheet";
import { useState } from "react";

const meta: Meta<typeof WarningSheet> = {
  title: "Components/UI/BottomSheet/WarningSheet",
  component: WarningSheet,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof WarningSheet>;

const WarningSheetWrapper = (args: any) => {
  const [open, setOpen] = useState(true);
  const [loading, setLoading] = useState(false);

  const handlePrimaryAction = async () => {
    setLoading(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setLoading(false);
    setOpen(false);
  };

  const handleSecondaryAction = () => {
    setOpen(false);
  };

  return (
    <>
      <button
        onClick={() => setOpen(true)}
        className="m-4 px-4 py-2 bg-[#F65D79] text-white rounded-lg"
      >
        Open Warning Sheet
      </button>
      <WarningSheet
        {...args}
        isOpen={open}
        onClose={() => setOpen(false)}
        onPrimaryAction={handlePrimaryAction}
        onSecondaryAction={handleSecondaryAction}
        primaryButtonLoading={loading}
        primaryButtonDisabled={loading}
      />
    </>
  );
};

export const Default: Story = {
  render: (args) => <WarningSheetWrapper {...args} />,
  args: {
    title: "Lưu ý",
    description: "Vui lòng đọc kỹ các lưu ý trước khi tiếp tục",
    warningList: [
      "Hành động này không thể hoàn tác",
      "Voucher sẽ hết hạn sau khi kích hoạt"
    ],
    primaryButtonText: "Xác nhận",
    secondaryButtonText: "Để sau",
    showMascot: true,
  },
};

export const WithTimeLimit: Story = {
  render: (args) => <WarningSheetWrapper {...args} />,
  args: {
    title: "Sử dụng voucher ngay",
    warningList: [
      "Hệ thống sẽ kích hoạt sử dụng voucher ngay sau khi bạn xác nhận.",
      "Voucher sẽ hết hạn sử dụng sau"
    ],
    timeLimit: 30,
    primaryButtonText: "Xác nhận",
    secondaryButtonText: "Để sau",
    showMascot: true,
  },
};

export const WithHighlightText: Story = {
  render: (args) => <WarningSheetWrapper {...args} />,
  args: {
    title: "Xác nhận xóa",
    description: "Bạn có chắc chắn muốn xóa mục này?",
    highlightText: "Lưu ý: Tất cả dữ liệu liên quan sẽ bị xóa vĩnh viễn",
    primaryButtonText: "Xóa",
    secondaryButtonText: "Hủy",
    showMascot: true,
  },
};

export const WithoutMascot: Story = {
  render: (args) => <WarningSheetWrapper {...args} />,
  args: {
    title: "Cảnh báo",
    description: "Thao tác này yêu cầu xác nhận của bạn",
    warningList: [
      "Dữ liệu sẽ được cập nhật ngay lập tức",
      "Các thay đổi sẽ ảnh hưởng đến toàn bộ hệ thống"
    ],
    primaryButtonText: "Tiếp tục",
    secondaryButtonText: "Quay lại",
    showMascot: false,
  },
};

export const SingleActionButton: Story = {
  render: (args) => <WarningSheetWrapper {...args} />,
  args: {
    title: "Thông báo",
    description: "Đây là một thông báo quan trọng cần sự chú ý của bạn",
    warningList: [
      "Vui lòng đọc kỹ thông tin",
      "Nhấn xác nhận để tiếp tục"
    ],
    primaryButtonText: "Đã hiểu",
    secondaryButtonText: undefined,
    showMascot: true,
  },
};