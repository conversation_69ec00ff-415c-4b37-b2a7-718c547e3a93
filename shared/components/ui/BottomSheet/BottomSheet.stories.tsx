import React, { useState } from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { BottomSheet } from './BottomSheet';
import { ExchangePointsSheet } from './ExchangePointsSheet';
import { SuccessSheet } from './SuccessSheet';
import { ErrorSheet } from './ErrorSheet';
import { InfoSheet } from './InfoSheet';

const meta: Meta<typeof BottomSheet> = {
  title: 'UI/BottomSheet',
  component: BottomSheet,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Bottom Sheet components that slide up from the bottom of the screen.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    isOpen: {
      control: 'boolean',
      description: 'Control the visibility of the bottom sheet',
    },
    title: {
      control: 'text',
      description: 'Title of the bottom sheet',
    },
    height: {
      control: 'select',
      options: ['auto', 'sm', 'md', 'lg', 'full'],
      description: 'Height preset for the bottom sheet',
    },
    showCloseButton: {
      control: 'boolean',
      description: 'Show close button in header',
    },
    showDragHandle: {
      control: 'boolean',
      description: 'Show drag handle at the top',
    },
    closeOnBackdropClick: {
      control: 'boolean',
      description: 'Close when clicking outside',
    },
  },
};

export default meta;
type Story = StoryObj<typeof BottomSheet>;

// Base BottomSheet Story
export const Default: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-pink-500 text-white rounded-lg"
        >
          Open Bottom Sheet
        </button>
        <BottomSheet
          {...args}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Bottom Sheet Title"
        >
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-3">Content Section</h3>
            <p className="text-gray-600 mb-4">
              This is the content of the bottom sheet. It can contain any React components
              and will automatically handle scrolling when content exceeds the height.
            </p>
            <div className="space-y-2">
              <div className="p-3 bg-gray-100 rounded">Item 1</div>
              <div className="p-3 bg-gray-100 rounded">Item 2</div>
              <div className="p-3 bg-gray-100 rounded">Item 3</div>
            </div>
          </div>
        </BottomSheet>
      </div>
    );
  },
};

// Exchange Points Sheet Story
export const ExchangePoints: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    const handleConfirm = () => {
      setLoading(true);
      setTimeout(() => {
        setLoading(false);
        setIsOpen(false);
        alert('Exchange confirmed!');
      }, 2000);
    };

    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-pink-500 text-white rounded-lg"
        >
          Open Exchange Points Sheet
        </button>
        <ExchangePointsSheet
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          totalPoints={320}
          currentPoints={950}
          itemName="Voucher giảm 20%"
          onConfirm={handleConfirm}
          confirmLoading={loading}
        />
      </div>
    );
  },
};

// Success Sheet Story
export const Success: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-green-500 text-white rounded-lg"
        >
          Open Success Sheet
        </button>
        <SuccessSheet
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          message="Đổi ưu đãi thành công!"
          subMessage="Voucher đã được thêm vào kho của bạn"
          points={100}
          buttonText="Xem voucher"
          onButtonClick={() => {
            setIsOpen(false);
            alert('Navigate to vouchers');
          }}
        />
      </div>
    );
  },
};

// Error Sheet Story
export const Error: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-red-500 text-white rounded-lg"
        >
          Open Error Sheet
        </button>
        <ErrorSheet
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          errorMessage="Không thể đổi ưu đãi"
          errorDetails="Bạn không đủ điểm để đổi ưu đãi này. Vui lòng tích lũy thêm điểm."
          buttonText="Thử lại"
          onButtonClick={() => {
            setIsOpen(false);
            console.log('Retry action');
          }}
        />
      </div>
    );
  },
};

// Info Sheet Story
export const Info: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <button
          onClick={() => setIsOpen(true)}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg"
        >
          Open Info Sheet
        </button>
        <InfoSheet
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Điều kiện sử dụng"
          infoTitle="Lưu ý quan trọng"
          infoList={[
            'Voucher có hiệu lực trong 30 ngày kể từ ngày đổi',
            'Không áp dụng cùng các chương trình khuyến mãi khác',
            'Áp dụng cho đơn hàng từ 500.000đ',
            'Mỗi khách hàng chỉ được sử dụng 1 lần'
          ]}
          primaryButtonText="Đã hiểu"
          secondaryButtonText="Xem thêm"
        />
      </div>
    );
  },
};

// Multiple Heights Demo
export const DifferentHeights: Story = {
  render: () => {
    const [openSheet, setOpenSheet] = useState<string | null>(null);

    const heights: Array<'sm' | 'md' | 'lg' | 'full'> = ['sm', 'md', 'lg', 'full'];

    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="space-y-4">
          {heights.map((height) => (
            <button
              key={height}
              onClick={() => setOpenSheet(height)}
              className="block w-full px-4 py-2 bg-purple-500 text-white rounded-lg"
            >
              Open {height.toUpperCase()} Height Sheet
            </button>
          ))}
        </div>

        {heights.map((height) => (
          <BottomSheet
            key={height}
            isOpen={openSheet === height}
            onClose={() => setOpenSheet(null)}
            title={`${height.toUpperCase()} Height Bottom Sheet`}
            height={height}
          >
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-3">Height: {height}</h3>
              <div className="space-y-3">
                {Array.from({ length: 20 }, (_, i) => (
                  <div key={i} className="p-3 bg-gray-100 rounded">
                    Content Item {i + 1}
                  </div>
                ))}
              </div>
            </div>
          </BottomSheet>
        ))}
      </div>
    );
  },
};

// Interactive Demo
export const InteractiveDemo: Story = {
  render: () => {
    const [exchangeOpen, setExchangeOpen] = useState(false);
    const [successOpen, setSuccessOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    const handleExchange = () => {
      setLoading(true);
      setTimeout(() => {
        setLoading(false);
        setExchangeOpen(false);
        setTimeout(() => setSuccessOpen(true), 300);
      }, 2000);
    };

    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-bold mb-4">Voucher Giảm Giá 20%</h2>
            <p className="text-gray-600 mb-4">
              Áp dụng cho đơn hàng từ 500.000đ
            </p>
            <div className="flex items-center justify-between mb-6">
              <span className="text-sm text-gray-500">Giá</span>
              <span className="text-lg font-bold text-pink-500">320 POINT</span>
            </div>
            <button
              onClick={() => setExchangeOpen(true)}
              className="w-full px-4 py-3 bg-pink-500 text-white rounded-lg font-semibold"
            >
              Đổi ngay
            </button>
          </div>
        </div>

        <ExchangePointsSheet
          isOpen={exchangeOpen}
          onClose={() => setExchangeOpen(false)}
          totalPoints={320}
          currentPoints={950}
          itemName="Voucher giảm 20%"
          onConfirm={handleExchange}
          confirmLoading={loading}
        />

        <SuccessSheet
          isOpen={successOpen}
          onClose={() => setSuccessOpen(false)}
          message="Đổi thành công!"
          subMessage="Voucher đã được thêm vào kho của bạn"
          points={320}
          buttonText="Xem voucher của tôi"
        />
      </div>
    );
  },
};