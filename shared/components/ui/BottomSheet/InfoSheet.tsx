import React from 'react';
import { BottomSheet, BottomSheetProps } from './BottomSheet';
import { Button } from '../Button/Button';

export interface InfoSheetProps extends Omit<BottomSheetProps, 'children'> {
  // Info specific props
  infoTitle?: string;
  infoContent?: string | React.ReactNode;
  infoList?: string[];
  primaryButtonText?: string;
  secondaryButtonText?: string;
  onPrimaryAction?: () => void;
  onSecondaryAction?: () => void;
}

export const InfoSheet: React.FC<InfoSheetProps> = ({
  infoTitle,
  infoContent,
  infoList,
  primaryButtonText = 'Đã hiểu',
  secondaryButtonText,
  onPrimaryAction,
  onSecondaryAction,
  title = 'Thông tin',
  ...rest
}) => {
  const handlePrimaryAction = () => {
    if (onPrimaryAction) {
      onPrimaryAction();
    } else {
      rest.onClose();
    }
  };

  const handleSecondaryAction = () => {
    if (onSecondaryAction) {
      onSecondaryAction();
    } else {
      rest.onClose();
    }
  };

  return (
    <BottomSheet
      {...rest}
      title={title}
      height="auto"
    >
      <div className="p-6 pb-safe">
        {/* Info Title */}
        {infoTitle && (
          <h3 
            className="text-lg font-semibold text-[#1A1818] mb-3"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            {infoTitle}
          </h3>
        )}

        {/* Info Content */}
        {infoContent && (
          <div className="mb-4">
            {typeof infoContent === 'string' ? (
              <p className="text-sm text-gray-600">{infoContent}</p>
            ) : (
              infoContent
            )}
          </div>
        )}

        {/* Info List */}
        {infoList && infoList.length > 0 && (
          <div className="space-y-2 mb-6">
            {infoList.map((item, index) => (
              <div key={index} className="flex gap-2">
                <span className="text-[#F65D79] flex-shrink-0">•</span>
                <p className="text-sm text-gray-700">{item}</p>
              </div>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            onClick={handlePrimaryAction}
            fullWidth
            className="h-11"
            variant="primary"
          >
            {primaryButtonText}
          </Button>
          
          {secondaryButtonText && (
            <button
              onClick={handleSecondaryAction}
              className="w-full h-11 text-[#F65D79] font-semibold text-sm hover:bg-gray-50 transition-colors rounded-lg"
              style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
            >
              {secondaryButtonText}
            </button>
          )}
        </div>
      </div>
    </BottomSheet>
  );
};

export default InfoSheet;