import React from 'react';
import { BottomSheet, BottomSheetProps } from './BottomSheet';
import { Button } from '../Button/Button';
import CoinIcon from '../../../assets/icons/vui.png';
export interface ExchangePointsSheetProps extends Omit<BottomSheetProps, 'children'> {
  // Exchange specific props
  totalPoints: number;
  currentPoints: number;
  itemName?: string;
  itemImage?: string;
  onConfirm?: () => void;
  confirmLoading?: boolean;
  confirmDisabled?: boolean;
}

export const ExchangePointsSheet: React.FC<ExchangePointsSheetProps> = ({
  totalPoints,
  currentPoints,
  itemName,
  itemImage,
  onConfirm,
  confirmLoading = false,
  confirmDisabled = false,
  title = 'Đổi lấy ưu đãi này',
  ...rest
}) => {
  const hasEnoughPoints = currentPoints >= totalPoints;

  return (
    <BottomSheet
      {...rest}
      title={title}
      height="auto"
      showDragHandle={false}
    >
      <div className="pb-safe">
        {/* Current Points Status */}
        <div className="flex justify-center items-center gap-1.5 px-4 py-4">
          <span className="text-xs text-[#1A1818]">*Bạn đang có</span>
          <span className="text-sm font-semibold text-[#1A1818]">
            {currentPoints} 
          </span>
        </div>

        {/* Item Info (if provided) */}
        {(itemName || itemImage) && (
          <div className="px-4 pb-4">
            {itemImage && (
              <div className="flex justify-center mb-3">
                <img 
                  src={itemImage} 
                  alt={itemName || 'Reward'} 
                  className="w-20 h-20 object-cover rounded-lg"
                />
              </div>
            )}
            {itemName && (
              <p className="text-center text-sm text-gray-600">{itemName}</p>
            )}
          </div>
        )}

        {/* Bottom Section with Gray Background */}
        <div className="bg-gray-50 border-t border-[#ECECEC]">
          {/* Total Points Section */}
          <div className="flex justify-between items-center px-4 py-4">
            <span className="text-sm text-[#1A1818]">Tổng cộng</span>
            <div className="flex items-center gap-2">
              <img src={CoinIcon} alt="Coin" className="w-8 h-8" />
              <span className="text-lg font-bold text-[#1A1818]">
                {totalPoints}
              </span>
            </div>
          </div>

          {/* Confirm Button */}
          <div className="px-4 pb-4">
            <Button
              onClick={onConfirm}
              disabled={confirmDisabled || confirmLoading || !hasEnoughPoints}
              loading={confirmLoading}
              fullWidth
              className="h-11"
              variant="primary"
            >
              {confirmLoading ? 'Đang xử lý...' : 'Xác nhận'}
            </Button>
            
            {!hasEnoughPoints && (
              <p className="text-xs text-red-500 text-center mt-2">
                Bạn không đủ điểm để đổi ưu đãi này
              </p>
            )}
          </div>
        </div>
      </div>
    </BottomSheet>
  );
};

export default ExchangePointsSheet;