import React from 'react';
import { BottomSheet, BottomSheetProps } from './BottomSheet';
import { Button } from '../Button/Button';

export interface ErrorSheetProps extends Omit<BottomSheetProps, 'children'> {
  // Error specific props
  errorMessage?: string;
  errorDetails?: string;
  buttonText?: string;
  onButtonClick?: () => void;
  showIcon?: boolean;
}

export const ErrorSheet: React.FC<ErrorSheetProps> = ({
  errorMessage = 'Đã có lỗi xảy ra',
  errorDetails,
  buttonText = 'Thử lại',
  onButtonClick,
  showIcon = true,
  title = 'Lỗi',
  ...rest
}) => {
  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick();
    } else {
      rest.onClose();
    }
  };

  return (
    <BottomSheet
      {...rest}
      title={title}
      height="auto"
    >
      <div className="p-6 pb-safe">
        {/* Error Icon */}
        {showIcon && (
          <div className="flex justify-center mb-4">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-10 h-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        )}

        {/* Error Message */}
        <h2 
          className="text-xl font-bold text-[#1A1818] text-center mb-2"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          {errorMessage}
        </h2>

        {/* Error Details */}
        {errorDetails && (
          <p 
            className="text-sm text-gray-600 text-center mb-6"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            {errorDetails}
          </p>
        )}

        {/* Action Button */}
        <Button
          onClick={handleButtonClick}
          fullWidth
          className="h-11"
          variant="primary"
        >
          {buttonText}
        </Button>
      </div>
    </BottomSheet>
  );
};

export default ErrorSheet;