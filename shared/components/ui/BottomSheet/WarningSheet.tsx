import React from 'react';
import { BottomSheet, BottomSheetProps } from './BottomSheet';
import { Button } from '../Button/Button';
import warningMascot from '../../../assets/images/mascotV3/warning.png';

export interface WarningSheetProps extends Omit<BottomSheetProps, 'children'> {
  // Warning specific props
  warningList?: string[];
  showMascot?: boolean;
  highlightText?: string;
  timeLimit?: number; // in minutes
  description?: string;
  
  // Button props
  primaryButtonText?: string;
  secondaryButtonText?: string;
  onPrimaryAction?: () => void | Promise<void>;
  onSecondaryAction?: () => void;
  primaryButtonLoading?: boolean;
  primaryButtonDisabled?: boolean;
}

export const WarningSheet: React.FC<WarningSheetProps> = ({
  showMascot = true,
  warningList,
  highlightText,
  timeLimit,
  title = 'Lưu ý',
  description,
  primaryButtonText = 'Xác nhận',
  secondaryButtonText = 'Để sau',
  onPrimaryAction,
  onSecondaryAction,
  primaryButtonLoading = false,
  primaryButtonDisabled = false,
  ...rest
}) => {
  return (
    <BottomSheet
      {...rest}
      title={title}
      height="auto"
    >
      <div className="px-4 pt-4 pb-8">
        {/* Mascot Image */}
        {showMascot && (
          <div className="flex justify-center mb-4">
            <img 
              src={warningMascot} 
              alt="Warning" 
              className="w-24 h-24 object-contain"
            />
          </div>
        )}

        {/* Description */}
        {description && (
          <p className="text-sm text-gray-600 text-center mb-4">
            {description}
          </p>
        )}

        {/* Warning list content */}
        {warningList && warningList.length > 0 && (
          <div className="bg-orange-50 rounded-lg p-4 mb-4">
            <p className="font-semibold text-sm text-[#1A1818] mb-3">
              Lưu ý khi sử dụng:
            </p>
            <div className="space-y-2">
              {warningList.map((warning, index) => (
                <div key={index} className="flex gap-2">
                  <span className="text-sm text-[#1A1818] font-medium">{index + 1}.</span>
                  <p className="text-sm text-gray-700 flex-1">
                    {warning}
                    {index === 1 && timeLimit && (
                      <>
                        {' '}
                        <span className="text-red-500 font-semibold">
                          {timeLimit} phút
                        </span>
                        .{' '}
                        <span className="text-red-500 font-semibold">
                          Không thể hoàn tác
                        </span>
                        .
                      </>
                    )}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Highlight text */}
        {highlightText && (
          <div className="px-3 py-2 rounded-md bg-orange-50 border border-orange-200 mb-4">
            <p className="text-sm text-orange-700 font-medium">
              {highlightText}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col gap-3 mt-6">
          {/* Primary Button */}
          <Button
            onClick={onPrimaryAction}
            disabled={primaryButtonDisabled || primaryButtonLoading}
            loading={primaryButtonLoading}
            className="w-full bg-[#F65D79] hover:bg-[#F65D79]/90 text-white font-semibold py-3 rounded-lg"
          >
            {primaryButtonText}
          </Button>

          {/* Secondary Button */}
          {secondaryButtonText && onSecondaryAction && (
            <Button
              onClick={onSecondaryAction}
              variant="secondary"
              className="w-full border border-[#F65D79] text-[#F65D79] font-semibold py-3 rounded-lg hover:bg-[#F65D79]/10"
            >
              {secondaryButtonText}
            </Button>
          )}
        </div>
      </div>
    </BottomSheet>
  );
};

export default WarningSheet;