import React from 'react';
import { BottomSheet, BottomSheetProps } from './BottomSheet';
import { Button } from '../Button/Button';
import successImage from '../../../assets/images/modals/success.png';

export interface SuccessSheetProps extends Omit<BottomSheetProps, 'children'> {
  // Success specific props
  message?: string;
  subMessage?: string;
  points?: number;
  showImage?: boolean;
  buttonText?: string;
  onButtonClick?: () => void;
}

export const SuccessSheet: React.FC<SuccessSheetProps> = ({
  message = 'Thành công!',
  subMessage,
  points,
  showImage = true,
  buttonText = 'Đóng',
  onButtonClick,
  title,
  ...rest
}) => {
  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick();
    } else {
      rest.onClose();
    }
  };

  return (
    <BottomSheet
      {...rest}
      title={title}
      height="auto"
      showCloseButton={false}
    >
      <div className="p-6 pb-safe">
        {/* Success Image */}
        {showImage && (
          <div className="flex justify-center mb-4">
            <img 
              src={successImage} 
              alt="Success" 
              className="w-20 h-20 object-contain"
            />
          </div>
        )}

        {/* Success Message */}
        <h2 
          className="text-2xl font-bold text-[#1A1818] text-center mb-2"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          {message}
        </h2>

        {/* Sub Message */}
        {subMessage && (
          <p 
            className="text-sm text-gray-600 text-center mb-4"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            {subMessage}
          </p>
        )}

        {/* Points Display */}
        {points && (
          <div className="flex justify-center items-center gap-2 mb-6">
            <div className="px-4 py-2 bg-[#F65D79]/10 rounded-full">
              <span className="text-lg font-bold text-[#F65D79]">
                +{points.toLocaleString()} POINT
              </span>
            </div>
          </div>
        )}

        {/* Action Button */}
        <Button
          onClick={handleButtonClick}
          fullWidth
          className="h-11"
          variant="primary"
        >
          {buttonText}
        </Button>
      </div>
    </BottomSheet>
  );
};

export default SuccessSheet;