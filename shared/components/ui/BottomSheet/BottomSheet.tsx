import React, { useEffect, useState } from 'react';
import { cn } from '../../../utils';

// Base BottomSheet Props
export interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  
  // Content
  title?: string;
  children?: React.ReactNode;
  
  // Height
  height?: 'auto' | 'sm' | 'md' | 'lg' | 'full';
  customHeight?: string;
  
  // Behavior
  closeOnBackdropClick?: boolean;
  showCloseButton?: boolean;
  showDragHandle?: boolean;
  
  // Styling
  className?: string;
  contentClassName?: string;
}

export const BottomSheet: React.FC<BottomSheetProps> = ({
  isOpen,
  onClose,
  title,
  children,
  height = 'auto',
  customHeight,
  closeOnBackdropClick = true,
  showCloseButton = true,
  showDragHandle = true,
  className,
  contentClassName,
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      // Small delay to trigger animation
      setTimeout(() => {
        setIsAnimating(true);
      }, 10);
      // Prevent body scroll when sheet is open
      document.body.style.overflow = 'hidden';
    } else {
      setIsAnimating(false);
      // Wait for animation to complete before hiding
      setTimeout(() => {
        setIsVisible(false);
      }, 300);
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const handleBackdropClick = () => {
    if (closeOnBackdropClick) {
      handleClose();
    }
  };

  // Get height class
  const getHeightClass = () => {
    if (customHeight) return customHeight;
    
    switch (height) {
      case 'sm':
        return 'max-h-[40vh]';
      case 'md':
        return 'max-h-[60vh]';
      case 'lg':
        return 'max-h-[80vh]';
      case 'full':
        return 'max-h-[95vh]';
      default:
        return 'max-h-[90vh]';
    }
  };

  if (!isVisible) return null;

  return (
    <div 
      className={cn(
        'fixed inset-0 z-drawer transition-all duration-300',
        isAnimating ? 'bg-black/50' : 'bg-transparent'
      )}
      onClick={handleBackdropClick}
    >
      {/* Bottom Sheet Container */}
      <div
        className={cn(
          'fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white rounded-t-2xl transition-transform duration-300 ease-out z-drawer',
          getHeightClass(),
          isAnimating ? 'translate-y-0' : 'translate-y-full',
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Drag Handle */}
        {showDragHandle && (
          <div className="flex justify-center pt-2 pb-1">
            <div className="w-12 h-1 bg-gray-300 rounded-full" />
          </div>
        )}

        {/* Header */}
        {(title || showCloseButton) && (
          <div className="relative px-4 py-3 border-b border-[#ECECEC]">
            {/* Title */}
            {title && (
              <h2 
                className="text-lg font-bold text-[#1A1818] text-center px-8"
                style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
              >
                {title}
              </h2>
            )}

            {/* Close Button */}
            {showCloseButton && (
              <button
                onClick={handleClose}
                className="absolute top-3 right-4 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-shadow"
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="w-6 h-6"
                >
                  <path
                    d="M18 6L6 18M6 6L18 18"
                    stroke="#1A1818"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Content */}
        <div 
          className={cn(
            'overflow-y-auto',
            contentClassName
          )}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

export default BottomSheet;