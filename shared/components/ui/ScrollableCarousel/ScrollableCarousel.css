/* ScrollableCarousel Custom Pagination Styles - Matching BannerCarousel */
.scrollable-carousel-pagination {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.scrollable-carousel-bullet {
  width: 12px;
  height: 2px;
  border-radius: 2px;
  background-color: #d1d5db;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-block;
}

.scrollable-carousel-bullet.swiper-pagination-bullet-active {
  background-color: #f65d79;
}

/* Override default Swiper pagination styles */
.scrollable-carousel .swiper-pagination {
  position: static;
  margin-top: 12px;
}

.scrollable-carousel .swiper-pagination-bullet {
  width: 12px;
  height: 2px;
  border-radius: 2px;
  background-color: #d1d5db;
  transition: all 0.3s ease;
  margin: 0 2px;
}

.scrollable-carousel .swiper-pagination-bullet-active {
  background-color: #f65d79;
}