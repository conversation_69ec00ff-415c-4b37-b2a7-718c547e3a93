import React, { useEffect, useMemo, useRef, useState } from "react";
import { ImageGallery } from "../ImageGallery";

export type MerchantContact = {
  phone?: string;
  email?: string;
  fanpage?: string; // facebook page url
  websiteURL?: string;
  zaloOA?: string;
  fanpageId?: string; // kept for parity, not used
};

export type MerchantInfoParamsTracking = {
  reward_name: string;
  reward_point?: number;
  reward_brandCurrency_point?: number;
  merchant_code?: string;
};

export type MerchantInfoProps = {
  onLayout?: (rect: { width: number; height: number }) => void;
  merchantCode: string;
  contact?: MerchantContact;
  description: string;
  paramsTracking?: MerchantInfoParamsTracking; // kept for parity, not used
  imageList: string[];
  onPressViewAll?: () => void; // kept for parity, not used here
  onPressViewItem?: (index: number) => void;
  labels?: {
    info?: string;
    intro?: string;
    contact?: string;
    copy?: string;
    copiedEmail?: string;
  };
};

const containerStyle: React.CSSProperties = {
  backgroundColor: "#FFFFFF",
  padding: 16,
  borderRadius: 8,
  boxShadow: "0 1px 2px rgba(0,0,0,0.06)",
};

const titleStyle: React.CSSProperties = {
  fontSize: 16,
  fontWeight: 600,
  marginBottom: 12,
};

// removed unused introTextStyle

const descriptionStyle: React.CSSProperties = {
  fontSize: 14,
  lineHeight: 22 / 14, // match ~22px line-height used elsewhere
  color: "#111827",
};

// Contact UI removed from this component

const LAST_IMAGE = 6;

const MerchantInfo: React.VFC<MerchantInfoProps> = ({
  onLayout,
  contact: _contact,
  description,
  merchantCode,
  imageList,
  labels,
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const descriptionRef = useRef<HTMLDivElement | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [canExpand, setCanExpand] = useState(false);
  // Contact display logic moved to BrandDetailSection; keep contact props for parity but don't render here

  useEffect(() => {
    if (!onLayout) return;
    const el = containerRef.current;
    if (!el) return;
    const report = () =>
      onLayout({
        width: el.offsetWidth ?? 0,
        height: el.offsetHeight ?? 0,
      });
    report();
    const RO: typeof ResizeObserver | undefined =
      typeof ResizeObserver !== "undefined" ? ResizeObserver : undefined;
    if (RO) {
      const ro = new RO(report);
      try {
        ro.observe(el);
      } catch {
        /* noop */
      }
      return () => {
        try {
          ro.disconnect();
        } catch {
          /* noop */
        }
      };
    }
    return;
  }, [onLayout]);

  // Measure description to determine if it exceeds 3 lines when collapsed
  useEffect(() => {
    const measure = () => {
      const el = descriptionRef.current;
      if (!el) return;
      if (!isExpanded) {
        const isTruncated = el.scrollHeight > el.clientHeight + 1;
        setCanExpand(isTruncated);
      } else {
        setCanExpand(true);
      }
    };
    measure();
    const onResize = () => measure();
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, [description, isExpanded]);

  const listImageShow = useMemo(
    () => (imageList || []).slice(0, LAST_IMAGE),
    [imageList]
  );

  return (
    <div
      ref={containerRef}
      style={containerStyle}
      data-merchant-code={merchantCode}
    >
      <div style={titleStyle}>{labels?.info ?? "Thông tin"}</div>
      <div className="text-sm font-bold text-[#1A1818] leading-[22px] font-['Archia']">
        {labels?.intro ?? "Giới thiệu"}
      </div>
      <div>
        <div
          ref={descriptionRef}
          style={{
            ...descriptionStyle,
            maxHeight: isExpanded ? "none" : 22 * 3,
            overflow: isExpanded ? "visible" : "hidden",
          }}
          className="font-['Archia']"
          dangerouslySetInnerHTML={{ __html: description }}
        />
        {(canExpand || isExpanded) && (
          <button
            type="button"
            onClick={() => setIsExpanded((prev) => !prev)}
            className="mt-2 text-sm font-medium text-primary-pink"
          >
            {isExpanded ? "Rút gọn" : "Xem thêm"}
          </button>
        )}
      </div>

      {listImageShow?.length ? (
        <ImageGallery
          images={listImageShow.map((src) => ({
            id: src,
            src,
          }))}
          title={null}
          className="pb-0 px-0"
        />
      ) : null}

      {/* Contact section has been moved to BrandDetailSection */}
    </div>
  );
};

export default MerchantInfo;
