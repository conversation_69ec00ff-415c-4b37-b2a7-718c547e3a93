# MissionSection Component

Component hiển thị danh sách nhiệm vụ (challenges) với khả năng hiển thị banner slider ở đầu.

## Props

### C<PERSON> bản

- `title`: Tiêu đề section (mặc định: "Nhiệm vụ có thưởng")
- `actionText`: Text cho nút action (mặc định: "Tất cả")
- `actionElement`: Element tùy chỉnh cho action
- `className`: CSS class tùy chỉnh

### Challenges

- `challenges`: Danh sách challenges từ props (nếu không dùng API)
- `onMissionClick`: Callback khi click vào challenge
- `onActionClick`: Callback khi click vào nút action

### API Configuration

- `limit`: <PERSON><PERSON> lượng challenges tối đa (mặc định: 10)
- `userProgressState`: Trạng thái tiến độ người dùng
- `home`: <PERSON><PERSON> phải màn hình home không
- `autoFetch`: Tự động fetch data (mặc định: true)
- `status`: Lọc theo status

### Banner Configuration

- `showBanners`: Hiển thị banner slider (mặc định: false)
- `bannerDisplayScreen`: Màn hình hiển thị banner (mặc định: "HOME")
- `onBannerClick`: Callback khi click vào banner

## Cách sử dụng

### Hiển thị với banner slider

```tsx
import { MissionSection } from "./MissionSection";

<MissionSection
  title="Nhiệm vụ hàng ngày"
  showBanners={true}
  bannerDisplayScreen="HOME"
  onBannerClick={(banner) => {
    console.log("Banner clicked:", banner);
    // Xử lý khi click banner
  }}
  onMissionClick={(challenge) => {
    console.log("Challenge clicked:", challenge);
    // Xử lý khi click challenge
  }}
/>;
```

### Hiển thị không có banner

```tsx
<MissionSection
  title="Nhiệm vụ đang thực hiện"
  userProgressState="RUNNING"
  showBanners={false}
/>
```

### Sử dụng với challenges từ props

```tsx
<MissionSection
  challenges={myChallenges}
  autoFetch={false}
  showBanners={true}
/>
```

## Tính năng

- **Banner Slider**: Hiển thị banners từ API với carousel
- **Image Error Handling**: Tự động hiển thị skeleton khi không thể load image
- **Auto-fetch**: Tự động lấy data từ API
- **Error Handling**: Xử lý lỗi và retry
- **Loading States**: Hiển thị skeleton loading
- **Responsive**: Tương thích với các kích thước màn hình
- **Pagination**: Hỗ trợ load more

## Dependencies

- `useChallenges` hook: Lấy danh sách challenges
- `useBanners` hook: Lấy danh sách banners
- `BannerSlider` component: Hiển thị banner carousel
- `MissionCard` component: Hiển thị từng challenge
