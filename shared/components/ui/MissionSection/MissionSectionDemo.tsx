import React, { useState } from "react";
import { MissionSection } from "./MissionSection";
import { useChallenges } from "../../../hooks/useChallenges";
import {
  ChallengeV3,
  ConditionChallengeType,
} from "../../../services/api/entertainmentService";

export const MissionSectionDemo: React.FC = () => {
  const [showApiData, setShowApiData] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>("");

  // Test different configurations
  const { challenges, loading, error, refetch } = useChallenges({
    limit: 5,
    home: true,
    autoFetch: false, // Don't auto-fetch for demo
    status: selectedStatus || undefined, // Apply status filter
  });

  const handleFetchData = async () => {
    setShowApiData(true);
    await refetch();
  };

  const handleMissionClick = (challenge: ChallengeV3) => {
    console.log("Mission clicked:", challenge._id);
    console.log("Challenge data:", challenge);
  };

  const handleActionClick = () => {
    console.log("Action clicked");
  };

  const handleStatusChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStatus(event.target.value);
  };

  // Sample challenges for static demo
  const sampleChallenges: ChallengeV3[] = [
    {
      _id: "demo-1",
      state: "PUBLISH",
      status: "RUNNING",
      challengeType: "SINGLE",
      joinChallengeType: "MANUAL",
      receiveGiftType: "AUTO",
      challengeImage:
        "https://via.placeholder.com/96x112/14A197/FFFFFF?text=Demo+1",
      displayChallengeName: "Nhiệm vụ demo 1",
      hexBackgroundColor: "#14A197",
      challengeNameColor: "#ffffff",
      index: 1,
      startDate: "2025-01-01T00:00:00.000Z",
      endDate: "2025-12-31T23:59:59.000Z",
      isDisplayRemainingTime: true,
      giftDisplay: [
        {
          giftTab: "+ 100 điểm",
          logoLink: undefined,
        },
      ],
      description: "Mô tả nhiệm vụ demo 1",
      conditionChallengeType: ConditionChallengeType.EARN,
      conditions: [],
      packageGift: {
        content: "Demo gift",
        stock: 100,
        remain: 100,
        gifts: [],
      },
      userProgress: {
        currentProgress: 0,
        maxProgress: 3,
        percentProgress: 0,
      },
      canUserJoinChallenge: true,
    },
    {
      _id: "demo-2",
      state: "PUBLISH",
      status: "RUNNING",
      challengeType: "SINGLE",
      joinChallengeType: "MANUAL",
      receiveGiftType: "AUTO",
      challengeImage:
        "https://via.placeholder.com/96x112/016A32/FFFFFF?text=Demo+2",
      displayChallengeName: "Nhiệm vụ demo 2",
      hexBackgroundColor: "#016A32",
      challengeNameColor: "#ffffff",
      index: 2,
      startDate: "2025-01-01T00:00:00.000Z",
      endDate: "2025-12-31T23:59:59.000Z",
      isDisplayRemainingTime: true,
      giftDisplay: [
        {
          giftTab: "+ 50.000 VND",
          logoLink: undefined,
        },
      ],
      description: "Mô tả nhiệm vụ demo 2",
      conditionChallengeType: ConditionChallengeType.EARN,
      conditions: [],
      packageGift: {
        content: "Demo gift",
        stock: 50,
        remain: 50,
        gifts: [],
      },
      userProgress: {
        currentProgress: 2,
        maxProgress: 5,
        percentProgress: 40,
      },
      canUserJoinChallenge: true,
    },
  ];

  return (
    <div className="p-4 space-y-6">
      <h1 className="text-2xl font-bold">MissionSection API Demo</h1>

      {/* Controls */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <button
            onClick={handleFetchData}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? "Đang tải..." : "Fetch Challenges từ API"}
          </button>

          <button
            onClick={() => setShowApiData(false)}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
          >
            Reset
          </button>
        </div>

        {/* Status Filter */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Filter theo Status:</label>
          <select
            value={selectedStatus}
            onChange={handleStatusChange}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value="">Tất cả</option>
            <option value="RUNNING">Đang chạy</option>
            <option value="COMPLETED">Hoàn thành</option>
            <option value="PENDING">Chờ xử lý</option>
          </select>
        </div>
      </div>

      {/* API Status */}
      {showApiData && (
        <div className="p-4 bg-gray-100 rounded-lg">
          <h3 className="font-semibold mb-2">API Status:</h3>
          <p>Loading: {loading ? "Yes" : "No"}</p>
          <p>Error: {error || "None"}</p>
          <p>Challenges count: {challenges.length}</p>
          <p>Status filter: {selectedStatus || "None"}</p>
          {challenges.length > 0 && (
            <div className="mt-2">
              <h4 className="font-medium">Raw API Data (first 2 items):</h4>
              <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(challenges.slice(0, 2), null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}

      {/* MissionSection with API */}
      {showApiData && (
        <div>
          <h3 className="text-lg font-semibold mb-4">
            MissionSection với API:
          </h3>
          <MissionSection
            title="Nhiệm vụ từ API"
            actionText="Xem tất cả"
            limit={5}
            home={true}
            autoFetch={false}
            status={selectedStatus || undefined}
            onMissionClick={handleMissionClick}
            onActionClick={handleActionClick}
          />
        </div>
      )}

      {/* MissionSection với dữ liệu tĩnh (fallback) */}
      {!showApiData && (
        <div>
          <h3 className="text-lg font-semibold mb-4">
            MissionSection với dữ liệu tĩnh:
          </h3>
          <MissionSection
            title="Nhiệm vụ mẫu"
            actionText="Xem tất cả"
            challenges={sampleChallenges}
            onMissionClick={handleMissionClick}
            onActionClick={handleActionClick}
          />
        </div>
      )}
    </div>
  );
};

export default MissionSectionDemo;
