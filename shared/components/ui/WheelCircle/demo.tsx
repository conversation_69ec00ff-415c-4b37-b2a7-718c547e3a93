import React, { useRef } from 'react';
import { WheelCircle, IActionParentProps, IWheelGift } from './index';

const DemoApp: React.FC = () => {
  const wheelRef = useRef<IActionParentProps>(null);

  // Sample data
  const sampleRewards: IWheelGift[] = [
    {
      index: 0,
      name: 'iPhone 15 Pro',
      type: 'LUCKY',
      logoUrl: 'https://via.placeholder.com/40x40/FF6B6B/FFFFFF?text=📱',
      label: 'Smartphone cao cấp',
      rate: 5,
      quantity: 1,
      _id: '1',
    },
    {
      index: 1,
      name: 'AirPods Pro',
      type: 'LUCKY',
      logoUrl: 'https://via.placeholder.com/40x40/4ECDC4/FFFFFF?text=🎧',
      label: 'Tai nghe không dây',
      rate: 10,
      quantity: 5,
      _id: '2',
    },
    {
      index: 2,
      name: '100.000 VNĐ',
      type: 'LUCKY',
      logoUrl: 'https://via.placeholder.com/40x40/45B7D1/FFFFFF?text=💰',
      label: 'Tiền mặt',
      rate: 20,
      quantity: 10,
      _id: '3',
    },
    {
      index: 3,
      name: 'Voucher 50K',
      type: 'LUCKY',
      logoUrl: 'https://via.placeholder.com/40x40/96CEB4/FFFFFF?text=🎫',
      label: 'Phiếu giảm giá',
      rate: 30,
      quantity: 20,
      _id: '4',
    },
    {
      index: 4,
      name: 'Chúc bạn may mắn',
      type: 'UNLUCKY',
      logoUrl: 'https://via.placeholder.com/40x40/FFEAA7/FFFFFF?text=😢',
      label: 'Không trúng thưởng',
      rate: 35,
      quantity: 100,
      _id: '5',
    },
  ];

  const handleFinish = (gift: IWheelGift) => {
    alert(`Chúc mừng! Bạn đã trúng: ${gift.name}`);
  };

  const handleSpin = () => {
    // Random select a prize
    const randomIndex = Math.floor(Math.random() * sampleRewards.length);
    wheelRef.current?.onPlay(randomIndex);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 flex flex-col items-center justify-center p-8">
      <div className="bg-white rounded-3xl shadow-2xl p-8 max-w-md w-full">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
          🎯 Vòng Quay May Mắn
        </h1>
        
        <div className="flex justify-center mb-8">
          <WheelCircle
            ref={wheelRef}
            rewards={sampleRewards}
            logo="https://via.placeholder.com/55x60/F7CC15/FFFFFF?text=🎯"
            backgroundColor="#F65D79"
            onFinish={handleFinish}
            size={300}
          />
        </div>

        <div className="text-center">
          <button
            onClick={handleSpin}
            className="bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-white font-bold py-4 px-8 rounded-full text-lg shadow-lg transform hover:scale-105 transition-all duration-200">
            🎲 QUAY NGAY!
          </button>
        </div>

        <div className="mt-8 bg-gray-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-700 mb-3">Giải thưởng:</h3>
          <div className="space-y-2">
            {sampleRewards.map((reward, index) => (
              <div key={reward._id} className="flex items-center justify-between text-sm">
                <span className="flex items-center">
                  <img 
                    src={reward.logoUrl} 
                    alt={reward.name}
                    className="w-6 h-6 mr-2 rounded"
                  />
                  {reward.name}
                </span>
                <span className="text-gray-500">{reward.rate}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-8 text-white text-center max-w-2xl">
        <h2 className="text-xl font-semibold mb-4">🚀 Cách sử dụng WheelCircle Component</h2>
        <div className="bg-black bg-opacity-20 rounded-lg p-4 text-left">
          <pre className="text-sm overflow-x-auto">
{`import { WheelCircle, IActionParentProps } from './WheelCircle';

const MyComponent = () => {
  const wheelRef = useRef<IActionParentProps>(null);
  
  const handleSpin = () => {
    const randomIndex = Math.floor(Math.random() * rewards.length);
    wheelRef.current?.onPlay(randomIndex);
  };

  return (
    <WheelCircle
      ref={wheelRef}
      rewards={rewards}
      logo="logo-url"
      backgroundColor="#F65D79"
      onFinish={(gift) => console.log('Won:', gift)}
      size={300}
    />
  );
};`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default DemoApp;
