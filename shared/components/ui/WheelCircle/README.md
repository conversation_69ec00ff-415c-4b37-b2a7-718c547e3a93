# WheelCircle Component

Một component vòng quay may mắn hoàn toàn độc lập cho web, được tạo từ React và D3-shape.

## 🎯 Tính năng

- ✅ **Hoàn toàn độc lập**: Chỉ cần React và d3-shape
- ✅ **Responsive**: Tự động điều chỉnh kích thước theo màn hình
- ✅ **Animation mượt mà**: Hiệu ứng quay với CSS transitions
- ✅ **Customizable**: <PERSON><PERSON> thể tùy chỉnh màu sắc, kích thước, logo
- ✅ **TypeScript**: Hỗ trợ đầy đủ TypeScript
- ✅ **Ref API**: Điều khiển từ component cha thông qua ref

## 📦 Dependencies

```json
{
  "react": "^18.0.0",
  "d3-shape": "^3.2.0"
}
```

## 🚀 Cách sử dụng

### Basic Usage

```tsx
import React, { useRef } from 'react';
import { WheelCircle, IActionParentProps, IWheelGift } from './WheelCircle';

const MyComponent = () => {
  const wheelRef = useRef<IActionParentProps>(null);

  const rewards: IWheelGift[] = [
    {
      index: 0,
      name: 'iPhone 15 Pro',
      type: 'LUCKY',
      logoUrl: 'https://example.com/iphone.png',
      label: 'Smartphone cao cấp',
      rate: 5,
      quantity: 1,
      _id: '1',
    },
    // ... more rewards
  ];

  const handleFinish = (gift: IWheelGift) => {
    console.log('Trúng thưởng:', gift.name);
  };

  const handleSpin = () => {
    const randomIndex = Math.floor(Math.random() * rewards.length);
    wheelRef.current?.onPlay(randomIndex);
  };

  return (
    <div>
      <WheelCircle
        ref={wheelRef}
        rewards={rewards}
        logo="https://example.com/logo.png"
        backgroundColor="#F65D79"
        onFinish={handleFinish}
        size={300}
      />
      <button onClick={handleSpin}>QUAY NGAY!</button>
    </div>
  );
};
```

## 📋 Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `rewards` | `IWheelGift[]` | ✅ | - | Danh sách giải thưởng |
| `onFinish` | `(gift: IWheelGift) => void` | ✅ | - | Callback khi quay xong |
| `logo` | `string` | ✅ | - | URL logo hiển thị ở giữa |
| `backgroundColor` | `string` | ✅ | - | Màu nền của vòng quay |
| `size` | `number` | ❌ | `perWidth(80)` | Kích thước vòng quay (px) |

## 🎮 Ref API

```tsx
interface IActionParentProps {
  onPlay: (index: number) => void;
}
```

- `onPlay(index)`: Bắt đầu quay và dừng tại giải thưởng có index được chỉ định

## 🏆 IWheelGift Interface

```tsx
interface IWheelGift {
  index: number;           // Vị trí trong mảng
  name: string;           // Tên giải thưởng
  type: string;           // Loại giải ('LUCKY', 'UNLUCKY', etc.)
  logoUrl: string;        // URL hình ảnh giải thưởng
  label: string;          // Mô tả giải thưởng
  rate: number;           // Tỷ lệ trúng (%)
  quantity: number;       // Số lượng
  _id: string;           // ID duy nhất
  brandCurrencyPoint?: number;
  unitPoint?: number;
}
```

## 🎨 Customization

### Màu sắc

Component sử dụng các màu mặc định:
- Viền ngoài: `#d8b228`
- Màu chữ chẵn: `#F65D79` 
- Màu chữ lẻ: `#1A1818`
- Màu segment: `['#FFF3BE', '#F7CC15', 'white']`

### Kích thước

- Mặc định: `perWidth(80)` (80% chiều rộng màn hình)
- Custom: Truyền prop `size={300}` để set kích thước cố định

## 🔧 Development

```bash
# Install dependencies
npm install react d3-shape

# Copy component files
cp -r web-components/WheelCircle ./src/components/

# Import and use
import { WheelCircle } from './src/components/WheelCircle';
```

## 📱 Responsive

Component tự động responsive dựa trên:
- `perWidth()`: Tính % theo chiều rộng màn hình
- `resWidth()`: Scale theo tỷ lệ màn hình (base: 375px)

## ⚡ Performance

- Sử dụng `memo()` để tránh re-render không cần thiết
- `useMemo()` cho các tính toán phức tạp
- `useCallback()` cho event handlers
- CSS transitions thay vì JavaScript animation

## 🐛 Troubleshooting

### Vòng quay không hiển thị
- Kiểm tra `rewards` array có dữ liệu
- Đảm bảo `size` > 0
- Kiểm tra CSS container có đủ không gian

### Animation không mượt
- Kiểm tra browser hỗ trợ CSS transitions
- Đảm bảo không có quá nhiều elements khác đang animate

### Images không load
- Kiểm tra URL trong `logoUrl` và `logo` prop
- Đảm bảo CORS được cấu hình đúng cho external images
