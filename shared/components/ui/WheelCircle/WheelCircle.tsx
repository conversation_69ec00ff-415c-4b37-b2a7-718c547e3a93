import React, {
  useRef,
  useImperativeHandle,
  forwardRef,
  memo,
  useMemo,
  useCallback,
  useState,
} from "react";
import * as d3Shape from "d3-shape";

// Types
export interface IWheelGift {
  index: number;
  name: string;
  type: string;
  logoUrl: string;
  label: string;
  rate?: number;
  quantity?: number;
  _id: string;
  brandCurrencyPoint?: number | null;
  unitPoint?: number | null;
}

export interface IActionParentProps {
  onPlay: (index: number) => void;
}

export interface WheelCircleProps {
  onFinish: (gift: IWheelGift) => void;
  rewards: IWheelGift[];
  logo: string;
  backgroundColor: string;
  size?: number;
}

// Constants - replicated from original
const ONE_TURN = 360;
const DURATION8 = 8000;
const SECONDS = 1000;

// Responsive function
const perWidth = (percentage: number): number => {
  if (typeof window !== "undefined") {
    return (window.innerWidth * percentage) / 100;
  }
  return (375 * percentage) / 100; // fallback for SSR
};

const resWidth = (size: number): number => {
  if (typeof window !== "undefined") {
    const shortDimension = Math.min(window.innerWidth, window.innerHeight);
    return Math.round((shortDimension / 375) * size);
  }
  return size; // fallback for SSR
};

// Colors - replicated from original
const Colors = {
  primary2: "#d8b228",
  primaryPink: "#F65D79",
  textColor: "#1A1818",
};

const WheelCircle = (
  props: WheelCircleProps,
  ref?: React.Ref<IActionParentProps>
) => {
  const { rewards, logo, backgroundColor, size = perWidth(80) } = props;
  const [rotation, setRotation] = useState(0);
  const [isSpinning, setIsSpinning] = useState(false);
  const wheelRef = useRef<HTMLDivElement>(null);

  const SIZE_WHEEL_BASE = size;
  const SCREEN_WIDTH = SIZE_WHEEL_BASE + 40;

  const numberOfSegments = rewards.length;
  const data = Array.from({ length: numberOfSegments }).fill(1) as number[];
  const angleBySegment = ONE_TURN / numberOfSegments;
  const angleOffset = angleBySegment / 2;
  const arcs = d3Shape.pie()(data);

  const colorsPie = ["#FFF3BE", "#F7CC15", "white"];
  const isDuplicateFill = data.length % colorsPie.length === 1;

  const baseRotation = (ONE_TURN * DURATION8) / SECONDS;

  // D3 path generators
  const drawPath = d3Shape
    .arc()
    .padAngle(0.0)
    .outerRadius(SIZE_WHEEL_BASE / 2)
    .innerRadius(50);

  const drawPathSmall = d3Shape
    .arc()
    .padRadius(0.1)
    .padAngle(0.0)
    .outerRadius(SIZE_WHEEL_BASE / 2 - 5)
    .innerRadius(40);

  const configPathForWheel = useMemo(() => {
    return arcs.map((arc: any, index: number) => {
      let colorFill = colorsPie[index % colorsPie.length];
      if (index === data.length - 1 && isDuplicateFill) {
        colorFill = colorsPie[(index % colorsPie.length) + 1];
      }

      return {
        key: index,
        centroid: drawPathSmall.centroid(arc),
        path: drawPath(arc),
        pathSmall: drawPathSmall(arc),
        color: colorFill,
        value: rewards[index].name,
        typeReward: rewards[index].type === "UNLUCKY",
        img: rewards[index].logoUrl,
      };
    });
  }, [arcs, data.length, isDuplicateFill, rewards, drawPath, drawPathSmall]);

  const startWheel = useCallback(
    (index: number) => {
      if (isSpinning) return;

      const { onFinish } = props;
      setIsSpinning(true);

      const adjustOffset = (numberOfSegments - index) * angleBySegment;
      const currentRotation = rotation / baseRotation;
      const rotationTime = Math.floor(currentRotation) + 1;
      const targetValue = baseRotation * rotationTime + adjustOffset;

      setRotation(targetValue);

      // Simulate animation completion
      setTimeout(() => {
        setIsSpinning(false);
        onFinish(rewards[index]);
      }, DURATION8);
    },
    [
      isSpinning,
      props,
      numberOfSegments,
      angleBySegment,
      rotation,
      baseRotation,
      rewards,
    ]
  );

  const actionParent = useCallback(
    (): IActionParentProps => ({
      onPlay: startWheel,
    }),
    [startWheel]
  );

  useImperativeHandle(ref, actionParent);

  const getCoordinatesDotWheel = (
    centerX: number,
    centerY: number,
    radius: number,
    angleInDegrees: number
  ): { x: number; y: number } => {
    const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180;
    return {
      x: centerX + radius * Math.cos(angleInRadians),
      y: centerY + radius * Math.sin(angleInRadians),
    };
  };

  const renderWheelSegments = () => {
    return configPathForWheel.map((arc, i) => {
      const pathAround = arc.path || "";
      const pathAngle = arc.pathSmall || "";
      const [x, y] = arc.centroid;
      const angleInRadians = (Math.abs(angleBySegment - 90) * Math.PI) / 180.0;
      const sizeText =
        angleInRadians === 0 ? 0 : angleBySegment / angleInRadians + 30;

      return (
        <g key={`arc-${arc.key}`}>
          {/* Outer path */}
          <path d={pathAround} strokeWidth={2} fill={Colors.primary2} />
          {/* Inner path */}
          <path d={pathAngle} strokeWidth={2} fill={arc.color} />
          {/* Content group */}
          <g
            transform={`rotate(${
              (i * ONE_TURN) / numberOfSegments + angleOffset
            } ${x} ${y})`}
          >
            {/* Gift image */}
            <image x={x - 20} y={y + 5} width={40} height={40} href={arc.img} />
            {/* Gift text */}
            <text
              x={x}
              y={y - 20}
              fill={i % 2 === 0 ? Colors.primaryPink : Colors.textColor}
              textAnchor="middle"
              fontSize={resWidth(14)}
              fontWeight="600"
            >
              <tspan x={x - (sizeText - sizeText / 3) / 2 - 5} dy="0">
                {arc.value}
              </tspan>
            </text>
          </g>
        </g>
      );
    });
  };

  const renderCoordinatesDots = () => {
    return configPathForWheel.map((arc, i) => {
      const dotPos = getCoordinatesDotWheel(
        SCREEN_WIDTH / 2 - 50,
        SCREEN_WIDTH / 2 - 50,
        SIZE_WHEEL_BASE / 2 + 8,
        angleBySegment * i
      );

      return (
        <circle
          key={arc.key}
          cx={dotPos.x}
          cy={dotPos.y}
          r={10}
          fill="#FFD700"
          stroke="#F7CC15"
          strokeWidth={2}
        />
      );
    });
  };

  return (
    <div
      className="relative flex items-center justify-center"
      style={{
        width: `${SCREEN_WIDTH}px`,
        height: `${SCREEN_WIDTH}px`,
      }}
    >
      {/* Main wheel container with shadow */}
      <div
        className="relative rounded-full"
        style={{
          width: `${SIZE_WHEEL_BASE}px`,
          height: `${SIZE_WHEEL_BASE}px`,
          backgroundColor,
          boxShadow: "0 8px 16px rgba(0,0,0,0.1)",
        }}
      >
        {/* Wheel SVG */}
        <div
          ref={wheelRef}
          className="absolute inset-0 rounded-full overflow-hidden"
          style={{
            transform: `rotate(${rotation}deg)`,
            transition: isSpinning
              ? `transform ${DURATION8}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`
              : "none",
          }}
        >
          <svg
            width={SCREEN_WIDTH}
            height={SCREEN_WIDTH}
            viewBox={`0 0 ${SCREEN_WIDTH} ${SCREEN_WIDTH}`}
            style={{
              transform: `rotate(-${angleOffset}deg)`,
              position: "absolute",
              top: "50%",
              left: "50%",
              marginTop: `-${SCREEN_WIDTH / 2}px`,
              marginLeft: `-${SCREEN_WIDTH / 2}px`,
            }}
          >
            <g
              transform={`translate(${SCREEN_WIDTH / 2}, ${SCREEN_WIDTH / 2})`}
            >
              {renderWheelSegments()}
            </g>
            {renderCoordinatesDots()}
          </svg>
        </div>

        {/* Center logo */}
        <div
          className="absolute inset-0 flex items-center justify-center"
          style={{ zIndex: 10 }}
        >
          {logo ? (
            <img
              src={logo}
              alt="Wheel center logo"
              className="object-contain"
              style={{
                width: `${resWidth(55)}px`,
                height: `${resWidth(60)}px`,
              }}
            />
          ) : (
            <div
              className="bg-white rounded-full flex items-center justify-center"
              style={{
                width: `${resWidth(55)}px`,
                height: `${resWidth(60)}px`,
              }}
            >
              <span className="text-2xl">🎯</span>
            </div>
          )}
        </div>
      </div>

      {/* Knob/Pointer */}
      <div
        className="absolute top-0 z-20"
        style={{
          width: `${resWidth(20)}px`,
          height: `${resWidth(25)}px`,
          transform: isSpinning ? "rotate(35deg)" : "rotate(0deg)",
          transition: "transform 0.3s ease",
        }}
      >
        <div
          className="w-0 h-0 mx-auto"
          style={{
            borderLeft: `${resWidth(10)}px solid transparent`,
            borderRight: `${resWidth(10)}px solid transparent`,
            borderTop: `${resWidth(25)}px solid #F65D79`,
          }}
        />
      </div>
    </div>
  );
};

export default memo(forwardRef(WheelCircle));
