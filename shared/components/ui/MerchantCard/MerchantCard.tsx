import React, { useState } from "react";
import { cn } from "../../../utils";
import { BasedImage } from "../BasedImage";
import { merchantAPI } from "../../../services/api/merchant/api";
import { MerchantType } from "../../../services/api/merchant/types";

export interface MerchantCardProps {
  merchant: MerchantType;
  onClick?: (merchant: MerchantType) => void;
  onFavoriteToggle?: (merchant: MerchantType) => void;
  className?: string;
}

export const MerchantCard: React.FC<MerchantCardProps> = ({
  merchant,
  onClick,
  onFavoriteToggle,
  className,
}) => {
  const [isFavorite, setIsFavorite] = useState(merchant.isFavorite || false);
  const [isLoading, setIsLoading] = useState(false);

  const handleFavoriteClick = async (e: React.MouseEvent) => {
    e.stopPropagation();

    console.log("merchant", merchant);

    if (!merchant.code) {
      console.warn("Merchant code is missing, cannot toggle favorite");
      return;
    }

    if (isLoading) return;

    const newFavoriteState = !isFavorite;
    setIsLoading(true);

    try {
      const action = newFavoriteState ? "ADD" : "REMOVE";
      console.log("action", action);
      const response = await merchantAPI.addMerchantToFavorite(
        [merchant.code],
        action
      );

      if (response.status.success) {
        setIsFavorite(newFavoriteState);
        onFavoriteToggle?.({ ...merchant, isFavorite: newFavoriteState });
      } else {
        console.error("Failed to toggle favorite:", response.status.message);
        // Revert UI state if API call failed
        setIsFavorite(!newFavoriteState);
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      // Revert UI state if API call failed
      setIsFavorite(!newFavoriteState);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      onClick={() => onClick?.(merchant)}
      className={cn(
        "bg-white rounded-lg shadow-[0px_8px_32px_0px_rgba(26,24,24,0.1)] cursor-pointer transition-shadow hover:shadow-[0px_12px_40px_0px_rgba(26,24,24,0.15)]",
        "w-full flex flex-col",
        className
      )}
    >
      {/* Brand banner with logo */}
      <div className="relative">
        {/* Banner container with favorite button */}
        <div className="relative h-[116px] rounded-t-lg flex">
          <BasedImage
            source={merchant.banner2 || merchant.banner}
            alt={`${merchant.name} banner`}
            resizeMode="cover"
            className="w-full h-full rounded-t-lg"
          />

          {/* Favorite button overlay */}
          <button
            onClick={handleFavoriteClick}
            disabled={isLoading}
            className={cn(
              "absolute top-2 right-2 w-6 h-6 flex items-center justify-center transition-all",
              isLoading && "opacity-50 cursor-not-allowed"
            )}
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 18 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="transition-all"
            >
              <path
                d="M13.3125 0.75C15.5163 0.75 17.25 2.45749 17.25 4.5C17.25 7.79546 15.1875 10.6091 12.9883 12.6592C11.9011 13.6727 10.8108 14.4708 9.99121 15.0156C9.58236 15.2874 9.24255 15.4951 9.00684 15.6338C9.00472 15.635 9.0021 15.6365 9 15.6377C8.9979 15.6365 8.99528 15.635 8.99316 15.6338C8.75745 15.4951 8.41764 15.2874 8.00879 15.0156C7.18917 14.4708 6.09894 13.6727 5.01172 12.6592C2.8125 10.6091 0.75 7.79546 0.75 4.5C0.75 2.45749 2.48373 0.75 4.6875 0.75C6.3296 0.75 7.72071 1.70442 8.31445 3.03809L9 4.57812L9.68555 3.03809C10.2793 1.70442 11.6704 0.75 13.3125 0.75Z"
                fill={isFavorite ? "#F65D79" : "transparent"}
                fillOpacity={isFavorite ? "1" : "0.3"}
                stroke={isFavorite ? "#F65D79" : "#CACACA"}
                strokeWidth="1.5"
              />
            </svg>
          </button>
        </div>

        {/* Logo positioned at bottom center */}
        <div className="flex justify-center -mt-4 relative z-10">
          <div className="w-8 h-8 rounded-full border-2 border-white bg-white overflow-hidden">
            <BasedImage
              source={merchant.logo}
              alt={`${merchant.name} logo`}
              resizeMode="cover"
              className="w-full h-full rounded-full"
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex flex-col items-center gap-1 pb-3 pt-2 px-3">
        <h3 className="text-sm font-semibold text-[#1A1818] text-center leading-[1.43] font-archia">
          {merchant.name}
        </h3>
        <p className="text-xs font-normal text-[#5A5A5A] leading-[1.5] font-archia">
          {merchant.numberOfReward} ưu đãi
        </p>
      </div>
    </div>
  );
};

export default MerchantCard;
