import React from 'react';
import { FlashSaleCountdown } from '../FlashSaleCountdown';
import { cn } from '../../../utils';
import type { FlashSaleCampaign } from '../../../types/reward';

interface InformFlashSaleProps {
  campaign: FlashSaleCampaign | null;
  rewardStartTime?: string;
  rewardName?: string;
  merchantName?: string;
  callback?: () => void;
  canPurchase?: boolean;
  currentDateTime: string;
  className?: string;
}

export const InformFlashSale: React.FC<InformFlashSaleProps> = ({
  campaign,
  rewardStartTime,
  callback,
  canPurchase = true,
  currentDateTime,
  className
}) => {
  if (!campaign) return null;

  const {
    campaignEndTime,
    campaignStartTime,
    campaignCountdownTime,
    campaignType = 'flashSale'
  } = campaign;

  // Check time periods
  const now = new Date(currentDateTime);
  const countdownTime = new Date(campaignCountdownTime || '');
  const startTime = new Date(campaignStartTime || '');
  const endTime = new Date(campaignEndTime || '');

  const inCountdown = now >= countdownTime && now < startTime;
  const inFlashSale = now >= startTime && now <= endTime;
  const isBeforeSale = rewardStartTime ? now < new Date(rewardStartTime) : false;

  // Don't show if campaign has ended
  if (now > endTime) return null;

  // Don't show for theme campaigns during certain periods
  if (campaignType === 'theme') {
    if (inFlashSale) return null;
    if (inCountdown && !isBeforeSale) return null;
  }

  const formatDateTime = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
      day: '2-digit',
      month: '2-digit'
    });
  };

  return (
    <>
      <div className={cn(
        'bg-[#feefbe] flex items-center justify-center px-4',
        inFlashSale ? 'py-3' : 'py-4',
        !canPurchase && 'border-b border-[#ECECEC]',
        className
      )}>
        {isBeforeSale && !inCountdown && !inFlashSale ? (
          <p className="text-sm font-semibold text-[#1A1818] text-center">
            Bắt đầu bán lúc {formatDateTime(rewardStartTime || '')}
          </p>
        ) : inCountdown && campaignType === 'flashSale' ? (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="text-[#F65D79]">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="currentColor"/>
              </svg>
              <p className="text-sm font-semibold text-[#1A1818]">
                Flash Sale bắt đầu lúc {formatDateTime(campaignStartTime)}
              </p>
            </div>
          </div>
        ) : inFlashSale ? (
          <FlashSaleCountdown
            campaignCountdownTime={campaignCountdownTime}
            campaignStartTime={campaignStartTime}
            campaignEndTime={campaignEndTime}
            currentDateTime={currentDateTime}
            callback={callback}
            campaignType={campaignType}
          />
        ) : null}
      </div>

      {!canPurchase && (
        <div className="bg-[#FED932] px-4 py-2">
          <p className="text-sm font-semibold text-[#1A1818] text-center">
            Chỉ xem, không thể mua
          </p>
        </div>
      )}
    </>
  );
};

export default InformFlashSale;