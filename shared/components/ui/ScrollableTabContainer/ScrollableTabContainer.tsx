import React, { useEffect, useRef, useState } from 'react';
import { cn } from '../../../utils';

export interface ScrollableTabItem {
  id: string;
  label: string;
  badge?: boolean;
  badgeText?: string;
  content: React.ReactNode;
}

export interface ScrollableTabContainerProps {
  tabs: ScrollableTabItem[];
  activeTabId: string;
  onTabChange: (tabId: string) => void;
  className?: string;
  containerClassName?: string;
  contentClassName?: string;
}

export const ScrollableTabContainer: React.FC<ScrollableTabContainerProps> = ({
  tabs,
  activeTabId,
  onTabChange,
  className = '',
  containerClassName = '',
  contentClassName = '',
}) => {
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);

  // Initialize section refs array
  useEffect(() => {
    sectionRefs.current = sectionRefs.current.slice(0, tabs.length);
  }, [tabs.length]);

  // Scroll to section function
  const scrollToSection = (index: number) => {
    console.log('📜 scrollToSection called with index:', index);
    const sectionRef = sectionRefs.current[index];
    const scrollContainer = scrollContainerRef.current;
    
    console.log('📜 Elements found:', {
      sectionRef: !!sectionRef,
      scrollContainer: !!scrollContainer,
      sectionRefsLength: sectionRefs.current.length
    });
    
    if (sectionRef && scrollContainer) {
      setIsScrolling(true);
      
      // Get the section's offset relative to the scroll container
      const containerRect = scrollContainer.getBoundingClientRect();
      const sectionRect = sectionRef.getBoundingClientRect();
      const scrollTop = scrollContainer.scrollTop;
      const targetScrollTop = scrollTop + (sectionRect.top - containerRect.top);
      
      console.log('📜 Scroll calculation:', {
        containerRect: { top: containerRect.top, height: containerRect.height },
        sectionRect: { top: sectionRect.top, height: sectionRect.height },
        currentScrollTop: scrollTop,
        targetScrollTop,
        offset: sectionRect.top - containerRect.top,
        scrollHeight: scrollContainer.scrollHeight,
        clientHeight: scrollContainer.clientHeight,
        canScroll: scrollContainer.scrollHeight > scrollContainer.clientHeight
      });
      
      // Force scroll even if container thinks it can't scroll
      if (scrollContainer.scrollHeight > scrollContainer.clientHeight || targetScrollTop !== scrollTop) {
        scrollContainer.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth'
        });
      } else {
        console.log('⚠️ Cannot scroll - content not tall enough or already at target position');
      }
      
      console.log('📜 Scroll initiated to:', targetScrollTop);
      
      // Check if scroll actually happened after a brief delay
      setTimeout(() => {
        console.log('📜 Scroll completed. Current scrollTop:', scrollContainer.scrollTop);
        setIsScrolling(false);
      }, 800);
    } else {
      console.log('❌ scrollToSection failed - missing elements');
    }
  };

  // Update current index when activeTabId changes externally
  useEffect(() => {
    const activeIndex = tabs.findIndex(tab => tab.id === activeTabId);
      console.log(activeIndex)
    if (activeIndex !== -1 && activeIndex !== currentIndex) {
      setCurrentIndex(activeIndex);
      scrollToSection(activeIndex);
    }
  }, [activeTabId, tabs, currentIndex]);


  // Handle scroll to update active tab
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
       console.log(scrollContainer)
    if (!scrollContainer || isScrolling) return;

    const handleScroll = () => {
      const containerHeight = scrollContainer.clientHeight;
      const threshold = containerHeight * 0.3; // 30% of container height
      
      console.log('🔍 Scroll event:', {
        containerHeight,
        threshold,
        scrollTop: scrollContainer.scrollTop,
        sectionRefsLength: sectionRefs.current.length,
        currentIndex,
        isScrolling
      });

      // Find which section is currently most visible
      for (let i = sectionRefs.current.length - 1; i >= 0; i--) {
        const section = sectionRefs.current[i];
        if (section) {
          const sectionRect = section.getBoundingClientRect();
          const containerRect = scrollContainer.getBoundingClientRect();
          const sectionTop = sectionRect.top - containerRect.top;
          
          console.log(`📍 Section ${i} (${tabs[i]?.id}):`, {
            sectionTop,
            threshold,
            isVisible: sectionTop <= threshold,
            currentIndex
          });
          
          if (sectionTop <= threshold) {
            if (i !== currentIndex) {
              console.log(`✅ Updating active tab from ${currentIndex} to ${i} (${tabs[i]?.id})`);
              setCurrentIndex(i);
              onTabChange(tabs[i].id);
            }
            break;
          }
        }
      }
    };

    scrollContainer.addEventListener('scroll', handleScroll, { passive: true });
    return () => scrollContainer.removeEventListener('scroll', handleScroll);
  }, [isScrolling, currentIndex, tabs, onTabChange]);

  const handleTabClick = (tabId: string, index: number) => {
    console.log('🖱️ Tab clicked:', { tabId, index, currentIndex });
    setCurrentIndex(index);
    onTabChange(tabId);
    scrollToSection(index);
  };

  return (
    <div className={cn('flex flex-col', containerClassName)}>
      {/* Tab Navigation */}
      <div className={cn('bg-white border-b border-[#ECECEC]', className)}>
        <div className="flex w-full">
          {tabs.map((tab, index) => {
            const isActive = tab.id === activeTabId;
            return (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab.id, index)}
                className={cn(
                  'flex-1 flex items-center justify-center gap-1 px-4 py-3 text-sm font-medium transition-colors relative',
                  'border-b-2 border-transparent',
                  isActive
                    ? 'text-[#007AFF] border-[#007AFF]'
                    : 'text-[#8E8E93] hover:text-[#007AFF]'
                )}
              >
                <span className="flex items-center gap-1">
                  {tab.label}
                  {tab.badge && tab.badgeText && (
                    <span className="text-xs px-1.5 py-0.5 bg-[#F65D79] text-white rounded-full min-w-[16px] h-4 flex items-center justify-center font-medium">
                      {tab.badgeText}
                    </span>
                  )}
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Scrollable Content */}
      <div className={cn('flex-1 overflow-y-auto', contentClassName)} ref={scrollContainerRef}>
        {tabs.map((tab, index) => (
          <div 
            key={tab.id}
            ref={(el) => {
              sectionRefs.current[index] = el;
            }}
            className="w-full"
          >
            {tab.content}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ScrollableTabContainer;