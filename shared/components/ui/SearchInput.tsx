import React from 'react';
import SearchIcon from '../../assets/icons/search-icon.svg';
import CloseIcon from '../../assets/icons/close-icon.svg';

export interface SearchInputProps {
  value?: string;
  placeholder?: string;
  disabled?: boolean;
  autoFocus?: boolean;
  className?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  value,
  placeholder = 'Tìm kiếm...',
  disabled = false,
  autoFocus = false,
  className = '',
  onChange,
  onSearch,
  onFocus,
  onBlur,
}) => {
  const [internalValue, setInternalValue] = React.useState('');
  const [isFocused, setIsFocused] = React.useState(false);
  const isControlled = value !== undefined;
  const currentValue = isControlled ? value : internalValue;

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    
    if (!isControlled) {
      setInternalValue(newValue);
    }
    
    onChange?.(newValue);
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      onSearch?.(currentValue);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleClear = () => {
    const newValue = '';
    
    if (!isControlled) {
      setInternalValue(newValue);
    }
    
    onChange?.(newValue);
  };

  return (
    <div className={`relative w-full ${className}`}>
      {/* Search Icon */}
      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#9A9A9A] pointer-events-none">
        <img src={SearchIcon} alt="Search" className="w-5 h-5" />
      </div>

      {/* Input Field */}
      <input
        type="text"
        value={currentValue}
        placeholder={placeholder}
        disabled={disabled}
        autoFocus={autoFocus}
        className={`
          w-full h-12 pl-11 pr-10
          bg-white border border-gray-200 rounded-full
          text-[#1A1818] text-sm font-medium
          placeholder:text-[#9A9A9A] placeholder:font-normal
          focus:outline-none focus:border-[#F65D79] focus:ring-1 focus:ring-[#F65D79]/20
          disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed
          transition-all duration-200
          ${isFocused ? 'border-[#F65D79]' : 'border-gray-200'}
        `}
        onChange={handleChange}
        onKeyPress={handleKeyPress}
        onFocus={handleFocus}
        onBlur={handleBlur}
        aria-label="Search input"
      />

      {/* Clear Button */}
      {currentValue && !disabled && (
        <button
          onClick={handleClear}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#9A9A9A] hover:text-[#1A1818] transition-colors"
          aria-label="Clear search"
        >
          <img src={CloseIcon} alt="Clear" className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};