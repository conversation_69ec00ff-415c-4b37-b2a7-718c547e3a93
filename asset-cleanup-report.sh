#!/bin/bash

# Enhanced asset cleanup report for TapTap
# Usage: ./asset-cleanup-report.sh

set -e

PROJECT_ROOT="."
TEMP_DIR="/tmp/taptap-assets-report"
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🧹 TapTap Asset Cleanup Report${NC}"
echo -e "${BLUE}================================${NC}"

# Extract all asset references with better patterns
extract_references() {
    local search_dirs=("apps" "shared")
    
    echo -e "\n${CYAN}📂 Extracting asset references...${NC}"
    
    # Find all references using multiple patterns
    for dir in "${search_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            echo "  🔍 Searching in $dir/"
            
            # Pattern 1: import statements
            find "$dir" -name "*.tsx" -o -name "*.ts" -o -name "*.css" -o -name "*.scss" | \
            xargs grep -h "import.*from.*\.\(png\|jpg\|jpeg\|gif\|svg\|webp\|ico\|woff\|woff2\|ttf\|eot\)" 2>/dev/null | \
            sed -E "s/.*['\"]([^'\"]*\.(png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|eot))['\"].*/\1/" | \
            sed 's|.*/||' >> "$TEMP_DIR/refs_temp.txt" || true
            
            # Pattern 2: require() statements  
            find "$dir" -name "*.tsx" -o -name "*.ts" -o -name "*.js" | \
            xargs grep -h "require.*\.\(png\|jpg\|jpeg\|gif\|svg\|webp\|ico\|woff\|woff2\|ttf\|eot\)" 2>/dev/null | \
            sed -E "s/.*require\(['\"]([^'\"]*\.(png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|eot))['\"].*/\1/" | \
            sed 's|.*/||' >> "$TEMP_DIR/refs_temp.txt" || true
            
            # Pattern 3: src attributes
            find "$dir" -name "*.tsx" -o -name "*.ts" | \
            xargs grep -h "src=['\"][^'\"]*\.\(png\|jpg\|jpeg\|gif\|svg\|webp\|ico\)" 2>/dev/null | \
            sed -E "s/.*src=['\"]([^'\"]*\.(png|jpg|jpeg|gif|svg|webp|ico))['\"].*/\1/" | \
            sed 's|.*/||' >> "$TEMP_DIR/refs_temp.txt" || true
            
            # Pattern 4: CSS url() functions
            find "$dir" -name "*.css" -o -name "*.scss" | \
            xargs grep -h "url(['\"].*\.\(png\|jpg\|jpeg\|gif\|svg\|webp\|ico\|woff\|woff2\|ttf\|eot\)" 2>/dev/null | \
            sed -E "s/.*url\(['\"]?([^'\"]*\.(png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|eot))['\"]?\).*/\1/" | \
            sed 's|.*/||' >> "$TEMP_DIR/refs_temp.txt" || true
            
            # Pattern 5: assets/ paths
            find "$dir" -name "*.tsx" -o -name "*.ts" | \
            xargs grep -h "assets/.*\.\(png\|jpg\|jpeg\|gif\|svg\|webp\|ico\|woff\|woff2\|ttf\|eot\)" 2>/dev/null | \
            sed -E "s/.*assets\/([^'\"]*\.(png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|eot)).*/\1/" | \
            sed 's|.*/||' >> "$TEMP_DIR/refs_temp.txt" || true
        fi
    done
    
    # Clean and dedupe references
    if [[ -f "$TEMP_DIR/refs_temp.txt" ]]; then
        # Remove paths, keep only filenames, remove empty lines, sort and dedupe
        grep -v "^$" "$TEMP_DIR/refs_temp.txt" 2>/dev/null | \
        sed 's|.*/||' | \
        sort -u > "$TEMP_DIR/all_references.txt"
    else
        touch "$TEMP_DIR/all_references.txt"
    fi
    
    local ref_count=$(wc -l < "$TEMP_DIR/all_references.txt" 2>/dev/null || echo "0")
    echo -e "  ${GREEN}✅ Found $ref_count unique asset references${NC}"
}

# List actual files
list_assets() {
    local asset_dir="$1"
    local output_file="$2"
    
    if [[ -d "$asset_dir" ]]; then
        find "$asset_dir" -type f \( -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.gif" -o -name "*.svg" -o -name "*.webp" -o -name "*.ico" -o -name "*.woff" -o -name "*.woff2" -o -name "*.ttf" -o -name "*.eot" \) | \
        sed 's|.*/||' | sort > "$output_file"
    else
        touch "$output_file"
    fi
}

# Generate detailed report
generate_report() {
    echo -e "\n${CYAN}📋 Generating detailed report...${NC}"
    
    # List all actual assets
    echo "  📁 Listing web assets..."
    list_assets "apps/web/src/assets" "$TEMP_DIR/web_assets.txt"
    
    echo "  📁 Listing shared assets..."  
    list_assets "shared/assets" "$TEMP_DIR/shared_assets.txt"
    
    # Calculate statistics
    local web_count=$(wc -l < "$TEMP_DIR/web_assets.txt" 2>/dev/null || echo "0")
    local shared_count=$(wc -l < "$TEMP_DIR/shared_assets.txt" 2>/dev/null || echo "0") 
    local total_assets=$((web_count + shared_count))
    local ref_count=$(wc -l < "$TEMP_DIR/all_references.txt" 2>/dev/null || echo "0")
    
    # Find unused assets
    cat "$TEMP_DIR/web_assets.txt" "$TEMP_DIR/shared_assets.txt" | sort > "$TEMP_DIR/all_assets.txt"
    comm -23 "$TEMP_DIR/all_assets.txt" "$TEMP_DIR/all_references.txt" > "$TEMP_DIR/unused_assets.txt"
    comm -23 "$TEMP_DIR/web_assets.txt" "$TEMP_DIR/all_references.txt" > "$TEMP_DIR/unused_web.txt"
    comm -23 "$TEMP_DIR/shared_assets.txt" "$TEMP_DIR/all_references.txt" > "$TEMP_DIR/unused_shared.txt"
    
    local unused_total=$(wc -l < "$TEMP_DIR/unused_assets.txt" 2>/dev/null || echo "0")
    local unused_web=$(wc -l < "$TEMP_DIR/unused_web.txt" 2>/dev/null || echo "0")
    local unused_shared=$(wc -l < "$TEMP_DIR/unused_shared.txt" 2>/dev/null || echo "0")
    
    # Calculate sizes
    local total_size=0
    local unused_size=0
    
    if [[ -f "$TEMP_DIR/unused_web.txt" ]]; then
        while IFS= read -r file; do
            if [[ -n "$file" && -f "apps/web/src/assets/$file" ]]; then
                local size=$(stat -f%z "apps/web/src/assets/$file" 2>/dev/null || echo "0")
                unused_size=$((unused_size + size))
            fi
        done < "$TEMP_DIR/unused_web.txt"
    fi
    
    if [[ -f "$TEMP_DIR/unused_shared.txt" ]]; then
        while IFS= read -r file; do
            if [[ -n "$file" && -f "shared/assets/$file" ]]; then
                local size=$(stat -f%z "shared/assets/$file" 2>/dev/null || echo "0")
                unused_size=$((unused_size + size))
            fi
        done < "$TEMP_DIR/unused_shared.txt"
    fi
    
    # Convert bytes to human readable
    local unused_size_mb=$(echo "scale=2; $unused_size / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
    
    # Print report
    echo -e "\n${BLUE}📊 ASSET CLEANUP REPORT${NC}"
    echo -e "${BLUE}========================${NC}"
    echo -e "📁 Total assets: ${YELLOW}$total_assets${NC} (Web: $web_count, Shared: $shared_count)"
    echo -e "🔗 Referenced assets: ${GREEN}$ref_count${NC}"  
    echo -e "🗑️  Unused assets: ${RED}$unused_total${NC} (Web: $unused_web, Shared: $unused_shared)"
    echo -e "💾 Space that could be saved: ${YELLOW}${unused_size_mb} MB${NC}"
    
    # Show examples of unused assets
    if [[ $unused_total -gt 0 ]]; then
        echo -e "\n${RED}🗑️  UNUSED WEB ASSETS (first 10):${NC}"
        head -10 "$TEMP_DIR/unused_web.txt" 2>/dev/null | while IFS= read -r file; do
            if [[ -n "$file" ]]; then
                local size_kb=$(du -k "apps/web/src/assets/$file" 2>/dev/null | cut -f1 || echo "0")
                echo -e "  ${YELLOW}• $file${NC} (${size_kb}KB)"
            fi
        done
        
        echo -e "\n${RED}🗑️  UNUSED SHARED ASSETS (first 10):${NC}"
        head -10 "$TEMP_DIR/unused_shared.txt" 2>/dev/null | while IFS= read -r file; do
            if [[ -n "$file" ]]; then
                local size_kb=$(du -k "shared/assets/$file" 2>/dev/null | cut -f1 || echo "0")
                echo -e "  ${YELLOW}• $file${NC} (${size_kb}KB)"
            fi
        done
        
        # Generate cleanup commands
        echo -e "\n${CYAN}🧹 CLEANUP COMMANDS:${NC}"
        echo -e "${YELLOW}# Remove unused web assets${NC}"
        head -10 "$TEMP_DIR/unused_web.txt" 2>/dev/null | while IFS= read -r file; do
            [[ -n "$file" ]] && echo "rm \"apps/web/src/assets/$file\""
        done
        
        echo -e "\n${YELLOW}# Remove unused shared assets${NC}"  
        head -10 "$TEMP_DIR/unused_shared.txt" 2>/dev/null | while IFS= read -r file; do
            [[ -n "$file" ]] && echo "rm \"shared/assets/$file\""
        done
        
        echo -e "\n${BLUE}📋 Full lists saved to:${NC}"
        echo -e "  • All references: $TEMP_DIR/all_references.txt"
        echo -e "  • Unused web: $TEMP_DIR/unused_web.txt"
        echo -e "  • Unused shared: $TEMP_DIR/unused_shared.txt"
    fi
}

# Main execution
extract_references
generate_report

echo -e "\n${GREEN}✅ Report complete!${NC}"
echo -e "${YELLOW}⚠️  Please verify unused assets before deleting!${NC}"