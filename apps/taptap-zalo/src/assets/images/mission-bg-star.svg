<svg width="343" height="128" viewBox="0 0 343 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_79)">
<g filter="url(#filter0_d_0_79)">
<path d="M282.16 -1.33192C284.241 -9.93013 295.013 -12.8165 301.114 -6.4108L322.704 16.2557C325.02 18.6867 328.306 19.9479 331.653 19.6907L362.864 17.2928C371.685 16.6151 377.759 25.9679 373.552 33.7502L358.666 61.288C357.07 64.2413 356.886 67.7559 358.165 70.86L370.09 99.8028C373.46 107.982 366.442 116.649 357.741 115.053L326.951 109.406C323.649 108.8 320.25 109.711 317.693 111.886L293.852 132.172C287.114 137.905 276.703 133.908 275.532 125.14L271.388 94.1118C270.944 90.7841 269.027 87.8325 266.168 86.073L239.507 69.6674C231.973 65.031 232.557 53.8944 240.534 50.071L268.763 36.5419C271.791 35.091 274.006 32.3559 274.795 29.0929L282.16 -1.33192Z" fill="url(#paint0_linear_0_79)" shape-rendering="crispEdges"/>
</g>
</g>
<defs>
<filter id="filter0_d_0_79" x="222.188" y="-21.8867" width="164.728" height="168.736" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.762028 0 0 0 0 0.762028 0 0 0 0 0.762028 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_79"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_79" result="shape"/>
</filter>
<linearGradient id="paint0_linear_0_79" x1="292.5" y1="6" x2="338.5" y2="180" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_0_79">
<rect width="343" height="128" fill="white"/>
</clipPath>
</defs>
</svg>
