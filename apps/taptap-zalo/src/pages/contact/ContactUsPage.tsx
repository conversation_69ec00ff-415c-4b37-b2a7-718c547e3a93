import React, { useEffect } from 'react';
import { Page } from 'zmp-ui';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import { LoadingSpinner, useContactStore } from '@taptap/shared';
import { ContactUsPage as ContactUsPageComponent } from '../ContactUs/ContactUsPage';
import YoutubeIcon from '../../../../../shared/assets/icons/youtube-icon.svg';
import FacebookIcon from '../../../../../shared/assets/icons/facebook-icon.svg';
import InstagramIcon from '../../../../../shared/assets/icons/instagram-icon.svg';

const PageAny = Page as any;

const ContactUsPageContainer: React.FC = () => {
  const { navigate } = useZaloNavigation();
  const { contactInfo, loading, fetchContactInfo } = useContactStore();
  
  useEffect(() => {
    fetchContactInfo();
  }, [fetchContactInfo]);

  const handleBackClick = () => {
    navigate('/profile');
  };

  if (loading) {
    return (
      <PageAny className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </PageAny>
    );
  }

  const socialItems = [
    {
      id: 'youtube',
      name: 'Youtube',
      iconSrc: YoutubeIcon,
      url: contactInfo?.youtubeLink || 'https://www.youtube.com'
    },
    {
      id: 'facebook',
      name: 'Facebook',
      iconSrc: FacebookIcon,
      url: contactInfo?.facebookLink || 'https://www.facebook.com'
    },
    {
      id: 'instagram',
      name: 'Instagram',
      iconSrc: InstagramIcon,
      url: contactInfo?.instagramLink || 'https://www.instagram.com'
    }
  ];

  return (
    <PageAny className="min-h-screen bg-[#EFF3F6]">
      <ContactUsPageComponent
        onBack={handleBackClick}
        socialItems={socialItems}
        phone={contactInfo?.phone}
        email={contactInfo?.email}
        website={contactInfo?.website}
        address={contactInfo?.companyAddress}
        workingHours={contactInfo?.companyInfo}
      />
    </PageAny>
  );
};

export default ContactUsPageContainer;