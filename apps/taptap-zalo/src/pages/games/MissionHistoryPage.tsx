import React from "react";
import { Page } from "zmp-ui";
import { useZaloNavigation } from "../../hooks/useZaloNavigation";
import { NavigationHeader, BackIcon } from "@taptap/shared";

// Import default mission thumbnail
import defaultThumbnail from "../../../../../shared/assets/images/<EMAIL>";

// Import VUI coin icon
import vuiCoinIcon from "../../../../../shared/assets/images/vui_coin_16px.svg";

interface MissionHistoryCard {
  id: string;
  title: string;
  status: "received" | "lost" | "releasing";
  thumbnailSrc: string;
  rewards?: Array<{
    type: "points" | "currency" | "brand";
    value: string;
    brandLogo?: string;
  }>;
}

const MissionHistoryCardComponent: React.FC<MissionHistoryCard> = ({
  title,
  status,
  thumbnailSrc,
  rewards = [],
}) => {
  const getBackgroundColor = () => {
    switch (status) {
      case "received":
        return "#016A32"; // Green for received
      case "lost":
        return "#14A197"; // Teal for lost
      case "releasing":
        return "#14A197"; // Teal for releasing
      default:
        return "#14A197";
    }
  };

  const getStatusInfo = () => {
    switch (status) {
      case "received":
        return {
          text: "Đã nhận quà!",
          iconBg: "#0DC98B",
          icon: (
            <svg width="8" height="6" viewBox="0 0 8 6" fill="none">
              <path
                d="M1 3L3 5L7 1"
                stroke="white"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      case "lost":
        return {
          text: "Quà tặng thất lạc",
          iconBg: "#FF7425",
          icon: (
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <rect width="16" height="16" rx="8" fill="white" />
              <path
                d="M8 3V8L11 11"
                stroke="#FF7425"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      case "releasing":
        return {
          text: "Quà đang đến...",
          iconBg: "#0DC98B",
          icon: (
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <rect width="16" height="16" rx="8" fill="white" />
              <path
                d="M8 6V10L11 13"
                stroke="#0DC98B"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      default:
        return {
          text: "Đã nhận quà!",
          iconBg: "#0DC98B",
          icon: null,
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div
      className="w-full h-[128px] rounded-xl flex items-center p-2 gap-2 relative overflow-hidden"
      style={{ backgroundColor: getBackgroundColor() }}
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-[215px] h-[215px] opacity-20">
        <div className="w-full h-full bg-gradient-to-br from-white/20 to-transparent rounded-full transform rotate-12 translate-x-12 -translate-y-12"></div>
      </div>

      {/* Mission Thumbnail */}
      <div className="w-24 h-28 rounded-lg overflow-hidden flex-shrink-0">
        <img
          src={thumbnailSrc}
          alt={title}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Mission Content */}
      <div className="flex-1 min-w-0 flex flex-col gap-3">
        {/* Title and Rewards */}
        <div className="flex flex-col gap-1.5">
          <h3 className="text-base font-semibold text-white leading-[22px] line-clamp-2">
            {title}
          </h3>

          {/* Rewards */}
          {rewards.length > 0 && (
            <div className="flex items-center gap-2 flex-wrap">
              {rewards.map((reward, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 bg-white/10 rounded-md px-1.5 py-1"
                >
                  {reward.type === "points" && (
                    <img src={vuiCoinIcon} alt="VUI" className="w-4 h-4" />
                  )}
                  {reward.type === "brand" && reward.brandLogo && (
                    <img
                      src={reward.brandLogo}
                      alt="Brand"
                      className="w-4 h-4 rounded-full"
                    />
                  )}
                  <span className="text-xs font-semibold text-white">
                    {reward.value}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Status and Action */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1.5">
            <div
              className="w-4 h-4 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: statusInfo.iconBg }}
            >
              {statusInfo.icon}
            </div>
            <span className="text-[10px] font-normal text-white leading-[16px]">
              {statusInfo.text}
            </span>
          </div>

          {/* Hidden button for layout consistency */}
          <div className="w-[85px] h-8 opacity-0"></div>
        </div>
      </div>
    </div>
  );
};

const PageAny = Page as any;

const MissionHistoryPage: React.FC = () => {
  const { navigate } = useZaloNavigation();

  // Mock mission history data based on Figma design
  const missionHistoryItems: MissionHistoryCard[] = [
    {
      id: "1",
      title: "Tích điểm từ đa dạng thương hiệu",
      status: "received",
      thumbnailSrc: defaultThumbnail,
      rewards: [
        { type: "points", value: "+ 1" },
        { type: "points", value: "+ 100.000" },
      ],
    },
    {
      id: "2",
      title: "Thử thách ẩm thực từ Chang Thái",
      status: "received",
      thumbnailSrc: defaultThumbnail,
      rewards: [
        { type: "points", value: "+ 20" },
        { type: "points", value: "+ 20" },
      ],
    },
    {
      id: "3",
      title: "Tích điểm từ đa dạng thương hiệu",
      status: "received",
      thumbnailSrc: defaultThumbnail,
      rewards: [
        {
          type: "brand",
          value: "+ BeautyX",
          brandLogo: "https://via.placeholder.com/16x16/FF6B6B/FFFFFF?text=B",
        },
      ],
    },
    {
      id: "4",
      title: "Tích điểm từ đa dạng thương hiệu",
      status: "lost",
      thumbnailSrc: defaultThumbnail,
      rewards: [
        { type: "points", value: "+ 100.000" },
        { type: "currency", value: "+ 100.000" },
      ],
    },
    {
      id: "5",
      title: "Tích điểm từ đa dạng thương hiệu",
      status: "releasing",
      thumbnailSrc: defaultThumbnail,
      rewards: [
        { type: "points", value: "+ 100.000" },
        { type: "currency", value: "+ 100.000" },
      ],
    },
  ];

  return (
    <PageAny className="min-h-screen bg-white">
      {/* Navigation Header */}
      <NavigationHeader
        title="Lịch sử"
        leftIcon={<BackIcon size="sm" />}
        onLeftClick={() => navigate('/games?tab=missions')}
        className="border-b border-[#ECECEC]"
      />

      {/* Content */}
      <div className="px-4 py-4">
        <div className="flex flex-col gap-2.5">
          {missionHistoryItems.map((item) => (
            <MissionHistoryCardComponent key={item.id} {...item} />
          ))}
        </div>
      </div>
    </PageAny>
  );
};

export default MissionHistoryPage;
