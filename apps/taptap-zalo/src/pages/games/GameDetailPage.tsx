import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Page } from 'zmp-ui';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import {
  NavigationHeader,
  Card,
  Button,
  Rating,
  Avatar,
  resizeImage,
} from '@taptap/shared';
import type { GameDetail } from '@taptap/shared';

const PageAny = Page as any;

const GameDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { navigate } = useZaloNavigation();
  const [game, setGame] = useState<GameDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedScreenshot, setSelectedScreenshot] = useState(0);
  const [showAllReviews, setShowAllReviews] = useState(false);

  useEffect(() => {
    const fetchGameDetail = async () => {
      try {
        setLoading(true);
        // Mock game data - in real app would fetch from API
        const mockGame: GameDetail = {
          id: id || '1',
          title: 'Genshin Impact',
          description: 'Phiêu lưu thế giới mở với đồ họa tuyệt đẹp',
          fullDescription: 'Genshin Impact là một trò chơi nhập vai hành động thế giới mở miễn phí được phát triển bởi miHoYo. Trò chơi có đồ họa anime tuyệt đẹp, hệ thống chiến đấu đa dạng với 7 nguyên tố khác nhau, và thế giới Teyvat rộng lớn để khám phá. Người chơi sẽ vào vai Traveler và khám phá các vùng đất bí ẩn, gặp gỡ những nhân vật độc đáo, và khám phá những bí mật của thế giới này.',
          thumbnail: 'https://picsum.photos/400/300?random=1',
          screenshots: [
            'https://picsum.photos/600/400?random=1',
            'https://picsum.photos/600/400?random=2',
            'https://picsum.photos/600/400?random=3',
            'https://picsum.photos/600/400?random=4',
            'https://picsum.photos/600/400?random=5',
          ],
          category: 'adventure',
          rating: 4.5,
          downloads: *********,
          size: 12500,
          version: '4.2.0',
          developer: 'miHoYo Limited',
          publishedAt: new Date('2020-09-28'),
          updatedAt: new Date('2024-01-15'),
          tags: ['RPG', 'Adventure', 'Anime', 'Free-to-Play', 'Open World'],
          isNew: false,
          isFeatured: true,
          requirements: {
            minAndroidVersion: '7.0',
            minIOSVersion: '9.0',
            minRAM: 3,
            minStorage: 15,
            internetRequired: true,
          },
          reviews: [
            {
              id: '1',
              userId: '1',
              userName: 'Nguyễn Văn A',
              userAvatar: 'https://picsum.photos/40/40?random=10',
              rating: 5,
              comment: 'Game tuyệt vời! Đồ họa đẹp, gameplay hay, nhạc phim xuất sắc. Rất đáng chơi!',
              createdAt: new Date('2024-01-10'),
              likes: 24,
              replies: [],
            },
            {
              id: '2',
              userId: '2',
              userName: 'Trần Thị B',
              userAvatar: 'https://picsum.photos/40/40?random=11',
              rating: 4,
              comment: 'Game hay nhưng hơi nặng máy. Cần tối ưu thêm cho các thiết bị cấu hình thấp.',
              createdAt: new Date('2024-01-08'),
              likes: 12,
              replies: [],
            },
            {
              id: '3',
              userId: '3',
              userName: 'Lê Văn C',
              userAvatar: 'https://picsum.photos/40/40?random=12',
              rating: 5,
              comment: 'Cốt truyện hấp dẫn, nhân vật được thiết kế rất đẹp. Chơi mãi không chán!',
              createdAt: new Date('2024-01-05'),
              likes: 18,
              replies: [],
            },
          ],
          relatedGames: [
            {
              id: '2',
              title: 'Honkai Impact 3rd',
              description: 'Game hành động 3D',
              thumbnail: 'https://picsum.photos/120/120?random=20',
              screenshots: [],
              category: 'action',
              rating: 4.3,
              downloads: 50000000,
              size: 8000,
              version: '6.9.0',
              developer: 'miHoYo Limited',
              publishedAt: new Date('2016-10-14'),
              updatedAt: new Date('2024-01-10'),
              tags: ['Action', 'Anime', '3D'],
              isNew: false,
              isFeatured: false,
            },
            {
              id: '3',
              title: 'Zenless Zone Zero',
              description: 'Action RPG đô thị',
              thumbnail: 'https://picsum.photos/120/120?random=21',
              screenshots: [],
              category: 'action',
              rating: 4.6,
              downloads: 30000000,
              size: 15000,
              version: '1.2.0',
              developer: 'miHoYo Limited',
              publishedAt: new Date('2024-07-04'),
              updatedAt: new Date('2024-01-12'),
              tags: ['Action', 'RPG', 'Urban'],
              isNew: true,
              isFeatured: true,
            },
          ],
        };

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        setGame(mockGame);
      } catch (err) {
        setError('Không thể tải thông tin game');
        // Error fetching game
      } finally {
        setLoading(false);
      }
    };

    fetchGameDetail();
  }, [id]);

  const handleBack = () => {
    navigate('/games');
  };

  const handleDownload = () => {
    if (game) {
      // TODO: Implement download functionality
    }
  };

  const handleScreenshotClick = (index: number) => {
    setSelectedScreenshot(index);
  };

  const formatSize = (sizeInMB: number) => {
    if (sizeInMB >= 1024) {
      return `${(sizeInMB / 1024).toFixed(1)} GB`;
    }
    return `${sizeInMB} MB`;
  };

  const formatDownloads = (downloads: number) => {
    if (downloads >= 1000000000) {
      return `${(downloads / 1000000000).toFixed(1)}B`;
    } else if (downloads >= 1000000) {
      return `${(downloads / 1000000).toFixed(1)}M`;
    } else if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}K`;
    }
    return downloads.toString();
  };

  // Back icon
  const BackIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M15 18L9 12L15 6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  // Share icon
  const ShareIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <polyline
        points="16,6 12,2 8,6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <line
        x1="12"
        y1="2"
        x2="12"
        y2="15"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Chi tiết game"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
        />
        <div className="flex items-center justify-center pt-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F65D79] mx-auto mb-4"></div>
            <p className="text-[#9A9A9A]">Đang tải...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !game) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Chi tiết game"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
        />
        <div className="flex items-center justify-center pt-20">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error || 'Game không tìm thấy'}</p>
            <Button onClick={handleBack} variant="outline">
              Quay lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const displayedReviews = showAllReviews ? game.reviews : game.reviews.slice(0, 2);

  return (
    <PageAny className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <NavigationHeader
        title="Chi tiết game"
        leftIcon={BackIcon}
        rightIcon={ShareIcon}
        onLeftClick={handleBack}
        onRightClick={() => {
          // TODO: Implement share functionality
        }}
      />

      <div className="max-w-md mx-auto pb-6">
        {/* Game Header */}
        <Card className="m-4 p-6">
          <div className="flex items-start space-x-4">
            <img
              src={resizeImage(game.thumbnail, { width: 160, height: 160, quality: 85, fit: 'cover' })}
              alt={game.title}
              className="w-20 h-20 rounded-xl object-cover"
            />
            <div className="flex-1">
              <h1 className="text-xl font-bold text-[#1A1818] mb-1">{game.title}</h1>
              <p className="text-[#9A9A9A] text-sm mb-2">{game.developer}</p>
              <div className="flex items-center space-x-4 text-sm text-[#9A9A9A]">
                <div className="flex items-center">
                  <Rating value={game.rating} readOnly />
                  <span className="ml-1">{game.rating}</span>
                </div>
                <span>{formatDownloads(game.downloads)} lượt tải</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 mt-6">
            <Button
              className="flex-1"
              onClick={handleDownload}
            >
              Tải game ({formatSize(game.size)})
            </Button>
            <Button
              variant="outline"
              className="px-4"
              onClick={() => {
                // TODO: Implement favorites functionality
              }}
            >
              ❤️
            </Button>
          </div>
        </Card>

        {/* Screenshots */}
        <Card className="m-4 p-6">
          <h2 className="text-lg font-semibold text-[#1A1818] mb-4">Ảnh chụp màn hình</h2>
          
          {/* Main Screenshot */}
          <div className="mb-4">
            <img
              src={resizeImage(game.screenshots[selectedScreenshot], { width: 750, height: 384, quality: 85, fit: 'cover' })}
              alt={`Screenshot ${selectedScreenshot + 1}`}
              className="w-full h-48 rounded-lg object-cover"
            />
          </div>

          {/* Screenshot Thumbnails */}
          <div className="flex space-x-2 overflow-x-auto">
            {game.screenshots.map((screenshot, index) => (
              <img
                key={index}
                src={resizeImage(screenshot, { width: 128, height: 128, quality: 85, fit: 'cover' })}
                alt={`Thumbnail ${index + 1}`}
                className={`w-16 h-16 rounded-lg object-cover cursor-pointer flex-shrink-0 border-2 ${
                  selectedScreenshot === index ? 'border-[#F65D79]' : 'border-transparent'
                }`}
                onClick={() => handleScreenshotClick(index)}
              />
            ))}
          </div>
        </Card>

        {/* Game Info */}
        <Card className="m-4 p-6">
          <h2 className="text-lg font-semibold text-[#1A1818] mb-4">Thông tin game</h2>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-[#9A9A9A]">Phiên bản</span>
              <span className="text-[#1A1818]">{game.version}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#9A9A9A]">Dung lượng</span>
              <span className="text-[#1A1818]">{formatSize(game.size)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#9A9A9A]">Thể loại</span>
              <span className="text-[#1A1818] capitalize">{game.category}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#9A9A9A]">Yêu cầu Android</span>
              <span className="text-[#1A1818]">{game.requirements.minAndroidVersion}+</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#9A9A9A]">RAM tối thiểu</span>
              <span className="text-[#1A1818]">{game.requirements.minRAM} GB</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#9A9A9A]">Kết nối mạng</span>
              <span className="text-[#1A1818]">
                {game.requirements.internetRequired ? 'Bắt buộc' : 'Không bắt buộc'}
              </span>
            </div>
          </div>

          {/* Tags */}
          <div className="mt-4">
            <div className="flex flex-wrap gap-2">
              {game.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-[#F8F8F8] text-[#1A1818] text-sm rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </Card>

        {/* Description */}
        <Card className="m-4 p-6">
          <h2 className="text-lg font-semibold text-[#1A1818] mb-4">Mô tả</h2>
          <p className="text-[#1A1818] leading-relaxed">{game.fullDescription}</p>
        </Card>

        {/* Reviews */}
        <Card className="m-4 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-[#1A1818]">
              Đánh giá ({game.reviews.length})
            </h2>
            <div className="flex items-center">
              <Rating value={game.rating} readOnly />
              <span className="ml-1 text-[#1A1818]">{game.rating}</span>
            </div>
          </div>

          <div className="space-y-4">
            {displayedReviews.map((review) => (
              <div key={review.id} className="border-b border-gray-100 last:border-b-0 pb-4 last:pb-0">
                <div className="flex items-start space-x-3">
                  <Avatar
                    src={review.userAvatar || `https://picsum.photos/40/40?random=${review.id}`}
                    alt={review.userName}
                    size="sm"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-[#1A1818]">{review.userName}</span>
                      <span className="text-xs text-[#9A9A9A]">
                        {review.createdAt.toLocaleDateString('vi-VN')}
                      </span>
                    </div>
                    <div className="flex items-center mb-2">
                      <Rating value={review.rating} readOnly />
                    </div>
                    <p className="text-[#1A1818] text-sm leading-relaxed">{review.comment}</p>
                    <div className="flex items-center mt-2">
                      <button className="text-xs text-[#9A9A9A] hover:text-[#F65D79]">
                        👍 {review.likes}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {game.reviews.length > 2 && (
            <div className="text-center mt-4">
              <button
                onClick={() => setShowAllReviews(!showAllReviews)}
                className="text-[#F65D79] text-sm font-medium hover:underline"
              >
                {showAllReviews ? 'Thu gọn' : `Xem thêm ${game.reviews.length - 2} đánh giá`}
              </button>
            </div>
          )}
        </Card>

        {/* Related Games */}
        <Card className="m-4 p-6">
          <h2 className="text-lg font-semibold text-[#1A1818] mb-4">Game liên quan</h2>
          
          <div className="space-y-4">
            {game.relatedGames.map((relatedGame) => (
              <div
                key={relatedGame.id}
                className="flex items-center space-x-4 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2"
                onClick={() => navigate(`/games/${relatedGame.id}`)}
              >
                <img
                  src={resizeImage(relatedGame.thumbnail, { width: 128, height: 128, quality: 85, fit: 'cover' })}
                  alt={relatedGame.title}
                  className="w-16 h-16 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h3 className="font-medium text-[#1A1818] mb-1">{relatedGame.title}</h3>
                  <p className="text-sm text-[#9A9A9A] mb-1">{relatedGame.description}</p>
                  <div className="flex items-center space-x-3 text-xs text-[#9A9A9A]">
                    <div className="flex items-center">
                      <Rating value={relatedGame.rating} readOnly />
                      <span className="ml-1">{relatedGame.rating}</span>
                    </div>
                    <span>{formatDownloads(relatedGame.downloads)} lượt tải</span>
                  </div>
                </div>
                <Button size="small" variant="outline">
                  Xem
                </Button>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </PageAny>
  );
};

export default GameDetailPage;
