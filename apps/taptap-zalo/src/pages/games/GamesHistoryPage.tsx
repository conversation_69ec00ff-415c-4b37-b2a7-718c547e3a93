import React from 'react';
import { Page } from 'zmp-ui';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import { NavigationHeader, BackIcon } from '@taptap/shared';
import { colors } from '../../../../../shared/styles/colors';

// Import default game thumbnail
import defaultGameThumbnail from '../../../../../shared/assets/images/game_thumbnail.png';

// Import VUI coin icon
import vuiCoinIcon from '../../../../../shared/assets/images/vui_coin_16px.svg';

interface HistoryCard {
  id: string;
  title: string;
  status: 'received' | 'lost' | 'releasing';
  thumbnailSrc: string;
  rewards?: Array<{
    type: 'vui' | 'currency';
    value: string;
  }>;
}

const HistoryCardComponent: React.FC<HistoryCard> = ({
  title,
  status,
  thumbnailSrc,
  rewards = []
}) => {
  const getBackgroundColor = () => {
    switch (status) {
      case 'received':
        return colors.mission.received;
      case 'lost':
        return colors.mission.lost;
      case 'releasing':
        return colors.mission.releasing;
      default:
        return colors.mission.inProgress;
    }
  };
  const getStatusText = () => {
    switch (status) {
      case 'received':
        return 'Đã nhận';
      case 'lost':
        return 'Trượt';
      case 'releasing':
        return 'Đang phát';
      default:
        return '';
    }
  };

  const getButtonText = () => {
    switch (status) {
      case 'received':
        return 'Xem chi tiết';
      case 'lost':
        return 'Xem chi tiết';
      case 'releasing':
        return 'Tham gia';
      default:
        return 'Xem chi tiết';
    }
  };

  return (
    <div 
      className="relative w-full h-32 rounded-xl p-2 flex items-center gap-2 overflow-hidden"
      style={{ backgroundColor: getBackgroundColor() }}
    >
      {/* Thumbnail */}
      <div className="w-24 h-28 flex-shrink-0 rounded-lg overflow-hidden">
        <img 
          src={thumbnailSrc}
          alt={title}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col gap-3 min-h-28 justify-between">
        {/* Title and Rewards */}
        <div className="flex flex-col gap-1.5">
          {/* Title */}
          <h3 className="text-base font-semibold leading-[1.375] text-white">
            {title}
          </h3>

          {/* Rewards */}
          {rewards.length > 0 && (
            <div className="flex items-center flex-wrap gap-2">
              {rewards.map((reward, index) => (
                <div key={index} className="flex items-center gap-1">
                  {reward.type === 'vui' && (
                    <img src={vuiCoinIcon} alt="VUI Coin" className="w-4 h-4" />
                  )}
                  <span className="text-xs font-semibold text-white">
                    {reward.value}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Bottom Section */}
        <div className="flex items-center justify-between gap-2">
          {/* Status */}
          <span className="text-xs text-white/80">
            {getStatusText()}
          </span>

          {/* Action Button */}
          <button 
            className="w-[85px] h-8 rounded-lg text-xs font-semibold text-white text-center transition-colors"
            style={{ 
              backgroundColor: colors.primary,
              '&:hover': { backgroundColor: colors.primaryHover },
              '&:active': { backgroundColor: colors.primaryActive }
            }}
          >
            {getButtonText()}
          </button>
        </div>
      </div>
    </div>
  );
};

const PageAny = Page as any;

export const GamesHistoryPage: React.FC = () => {
  const { navigate } = useZaloNavigation();

  // Mock history data based on Figma design
  const historyItems: HistoryCard[] = [
    {
      id: '1',
      title: 'Khuyến mãi săn sale tháng 6',
      status: 'received',
      thumbnailSrc: defaultGameThumbnail,
      rewards: [
        { type: 'vui', value: '50' },
        { type: 'currency', value: '10K' }
      ]
    },
    {
      id: '2', 
      title: 'Trò chơi khuyến mãi mùa hè',
      status: 'lost',
      thumbnailSrc: defaultGameThumbnail,
      rewards: [
        { type: 'vui', value: '100' },
        { type: 'currency', value: '20K' }
      ]
    },
    {
      id: '3',
      title: 'Sự kiện đặc biệt cuối năm', 
      status: 'received',
      thumbnailSrc: defaultGameThumbnail,
      rewards: [
        { type: 'vui', value: '75' }
      ]
    },
    {
      id: '4',
      title: 'Thử thách tháng mới',
      status: 'lost', 
      thumbnailSrc: defaultGameThumbnail,
      rewards: []
    },
    {
      id: '5',
      title: 'Chương trình ưu đãi hè',
      status: 'releasing',
      thumbnailSrc: defaultGameThumbnail,
      rewards: [
        { type: 'vui', value: '200' }
      ]
    }
  ];


  return (
    <PageAny className="min-h-screen bg-white">
      {/* Navigation Header */}
      <NavigationHeader
        title="Lịch sử"
        leftIcon={<BackIcon size="sm" />}
        onLeftClick={() => navigate('/games')}
        className="border-b border-[#ECECEC]"
      />

      {/* Content */}
      <div className="px-4 py-4">
        <div className="flex flex-col gap-2.5">
          {historyItems.map((item) => (
            <HistoryCardComponent key={item.id} {...item} />
          ))}
        </div>
      </div>
    </PageAny>
  );
};

export default GamesHistoryPage;