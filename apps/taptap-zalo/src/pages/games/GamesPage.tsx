import React, { useState, useEffect } from "react";
import { Page } from "zmp-ui";
import { useZaloNavigation } from "../../hooks/useZaloNavigation";
import {
  NavigationHeader,
  TabNavigation,
  BannerCarousel,
  SectionHeader,
  BackIcon,
  BrandedWheelCard,
  LoadingSpinner,
  MissionSection,
  Icon,
} from "@taptap/shared";
import type { TabItem, BannerItem, ChallengeV3 } from "@taptap/shared";
import { gameService, gameCardService } from "@taptap/shared";
import type { IGameCard } from "@taptap/shared";

const PageAny = Page as any;

export const GamesPage: React.FC = () => {
  const { navigate, pathname } = useZaloNavigation();
  // Parse tab from pathname since <PERSON>alo doesn't have searchParams
  const getTabFromPath = () => {
    const params = new URLSearchParams(window.location.search);
    return params.get("tab") || "games";
  };
  const initialTab = getTabFromPath();
  const [activeTab, setActiveTab] = useState(initialTab);
  const [banners, setBanners] = useState<BannerItem[]>([]);
  const [isLoadingBanners, setIsLoadingBanners] = useState(true);
  const [gameCards, setGameCards] = useState<IGameCard[]>([]);
  const [isLoadingGameCards, setIsLoadingGameCards] = useState(true);
  const offset = 0;
  const limit = 8;

  // Fetch banners from API
  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setIsLoadingBanners(true);
        const bannersData = await gameService.getGameBanners("GAME");

        // Map GameBanner to BannerItem format
        const mappedBanners: BannerItem[] = bannersData.map((banner) => ({
          id: banner.id,
          imageUrl: banner.imageUrl,
          title: banner.title,
          subtitle: banner.description,
          link: banner.redirectUrl,
        }));

        setBanners(mappedBanners);
      } catch (error) {
        console.error("Failed to fetch game banners:", error);
        // Set empty array on error to prevent UI breaking
        setBanners([]);
      } finally {
        setIsLoadingBanners(false);
      }
    };

    fetchBanners();
  }, []);

  // Fetch game cards from API
  useEffect(() => {
    const fetchGameCards = async () => {
      try {
        setIsLoadingGameCards(true);
        const cards = await gameCardService.fetchGameCards(offset, limit);
        setGameCards(cards);
      } catch (error) {
        console.error("Failed to fetch game cards:", error);
        setGameCards([]);
      } finally {
        setIsLoadingGameCards(false);
      }
    };

    fetchGameCards();
  }, [offset]);

  // Tab data
  const tabs: TabItem[] = [
    { id: "games", label: "Kho game" },
    { id: "missions", label: "Nhiệm vụ" },
  ];
  
  console.log('[GamesPage] Tabs configuration:', tabs);
  console.log('[GamesPage] Current active tab:', activeTab);

  const handleWheelClick = (wheelId: string) => {
    // Navigate to wheel detail page
    navigate(`/games/wheel/${wheelId}`);
  };

  const handleGamePress = (item: IGameCard, index: number) => {
    if (item.type === "branded_wheel") {
      handleWheelClick(item.data._id);
    }
  };

  // Simplified tab handling for Zalo
  const handleTabChange = (tabId: string) => {
    console.log('[GamesPage] Tab change requested:', tabId);
    setActiveTab(tabId);
    // Update URL with tab parameter
    const url = new URL(window.location.href);
    url.searchParams.set("tab", tabId);
    window.history.replaceState({}, "", url.toString());
    console.log('[GamesPage] Tab changed to:', tabId);
  };

  // Update browser title (does not affect UI labels)
  useEffect(() => {
    document.title =
      activeTab === "missions"
        ? "Nhiệm vụ có thưởng - TAPTAP"
        : "Kho game - TAPTAP";
  }, [activeTab]);

  const handleMissionHistoryClick = () => {
    navigate("/games/missions/history");
  };

  const handleMissionClick = (mission: ChallengeV3) => {
    console.log("Mission clicked:", mission);
    navigate(`/games/missions/${mission._id}`);
  };

  return (
    <PageAny className="min-h-screen bg-[#EFF3F6]">
      {/* Navigation Header */}
      <NavigationHeader
        title="Vui Chơi - Trò chơi"
        leftIcon={<BackIcon />}
        onLeftClick={() => navigate('/')}
        className="border-b border-[#ECECEC]"
      />

      {/* Tab Navigation */}
      <div className="bg-white">
        <TabNavigation
          tabs={tabs}
          variant="full-width"
          activeTabId={activeTab}
          onTabChange={handleTabChange}
        />
      </div>

      {/* Content */}
      <div className="pb-4">
        {/* Banner Section */}

        {/* Games or Missions Section */}
        {activeTab === "games" && (
          <div className="pt-8 pb-4">
            <div className="pt-4 pb-2">
              {!isLoadingBanners && banners.length > 0 && (
                <BannerCarousel
                  banners={banners}
                  onBannerClick={async (banner) => {
                    // Track banner click
                    try {
                      await gameService.trackBannerClick(banner.id, {
                        screen: "GAME",
                        title: banner.title,
                      });
                    } catch (error) {
                      // Silently fail tracking
                    }
                    // Handle navigation if link exists
                    if (banner.link) {
                      if (banner.link.startsWith("http")) {
                        window.open(banner.link, "_blank");
                      } else {
                        navigate(banner.link);
                      }
                    } else {
                      // Banner clicked without link
                    }
                  }}
                />
              )}
            </div>
            {/* Section Header */}
            <div className="px-4 pb-8">
              <SectionHeader
                title="Kho game"
                //actionText="Lịch sử"
                //onActionClick={() => navigate('/games/history')}
                className="text-base font-bold text-[#1A1818]"
              />
            </div>
            {/* Games Grid */}
            <div className="">
              {isLoadingGameCards ? (
                <div className="flex justify-center items-center py-12">
                  <LoadingSpinner />
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-x-2 gap-y-12">
                  {/* Render game cards from API */}
                  {gameCards.map((card, index) => {
                    return (
                      <div key={card._id} className="flex justify-center">
                        <BrandedWheelCard
                          gameCard={card}
                          onClick={() => handleGamePress(card, index)}
                        />
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        )}
        {activeTab === "missions" && (
          <MissionSection
            actionElement={
              <Icon
                name="ticket"
                size={20}
                color="#F65D79"
                className="hover:opacity-80 transition-opacity"
              />
            }
            onMissionClick={handleMissionClick}
            onActionClick={handleMissionHistoryClick}
            className="mb-6"
          />
        )}
      </div>
    </PageAny>
  );
};

export default GamesPage;
