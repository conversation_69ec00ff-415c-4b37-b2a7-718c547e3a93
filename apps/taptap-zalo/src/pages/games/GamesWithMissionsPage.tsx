import React, { useState } from 'react';
import { Page } from 'zmp-ui';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import { 
  NavigationHeader,
  TabNavigation,
} from '@taptap/shared';
import type { TabItem } from '@taptap/shared';
import { GamesPage } from './GamesPage';
import { MissionsTab } from './MissionsTab';

// Tab items for main navigation
const MAIN_TABS: TabItem[] = [
  {
    id: 'games',
    label: 'Trò chơi',
  },
  {
    id: 'missions',
    label: 'Nhiệm vụ',
  },
];

const PageAny = Page as any;

export const GamesWithMissionsPage: React.FC = () => {
  const { navigate } = useZaloNavigation();
  const [activeTab, setActiveTab] = useState('missions'); // Default to missions tab as per Figma


  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleMissionHistoryClick = () => {
    // Navigate to mission history
    navigate('/games/missions/history');
  };

  return (
    <PageAny className="w-full min-h-screen bg-white">
 

      {/* Navigation Header */}
      <NavigationHeader
        title="VUI Chơi"
        rightIcon={
          activeTab === 'missions' ? (
            <button 
              onClick={handleMissionHistoryClick}
              className="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-shadow"
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path 
                  d="M10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18Z" 
                  stroke="#1A1818" 
                  strokeWidth="1.5" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
                <path 
                  d="M10 6V10L13 13" 
                  stroke="#1A1818" 
                  strokeWidth="1.5" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          ) : undefined
        }
        className="bg-white border-b border-[#ECECEC]"
      />

      {/* Tab Navigation */}
      <div className="bg-white border-b border-[#ECECEC]">
        <TabNavigation
          tabs={MAIN_TABS}
          activeTabId={activeTab}
          onTabChange={handleTabChange}
          className="px-0"
        />
      </div>

      {/* Content Area */}
      <div className="flex-1 pb-20">
        {activeTab === 'missions' && <MissionsTab />}
        
        {activeTab === 'games' && (
          <div className="pt-4">
            {/* Games content would go here or integrate existing GamesPage content */}
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="text-6xl mb-4">🎮</div>
                <div className="text-lg font-medium text-[#1A1818] mb-2">
                  Trò chơi sắp ra mắt
                </div>
                <div className="text-sm text-[#9A9A9A]">
                  Hãy quay lại sau để trải nghiệm
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

    </PageAny>
  );
};

export default GamesWithMissionsPage;