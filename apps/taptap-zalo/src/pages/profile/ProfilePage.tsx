import React, { useState, useEffect } from 'react';
import { Page } from "zmp-ui";
import {
  LoyaltyCard,
  type LoyaltyCardData,
  ProfileSectionHeader,
  ProfileNavSection,
  ProfileMenuItem,
  HistoryIcon,
  MenuReceiptIcon,
  OrderIcon,
  ContactSupportIcon,
  LogoutIcon,
  LogoutConfirmationDialog,
  useAuthStore,
  membershipApi,
  ITierMember,
  ITierClassDetail,
  membershipHelpers,
  AvatarModal,
  useAvatar,
} from '@taptap/shared';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';

const PageAny = Page as any;

const ProfilePage: React.FC = () => {
  const { navigate } = useZaloNavigation();
  const { logout, profile, currentAvatar, updateProfile, getCurrentAvatar, setCurrentAvatar } = useAuthStore();
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const [tierMembers, setTierMembers] = useState<ITierMember[]>([]);
  const [membershipLoading, setMembershipLoading] = useState(false);
  const { updateAvatar } = useAvatar();

  useEffect(() => {
    fetchTierMembers();
    // Fetch current avatar when profile page loads
    getCurrentAvatar().catch(err => {
      console.error('Failed to get current avatar:', err);
    });
  }, []);

  const fetchTierMembers = async () => {
    try {
      setMembershipLoading(true);
      // Fetch first 4 tier members for the profile page display
      const response = await membershipApi.getTierMemberList(0, 5);
      const tierMemberData = response?.data || [];
      setTierMembers(tierMemberData);
    } catch (err: unknown) {
      console.error('Failed to fetch tier members:', err);
    } finally {
      setMembershipLoading(false);
    }
  };

  const handleHistoryClick = () => {
    navigate('/transaction-history');
  };

  const handleReceiptClick = () => {
    navigate('/bills');
  };

  const handleOrderClick = () => {
    // TODO: Implement order functionality
  };

  const handleContactSupportClick = () => {
    navigate('/contact');
  };

  const handleLoyaltyCardClick = () => {
    // Navigate to tier member list page
    navigate('/membership/tier-list');
  };

  const handlePointsClick = () => {
    // Navigate to points page
    navigate('/point');
  };

  const handleProfileClick = () => {
    // Open avatar change modal
    setShowAvatarModal(true);
  };

  const handleAvatarChanged = (newAvatarUrl: string) => {
    // Update avatar in the auth store
    updateAvatar(newAvatarUrl);
    // Also update the auth store's currentAvatar directly
    setCurrentAvatar(newAvatarUrl);
  };

  const handleVoucherClick = () => {
    navigate('/my-rewards');
  };

  const handleNotificationClick = () => {
    navigate('/inbox');
  };

  const handleLogoutClick = () => {
    // Show confirmation dialog
    setShowLogoutDialog(true);
  };

  const handleLogoutConfirm = () => {
    // Perform logout
    logout();
    setShowLogoutDialog(false);
    // Navigate to home after logout
    navigate('/');
  };

  const handleLogoutCancel = () => {
    // Close dialog without logout
    setShowLogoutDialog(false);
  };

  // Helper functions for LoyaltyCard
  const getCardStatus = (tierMember?: ITierMember): 'no-join' | 'without-rank' | 'with-rank' | 'max-rank' => {
    if (!tierMember) return 'no-join';
    
    const stateTier = membershipHelpers.getStateTier(tierMember as ITierClassDetail);
    
    switch (stateTier) {
      case 'noRank':
        return 'no-join';
      case 'hasRankTier':
      case 'hasRankAndVoucher':
        return 'with-rank';
      case 'maxTier':
        return 'max-rank';
      default:
        return 'without-rank';
    }
  };

  const calculateProgress = (tierMember?: ITierMember): number => {
    if (!tierMember?.current?.[0] || !tierMember.merchantTiers?.length) return 0;
    
    const currentValue = tierMember.current[0].value;
    const listTier = tierMember.merchantTiers.map(tier => ({
      value: tier.conditions?.[0]?.value || 0,
      tierName: tier.tierName
    }));

    return membershipHelpers.calculateProgress(currentValue, listTier);
  };

  const getPointsToNextLevel = (tierMember?: ITierMember): number => {
    if (!tierMember?.current?.[0] || !tierMember.merchantTiers?.length) return 0;
    
    const currentValue = tierMember.current[0].value;
    const listTier = tierMember.merchantTiers.map(tier => ({
      value: tier.conditions?.[0]?.value || 0
    }));

    return membershipHelpers.totalEarnCurrency(listTier, currentValue);
  };

  // Convert tier members to loyalty card data
  const convertToLoyaltyCards = (tierMembers: ITierMember[]): LoyaltyCardData[] => {
    return tierMembers.slice(0, 3).map((tierMember, index) => ({
      status: getCardStatus(tierMember),
      brandName: tierMember.merchantName || "TapTap",
      userTitle: tierMember.current?.[0]?.tierName || "Thành viên VUI",
      pointsToNextLevel: getPointsToNextLevel(tierMember),
      currentProgress: calculateProgress(tierMember),
      maxProgress: 100,
      badgeIconSrc: tierMember.current?.[0]?.tierLogo,
      brandCode: tierMember.merchantCode || `merchant-${index}`,
      onActionClick: () => handleLoyaltyCardClick(),
      onVoucherClick: () => handleLoyaltyCardClick(),
      // Custom tier styling
      tierBackground: tierMember.tierBackground,
      tierLabelBackground: tierMember.tierLabelBackground,
      tierProgressBarColor: tierMember.tierProgressBarColor,
      merchantBackgroundColor: tierMember.merchantBackgroundColor,
    }));
  };

  return (
    <PageAny className="min-h-screen bg-[#EFF3F6]">
      {/* Container with pink header background */}
      <div className="bg-[#F65D79] pb-1.5">
        <ProfileNavSection
          userName={`${profile.firstname || 'Người dùng'}`}
          userTitle="Đổi avatar"
          avatarUrl={currentAvatar}
          notificationCount={3}
          onProfileClick={handleProfileClick}
          onVoucherClick={handleVoucherClick}
          onNotificationClick={handleNotificationClick}
        />
      </div>
      
      {/* Content area with gradient background */}
      <div className="bg-gradient-to-b from-[#F5F8FA] to-[#EFF3F6]">
        {/* Available Points Section */}
        <div className="bg-white px-4 py-4 mb-2">
          <button
            onClick={handlePointsClick}
            className="w-full flex items-center justify-between hover:bg-gray-50 rounded-lg p-3 -m-3 transition-colors"
          >
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-b from-[#F8DF63] to-[#FED932] flex items-center justify-center">
                <span className="text-xs font-bold text-[#594009]">V</span>
              </div>
              <div className="text-left">
                <div className="text-sm text-[#5A5A5A] mb-0.5" style={{ fontFamily: 'Archia, system-ui, sans-serif' }}>
                  Điểm khả dụng
                </div>
                <div className="text-lg font-bold text-[#1A1818]" style={{ fontFamily: 'Archia, system-ui, sans-serif' }}>
                  {new Intl.NumberFormat('vi-VN').format(profile?.loyaltyPoint || 0)} VUI
                </div>
              </div>
            </div>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M9 18L15 12L9 6"
                stroke="#9A9A9A"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Membership Card Section */}
        {tierMembers.length > 0 && (
          <div className="bg-white px-4 pb-3 mb-2">
            <ProfileSectionHeader title="Thẻ thành viên" />
            <div className="flex justify-center">
              {membershipLoading ? (
                <div className="w-[345px] h-[160px] bg-gray-100 animate-pulse rounded-lg flex items-center justify-center">
                  <p className="text-gray-400">Đang tải...</p>
                </div>
              ) : (
                <LoyaltyCard
                  cards={convertToLoyaltyCards(tierMembers)}
                  theme="merchant-detail"
                />
              )}
            </div>
          </div>
        )}
        
        {/* Menu Items */}
        <div className="space-y-4">
          {/* First Group */}
          <div className="bg-white">
            <ProfileMenuItem
              title="Lịch sử tích - đổi"
              icon={<HistoryIcon />}
              onClick={handleHistoryClick}
            />
            <ProfileMenuItem
              title="Hoá đơn đã chụp"
              icon={<MenuReceiptIcon />}
              onClick={handleReceiptClick}
            />
          </div>
          
          {/* Second Group */}
          <div className="bg-white">
            <ProfileMenuItem
              title="Liên hệ & Hỗ trợ"
              icon={<ContactSupportIcon />}
              onClick={handleContactSupportClick}
            />
          </div>

          {/* Logout Button - Separate section */}
          <div className="px-4 pb-4">
            <button
              onClick={handleLogoutClick}
              className="w-full flex items-center justify-center gap-2 py-3 px-4 bg-white border border-[#F65D79] rounded-lg hover:bg-gray-50 transition-colors"
            >
              <LogoutIcon className="w-6 h-6 text-[#F65D79]" />
              <span className="text-[#F65D79] font-semibold">Đăng xuất</span>
            </button>
          </div>
        </div>
      </div>

      {/* Logout Confirmation Dialog */}
      <LogoutConfirmationDialog
        open={showLogoutDialog}
        onClose={handleLogoutCancel}
        onConfirm={handleLogoutConfirm}
        maxDeviceNumber={3}
      />

      {/* Avatar Change Modal */}
      <AvatarModal
        isOpen={showAvatarModal}
        onClose={() => setShowAvatarModal(false)}
        onAvatarChanged={handleAvatarChanged}
      />
    </PageAny>
  );
};

export default ProfilePage;