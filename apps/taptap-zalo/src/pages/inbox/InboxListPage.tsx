import React, { useState, useEffect } from 'react';
import { Page } from 'zmp-ui';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import { NavigationHeader, InboxCard, InboxCardSkeleton, FullWidthTabNavigation, useInboxStore } from '@taptap/shared';
import type { InboxCardProps, TabItem } from '@taptap/shared';

// Import SVG icons from mobile project
import coinIcon from '@shared/assets/icons/inbox-coin.svg';
import voucherIcon from '@shared/assets/icons/inbox-voucher.svg';
import giftIcon from '@shared/assets/icons/inbox-gift.svg';
import newsIcon from '@shared/assets/icons/inbox-info.svg';
import messageIcon from '@shared/assets/icons/inbox-message.svg';
import bellIcon from '@shared/assets/icons/inbox-bell.svg';
import backIcon from '@shared/assets/icons/icon-24px-outline-back.svg';

interface InboxMessage extends Omit<InboxCardProps, 'onClick'> {
  id: string;
}

const PageAny = Page as any;

const InboxListPage: React.FC = () => {
  const { navigate } = useZaloNavigation();
  const [activeTab, setActiveTab] = useState('history');
  
  const {
    items,
    loading,
    error,
    hasMore,
    fetchInboxList,
    markAsRead,
    reset
  } = useInboxStore();

  const tabs: TabItem[] = [
    { id: 'history', label: 'Lịch sử' },
    { id: 'campaign', label: 'Tin mới' }
  ];

  // Helper function to map inbox type to icon type (matching mobile CardInbox)
  const mapInboxTypeToIconType = (type: string): InboxCardProps['type'] => {
    const typeMap: Record<string, InboxCardProps['type']> = {
      'VUI': 'point',
      'BRAND_CURRENCY': 'point', 
      'VUI_BRAND_CURRENCY': 'point',
      'POINT_EARNED': 'point',
      'POINT_USED': 'point',
      'VOUCHER': 'voucher',
      'VOUCHER_RECEIVED': 'voucher',
      'VOUCHER_USED': 'voucher',
      'MIXED': 'mixed',
      'CONTENT': 'content',
      'PROMOTION': 'content',
      'NEWS': 'content',
      'ORDER': 'content',
      'DEFAULT': 'content'
    };
    return typeMap[type] || 'content';
  };

  // Helper function to get icon path based on type
  const getIconPath = (type: string): string => {
    const iconMap: Record<string, string> = {
      'POINT_EARNED': coinIcon,
      'POINT_USED': coinIcon,
      'VOUCHER_RECEIVED': voucherIcon,
      'VOUCHER_USED': voucherIcon,
      'VOUCHER_REFUND': voucherIcon,
      'PROMOTION': bellIcon,
      'NEWS': messageIcon,
      'ORDER': messageIcon,
      'ORDER_RECEIVED': giftIcon,
      'ORDER_READY': giftIcon,
      'NOTIFICATION': bellIcon,
      'MESSAGE': messageIcon,
      'DEFAULT': newsIcon
    };
    return iconMap[type] || newsIcon;
  };

  // Helper function to format date and time
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const time = date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
    const formattedDate = date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    return { time, date: formattedDate };
  };

  // Transform store data to component format
  const messages: InboxMessage[] = items.map(item => {
    const { time, date } = formatDateTime(item.createdAt);
    
    return {
      id: item.id,
      type: mapInboxTypeToIconType(item.templateBasicData?.type || 'DEFAULT'),
      title: item.templateBasicData?.title || '',
      subtitle: item.templateBasicData?.body || item.templateBasicData?.subTitle || '',
      time,
      date,
      isRead: item.isSeen,
      iconPath: getIconPath(item.templateBasicData?.type || '')
    };
  });

  useEffect(() => {
    fetchInboxList(true);
    
    return () => {
      reset();
    };
  }, [fetchInboxList, reset]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleMessageClick = async (messageId: string) => {
    // Mark as read in store
    await markAsRead(messageId);
    
    // Navigate to detail page
    navigate(`/inbox/${messageId}`);
  };

  const filteredMessages = activeTab === 'campaign' 
    ? messages.filter(msg => !msg.isRead)
    : messages;


  useEffect(() => {
    const scrollHandler = () => {
      if (loading || !hasMore) return;
      
      const scrollTop = window.scrollY;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      
      if (scrollTop + windowHeight >= documentHeight - 100) {
        fetchInboxList();
      }
    };
    
    window.addEventListener('scroll', scrollHandler);
    return () => window.removeEventListener('scroll', scrollHandler);
  }, [loading, hasMore, fetchInboxList]);

  return (
    <div className="min-h-screen bg-gray-50">
      <NavigationHeader 
        title="Hộp thư"
        leftIcon={<img src={backIcon} alt="Back" width="24" height="24" />}
        onLeftClick={() => navigate(-1)}
      />
      
      <div className="sticky top-[48px] z-10 bg-white">
        <FullWidthTabNavigation
          tabs={tabs}
          activeTabId={activeTab}
          onTabChange={handleTabChange}
        />
      </div>

      <div className="px-4 pt-2 pb-12 bg-gray-50">
        {loading && messages.length === 0 ? (
          <div className="bg-white rounded-lg">
            {Array.from({ length: 5 }, (_, index) => (
              <InboxCardSkeleton 
                key={`skeleton-${index}`} 
                isLast={index === 4}
              />
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-500 mb-4">{error}</p>
            <button 
              onClick={() => fetchInboxList(true)}
              className="text-[#F65D79] underline"
            >
              Thử lại
            </button>
          </div>
        ) : filteredMessages.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {activeTab === 'campaign' ? 'Chưa có tin gì mới' : 'Chưa có thông báo tích - đổi điểm nào\nđược ghi nhận.'}
            </p>
          </div>
        ) : (
          <div className="bg-white rounded-lg">
            {filteredMessages.map((message, index) => (
              <InboxCard
                key={message.id}
                {...message}
                isLast={index === filteredMessages.length - 1}
                onClick={() => handleMessageClick(message.id)}
              />
            ))}
          </div>
        )}
        
        {loading && messages.length > 0 && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F65D79] mx-auto"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InboxListPage;