import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, Button, useInboxStore, resizeImage } from '@taptap/shared';

export type InboxDetailType = 'points' | 'voucher' | 'campaign';

interface InboxDetailData {
  id: string;
  type: InboxDetailType;
  title: string;
  mainMessage: string;
  subMessage: string;
  actionText?: string;
  backgroundImage?: string;
  pointsEarned?: number;
  location?: string;
  campaignDetails?: string;
  rewards?: Array<{
    id: string;
    name: string;
    image: string;
    points?: number;
  }>;
}

const InboxDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const { 
    currentItem, 
    loading, 
    error, 
    fetchInboxItem,
    clearError 
  } = useInboxStore();

  useEffect(() => {
    if (id) {
      fetchInboxItem(id);
    }
    
    return () => {
      clearError();
    };
  }, [id, fetchInboxItem, clearError]);

  // Transform store data to component format
  const inboxData: InboxDetailData | null = currentItem ? {
    id: currentItem.id,
    type: (currentItem.templateBasicData?.type || 'points') as InboxDetailType,
    title: currentItem.templateBasicData?.title || '',
    mainMessage: currentItem.templateBasicData?.body || '',
    subMessage: currentItem.templateBasicData?.subTitle || '',
    actionText: currentItem.templateBasicData?.ctaButton || 'Xem chi tiết',
    backgroundImage: currentItem.templateBasicData?.image,
    pointsEarned: currentItem.templateMetaData?.vuiPoint,
    location: currentItem.templateParams?.location || '',
    campaignDetails: currentItem.templateParams?.details || '',
    rewards: currentItem.templateParams?.rewards || []
  } : null;

  const handleBack = () => {
    navigate('/inbox');
  };

  const handleAction = () => {
    if (!inboxData) return;
    
    if (inboxData.type === 'points') {
      navigate('/exchange');
    } else if (inboxData.type === 'voucher') {
      navigate('/rewards');
    } else if (inboxData.type === 'campaign') {
      // Handle campaign action
      const ctaLink = inboxData.actionText || 'http://www.tinyurl.com';
      window.open(ctaLink, '_blank');
    }
  };

  const BackIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M15 18L9 12L15 6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-[#EFF3F6] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F65D79] mx-auto mb-4"></div>
          <p className="text-[#1A1818] text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (error || !inboxData) {
    return (
      <div className="min-h-screen bg-[#EFF3F6]">
        <NavigationHeader
          title="Thông báo"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
          className="bg-white border-b border-[#ECECEC]"
        />
        <div className="flex flex-col items-center justify-center py-20">
          <p className="text-[#1A1818] text-base mb-4">{error || 'Không tìm thấy thông báo'}</p>
          <Button onClick={handleBack} className="w-32">
            Quay lại
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#EFF3F6]">
      {/* Navigation Header */}
      <NavigationHeader
        title={inboxData.title}
        leftIcon={BackIcon}
        onLeftClick={handleBack}
        className="bg-white border-b border-[#ECECEC]"
      />

      {inboxData.type === 'points' && (
        <PointsEarnedContent data={inboxData} onAction={handleAction} />
      )}

      {inboxData.type === 'voucher' && (
        <VoucherReceivedContent data={inboxData} onAction={handleAction} />
      )}

      {inboxData.type === 'campaign' && (
        <CampaignDetailContent data={inboxData} onAction={handleAction} />
      )}
    </div>
  );
};

// Points Earned Content Component
const PointsEarnedContent: React.FC<{ data: InboxDetailData; onAction: () => void }> = ({ 
  data, 
  onAction 
}) => (
  <div className="px-6 py-8">
    {/* Main Content */}
    <div className="flex flex-col items-center gap-6 mb-8">
      {/* Message */}
      <div className="flex flex-col items-center gap-1">
        <h1 className="text-[18px] font-[700] text-[#1A1818] text-center leading-[24px]" 
            style={{ fontFamily: 'Archia' }}>
          {data.mainMessage}
        </h1>
        <p className="text-[14px] font-[400] text-[#1A1818] text-center leading-[22px] max-w-[327px]" 
           style={{ fontFamily: 'Archia' }}>
          {data.subMessage}
        </p>
      </div>

      {/* Points Card */}
      <div className="bg-white rounded-xl p-4 shadow-lg flex flex-col items-center gap-6 w-[165px]">
        <div className="flex flex-col items-center gap-3">
          {/* Points Display Placeholder */}
          <div className="w-16 h-16 bg-[#F65D79] rounded-full flex items-center justify-center">
            <span className="text-white text-xl font-bold">{data.pointsEarned}</span>
          </div>
        </div>
        <Button 
          onClick={onAction}
          className="w-[85px] h-8 text-sm"
        >
          {data.actionText}
        </Button>
      </div>
    </div>

    {/* Rewards Section */}
    <div className="bg-white rounded-t-lg">
      <div className="flex items-center justify-between p-4">
        <h2 className="text-[16px] font-[700] text-[#1A1818]" 
            style={{ fontFamily: 'Archia' }}>
          Đổi nhiều nhất
        </h2>
      </div>
      <div className="px-4 pb-4">
        <div className="flex gap-3 overflow-x-auto">
          {data.rewards?.map((reward) => (
            <div key={reward.id} className="min-w-[120px] bg-gray-100 rounded-lg p-3">
              <div className="w-full h-20 bg-gray-200 rounded-lg mb-2"></div>
              <p className="text-xs text-[#1A1818] font-medium">{reward.name}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

// Voucher Received Content Component
const VoucherReceivedContent: React.FC<{ data: InboxDetailData; onAction: () => void }> = ({ 
  data, 
  onAction 
}) => (
  <div className="px-4 py-8">
    <div className="flex flex-col items-center gap-6">
      <div className="flex flex-col items-center gap-1">
        <h1 className="text-[18px] font-[700] text-[#1A1818] text-center leading-[24px]" 
            style={{ fontFamily: 'Archia' }}>
          {data.mainMessage}
        </h1>
        <p className="text-[14px] font-[400] text-[#1A1818] text-center leading-[22px]" 
           style={{ fontFamily: 'Archia' }}>
          {data.subMessage}
        </p>
      </div>

      <div className="bg-white rounded-xl p-6 shadow-lg flex flex-col items-center gap-6">
        <div className="flex flex-col items-center gap-3">
          {/* Voucher List Placeholder */}
          <div className="w-40 h-24 bg-[#F65D79] rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-semibold">Voucher List</span>
          </div>
        </div>
        <Button 
          onClick={onAction}
          className="w-[85px] h-8 text-sm"
        >
          {data.actionText}
        </Button>
      </div>
    </div>
  </div>
);

// Campaign Detail Content Component  
const CampaignDetailContent: React.FC<{ data: InboxDetailData; onAction: () => void }> = ({ 
  data, 
  onAction 
}) => (
  <>
    {/* Background Section */}
    <div 
      className="h-[221px] bg-gradient-to-r from-[#EAB494] to-[#D4A574] flex items-center justify-center"
      style={{
        backgroundImage: data.backgroundImage ? `url(${resizeImage(data.backgroundImage, { width: 828, height: 442, quality: 85, fit: 'cover' })})` : undefined,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      {/* Background content can go here */}
    </div>

    {/* Main Title */}
    <div className="px-4 py-4">
      <h1 className="text-[24px] font-[700] text-[#1A1818] text-center leading-[32px]" 
          style={{ fontFamily: 'Archia' }}>
        🎉 Trúng rồi Lan Van ơiii
      </h1>
    </div>

    {/* Campaign Details */}
    <div className="px-4 pb-24">
      <p className="text-[14px] font-[400] text-[#1A1818] leading-[22px]" 
         style={{ fontFamily: 'Archia' }}>
        {data.campaignDetails || `Ố là là! Chúc mừng bạn đã nhận được combo quà từ ${data.location} trị giá 470k từ minigame.

⏰ Quà của bạn sẽ được chuyển đi từ ngày 08.02.2025. Nhớ chụp hình khoe ${data.location} khi nhận được quà nhen!

Xem qua link: http://www.tinyurl.com`}
      </p>
    </div>

    {/* Bottom CTA Panel */}
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-[#ECECEC] px-4 py-4 shadow-lg">
      <div className="w-full h-[1px] bg-[#ECECEC] mb-4"></div>
      <Button 
        onClick={onAction}
        className="w-full h-11"
      >
        Xem ngay
      </Button>
    </div>
  </>
);

export default InboxDetailPage;