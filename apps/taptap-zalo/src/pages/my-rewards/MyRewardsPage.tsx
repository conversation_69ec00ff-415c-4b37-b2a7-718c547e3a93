import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  NavigationHeader, 
  SwiperTabContainer, 
  MyRewardItem, 
  useMyRewards,
  usePullToRefresh,
  useMyRewardsStore
} from '@taptap/shared';
import type { SwiperTabContainerItem, MyRewardItemProps, VoucherListItem } from '@taptap/shared';

const MyRewardsPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('available');
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreTriggerRef = useRef<HTMLDivElement>(null);
  
  // Use the store for voucher counts
  const { voucherCounts, fetchVoucherCounts } = useMyRewardsStore();
  
  // Use the real API hook
  const { 
    activeVouchers, 
    redeemedVouchers, 
    expiredVouchers, 
    loading, 
    loadingMore,
    refreshing,
    error, 
    refresh,
    loadMore,
    hasMore
  } = useMyRewards();

  // Fetch counts on mount and when vouchers change
  useEffect(() => {
    fetchVoucherCounts();
  }, [fetchVoucherCounts, activeVouchers, redeemedVouchers, expiredVouchers]);

  // Enhanced refresh function that also refreshes counts
  const handleRefresh = useCallback(async () => {
    await Promise.all([
      refresh(),
      fetchVoucherCounts()
    ]);
  }, [refresh, fetchVoucherCounts]);

  // Use pull to refresh hook
  const { isRefreshing: pullRefreshing } = usePullToRefresh({
    onRefresh: handleRefresh,
    isDisabled: false
  });

  const tabs: SwiperTabContainerItem[] = [
    { id: 'available', label: 'Đang có', badge: true, badgeText: `(${voucherCounts.numberOfActive})` },
    { id: 'used', label: 'Đã dùng', badge: true, badgeText: `(${voucherCounts.numberOfRedeemed})` },
    { id: 'expired', label: 'Hết hạn', badge: true, badgeText: `(${voucherCounts.numberOfExpired})` }
  ];

  const getCurrentVouchers = (): VoucherListItem[] => {
    switch (activeTab) {
      case 'available':
        return activeVouchers;
      case 'used':
        return redeemedVouchers;
      case 'expired':
        return expiredVouchers;
      default:
        return [];
    }
  };

  const filteredRewards = getCurrentVouchers();

  const handleRewardClick = (voucher: VoucherListItem) => {
    console.log('Voucher clicked:', voucher);
    navigate(`/my-rewards/${voucher.codeId}`);
  };

  // Convert VoucherListItem to MyRewardItemProps format
  const mapVoucherToItemProps = (voucher: VoucherListItem): MyRewardItemProps => {
    const isExpiredOrUsed = activeTab === 'expired' || activeTab === 'used';
    
    return {
      brand: voucher.merchantName,
      title: voucher.name,
      expiryDate: voucher.endTime,
      logoSrc: voucher.merchantLogo || voucher.thumbnails || undefined,
      // Always allow click to view details
      onClick: () => handleRewardClick(voucher),
      // Apply grayscale styling but keep clickable
      disabled: false, // Keep clickable
      isGrayscale: isExpiredOrUsed, // Only apply visual styling
    };
  };


  // Handle load more for current tab
  const handleLoadMore = useCallback(() => {
    const tabMap: { [key: string]: 'active' | 'redeemed' | 'expired' } = {
      'available': 'active',
      'used': 'redeemed',
      'expired': 'expired'
    };
    const tab = tabMap[activeTab];
    if (tab) {
      loadMore(tab);
    }
  }, [activeTab, loadMore]);

  // Check if current tab has more data
  const currentTabHasMore = useCallback(() => {
    const tabMap: { [key: string]: 'active' | 'redeemed' | 'expired' } = {
      'available': 'active',
      'used': 'redeemed',
      'expired': 'expired'
    };
    const tab = tabMap[activeTab];
    return tab ? hasMore(tab) : false;
  }, [activeTab, hasMore]);

  // Intersection Observer for infinite loading
  useEffect(() => {
    const trigger = loadMoreTriggerRef.current;
    if (!trigger) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !loadingMore && currentTabHasMore()) {
          handleLoadMore();
        }
      },
      {
        root: scrollContainerRef.current,
        rootMargin: '100px', // Start loading 100px before the trigger becomes visible
        threshold: 0.1
      }
    );

    observer.observe(trigger);

    return () => {
      observer.unobserve(trigger);
    };
  }, [loadingMore, currentTabHasMore, handleLoadMore]);

  return (
    <div className="min-h-screen bg-[#EFF3F6]">
      
      {/* Navigation Header */}
      <div className="bg-white border-b border-[#ECECEC]">
        <NavigationHeader 
          title="Ưu đãi của bạn"
          className="h-[48px]"
          showBackButton={true}
          onBackClick={() => navigate(-1)}
        />
      </div>

      {/* Swiper Tab Container with Swipe Navigation */}
      <SwiperTabContainer
        tabs={tabs}
        activeTabId={activeTab}
        onTabChange={setActiveTab}
        contentClassName="bg-[#EFF3F6] flex-1"
        containerClassName="flex-1"
      >
        {/* Scrollable Rewards List */}
        <div 
          ref={scrollContainerRef}
          className="flex-1 overflow-y-auto"
          style={{ height: 'calc(100vh - 200px)' }} // Adjust based on header heights
        >
          <div className="px-4 py-4">
            {loading && !refreshing && !pullRefreshing ? (
              <div className="flex justify-center items-center py-8">
                <div className="text-gray-500">Đang tải...</div>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center py-8">
                <div className="text-red-500 mb-4">{error}</div>
                <button 
                  onClick={handleRefresh}
                  className="bg-blue-500 text-white px-4 py-2 rounded-lg"
                >
                  Thử lại
                </button>
              </div>
            ) : filteredRewards.length === 0 ? (
              <div className="flex justify-center items-center py-8">
                <div className="text-gray-500">Không có ưu đãi nào</div>
              </div>
            ) : (
              <>
                <div className="space-y-3">
                  {filteredRewards.map((voucher, index) => (
                    <MyRewardItem
                      key={`${activeTab}-${voucher.codeId}-${index}`}
                      {...mapVoucherToItemProps(voucher)}
                    />
                  ))}
                </div>
                
                {/* Intersection Observer Trigger - invisible element to detect scroll */}
                {currentTabHasMore() && (
                  <div 
                    ref={loadMoreTriggerRef}
                    className="h-1 w-full"
                    aria-hidden="true"
                  />
                )}
                
                {/* Loading indicator for infinite scroll */}
                {loadingMore && (
                  <div className="flex justify-center items-center py-4">
                    <div className="text-gray-500 text-sm">Đang tải thêm...</div>
                  </div>
                )}

                {/* End of list indicator */}
                {!currentTabHasMore() && filteredRewards.length > 0 && (
                  <div className="flex justify-center items-center py-4">
                    <div className="text-gray-400 text-sm">Đã hết dữ liệu</div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </SwiperTabContainer>

      {/* Bottom Navigation would be handled by the main app layout */}
    </div>
  );
};

export default MyRewardsPage;