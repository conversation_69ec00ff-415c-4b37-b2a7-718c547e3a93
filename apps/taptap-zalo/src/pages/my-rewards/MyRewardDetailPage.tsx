import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  NavigationHeader,
  Button,
  ImageGallery,
  GalleryImage,
  SectionInfo,
  ContactInfo,
  SectionHowToUse,
  EnhancedBarcodeCard,
  Toast,
  useToast,
  WarningSheet,
  resizeImage,
} from '@taptap/shared';
import { myRewardsAPI as myRewardsAPIModule } from '@taptap/shared';
import type { MyRewardDetail } from '@taptap/shared';

// Import the downloaded images (fallback logos)
import dashiKitchenLogo from '../../assets/images/logo-dashi-kitchen.png';
import rosieVintageLogo from '../../assets/images/logo-rosie-vintage.png';

// Constants
const MESSAGES = {
  LOADING: 'Đang tải...',
  INVALID_ID: 'ID voucher không hợp lệ',
  FETCH_ERROR: 'Không thể tải thông tin phần thưởng',
  MARKED_AS_USED: 'Đã đánh dấu voucher là đã sử dụng',
  UNMARKED_AS_USED: 'Đã bỏ đánh dấu voucher đã sử dụng',
  UPDATE_STATUS_ERROR: 'Không thể cập nhật trạng thái voucher',
  CODE_COPIED: 'Mã đã được sao chép!',
  COPY_ERROR: 'Không thể sao chép mã',
  ACTIVATE_SUCCESS: 'Voucher đã được kích hoạt thành công!',
  ACTIVATE_ERROR: 'Không thể kích hoạt voucher',
} as const;

// Helper Functions
const getFallbackLogo = (merchantName?: string): string => {
  if (merchantName?.toLowerCase().includes('dashi')) {
    return dashiKitchenLogo;
  }
  if (merchantName?.toLowerCase().includes('rosie')) {
    return rosieVintageLogo;
  }
  return dashiKitchenLogo;
};

const formatExpiryDate = (dateString?: string): string => {
  if (!dateString) return 'Không có hạn sử dụng';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  } catch {
    return dateString;
  }
};

const getGalleryImages = (images: string[], merchantName?: string): GalleryImage[] => {
  return images.map((image, index) => ({
    id: `image-${index}`,
    src: image,
    alt: `${merchantName || 'Reward'} image ${index + 1}`,
    thumbnail: image,
  }));
};

const getContactInfo = (): ContactInfo => ({
  website: undefined,
  phone: undefined,
  email: undefined,
  facebook: undefined,
  zalo: undefined,
});

// Validation Functions
const canUseVoucher = (reward: MyRewardDetail | null): boolean => {
  if (!reward) return false;
  
  const now = new Date();
  const endTime = new Date(reward.endTime);
  const startTime = new Date(reward.startTime);
  
  return (
    reward.ownershipStatus !== 'TRANSFERRED' &&
    reward.status !== 3 && // DONE
    reward.status !== 4 && // EXPIRED
    now >= startTime &&
    now <= endTime
  );
};

const canTransferVoucher = (reward: MyRewardDetail | null): boolean => {
  if (!reward) return false;
  
  return (
    reward.allowTransfer &&
    reward.ownershipStatus === 'OWNED' &&
    reward.status === 1 &&
    canUseVoucher(reward)
  );
};

// Component: Mark as Used Toggle
interface MarkAsUsedToggleProps {
  isMarkedAsUsed: boolean;
  markingAsUsed: boolean;
  onToggle: () => void;
}

const MarkAsUsedToggle: React.FC<MarkAsUsedToggleProps> = ({
  isMarkedAsUsed,
  markingAsUsed,
  onToggle,
}) => (
  <div className="px-4 py-3 bg-white mx-4 my-4 rounded-lg">
    <div className="flex justify-between items-center">
      <div className="flex-1">
        <p className="font-semibold text-base">Đánh dấu đã sử dụng</p>
        <p className="text-sm text-gray-500 mt-1">
          Giúp bạn nhớ voucher này đã được sử dụng
        </p>
      </div>
      <button
        onClick={onToggle}
        disabled={markingAsUsed}
        className={`
          w-14 h-8 rounded-full transition-colors duration-200 relative
          ${isMarkedAsUsed ? 'bg-[#0DC98B]' : 'bg-gray-300'}
          ${markingAsUsed ? 'opacity-50' : ''}
        `}
      >
        <span
          className={`
            absolute top-1 transition-transform duration-200
            w-6 h-6 bg-white rounded-full shadow-md
            ${isMarkedAsUsed ? 'translate-x-7' : 'translate-x-1'}
          `}
        />
      </button>
    </div>
  </div>
);

// Component: Transfer Button
interface TransferButtonProps {
  onTransfer: () => void;
}

const TransferButton: React.FC<TransferButtonProps> = ({ onTransfer }) => (
  <div className="px-4 my-4">
    <Button
      onClick={onTransfer}
      variant="outline"
      className="w-full"
    >
      <span className="flex items-center justify-center gap-2">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path
            d="M14 7L17 10M17 10L14 13M17 10H7M6 7L3 10M3 10L6 13M3 10H13"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        Chuyển voucher cho bạn bè
      </span>
    </Button>
  </div>
);

// Back Icon
const BackIcon = (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path
      d="M15 18L9 12L15 6"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const MyRewardDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [reward, setReward] = useState<MyRewardDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [, setError] = useState<string | null>(null);
  const [isMarkedAsUsed, setIsMarkedAsUsed] = useState(false);
  const [markingAsUsed, setMarkingAsUsed] = useState(false);
  const [activatingAutoRedeem, setActivatingAutoRedeem] = useState(false);
  const [showAutoRedeemModal, setShowAutoRedeemModal] = useState(false);
  const { toast, showSuccessToast, showErrorToast, hideToast } = useToast();

  useEffect(() => {
    const fetchRewardDetail = async () => {
      if (!id) {
        setError(MESSAGES.INVALID_ID);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Call the API to get reward detail
        const response = await myRewardsAPIModule.myRewardsAPI.getMyRewardDetail(id);
        setReward(response.data);
        setIsMarkedAsUsed(response.data.markAsUsed || false);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : MESSAGES.FETCH_ERROR;
        setError(errorMessage);
        console.error('Error fetching reward detail:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchRewardDetail();
  }, [id]);

  const handleBack = () => {
    navigate('/my-rewards');
  };

  const handleMarkAsUsed = async () => {
    if (!reward || !id) return;

    try {
      setMarkingAsUsed(true);
      const newStatus = !isMarkedAsUsed;
      
      // Call API to mark as used
      await myRewardsAPIModule.myRewardsAPI.markAsUsed(id, newStatus);
      
      setIsMarkedAsUsed(newStatus);
      
      const message = newStatus ? MESSAGES.MARKED_AS_USED : MESSAGES.UNMARKED_AS_USED;
      showSuccessToast(message);
    } catch (error) {
      console.error('Error marking voucher as used:', error);
      showErrorToast(MESSAGES.UPDATE_STATUS_ERROR);
    } finally {
      setMarkingAsUsed(false);
    }
  };

  const handleTransferVoucher = () => {
    if (!reward) return;
    navigate(`/my-rewards/${id}/transfer`);
  };

  const handleCopyCode = () => {
    if (!reward || !reward.code) return;
    
    navigator.clipboard.writeText(reward.code)
      .then(() => showSuccessToast(MESSAGES.CODE_COPIED))
      .catch(() => showErrorToast(MESSAGES.COPY_ERROR));
  };

  const handleActivateAutoRedeem = () => {
    setShowAutoRedeemModal(true);
  };

  const handleCancelAutoRedeem = () => {
    setShowAutoRedeemModal(false);
  };

  const handleConfirmAutoRedeem = async () => {
    if (!reward || !id) return;

    try {
      setActivatingAutoRedeem(true);
      
      // Call API to activate auto redeem
      const response = await myRewardsAPIModule.myRewardsAPI.redeemAutoVoucher(id);
      
      // Check if response is successful
      if (response?.status?.success === false) {
        // Show error message from server
        // Example: "Hãy chờ đến 2025-12-10 13:46:19 để dùng ưu đãi này"
        showErrorToast(response.status.message || MESSAGES.ACTIVATE_ERROR);
        setShowAutoRedeemModal(false);
        return;
      }
      
      // Update reward data with new code if successful
      if (response?.data) {
        setReward(response.data);
        showSuccessToast(MESSAGES.ACTIVATE_SUCCESS);
      }
      
      // Close modal
      setShowAutoRedeemModal(false);
      
    } catch (error) {
      console.error('Error activating auto redeem:', error);
      
      // The error might be the response itself with status object
      let errorMessage = MESSAGES.ACTIVATE_ERROR;
      
      // Check various possible error structures
      const err = error as any; // Type assertion for error handling
      if (err?.status?.message) {
        // Direct status object in error
        errorMessage = err.status.message;
      } else if (err?.response?.data?.status?.message) {
        // Axios error response
        errorMessage = err.response.data.status.message;
      } else if (err?.response?.status?.message) {
        errorMessage = err.response.status.message;
      } else if (err?.message) {
        // Standard Error object
        errorMessage = err.message;
      }
      
      showErrorToast(errorMessage);
      setShowAutoRedeemModal(false);
    } finally {
      setActivatingAutoRedeem(false);
    }
  };


  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Chi tiết ưu đãi"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
        />
        <div className="flex items-center justify-center pt-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F65D79] mx-auto mb-4"></div>
            <p className="text-[#9A9A9A]">{MESSAGES.LOADING}</p>
          </div>
        </div>
      </div>
    );
  }

  // if (error || !reward) {
  //   return (
  //     <div className="min-h-screen bg-gray-50">
  //       <NavigationHeader
  //         title="Chi tiết ưu đãi"
  //         leftIcon={BackIcon}
  //         onLeftClick={handleBack}
  //       />
  //       <div className="flex items-center justify-center pt-20">
  //         <div className="text-center">
  //           <p className="text-red-500 mb-4">{error || 'Phần thưởng không tìm thấy'}</p>
  //           <Button onClick={handleBack} variant="outline">
  //             Quay lại
  //           </Button>
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="min-h-screen bg-[#EFF3F6]">
      {/* Navigation Header with green background */}
      <div className="bg-[#0DC98B]">
        <NavigationHeader
          title="Chi tiết ưu đãi"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
          className="text-white"
        />
      </div>

      <div className="max-w-md mx-auto relative">


        {/* Enhanced Barcode Card - handles both auto-redeem and normal vouchers */}
        <div className="flex justify-center">
          <EnhancedBarcodeCard
            brandName={reward.merchantName || 'Thương hiệu'}
            brandLogo={resizeImage(reward.merchantLogo || getFallbackLogo(reward.merchantName), { width: 160, height: 160, quality: 85, fit: 'cover' })}
            title={reward.name}
            barcodeValue={reward.code}
            expiryDate={formatExpiryDate(reward.endTime)}
            isAutoRedeem={reward.autoRedeem}
            autoRedeemMinutes={reward.timeRedeemAuto ? parseInt(reward.timeRedeemAuto) : undefined}
            executeAutoRedeemDate={reward.executeAutoRedeemDate}
            onActivateAutoRedeem={handleActivateAutoRedeem}
            activatingAutoRedeem={activatingAutoRedeem}
            pointsValue="20k"
            onCopyCode={handleCopyCode}
            countdownEndTime={reward.endTime}
            showCountdown={false}
            canUseNow={canUseVoucher(reward)}
            canTransfer={canTransferVoucher(reward)}
            canMarkAsUsed={reward.enableMarkAsUsed && canUseVoucher(reward)}
          />
        </div>


        {/* Mark as Used Toggle */}
        {reward.enableMarkAsUsed && canUseVoucher(reward) && !reward.autoRedeem && reward.code && (
          <MarkAsUsedToggle
            isMarkedAsUsed={isMarkedAsUsed}
            markingAsUsed={markingAsUsed}
            onToggle={handleMarkAsUsed}
          />
        )}

        {/* Transfer Button */}
        {/* // TODO CHƯA LÀM */}
        {/* {canTransferVoucher(reward) && !reward.autoRedeem && reward.code && (
          <TransferButton onTransfer={handleTransferVoucher} />
        )} */}



        {/* How to Use Section */}
        <div className="my-4">
          <SectionHowToUse className="w-full" />
        </div>


        {/* Section Info */}
        <div className="my-4">
          <SectionInfo
            description={reward.description || reward.tnc || 'Thông tin chi tiết về voucher'}
            contactInfo={getContactInfo()}
            onWebsiteClick={() => alert('Thông tin website không có sẵn')}
            onPhoneClick={() => alert('Thông tin điện thoại không có sẵn')}
            onEmailClick={() => alert('Thông tin email không có sẵn')}
            onEmailCopy={() => alert('Thông tin email không có sẵn')}
            onFacebookClick={() => alert('Thông tin Facebook không có sẵn')}
            onZaloClick={() => alert('Thông tin Zalo không có sẵn')}
          />
        </div>


        {/* Image Gallery */}
        {(reward.images || reward.thumbnails || reward.avatarImage) && (
          <div className="my-4">
            <ImageGallery
              title="Hình ảnh"
              images={getGalleryImages(
                [reward.images || reward.thumbnails || reward.avatarImage!].filter(Boolean),
                reward.merchantName
              )}
              className="w-full"
            />
          </div>
        )}
      </div>

      {/* Auto Redeem Warning Sheet */}
      <WarningSheet
        isOpen={showAutoRedeemModal}
        onClose={handleCancelAutoRedeem}
        title="Cần dùng ưu đãi ngay?"
        warningList={[
          'Hệ thống sẽ kích hoạt sử dụng voucher ngay sau khi bạn xác nhận.',
          'Voucher sẽ hết hạn sử dụng sau'
        ]}
        timeLimit={reward.timeRedeemAuto ? parseInt(reward.timeRedeemAuto) : undefined}
        primaryButtonText={activatingAutoRedeem ? 'Đang xử lý...' : 'Xác nhận'}
        secondaryButtonText="Để sau"
        onPrimaryAction={handleConfirmAutoRedeem}
        onSecondaryAction={handleCancelAutoRedeem}
        primaryButtonLoading={activatingAutoRedeem}
        primaryButtonDisabled={activatingAutoRedeem}
        showMascot={true}
        closeOnBackdropClick={!activatingAutoRedeem}
      />

      {/* Toast */}
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
        position="bottom"
      />
    </div>
  );
};

export default MyRewardDetailPage;