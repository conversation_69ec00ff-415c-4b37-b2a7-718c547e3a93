import React from 'react';
import { ZaloDataHubEntrance } from '@taptap/shared';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';

/**
 * Intro Page for Zalo Mini App
 * First page user sees if not logged in
 * Redirects to login when "<PERSON>ên kết số điện thoại" button is clicked
 */
const IntroPage: React.FC = () => {
  const { navigate } = useZaloNavigation();
  
  console.log('🎯 IntroPage component loaded');

  const handleLinkPhone = () => {
    // Navigate to login page instead of data hub functionality
    console.log('🚀 IntroPage: Navigating to login page...');
    navigate('/login');
  };

  const handleDecline = () => {
    // Navigate back to previous page or close app
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Main Content */}
      <ZaloDataHubEntrance 
        onLinkPhone={handleLinkPhone}
        onDecline={handleDecline}
      />
    </div>
  );
};

export default IntroPage;