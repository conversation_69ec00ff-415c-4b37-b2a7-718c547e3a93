import React from 'react';
import { Page } from 'zmp-ui';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import { 
  NavigationHeader,
  InformFlashSale,
  RewardListCard,
  LoadingSpinner,
  EmptyBillState,
  BackIcon,
  useFlashSale
} from '@taptap/shared';
import type { RewardItemType } from '@taptap/shared';

const PageAny = Page as any;

export const FlashSalePage: React.FC = () => {
  const { navigate } = useZaloNavigation();
  
  // Parse params from URL since Zalo doesn't have useSearchParams
  const getUrlParams = () => {
    const params = new URLSearchParams(window.location.search);
    return {
      collectionCode: params.get('collectionCode') || 'flash-sale',
      collectionName: params.get('collectionName') || 'Flash Sale',
      hasFlashSale: params.get('hasFlashSale') === 'true',
      status: params.get('status') || ''
    };
  };
  
  // Get params from URL
  const { collectionCode, collectionName, hasFlashSale, status } = getUrlParams();
  
  // Use extracted hook
  const {
    flashSaleCampaign,
    rewardList,
    loading,
    error,
    currentDateTime,
    handleCountdownCallback,
    handleRewardClick,
    handleBack,
    isFlashSaleActive,
    isCountdownActive,
    hasTeasing,
    backgroundColor,
  } = useFlashSale({
    collectionCode,
    hasFlashSale,
    status,
    onNavigate: (path: string) => navigate(path),
    onNavigateBack: (steps?: number) => navigate('/'),
  });


  return (
    <PageAny className="w-full min-h-screen bg-[#EFF3F6] flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title={collectionName}
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        className={isFlashSaleActive ? "bg-[#F65D79] text-white border-b border-[#F65D79]" : ""}
      />

      {/* Flash Sale Info Bar */}
      {(isFlashSaleActive || isCountdownActive || hasTeasing) && (
        <InformFlashSale
          campaign={flashSaleCampaign}
          currentDateTime={currentDateTime}
          callback={handleCountdownCallback}
          canPurchase={true}
        />
      )}

      {/* Main Content */}
      <div 
        className="flex-1 px-4 py-4 pb-20"
        style={{ backgroundColor }}
      >
        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-16">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <EmptyBillState
            title="Có lỗi xảy ra"
            description={error}
            mascotName="error"
            onActionClick={() => window.location.reload()}
            actionText="Thử lại"
          />
        )}

        {/* Flash Sale Content */}
        {!loading && !error && (
          <>
            {/* Rewards List */}
            {rewardList.length > 0 ? (
              <div className="space-y-3">
                {rewardList.map((reward) => {
                  // Flash sale pricing logic based on mobile implementation
                  const hasFlashSalePrice = reward.displayOnPrice && 
                    reward.issueVUIPointFlsOrPP && 
                    reward.issueVUIPointFlsOrPP !== reward.issueVUIPoint;
                  
                  return (
                    <RewardListCard
                      key={reward.id}
                      id={reward.id}
                      brand={reward.merchant?.name}
                      title={reward.name}
                      points={hasFlashSalePrice ? undefined : (reward.issueVUIPoint || 0)}
                      originalPoints={hasFlashSalePrice ? reward.issueVUIPoint : undefined}
                      flashSalePoints={hasFlashSalePrice ? reward.issueVUIPointFlsOrPP : undefined}
                      logoSrc={reward.merchant?.logo || '/shared/assets/images/placeholder-logo.png'}
                      imageSrc={reward.image1 || '/shared/assets/images/placeholder-reward.png'}
                      discountPercent={reward.discountPercentage || 0}
                      available={true}
                      expiry={reward.expiryDate || undefined}
                      onClick={() => handleRewardClick(reward)}
                    />
                  );
                })}
              </div>
            ) : (
              <EmptyBillState
                title="Chưa có sản phẩm Flash Sale"
                description="Hiện tại chưa có sản phẩm nào trong chương trình Flash Sale. Hãy quay lại sau nhé!"
                mascotName="meditate"
                onActionClick={() => navigate('/exchange')}
                actionText="Xem sản phẩm khác"
              />
            )}
          </>
        )}
      </div>

    </PageAny>
  );
};

export default FlashSalePage;