import React, { useState, useCallback } from 'react';
import { Page } from 'zmp-ui';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import { ChevronLeft } from 'lucide-react';
import { useTransactionHistory } from './hooks/useTransactionHistory';
import { MonthFilter } from './components/MonthFilter';
import { TransactionItem } from './components/TransactionItem';
import { EmptyState } from './components/EmptyState';

const PageAny = Page as any;

const TransactionHistoryPage: React.FC = () => {
  const { navigate } = useZaloNavigation();
  const [dateSelect, setDateSelect] = useState(new Date());
  
  const {
    data: transactions,
    isLoading,
    isError,
    isRefreshing,
    onRefresh,
    onLoadMore,
    hasMore
  } = useTransactionHistory(dateSelect);

  const handleDateChange = useCallback((newDate: Date) => {
    setDateSelect(newDate);
  }, []);

  const handleBack = () => {
    navigate('/profile');
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="bg-white">
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className="px-4 py-4 border-b border-[#F0F0F0]">
          <div className="flex items-center">
            <div className="w-9 h-9 bg-gray-200 rounded-full animate-pulse" />
            <div className="flex-1 ml-2 mr-6">
              <div className="h-4 bg-gray-200 rounded animate-pulse mb-2" />
              <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3" />
            </div>
            <div className="flex flex-col items-end">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-20 mb-1" />
              <div className="h-3 bg-gray-200 rounded animate-pulse w-16" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  if (isError) {
    return (
      <PageAny className="min-h-screen bg-[#EFF3F6] flex items-center justify-center">
        <div className="text-center px-4">
          <p className="text-[#5A5A5A] mb-4" style={{ fontFamily: 'Archia, system-ui, sans-serif' }}>
            Có lỗi xảy ra khi tải dữ liệu
          </p>
          <button 
            onClick={onRefresh}
            className="px-6 py-3 bg-[#F65D79] text-white rounded-lg font-semibold"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            Thử lại
          </button>
        </div>
      </PageAny>
    );
  }

  return (
    <PageAny className="min-h-screen bg-[#fff]">
      {/* Header */}
      <div className="bg-white sticky top-0 z-10 border-b border-[#F0F0F0]">
        <div className="flex items-center px-4 py-3">
          <button 
            onClick={handleBack}
            className="p-2 -ml-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <ChevronLeft className="w-5 h-5 text-[#1A1818]" />
          </button>
          <h1 
            className="flex-1 text-center text-base font-bold text-[#1A1818]"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            Lịch sử tích - đổi
          </h1>
          <div className="w-9" />
        </div>
      </div>

      {/* Month Filter */}
      <div className="mt-6">
        <MonthFilter 
          value={dateSelect}
          onChange={handleDateChange}
        />
      </div>

      {/* Content */}
      <div className="mt-4">
        {isLoading && transactions.length === 0 ? (
          <LoadingSkeleton />
        ) : transactions.length === 0 ? (
          <div className="mt-16">
            <EmptyState />
          </div>
        ) : (
          <div className="bg-white">
            {transactions.map((transaction, index) => (
              <TransactionItem 
                key={transaction.id}
                transaction={transaction}
                isLast={index === transactions.length - 1}
              />
            ))}
            
            {/* Load more */}
            {hasMore && !isLoading && (
              <div className="py-4 text-center border-t border-[#F0F0F0]">
                <button
                  onClick={onLoadMore}
                  className="text-[#F65D79] text-sm font-semibold"
                  style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
                >
                  Xem thêm
                </button>
              </div>
            )}

            {isLoading && transactions.length > 0 && (
              <div className="flex justify-center py-4 border-t border-[#F0F0F0]">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#F65D79]"></div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Bottom spacing */}
      <div className="h-12" />
    </PageAny>
  );
};

export default TransactionHistoryPage;