import { useState, useEffect, useCallback } from 'react';
import { transactionApi, type IHistoryPoint } from '@taptap/shared';

// Re-export for backward compatibility
export type { IHistoryPoint };

const LIMIT = 10;

export const useTransactionHistory = (dateSelect: Date) => {
  const [data, setData] = useState<IHistoryPoint[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isError, setIsError] = useState(false);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const fetchHistory = async (currentOffset: number, isRefresh = false) => {
    try {
      if (!isRefresh) setIsLoading(true);
      
      const response = await transactionApi.getTransactionHistoryByMonth(
        dateSelect,
        currentOffset,
        LIMIT
      );

      console.log('[fetchHistory]',response)
      
      if (response) {
        const newData = response.filter((item: any) => Object.keys(item).length > 0);
        
        if (currentOffset === 0) {
          setData(newData);
        } else {
          setData(prev => [...prev, ...newData]);
        }
        
        setHasMore(newData.length >= LIMIT);
        setIsError(false);
      }
    } catch (error) {
      console.error('Error fetching transaction history:', error);
      setIsError(true);
    } finally {
      setIsLoading(false);
      if (isRefresh) setIsRefreshing(false);
    }
  };

  useEffect(() => {
    setData([]);
    setOffset(0);
    setHasMore(true);
    fetchHistory(0);
  }, [dateSelect]);

  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    setOffset(0);
    setHasMore(true);
    fetchHistory(0, true);
  }, [dateSelect]);

  const onLoadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      const newOffset = offset + LIMIT;
      setOffset(newOffset);
      fetchHistory(newOffset);
    }
  }, [offset, isLoading, hasMore]);

  return {
    data,
    isLoading,
    isRefreshing,
    isError,
    onRefresh,
    onLoadMore,
    hasMore
  };
};