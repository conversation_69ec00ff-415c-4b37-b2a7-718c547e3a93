import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import moment from 'moment';
import 'moment/locale/vi';

moment.locale('vi');

interface MonthFilterProps {
  value: Date;
  onChange: (date: Date) => void;
}

export const MonthFilter: React.FC<MonthFilterProps> = ({ value, onChange }) => {
  const [showPicker, setShowPicker] = useState(false);

  const handleMonthSelect = (date: Date) => {
    onChange(date);
    setShowPicker(false);
  };

  const formatDisplay = () => {
    return moment(value).format('MMMM YYYY');
  };

  const generateMonthOptions = () => {
    const options = [];
    const currentDate = new Date();
    const startDate = new Date(2019, 0, 1);
    
    const date = new Date(currentDate);
    while (date >= startDate) {
      options.push(new Date(date));
      date.setMonth(date.getMonth() - 1);
    }
    
    return options;
  };

  return (
    <div className="px-4">
      <div className="relative">
        <button
          onClick={() => setShowPicker(!showPicker)}
          className="w-full flex items-center justify-between py-3 bg-white hover:bg-gray-50 transition-colors"
        >
          <span 
            className="text-sm text-[#1A1818]"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            Chọn tháng
          </span>
          <div className="flex items-center gap-2">
            <span 
              className="text-sm font-bold text-[#F65D79] capitalize"
              style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
            >
              {formatDisplay()}
            </span>
            <ChevronDown className={`w-4 h-4 text-[#F65D79] transition-transform ${showPicker ? 'rotate-180' : ''}`} />
          </div>
        </button>

        {showPicker && (
          <>
            <div 
              className="fixed inset-0 z-40"
              onClick={() => setShowPicker(false)}
            />
            <div className="absolute top-full mt-2 right-0 w-64 max-h-80 overflow-auto bg-white rounded-lg shadow-xl border border-[#F0F0F0] z-50">
              <div className="py-2">
                {generateMonthOptions().map((date, index) => {
                  const isSelected = moment(date).isSame(value, 'month');
                  return (
                    <button
                      key={index}
                      onClick={() => handleMonthSelect(date)}
                      className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors capitalize
                        ${isSelected ? 'bg-[#F65D79]/10' : ''}
                      `}
                    >
                      <span 
                        className={`text-sm ${isSelected ? 'font-bold text-[#F65D79]' : 'text-[#1A1818]'}`}
                        style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
                      >
                        {moment(date).format('MMMM YYYY')}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};