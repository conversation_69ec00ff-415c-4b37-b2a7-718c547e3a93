import React, { useEffect } from 'react';
import { Page } from 'zmp-ui';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import {
  NavigationHeader,
  CardVUIPoint,
  CardBrandPoint,
  CardBrandPointSkeleton,
  Mascot,
  useBrandCurrencyList,
  BackIcon,
} from '@taptap/shared';
import { useAuthStore } from '@taptap/shared';

const SKELETON = Array.from({ length: 6 }, (_, i) => i);

const PageAny = Page as any;

export const MyPointPage: React.FC = () => {
  const { navigate } = useZaloNavigation();
  const { profile } = useAuthStore();
  const { brandCurrencyList, loading } = useBrandCurrencyList();

  useEffect(() => {
    // Track page view
  }, []);

  const handleBack = () => {
    navigate('/profile');
  };

  const handleHistoryClick = () => {
    navigate('/transaction-history');
  };

  const onPressCardVuiPoint = () => {
    navigate('/point/vui');
  };

  const renderHistoryEntry = () => (
    <>
      <div className="w-8"></div>
      <button
        onClick={handleHistoryClick}
        className="text-sm text-[#F65D79] font-semibold min-w-[48px] text-right"
        style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
      >
        Lịch sử
      </button>
    </>
  );


  return (
    <PageAny className="w-full min-h-screen bg-[#EFF3F6] flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title="Kho điểm của bạn"
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        rightComponent={renderHistoryEntry()}
        className="bg-white border-b border-[#ECECEC]"
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto pb-20">
        <div className="bg-[#EFF3F6] pt-6 pb-12 flex flex-col items-center px-4">
          {/* Rich Mascot */}
          <Mascot 
            name="rich" 
            className="w-[122px] h-[88px] mb-4"
          />

          {/* VUI Point Card */}
          <CardVUIPoint
            point={profile?.loyaltyPoint || 0}
            onPress={onPressCardVuiPoint}
          />

          {/* Brand Currencies Container */}
          <div className="w-full  mt-6 px-4">
            <div className="flex flex-wrap justify-start">
              {loading
                ? SKELETON.map(item => (
                    <CardBrandPointSkeleton key={item} index={item} />
                  ))
                : brandCurrencyList.map((item, index) => {
                    const onPress = () => {
                      // Navigate to brand currency detail
                      navigate(`/point/brand/${item.currencyCode}`);
                    };

                    return (
                      <CardBrandPoint
                        data={item}
                        key={item.currencyCode}
                        index={index}
                        onPress={onPress}
                      />
                    );
                  })}
            </div>
          </div>
        </div>
      </div>

    </PageAny>
  );
};

export default MyPointPage;