import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  NavigationHeader,
  CameraInterface,
  Card,
  Button,
} from '@taptap/shared';

interface PaymentResult {
  amount: number;
  merchant: string;
  transactionId: string;
  timestamp: Date;
}

const QRPaymentPage: React.FC = () => {
  const navigate = useNavigate();
  const [scanning, setScanning] = useState(false);
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [paymentResult, setPaymentResult] = useState<PaymentResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const cameraRef = useRef<HTMLVideoElement>(null);

  const handleBack = () => {
    navigate('/');
  };

  const handleStartScan = () => {
    setScanning(true);
    setError(null);
    // In real app, would start camera and QR scanning
    console.log('Starting QR scan...');
  };

  const handleStopScan = () => {
    setScanning(false);
    setFlashEnabled(false);
    // In real app, would stop camera
    console.log('Stopping QR scan...');
  };

  const handleFlashToggle = () => {
    setFlashEnabled(!flashEnabled);
    // In real app, would toggle camera flash
    console.log('Flash toggled:', !flashEnabled);
  };

  const handleQRDetected = async (qrData: string) => {
    setProcessing(true);
    
    try {
      // Mock QR payment processing
      console.log('QR Code detected:', qrData);
      
      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock payment validation and processing
      if (qrData.includes('invalid')) {
        throw new Error('Mã QR không hợp lệ');
      } else if (qrData.includes('expired')) {
        throw new Error('Mã QR đã hết hạn');
      } else if (qrData.includes('insufficient')) {
        throw new Error('Số dư không đủ để thực hiện giao dịch');
      }
      
      // Mock successful payment
      const mockPayment: PaymentResult = {
        amount: Math.floor(Math.random() * 500000) + 50000, // Random amount 50k-550k
        merchant: 'Cửa hàng ABC',
        transactionId: `TXN${Date.now()}`,
        timestamp: new Date(),
      };
      
      setPaymentResult(mockPayment);
      setScanning(false);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi xử lý thanh toán');
      setScanning(false);
    } finally {
      setProcessing(false);
    }
  };

  const handleRetry = () => {
    setError(null);
    setPaymentResult(null);
    handleStartScan();
  };

  const handleNewScan = () => {
    setPaymentResult(null);
    setError(null);
    handleStartScan();
  };

  const handleViewHistory = () => {
    navigate('/payment-history');
  };

  // Mock QR scan simulation
  const simulateQRScan = (type: 'success' | 'invalid' | 'expired' | 'insufficient') => {
    const mockQRData = {
      success: 'payment://merchant123/amount=150000',
      invalid: 'invalid-qr-code',
      expired: 'payment://expired-code',
      insufficient: 'payment://insufficient-balance'
    };
    
    handleQRDetected(mockQRData[type]);
  };

  // Back icon
  const BackIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M15 18L9 12L15 6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  // Success Result Component
  if (paymentResult) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Thanh toán thành công"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
        />
        
        <div className="flex flex-col items-center justify-center px-4 pt-20">
          {/* Success Icon */}
          <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-6">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" className="text-green-500">
              <path
                d="M20 6L9 17L4 12"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-[#1A1818] text-center mb-4">
            Thanh toán thành công!
          </h2>
          
          {/* Payment Details */}
          <Card className="w-full max-w-sm p-6 mb-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-[#F65D79] mb-2">
                {paymentResult.amount.toLocaleString()}đ
              </div>
              <div className="text-[#9A9A9A] text-sm mb-4">
                Thanh toán tại {paymentResult.merchant}
              </div>
              <div className="border-t pt-4 space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-[#9A9A9A]">Mã giao dịch:</span>
                  <span className="text-[#1A1818] font-mono">{paymentResult.transactionId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#9A9A9A]">Thời gian:</span>
                  <span className="text-[#1A1818]">
                    {paymentResult.timestamp.toLocaleString('vi-VN')}
                  </span>
                </div>
              </div>
            </div>
          </Card>
          
          {/* Action Buttons */}
          <div className="w-full max-w-sm space-y-3">
            <Button onClick={handleNewScan} className="w-full">
              Quét mã mới
            </Button>
            <Button onClick={handleViewHistory} variant="outline" className="w-full">
              Xem lịch sử giao dịch
            </Button>
            <Button onClick={handleBack} variant="outline" className="w-full">
              Về trang chủ
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#1A1818]">
      {/* Navigation Header */}
      <NavigationHeader
        title="Quét mã QR thanh toán"
        leftIcon={BackIcon}
        onLeftClick={handleBack}
        className="bg-[#1A1818] text-white"
      />

      {/* Camera Interface */}
      <div className="flex-1 relative">
        <CameraInterface
          isActive={scanning}
          hasFlash={flashEnabled}
          onFlashToggle={handleFlashToggle}
          onCapture={() => {}} // QR scanning doesn't need capture
          className="h-full"
        />

        {/* Scanning Overlay */}
        {scanning && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-64 h-64 border-2 border-[#F65D79] rounded-lg relative">
              {/* Scanning animation corners */}
              <div className="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-[#F65D79]"></div>
              <div className="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-[#F65D79]"></div>
              <div className="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-[#F65D79]"></div>
              <div className="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-[#F65D79]"></div>
              
              {/* Scanning line animation */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="w-full h-1 bg-[#F65D79] animate-pulse absolute top-1/2 transform -translate-y-1/2"></div>
              </div>
            </div>
          </div>
        )}

        {/* Processing Overlay */}
        {processing && (
          <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
            <div className="text-center text-white">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-lg">Đang xử lý thanh toán...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="absolute bottom-32 left-4 right-4">
            <Card className="p-4 bg-red-50 border border-red-200">
              <div className="text-center">
                <div className="text-red-600 font-medium mb-2">{error}</div>
                <Button onClick={handleRetry} size="small" className="bg-red-600 hover:bg-red-700">
                  Thử lại
                </Button>
              </div>
            </Card>
          </div>
        )}
      </div>

      {/* Bottom Controls */}
      <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
        {!scanning ? (
          <div className="space-y-4">
            {/* Instructions */}
            <div className="text-center text-white mb-6">
              <h3 className="text-lg font-semibold mb-2">Quét mã QR để thanh toán</h3>
              <p className="text-sm opacity-80">
                Đưa camera về phía mã QR để bắt đầu thanh toán
              </p>
            </div>
            
            {/* Start Scan Button */}
            <Button onClick={handleStartScan} className="w-full" size="large">
              Bắt đầu quét
            </Button>
            
            {/* Demo Buttons */}
            <div className="grid grid-cols-2 gap-2 mt-4">
              <button
                onClick={() => simulateQRScan('success')}
                className="py-2 px-3 bg-green-600 text-white rounded text-sm"
              >
                Demo: Thành công
              </button>
              <button
                onClick={() => simulateQRScan('invalid')}
                className="py-2 px-3 bg-red-600 text-white rounded text-sm"
              >
                Demo: Lỗi
              </button>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <Button onClick={handleStopScan} variant="outline" className="border-white text-white">
              Dừng quét
            </Button>
          </div>
        )}
      </div>

      {/* Payment Tips */}
      {!scanning && (
        <div className="absolute top-32 left-4 right-4">
          <Card className="p-4 bg-white/10 backdrop-blur-sm border border-white/20">
            <div className="text-white">
              <h4 className="font-semibold mb-2">💡 Mẹo thanh toán</h4>
              <ul className="text-sm space-y-1 opacity-90">
                <li>• Đảm bảo mã QR sạch và không bị mờ</li>
                <li>• Giữ camera ổn định khi quét</li>
                <li>• Sử dụng đèn flash nếu thiếu sáng</li>
              </ul>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default QRPaymentPage;