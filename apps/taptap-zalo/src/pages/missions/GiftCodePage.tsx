import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>H<PERSON>er,
  Card,
  Button,
  Input,
} from '@taptap/shared';

const GiftCodePage: React.FC = () => {
  const navigate = useNavigate();
  const [giftCode, setGiftCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleBack = () => {
    navigate('/');
  };

  const validateGiftCode = (code: string): boolean => {
    // Basic validation - gift codes should be alphanumeric and 6-12 characters
    const codePattern = /^[A-Za-z0-9]{6,12}$/;
    return codePattern.test(code);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!giftCode.trim()) {
      setError('<PERSON><PERSON> lòng nhập mã đổi quà');
      return;
    }

    if (!validateGiftCode(giftCode)) {
      setError('Mã đổi quà không hợp lệ. Mã phải từ 6-12 ký tự và chỉ chứa chữ cái, số');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Mock API call - in real app would call gift code redemption API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate different outcomes based on code
      if (giftCode.toLowerCase() === 'invalid') {
        throw new Error('Mã đổi quà không tồn tại hoặc đã hết hạn');
      } else if (giftCode.toLowerCase() === 'used') {
        throw new Error('Mã đổi quà đã được sử dụng');
      }
      
      setSuccess(true);
      // Auto redirect to rewards page after success
      setTimeout(() => {
        navigate('/exchange');
      }, 2000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra, vui lòng thử lại');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase(); // Convert to uppercase for consistency
    setGiftCode(value);
    if (error) {
      setError(null); // Clear error when user starts typing
    }
  };

  // Back icon
  const BackIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M15 18L9 12L15 6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  if (success) {
    return (
      <div className="min-h-screen bg-[#F65D79]">
        <NavigationHeader
          title="Nhập mã đổi quà"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
          className="bg-[#F65D79] text-white"
        />
        
        <div className="flex flex-col items-center justify-center px-4 pt-20">
          {/* Success Animation */}
          <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center mb-6">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" className="text-green-500">
              <path
                d="M20 6L9 17L4 12"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-white text-center mb-4">
            Đổi quà thành công!
          </h2>
          
          <p className="text-white text-center mb-8 px-4">
            Mã đổi quà <span className="font-semibold">{giftCode}</span> đã được áp dụng thành công. 
            Bạn sẽ được chuyển đến trang đổi thưởng.
          </p>
          
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F65D79]">
      {/* Navigation Header */}
      <NavigationHeader
        title="Nhập mã đổi quà"
        leftIcon={BackIcon}
        onLeftClick={handleBack}
        className="bg-[#F65D79] text-white"
      />

      <div className="px-4 py-6">
        {/* Instructional Text */}
        <Card className="mb-6 p-6">
          <p className="text-[#1A1818] text-sm leading-relaxed text-center">
            Mở Dashi Kitchen - Bếp Nhà Lục Tỉnh mỗi ngày để theo dõi chương trình 
            Nhập Mã Đổi Quà mới tại đây nhé
          </p>
        </Card>

        {/* Gift Code Input Form */}
        <Card className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Input Field */}
            <div>
              <label htmlFor="giftCode" className="block text-sm font-medium text-[#1A1818] mb-2">
                Mã đổi quà
              </label>
              <Input
                id="giftCode"
                type="text"
                placeholder="Nhập mã đổi quà"
                value={giftCode}
                onChange={handleInputChange}
                error={!!error}
                disabled={loading}
                className="text-center text-lg font-semibold tracking-wider"
                maxLength={12}
              />
              {error && (
                <p className="text-red-500 text-sm mt-2 text-center">{error}</p>
              )}
            </div>

            {/* Completion Button */}
            <Button
              type="submit"
              className="w-full"
              disabled={loading || !giftCode.trim()}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Đang xử lý...
                </div>
              ) : (
                'Hoàn tất'
              )}
            </Button>
          </form>
        </Card>

        {/* Additional Instructions */}
        <div className="mt-6 px-4">
          <div className="bg-white/20 rounded-lg p-4">
            <h3 className="text-white font-semibold mb-2">Lưu ý:</h3>
            <ul className="text-white text-sm space-y-1">
              <li>• Mã đổi quà chỉ sử dụng được một lần</li>
              <li>• Kiểm tra hạn sử dụng của mã</li>
              <li>• Liên hệ hỗ trợ nếu gặp vấn đề</li>
            </ul>
          </div>
        </div>

        {/* Bottom Image Placeholder */}
        <div className="mt-8 relative">
          <div className="bg-white/10 rounded-lg h-48 flex items-center justify-center">
            <p className="text-white/60 text-sm">Hình ảnh minh họa</p>
          </div>
          
          {/* "Xong" Button Overlay */}
          <div className="absolute top-4 right-4">
            <div className="bg-[#CACACA] rounded px-4 py-2">
              <span className="text-[#363636] text-sm font-semibold">Xong</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GiftCodePage;