import React, { useMemo, useState } from "react";
import {
  Button,
  Icon,
  entertainmentService,
  handleDeeplinkNavigation,
} from "@taptap/shared";
import { useNavigate } from "react-router-dom";

type IUserProgressState =
  | "PROCESSING"
  | "NOT_RECEIVED_YET"
  | "SUCCESSFULLY"
  | "FAILED"
  | "RELEASING"
  | "";

type IChallengeUserProgress = {
  brandId?: string;
  logoLink: string;
  validTransactions?: string[];
};

type IUserProgress = {
  challengeId: string;
  userId: string;
  challengeName: string;
  mobile: string;
  displayName: string;
  state: IUserProgressState;
  percentProgress: number;
  progress: IChallengeUserProgress[];
  maxProgress: number;
  hadJoinChallenge: boolean;
};

type ICondition = {
  conditionType: "BRAND" | "OPEN_LINK" | string;
  buttonLabel?: string;
  directBtnLink?: string;
};

type IPackageGift = { remain?: number };

type IChallengeV3 = {
  _id: string;
  displayChallengeName: string;
  endDate: string;
  conditionChallengeType: string;
  conditions: ICondition[];
  packageGift?: IPackageGift;
  userProgress?: IUserProgress | null;
  canUserJoinChallenge?: boolean;
  joinChallengeType: "AUTO" | "MANUAL";
};

type Props = {
  challenge: IChallengeV3;
  refetch: () => void;
};

const panelStyle: React.CSSProperties = {
  position: "fixed",
  left: 0,
  right: 0,
  bottom: 0,
  background: "#fff",
  boxShadow: "0 -8px 24px rgba(0,0,0,0.08)",
  padding: "12px 16px",
  display: "flex",
  flexDirection: "column",
  gap: 12,
  zIndex: 1000,
};

const textWarnStyle: React.CSSProperties = {
  color: "#ff3b30",
  fontSize: 14,
};
const textWarnBoldStyle: React.CSSProperties = {
  color: "#ff3b30",
  fontSize: 14,
  fontWeight: 700,
};

function calcExpired(
  challenge: IChallengeV3,
  isJoinedChallenge: boolean
): boolean {
  const endTs = challenge.endDate ? new Date(challenge.endDate).getTime() : 0;
  const now = Date.now();
  if (endTs && endTs - now < 24 * 60 * 60 * 1000) return true;
  if (
    challenge.userProgress &&
    challenge.userProgress.state === "NOT_RECEIVED_YET" &&
    isJoinedChallenge
  )
    return false;
  return (challenge.packageGift?.remain ?? 1) === 0;
}

function pickCondition(
  conditions: ICondition[] | undefined
): ICondition | undefined {
  if (!Array.isArray(conditions)) return undefined;
  return conditions.find(
    (c) => c.conditionType === "BRAND" || c.conditionType === "OPEN_LINK"
  );
}

function formatDeadline(endDate: string): string {
  const d = new Date(endDate);
  const hh = String(d.getHours()).padStart(2, "0");
  const dd = String(d.getDate()).padStart(2, "0");
  const mm = String(d.getMonth() + 1).padStart(2, "0");
  const yyyy = d.getFullYear();
  return `${hh}h ${dd}/${mm}/${yyyy}`;
}

export const FloatButtonWeb: React.FC<Props> = ({ challenge, refetch }) => {
  const navigate = useNavigate();
  const condition = useMemo(
    () => pickCondition(challenge.conditions),
    [challenge.conditions]
  );
  const buttonLabel = condition?.buttonLabel || "";
  const directBtnLink = condition?.directBtnLink || "";
  const progressState: IUserProgressState = challenge.userProgress?.state || "";
  const canUserJoinChallenge = challenge.canUserJoinChallenge ?? true;
  const isJoinedChallenge =
    (challenge.joinChallengeType === "MANUAL" &&
      !!challenge.userProgress?.hadJoinChallenge) ||
    challenge.joinChallengeType === "AUTO";

  const isExpired = useMemo(
    () => calcExpired(challenge, isJoinedChallenge),
    [challenge, isJoinedChallenge]
  );

  const [joining, setJoining] = useState(false);

  if (
    !canUserJoinChallenge ||
    isExpired ||
    ["SUCCESSFULLY", "FAILED", "RELEASING"].includes(progressState)
  ) {
    return null;
  }

  const onReceiveGiftNow = () => {
    window.dispatchEvent(
      new CustomEvent("challenge:receive_gift_now", {
        detail: {
          id: challenge._id,
          name: challenge.displayChallengeName,
        },
      })
    );
  };

  const handleJoinChallenge = async () => {
    try {
      setJoining(true);
      // Call API to join challenge if not joined yet
      const ok = await entertainmentService.joinChallenge(challenge._id);
      // Emit events for external listeners
      if (ok?.status?.success) {
        // call refetch
        refetch();
      }
    } finally {
      setJoining(false);
      window.dispatchEvent(
        new CustomEvent("challenge:join:done", {
          detail: { id: challenge._id },
        })
      );
    }
  };

  const goToEarnPage = async () => {
    try {
      const userId = challenge.userProgress?.userId || "";
      const mobile = challenge.userProgress?.mobile || "";
      // Map deeplink to web route or external URL
      const webUrl = handleDeeplinkNavigation(directBtnLink);
      if (webUrl.startsWith("http://") || webUrl.startsWith("https://")) {
        window.open(webUrl, "_blank", "noopener,noreferrer");
      } else {
        navigate(webUrl);
      }

      // Callback to backend to log open-link action (if service available)
      try {
        await entertainmentService.callbackOpenLink({
          topic: "new_challenge",
          action: "open_link",
          data: {
            userId,
            mobile,
            date: new Date().toString(),
            link: directBtnLink,
          },
        });
      } catch {
        // ignore callback errors
      }
    } catch {
      // ignore navigation errors
    }
  };

  if (progressState === "NOT_RECEIVED_YET" && isJoinedChallenge) {
    return (
      <div style={panelStyle}>
        <div style={textWarnStyle}>
          Hạn nhận quà:{" "}
          <span style={textWarnBoldStyle}>
            {formatDeadline(challenge.endDate)}
          </span>
        </div>
        <Button
          variant="primary"
          className="flex items-center justify-center gap-2"
          onClick={onReceiveGiftNow}
        >
          Nhận quà ngay
          <Icon name="arrow-right" />
        </Button>
      </div>
    );
  }

  if (!isJoinedChallenge) {
    return (
      <div style={panelStyle}>
        <Button
          variant="primary"
          className="flex items-center justify-center gap-2"
          onClick={handleJoinChallenge}
          disabled={joining}
        >
          {joining ? "Đang tham gia..." : "Chấp nhận nhiệm vụ"}
        </Button>
      </div>
    );
  }

  if (buttonLabel && directBtnLink) {
    return (
      <div style={panelStyle}>
        <Button variant="primary" onClick={goToEarnPage}>
          {buttonLabel}
        </Button>
      </div>
    );
  }

  return null;
};
