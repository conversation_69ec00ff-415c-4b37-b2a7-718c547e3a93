import React from "react";
import { Page } from "zmp-ui";
import { useZaloNavigation } from "../../hooks/useZaloNavigation";
import { NavigationHeader, MissionSection, BackIcon } from "@taptap/shared";
import { ChallengeV3 } from "@taptap/shared";

const PageAny = Page as any;

export const MissionsPage: React.FC = () => {
  const { navigate } = useZaloNavigation();

  const handleMissionClick = (challenge: ChallengeV3) => {
    console.log("Mission clicked:", challenge._id);
    console.log("Challenge data:", challenge);
    // Navigate to mission detail page
    navigate(`/games/missions/${challenge._id}`);
  };

  const handleMissionHistoryClick = () => {
    navigate("/games/missions/history");
  };

  return (
    <PageAny className="bg-gray-50 min-h-screen px-3">
      {/* Header */}
      <NavigationHeader
        title="Nhiệm vụ có thưởng"
        leftIcon={<BackIcon />}
        onLeftClick={() => navigate('/')}
      />

      {/* Mission Section - Now using API */}
      <MissionSection
        title="Nhiệm vụ có thưởng"
        actionText="Tất cả"
        limit={10}
        home={undefined}
        autoFetch={true}
        onMissionClick={handleMissionClick}
        onActionClick={handleMissionHistoryClick}
        className="mb-6"
      />

      {/* Additional content can be added here */}
    </PageAny>
  );
};

export default MissionsPage;
