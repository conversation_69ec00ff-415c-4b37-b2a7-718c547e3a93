import React, { useState, useEffect, useCallback, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  NavigationHeader,
  LoadingSpinner,
  MerchantItem,
  appPath,
  merchantService,
  BackIcon,
} from "@taptap/shared";
import type { MerchantType, MerchantItemData } from "@taptap/shared";

const PAGE_LIMIT = 12;

const FavoritesPage: React.FC = () => {
  const navigate = useNavigate();
  const [favoriteMerchants, setFavoriteMerchants] = useState<MerchantType[]>(
    []
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [offset, setOffset] = useState<number>(0);

  const sentinelRef = useRef<HTMLDivElement | null>(null);

  const fetchFavoriteMerchants = useCallback(
    async (isLoadMore: boolean = false) => {
      try {
        if (isLoadMore) {
          setLoadingMore(true);
        } else {
          setLoading(true);
        }
        setError(null);

        const res = await merchantService.getFavoriteMerchants(
          "LOAD_MORE",
          isLoadMore ? offset : 0,
          PAGE_LIMIT
        );

        if (!res.status.success) throw new Error(res.status.message);

        const list = res.data || [];

        if (isLoadMore) {
          setFavoriteMerchants((prev) => [...prev, ...list]);
          setHasMore(list.length === PAGE_LIMIT);
          setOffset((prev) => prev + PAGE_LIMIT);
        } else {
          setFavoriteMerchants(list);
          setHasMore(list.length === PAGE_LIMIT);
          setOffset(PAGE_LIMIT);
        }
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Không thể tải danh sách yêu thích"
        );
      } finally {
        if (isLoadMore) {
          setLoadingMore(false);
        } else {
          setLoading(false);
        }
      }
    },
    [offset]
  );

  useEffect(() => {
    fetchFavoriteMerchants(false);
  }, [fetchFavoriteMerchants]);

  // Infinite scroll observer
  useEffect(() => {
    if (!hasMore) return;
    const target = sentinelRef.current;
    if (!target) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !loading && !loadingMore) {
          fetchFavoriteMerchants(true);
        }
      },
      { root: null, rootMargin: "200px 0px", threshold: 0.01 }
    );

    observer.observe(target);
    return () => observer.disconnect();
  }, [hasMore, loading, loadingMore, fetchFavoriteMerchants]);

  const handleBack = () => navigate(-1);
  const handleMerchantClick = (merchant: MerchantItemData) =>
    navigate(appPath.merchantDetail(merchant.id));

  const renderContent = () => {
    if (loading) {
      return (
        <div className="w-full flex justify-center py-10">
          <LoadingSpinner />
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center text-red-500 text-sm py-6">
          {error}
          <button
            onClick={() => fetchFavoriteMerchants(false)}
            className="block mx-auto mt-2 text-[#F65D79] underline"
          >
            Thử lại
          </button>
        </div>
      );
    }

    if (favoriteMerchants.length === 0) {
      return (
        <div className="text-center text-gray-500 text-sm py-6">
          Chưa có thương hiệu yêu thích nào
        </div>
      );
    }

    return (
      <>
        <div className="grid grid-cols-2 gap-3">
          {favoriteMerchants.map((item: MerchantType) => (
            <MerchantItem
              key={item.id}
              merchant={item as any}
              onClick={handleMerchantClick}
            />
          ))}
        </div>

        {/* Infinite scroll sentinel */}
        {hasMore && <div ref={sentinelRef} className="h-6" />}

        {/* Loading more indicator */}
        {loadingMore && (
          <div className="w-full flex justify-center py-4">
            <LoadingSpinner />
          </div>
        )}
      </>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <NavigationHeader
        title="Danh sách yêu thích"
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        className="border-b border-[#ECECEC]"
      />

      <div className="px-4 py-4">{renderContent()}</div>
    </div>
  );
};

export default FavoritesPage;
