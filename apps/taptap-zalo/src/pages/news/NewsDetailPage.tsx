import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Page } from 'zmp-ui';
import { WebView, NavigationHeader, LoadingSpinner } from '@taptap/shared';
import { newsAPI } from '@taptap/shared';
import type { NewsDetail } from '@taptap/shared';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';

const PageAny = Page as any;

const NewsDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { navigate, goBack, state } = useZaloNavigation();
  const [newsDetail, setNewsDetail] = useState<NewsDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get URL and title from query parameters
  const searchParams = new URLSearchParams(location.search);
  const urlFromQuery = searchParams.get('url');
  const titleFromQuery = searchParams.get('title');
  
  // Get news data from navigation state if available (fallback)
  const stateNewsData = state?.newsData;

  useEffect(() => {
    const initializeNewsDetail = async () => {
      if (!id) {
        console.error('🚨 Zalo NewsDetailPage: No news ID provided');
        setError('News ID is required');
        setLoading(false);
        return;
      }

      console.log('📰 Zalo NewsDetailPage: Initializing');
      console.log('🔗 URL from query:', urlFromQuery);
      console.log('📦 State news data:', stateNewsData);
      console.log('🆔 ID from params:', id);

      try {
        setLoading(true);
        setError(null);
        
        // Priority 1: Use URL from query parameter
        if (urlFromQuery) {
          console.log('✅ Using URL from query parameter:', urlFromQuery);
          // Create a minimal news detail object with URL and title
          const newsFromQuery = {
            _id: id || 'query-news',
            title: titleFromQuery || 'News Article',
            contentType: 'External link' as const,
            externalLink: urlFromQuery
          };
          setNewsDetail(newsFromQuery as NewsDetail);
          setLoading(false);
          return;
        }

        // Priority 2: Use news data from navigation state
        if (stateNewsData) {
          console.log('✅ Using news data from navigation state:', stateNewsData);
          setNewsDetail(stateNewsData);
          setLoading(false);
          return;
        }

        // Priority 3: Fallback - require either URL query or ID
        if (!id) {
          console.error('🚨 Zalo NewsDetailPage: No URL query or news ID provided');
          setError('News URL or ID is required');
          setLoading(false);
          return;
        }

        // Fallback to API call (though this endpoint doesn't exist)
        console.log('🔄 No URL query or state data, trying API fallback...');
        console.log('⚠️ Note: /news-config API endpoint does not exist');
        
        const response = await newsAPI.getNewsDetail(id);
        
        if (response?.status?.success && response?.data) {
          console.log('✅ News detail loaded from API:', response.data);
          setNewsDetail(response.data);
        } else {
          console.error('❌ API returned unsuccessful response');
          setError('News not found');
        }
      } catch (err) {
        console.error('🚨 Error loading news detail:', {
          error: err,
          message: err?.message,
          newsId: id,
          hasStateData: !!stateNewsData
        });
        setError('Failed to load news');
      } finally {
        setLoading(false);
        console.log('🏁 Zalo NewsDetailPage initialization completed');
      }
    };

    initializeNewsDetail();
  }, [id, urlFromQuery, stateNewsData]);

  const handleBack = () => {
    goBack();
  };

  // Get the URL to display in WebView
  const getNewsUrl = (newsDetail: NewsDetail): string => {
    // If it's external content, use the external link
    if (newsDetail.contentType === 'External link' && newsDetail.externalLink) {
      return newsDetail.externalLink;
    }
    
    // If news has a webUrl property, use it
    if (newsDetail.webUrl) {
      return newsDetail.webUrl;
    }

    // If news has a detailUrl property, use it
    if (newsDetail.detailUrl) {
      return newsDetail.detailUrl;
    }

    // Otherwise, construct URL to our news detail endpoint
    return `/api/news/${id}/view`;
  };

  if (loading) {
    return (
      <PageAny className="min-h-screen bg-white">
        <NavigationHeader 
          title="Loading..."
          showBackButton={true}
          onBackClick={handleBack}
        />
        <div className="flex-1 flex items-center justify-center" style={{ height: 'calc(100vh - 48px)' }}>
          <LoadingSpinner />
        </div>
      </PageAny>
    );
  }

  if (error || !newsDetail) {
    return (
      <PageAny className="min-h-screen bg-white">
        <NavigationHeader 
          title="Error"
          showBackButton={true}
          onBackClick={handleBack}
        />
        <div className="flex-1 flex flex-col items-center justify-center p-4" style={{ height: 'calc(100vh - 48px)' }}>
          <div className="text-center">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              {error || 'News not found'}
            </h2>
            <p className="text-gray-600 mb-4">
              The news article you're looking for is not available.
            </p>
            <button
              onClick={handleBack}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </PageAny>
    );
  }

  const newsUrl = getNewsUrl(newsDetail);

  return (
    <PageAny className="min-h-screen bg-white">
      {/* Navigation Header */}
      <NavigationHeader 
        title={newsDetail.title || 'News Detail'}
        showBackButton={true}
        onBackClick={handleBack}
        className="border-b border-gray-200"
      />

      {/* WebView Content */}
      <div 
        className="flex-1" 
        style={{ height: 'calc(100vh - 48px)' }}
      >
        <WebView 
          url={newsUrl}
          title={newsDetail.title || 'News Detail'}
          width="100%"
          height="100%"
          loading="eager"
          onLoad={() => {
            console.log('News webview loaded in Zalo:', newsDetail.title);
          }}
          onError={() => {
            console.error('News webview failed to load in Zalo:', newsUrl);
          }}
        />
      </div>
    </PageAny>
  );
};

export default NewsDetailPage;