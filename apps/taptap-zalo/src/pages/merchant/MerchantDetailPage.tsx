import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Page } from "zmp-ui";
import { useZaloNavigation } from "../../hooks/useZaloNavigation";
import {
  NavigationHeader,
  HorizontalScrollTabs,
  HorizontalScrollTabItem,
  LoyaltyCard,
  StoreNearbySection,
  BrandDetailSection,
  ImageGallery,
  VoucherSection,
  VoucherItem,
  HowToEarnSection,
  LoadingSpinner,
  BackIcon,
  useMembershipStore,
  membershipHelpers,
  resizeImage,
} from "@taptap/shared";

// Import API services
import {
  merchantService as merchantAPI,
  rewardService as rewardsAPI,
  entertainmentService,
} from "@taptap/shared";

// Import types
import type {
  MerchantType,
  OfflineStoreType,
  RewardItemType,
  IGameCard,
} from "@taptap/shared";
import { RewardDetailResponse } from "@taptap/shared/services/api/rewards";

const PageAny = Page as any;

// Default images for placeholder
const merchantDetailBg = "/shared/assets/images/merchant_detail_bg.png";
const brandLogoPlaceholder = "/shared/assets/images/brand-logo-sample.png";
const rewardCardImage = "/shared/assets/images/reward_card_image.png";

export const MerchantDetailPageComponent: React.FC = () => {
  const { navigate, getUrlParams } = useZaloNavigation();
  const params = getUrlParams();
  const id = params.id || window.location.pathname.split("/").pop();
  const [activeTab, setActiveTab] = useState("offers");
  const [favorite, setFavorite] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [merchantDetail, setMerchantDetail] = useState<MerchantType | null>(
    null
  );
  const [stores, setStores] = useState<OfflineStoreType[]>([]);
  const [rewards, setRewards] = useState<RewardDetailResponse | null>(null);
  const [tabsConfig, setTabsConfig] = useState<HorizontalScrollTabItem[]>([]);

  // Membership store
  const {
    tierDetail,
    loading: tierLoading,
    stateTier,
    fetchTierDetail,
    registerTier,
  } = useMembershipStore();

  const fetchMerchantData = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch merchant details
      const merchantResponse = await merchantAPI.getMerchantByCode(id);
      if (!merchantResponse.status.success) {
        throw new Error(merchantResponse.status.message);
      }

      const merchant = merchantResponse.data;
      setMerchantDetail(merchant);
      setFavorite(merchant.isFavorite || false);

      // Build tabs based on merchant features
      const tabs = buildTabsConfiguration(merchant);
      setTabsConfig(tabs);

      // Fetch additional data in parallel
      await Promise.all([fetchStores(merchant), fetchRewards(merchant)]);
    } catch (err) {
      console.error("Failed to fetch merchant data:", err);
      setError(
        err instanceof Error ? err.message : "Có lỗi xảy ra khi tải dữ liệu"
      );
    } finally {
      setLoading(false);
    }
  }, [id]);

  // Fetch all merchant data when component mounts
  useEffect(() => {
    if (id) {
      fetchMerchantData();
      fetchTierDetail(id);
    }
  }, [id, fetchTierDetail, fetchMerchantData]);

  const fetchStores = async (merchant: MerchantType) => {
    if (!merchant.isEnableOfflineStore) return;

    try {
      // Try to get user location first (simplified for web)
      const storesResponse = await merchantAPI.getStoreListWithDistance({
        merchantCode: merchant.code,
        latitude: 10.8231, // Default to Ho Chi Minh City
        longitude: 106.6297,
        radius: 10000, // 10km
        offset: 0,
        limit: 10,
      });

      if (storesResponse.status.success && storesResponse.data) {
        setStores(storesResponse.data);
      }
    } catch (error) {
      console.error("Failed to fetch stores:", error);
    }
  };

  const fetchRewards = async (merchant: MerchantType) => {
    if (!merchant.numberOfReward || merchant.numberOfReward === 0) return;

    try {
      const rewardsResponse = await rewardsAPI.getListRewardV2({
        brandCode: merchant.code,
        mobileType: "running",
        offset: 0,
        limit: 8,
      });

      if (rewardsResponse.status.success && rewardsResponse.data) {
        setRewards(rewardsResponse.data);
      }
    } catch (error) {
      console.error("Failed to fetch rewards:", error);
    }
  };

  const buildTabsConfiguration = (
    merchant: MerchantType
  ): HorizontalScrollTabItem[] => {
    const tabs: HorizontalScrollTabItem[] = [];

    // Offers tab - always show if has rewards
    if (merchant.numberOfReward && merchant.numberOfReward > 0) {
      tabs.push({
        id: "offers",
        label: "ưu đãi Trên kệ",
      });
    }

    // Earn points tab - show if has earn models
    if (merchant.earnModels && merchant.earnModels.length > 0) {
      tabs.push({
        id: "earn-points",
        label: merchant.isUsingBC ? "tích điểm POINT và XU" : "tích điểm VUI",
      });
    }

    // Online store tab - show if enabled
    if (merchant.isShowOnlineStore && merchant.onlineStore) {
      tabs.push({
        id: "order-online",
        label: "Đặt hàng online",
      });
    }

    // Stores tab - show if has offline stores
    if (merchant.isEnableOfflineStore) {
      tabs.push({
        id: "stores",
        label: "Cửa hàng",
        count: merchant.offlineStores?.length || 0,
      });
    }

    // Gift cards tab - placeholder for future implementation
    tabs.push({
      id: "gift-cards",
      label: "thẻ quà TAPTAP",
    });

    // Info tab - always show
    tabs.push({
      id: "info",
      label: "THÔNG TIN",
    });

    // Gallery tab - show if has images
    if (merchant.images && merchant.images.length > 0) {
      tabs.push({
        id: "gallery",
        label: "hình ảnh",
      });
    }

    return tabs;
  };

  const handleBack = () => {
    navigate("/");
  };

  const handleToggleFavorite = async () => {
    if (!id) return;

    try {
      const newFavoriteStatus = !favorite;
      setFavorite(newFavoriteStatus);

      const action = newFavoriteStatus ? "ADD" : "REMOVE";
      const response = await merchantAPI.toggleFavorite([id], action);

      if (!response.status.success) {
        // Revert on failure
        setFavorite(!newFavoriteStatus);
        console.error("Failed to toggle favorite:", response.status.message);
      }
    } catch (error) {
      // Revert on error
      setFavorite(!favorite);
      console.error("Error toggling favorite:", error);
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    console.log("Tab changed to:", tabId);
  };

  // Handle loyalty card actions
  const handleLoyaltyCardAction = async () => {
    if (!id) return;

    if (stateTier === "noRank") {
      // Register for membership - the modal will be handled inside TierNoRank
      const success = await registerTier(id);
      if (success) {
        // Refresh tier details after successful registration
        await fetchTierDetail(id);
      }
      // Return the success status for the TierNoRank component to handle
      return success;
    } else {
      // Navigate to membership details or voucher page
      navigate(`/membership/${id}/details`);
    }
  };

  const handleVoucherClick = () => {
    navigate(`/membership/${id}/benefits`);
  };

  const handleRewardClick = (voucher: VoucherItem) => {
    navigate(`/rewards/${voucher.id}`);
  };

  const handleShowAllRewards = () => {
    navigate(`/rewards?merchantCode=${id}`);
  };

  // Prepare loyalty card data based on tier details
  const getLoyaltyCardStatus = () => {
    if (!tierDetail) return "no-join" as const;
    return stateTier === "noRank"
      ? ("no-join" as const)
      : stateTier === "maxTier"
      ? ("max-rank" as const)
      : stateTier === "hasRankTier" || stateTier === "hasRankAndVoucher"
      ? ("with-rank" as const)
      : ("without-rank" as const);
  };

  const getLoyaltyCardData = () => {
    if (!tierDetail) {
      return {
        status: "no-join" as const,
        brandName: merchantName,
        theme: "merchant-detail" as const,
        onActionClick: handleLoyaltyCardAction,
      };
    }

    const currentValue = tierDetail.current?.[0]?.value || 0;
    const listTier = membershipHelpers.getListProgressTier(
      tierDetail.tierLevel || 0,
      tierDetail.merchantTiers || []
    );
    const totalEarnNeeded = membershipHelpers.totalEarnCurrency(
      listTier.map((tier) => ({
        value: tier.conditions[0]?.value || 0,
      })),
      currentValue
    );
    const progress = membershipHelpers.calculateProgress(
      currentValue,
      listTier.map((tier) => ({
        value: tier.conditions[0]?.value || 0,
        tierName: tier.tierName,
      }))
    );

    return {
      status: getLoyaltyCardStatus(),
      theme: "merchant-detail" as const,
      brandName: tierDetail.merchantName || merchantName,
      userTitle: tierDetail.tierName,
      pointsToNextLevel: totalEarnNeeded,
      currentProgress: progress,
      maxProgress: 100,
      badgeIconSrc: tierDetail.currencyLogo,
      onActionClick: handleLoyaltyCardAction,
      onVoucherClick: handleVoucherClick,
    };
  };

  // Prepare converted vouchers for VoucherSection
  const convertedVouchers: VoucherItem[] = useMemo(() => {
    return rewards.map((reward) => ({
      id: reward.id,
      title: reward.name,
      brandName: (reward as any).brand?.name || merchantDetail?.name || "",
      brandLogo:
        (reward as any).brand?.logo ||
        merchantDetail?.logo ||
        brandLogoPlaceholder,
      price: (reward as any).burnCost || 0,
      productImage: reward.image1 || rewardCardImage,
    }));
  }, [rewards, merchantDetail]);

  // Prepare store locations
  const storeLocations = useMemo(() => {
    return stores.map((store) => ({
      id: store.id,
      storeName: store.name,
      address: store.address,
      distance: store.distance
        ? `${(store.distance / 1000).toFixed(1)} km`
        : "N/A",
      phoneNumber: store.phone || "N/A",
    }));
  }, [stores]);

  // Prepare gallery images
  const galleryImages = useMemo(() => {
    if (!merchantDetail?.images) return [];
    return merchantDetail.images.map((src, index) => ({
      id: index.toString(),
      src,
      alt: `${merchantDetail.name} Image ${index + 1}`,
    }));
  }, [merchantDetail]);

  // Get merchant name safely
  const merchantName = merchantDetail?.name || "Merchant";
  const merchantLogo = merchantDetail?.logo || brandLogoPlaceholder;
  const merchantBanner = merchantDetail?.banner || merchantDetailBg;

  // Loading state
  if (loading) {
    return (
      <PageAny className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </PageAny>
    );
  }

  // Error state
  if (error || !merchantDetail) {
    return (
      <PageAny className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Chi tiết thương hiệu"
          leftIcon={<BackIcon />}
          onLeftClick={handleBack}
          className="border-b border-[#ECECEC]"
        />
        <div className="flex flex-col items-center justify-center p-8">
          <p className="text-red-500 text-center">
            {error || "Không tìm thấy thông tin thương hiệu"}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            Thử lại
          </button>
        </div>
      </PageAny>
    );
  }

  return (
    <PageAny className="w-full min-h-screen bg-[#EFF3F6] relative">
      {/* Background Header with Image */}
      <div className="relative w-full h-[221px] overflow-hidden">
        {/* Background Image */}
        <img
          src={resizeImage(merchantBanner, {
            width: 750,
            height: 442,
            quality: 85,
            fit: "cover",
          })}
          alt={merchantName}
          className="w-full h-full object-cover"
        />

        {/* Gradient Overlay */}
        <div
          className="absolute inset-0"
          style={{
            background:
              "linear-gradient(180deg, rgba(0, 0, 0, 1) 0%, rgba(25, 24, 24, 0) 100%)",
          }}
        />

        {/* Navigation Header */}
        <div className="absolute top-0 left-0 right-0">
          <NavigationHeader
            title=""
            leftIcon={<BackIcon />}
            rightIcon={
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 5V19M5 12L19 12"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            }
            onLeftClick={handleBack}
            className="bg-transparent border-none"
            textClassName="text-white"
          />
        </div>
      </div>

      {/* Merchant Info Card - Floating */}
      <div className="relative px-3 -mt-16 z-10">
        <div className="bg-white rounded-xl shadow-lg px-3 pt-3">
          {/* Merchant Logo - Positioned above card */}
          <div className="absolute -top-[25px] left-3">
            <div className="w-12 h-12 rounded-lg overflow-hidden bg-white shadow-sm">
              <img
                src={resizeImage(merchantLogo, {
                  width: 96,
                  height: 96,
                  quality: 85,
                  fit: "cover",
                })}
                alt={merchantName}
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Favorite Button */}
          <button
            onClick={handleToggleFavorite}
            className="absolute top-2 right-3 w-5 h-5 rounded flex items-center justify-center"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill={favorite ? "#F65D79" : "none"}
              stroke={favorite ? "#F65D79" : "#9A9A9A"}
              strokeWidth="1.5"
            >
              <path d="M8 14L6.5 12.7C3.4 9.9 1.5 8.2 1.5 6.1C1.5 4.6 2.6 3.5 4.1 3.5C5 3.5 5.9 3.9 6.4 4.6H9.6C10.1 3.9 11 3.5 11.9 3.5C13.4 3.5 14.5 4.6 14.5 6.1C14.5 8.2 12.6 9.9 9.5 12.7L8 14Z" />
            </svg>
          </button>

          {/* Merchant Name and Earn Rate */}
          <div className="flex items-center justify-between pt-2">
            <div className="flex-1">
              <h1
                className="text-lg font-bold text-[#1A1818] mb-1"
                style={{ fontFamily: "Archia, system-ui, sans-serif" }}
              >
                {merchantName}
              </h1>

              {/* Earn Rate */}
              {merchantDetail.earnRate && (
                <div className="flex items-center gap-1.5">
                  <span className="text-xs text-[#9A9A9A]">Tỷ lệ:</span>
                  <span
                    className="text-sm font-semibold text-[#F65D79]"
                    style={{ fontFamily: "Archia, system-ui, sans-serif" }}
                  >
                    {merchantDetail.earnRateDescription ||
                      `${merchantDetail.earnRate}%`}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="mt-6">
        <HorizontalScrollTabs
          tabs={tabsConfig}
          activeTabId={activeTab}
          onTabChange={handleTabChange}
        />
      </div>

      {/* Content Sections */}
      <div className="mt-3 space-y-3">
        {/* Show Membership Tier Section if not on earn-points tab and user has tier */}
        {activeTab !== "earn-points" &&
          tierDetail &&
          stateTier !== "noRank" && (
            <div className="px-4">
              <div className="mb-3">
                <h3 className="text-sm font-semibold text-[#1A1818]">
                  Hạng thành viên
                </h3>
              </div>
              {!tierLoading ? (
                <LoyaltyCard {...getLoyaltyCardData()} />
              ) : (
                <div className="w-[343px] h-[184px] bg-gray-200 animate-pulse rounded-xl" />
              )}
            </div>
          )}

        {/* Offers Section */}
        {activeTab === "offers" && (
          <VoucherSection
            rewards={rewards?.rewards || []}
            totalCount={rewards?.totalItem || 0}
            onShowAll={handleShowAllRewards}
            onVoucherClick={handleRewardClick}
          />
        )}

        {/* Earn Points Section */}
        {activeTab === "earn-points" && (
          <div className="space-y-3">
            <div className="px-4">
              {!tierLoading ? (
                <LoyaltyCard {...getLoyaltyCardData()} />
              ) : (
                <div className="w-[343px] h-[184px] bg-gray-200 animate-pulse rounded-xl" />
              )}
            </div>
            <HowToEarnSection
              description={
                merchantDetail.earnRateDescription ||
                `Tích điểm khi mua sắm tại các cửa hàng ${merchantName}`
              }
              imageSrc={merchantDetail.earnRateImage || rewardCardImage}
            />
          </div>
        )}

        {/* Order Online Section */}
        {activeTab === "order-online" && (
          <div className="bg-white">
            <div className="px-4 py-6">
              {merchantDetail.onlineStore ? (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-[#1A1818]">
                    {merchantDetail.onlineStore.title || "Đặt hàng online"}
                  </h3>
                  {merchantDetail.onlineStore.description && (
                    <p className="text-sm text-[#666666]">
                      {merchantDetail.onlineStore.description}
                    </p>
                  )}
                  {merchantDetail.onlineStore.website && (
                    <a
                      href={merchantDetail.onlineStore.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-block px-4 py-2 bg-[#F65D79] text-white rounded-lg hover:bg-[#E54D69] transition-colors"
                    >
                      Truy cập website
                    </a>
                  )}
                </div>
              ) : (
                <p className="text-center text-[#9A9A9A] text-sm">
                  Đặt hàng online sẽ được hiển thị ở đây
                </p>
              )}
            </div>
          </div>
        )}

        {/* Stores Section */}
        {activeTab === "stores" && (
          <StoreNearbySection stores={storeLocations} />
        )}

        {/* Gift Cards Section */}
        {activeTab === "gift-cards" && (
          <div className="bg-white">
            <div className="px-4 py-6">
              <p className="text-center text-[#9A9A9A] text-sm">
                Thẻ quà TAPTAP sẽ được hiển thị ở đây
              </p>
            </div>
          </div>
        )}

        {/* Info Section */}
        {activeTab === "info" && (
          <BrandDetailSection
            title={`Về ${merchantName}`}
            description={
              merchantDetail.description ||
              `Thông tin chi tiết về ${merchantName} sẽ được cập nhật sớm.`
            }
            imageSrc={merchantLogo}
          />
        )}

        {/* Gallery Section */}
        {activeTab === "gallery" && (
          <div className="px-4">
            <ImageGallery images={galleryImages} title="Hình ảnh" />
          </div>
        )}
      </div>
    </PageAny>
  );
};

export default MerchantDetailPageComponent;
