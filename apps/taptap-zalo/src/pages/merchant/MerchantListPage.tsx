import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Page } from "zmp-ui";
import { useZaloNavigation } from "../../hooks/useZaloNavigation";
import {
  NavigationHeader,
  HorizontalScrollTabs,
  HorizontalScrollTabItem,
  LoadingSpinner,
  MerchantItem,
  BackIcon,
  appPath,
} from "@taptap/shared";
import { merchantService as merchantAPI } from "@taptap/shared";
import type {
  MerchantType,
  MerchantCollection as CollectionType,
  MerchantItemData,
} from "@taptap/shared";

const PageAny = Page as any;
const PAGE_LIMIT = 12;

// Thinking in React: split data fetching into hooks and UI into small components

type PinnedHeaderProps = {
  title: string;
  tabs: HorizontalScrollTabItem[];
  activeTabId: string;
  onTabChange: (tabId: string) => void;
  onBack: () => void;
};

const PinnedHeader: React.FC<PinnedHeaderProps> = ({
  title,
  tabs,
  activeTabId,
  onTabChange,
  onBack,
}) => {
  return (
    <div className="sticky top-0 z-40 bg-gray-50">
      <NavigationHeader
        title={title}
        leftIcon={<BackIcon />}
        onLeftClick={onBack}
        className="border-b border-[#ECECEC]"
      />
      {tabs.length > 0 && (
        <div className="bg-gray-50">
          <HorizontalScrollTabs
            tabs={tabs}
            activeTabId={activeTabId}
            onTabChange={onTabChange}
          />
        </div>
      )}
    </div>
  );
};

type MerchantGridProps = {
  merchants: MerchantType[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadingMore: boolean;
  onLoadMore: () => void;
  onItemClick: (merchant: MerchantItemData) => void;
};

const MerchantGrid: React.FC<MerchantGridProps> = ({
  merchants,
  loading,
  error,
  hasMore,
  loadingMore,
  onLoadMore,
  onItemClick,
}) => {
  const sentinelRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!hasMore) return;
    const target = sentinelRef.current;
    if (!target) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !loading && !loadingMore) {
          onLoadMore();
        }
      },
      { root: null, rootMargin: "200px 0px", threshold: 0.01 }
    );

    observer.observe(target);
    return () => observer.disconnect();
  }, [hasMore, loading, loadingMore, onLoadMore, merchants.length]);

  return (
    <div className="px-4 py-4">
      {loading && (
        <div className="w-full flex justify-center py-10">
          <LoadingSpinner />
        </div>
      )}

      {!loading && error && (
        <div className="text-center text-red-500 text-sm py-6">{error}</div>
      )}

      {!loading && !error && merchants.length === 0 && (
        <div className="text-center text-gray-500 text-sm py-6">
          Chưa có thương hiệu trong danh mục này
        </div>
      )}

      {!loading && !error && merchants.length > 0 && (
        <div className="grid grid-cols-2 gap-3">
          {merchants.map((item: MerchantType) => {
            return (
              <MerchantItem
                key={item.id}
                merchant={item as any}
                onClick={onItemClick}
              />
            );
          })}
        </div>
      )}

      {/* Infinite scroll sentinel */}
      {!loading && !error && merchants.length > 0 && (
        <div ref={sentinelRef} className="h-6" />
      )}

      {/* Loading more indicator */}
      {loadingMore && (
        <div className="w-full flex justify-center py-4">
          <LoadingSpinner />
        </div>
      )}
    </div>
  );
};

// Data hooks
const useMerchantCollections = () => {
  const [collections, setCollections] = useState<CollectionType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const refresh = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const res = await merchantAPI.getCollections();
      if (!res.status.success) throw new Error(res.status.message);
      setCollections(res.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Không thể tải danh mục");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    refresh();
  }, [refresh]);

  return { collections, loading, error, refresh };
};

const useMerchantsByCollection = (collectionCode: string) => {
  const [merchants, setMerchants] = useState<MerchantType[]>([]);
  const [offset, setOffset] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPage = useCallback(
    async (startOffset: number, append: boolean) => {
      if (!collectionCode) return;
      try {
        if (append) setLoadingMore(true);
        else setLoading(true);
        setError(null);
        const res = await merchantAPI.getMerchantListByCollection({
          collectionCode,
          offset: startOffset,
          limit: PAGE_LIMIT,
        });
        if (!res.status.success) throw new Error(res.status.message);
        const list = res.data || [];
        setMerchants((prev) => (append ? [...prev, ...list] : list));
        setHasMore(list.length === PAGE_LIMIT);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Không thể tải danh sách thương hiệu"
        );
      } finally {
        if (append) setLoadingMore(false);
        else setLoading(false);
      }
    },
    [collectionCode]
  );

  useEffect(() => {
    // reset when collection changes
    setOffset(0);
    setMerchants([]);
    setHasMore(true);
    if (collectionCode) fetchPage(0, false);
  }, [collectionCode, fetchPage]);

  const loadMore = useCallback(() => {
    if (!hasMore || loadingMore) return;
    const nextOffset = offset + PAGE_LIMIT;
    setOffset(nextOffset);
    fetchPage(nextOffset, true);
  }, [hasMore, loadingMore, offset, fetchPage]);

  return { merchants, loading, loadingMore, error, hasMore, loadMore };
};

const MerchantListPage: React.FC = () => {
  const { navigate } = useZaloNavigation();

  // Collections and selection state
  const {
    collections,
    loading: collectionsLoading,
    error: collectionsError,
  } = useMerchantCollections();
  const [selectedCollectionCode, setSelectedCollectionCode] =
    useState<string>("");

  // Set default selected collection when loaded
  useEffect(() => {
    if (!selectedCollectionCode && collections.length > 0) {
      setSelectedCollectionCode(collections[0].code);
    }
  }, [collections, selectedCollectionCode]);

  // Merchants for the selected collection
  const {
    merchants,
    loading: merchantsLoading,
    loadingMore,
    error: merchantsError,
    hasMore,
    loadMore,
  } = useMerchantsByCollection(selectedCollectionCode);

  const tabs: HorizontalScrollTabItem[] = useMemo(() => {
    return collections
      .slice()
      .sort((a, b) => (a.index || 0) - (b.index || 0))
      .map((c) => ({ id: c.code, label: c.name }));
  }, [collections]);

  const handleBack = () => navigate('/');
  const handleTabChange = (tabId: string) => setSelectedCollectionCode(tabId);
  const handleMerchantClick = (merchant: MerchantItemData) =>
    navigate(`/merchant/${merchant.id}`);

  return (
    <PageAny className="min-h-screen bg-gray-50">
      <PinnedHeader
        title="Thương hiệu tích điểm"
        tabs={tabs}
        activeTabId={selectedCollectionCode}
        onTabChange={handleTabChange}
        onBack={handleBack}
      />
      <MerchantGrid
        merchants={merchants}
        loading={collectionsLoading || merchantsLoading}
        error={collectionsError || merchantsError}
        hasMore={hasMore}
        loadingMore={loadingMore}
        onLoadMore={loadMore}
        onItemClick={handleMerchantClick}
      />

 
    </PageAny>
  );
};

export default MerchantListPage;
