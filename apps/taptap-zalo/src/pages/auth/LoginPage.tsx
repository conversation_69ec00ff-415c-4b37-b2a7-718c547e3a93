import React, { useState, useEffect } from 'react';
import { Page } from "zmp-ui";
import {
  NavigationHeader,
  Card,
  Button,
  Input,
  useAuthStore,
  DeviceIDService,
} from '@taptap/shared';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import { useZaloAuth } from '../../hooks/useZaloAuth';

const PageAny = Page as any;

type AuthStep = 'phone' | 'otp' | 'password';

const LoginPage: React.FC = () => {
  const { navigate, goBack } = useZaloNavigation();
  
  // Zustand auth store (legacy OTP flow)
  const {
    generateOTP,
    verifyOTP,
    checkRegistrationState,
    signinWithPassword,
    profile,
    token,
    loadingStates,
    clearError
  } = useAuthStore();
  
  // New Zalo auth system
  const { 
    user: zaloUser, 
    isAuthenticated: isZaloAuthenticated,
    refreshUser 
  } = useZaloAuth();
  
  const [currentStep, setCurrentStep] = useState<AuthStep>('phone');
  const [formData, setFormData] = useState({
    mobile: '',
    otp: '',
    password: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [error, setError] = useState('');
  const [otpTimer, setOtpTimer] = useState(0);
  const [sessionId, setSessionId] = useState('');
  const [deviceId] = useState(() => {
    // Use proper mobile-style device ID generation
    const deviceService = DeviceIDService.getInstance();
    return deviceService.getRawDeviceId();
  });
  
  // Redirect if already authenticated (either old or new system)
  useEffect(() => {
    if (isZaloAuthenticated && zaloUser) {
      console.log('[LoginPage] User authenticated via new Zalo auth, redirecting...');
      navigate('/');
    } else if (token && profile.mobile) {
      console.log('[LoginPage] User authenticated via old auth system, bridging to new system...');
      // Bridge old auth success to new auth system
      bridgeAuthSuccess();
    }
  }, [token, profile.mobile, navigate, isZaloAuthenticated, zaloUser]);
  
  // Bridge old auth success to new web auth system
  const bridgeAuthSuccess = async () => {
    try {
      console.log('[LoginPage] Bridging auth success - token:', token, 'profile:', profile);
      
      if (token && profile.mobile) {
        // Store the token in sessionStorage like web auth does
        sessionStorage.setItem('taptap_auth_token', token);
        
        // Create a user object compatible with web auth
        const user = {
          id: profile.id || profile.userId,
          name: `${profile.firstname} ${profile.lastname}`.trim() || profile.mobile,
          email: profile.email || '',
          phone: profile.mobile,
          avatar: '', // Will be filled by Zalo user info if available
          displayName: `${profile.firstname} ${profile.lastname}`.trim() || profile.mobile,
          createdAt: new Date(profile.createdAt || Date.now()),
          updatedAt: new Date(profile.updatedAt || Date.now()),
        };
        
        sessionStorage.setItem('taptap_auth_user', JSON.stringify(user));
        
        console.log('[LoginPage] Auth bridged successfully, refreshing user...');
        
        // Refresh the new auth system to pick up the bridged auth
        await refreshUser();
        
        // Navigate to home
        navigate('/');
      }
    } catch (error) {
      console.error('[LoginPage] Failed to bridge auth success:', error);
    }
  };

  // OTP timer countdown
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (otpTimer > 0) {
      interval = setInterval(() => {
        setOtpTimer(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [otpTimer]);

  const validatePhone = (phone: string) => {
    // Vietnamese phone number validation (mobile style)
    const cleanPhone = phone.replace(/\D/g, '');
    
    if (!cleanPhone) {
      return 'Vui lòng nhập số điện thoại';
    }
    
    // Check if it starts with 84 (country code)
    if (cleanPhone.startsWith('84')) {
      if (cleanPhone.length < 11 || cleanPhone.length > 12) {
        return 'Số điện thoại không hợp lệ';
      }
    } else {
      // Assume Vietnamese domestic number (should be 9-10 digits)
      if (cleanPhone.length < 9 || cleanPhone.length > 10) {
        return 'Số điện thoại không hợp lệ';
      }
    }
    
    return '';
  };

  const formatPhoneNumber = (phone: string) => {
    const cleanPhone = phone.replace(/\D/g, '');
    
    // Add +84 country code if not present
    if (!cleanPhone.startsWith('84') && cleanPhone.length >= 9) {
      return `84${cleanPhone}`;
    }
    
    return cleanPhone;
  };

  const validateCurrentStep = () => {
    const errors: Record<string, string> = {};

    if (currentStep === 'phone') {
      const phoneError = validatePhone(formData.mobile);
      if (phoneError) {
        errors.mobile = phoneError;
      }
    } else if (currentStep === 'otp') {
      if (!formData.otp || formData.otp.length !== 6) {
        errors.otp = 'Vui lòng nhập mã OTP 6 số';
      }
    } else if (currentStep === 'password') {
      if (!formData.password) {
        errors.password = 'Vui lòng nhập mật khẩu';
      } else if (formData.password.length < 6) {
        errors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateCurrentStep()) return;

    try {
      setError('');
      clearError();
      
      if (currentStep === 'phone') {
        const formattedPhone = formatPhoneNumber(formData.mobile);
        
        // First check authorization state to determine flow
        try {
          const authStateResult = await checkRegistrationState({
            mobile: formattedPhone,
            deviceId: deviceId,
            callback: () => {}
          });
          
          // Based on mobile implementation, check if user has password set
          if (authStateResult && authStateResult.data) {
            const hasPassword = authStateResult.data === 'password' || 
                               (typeof authStateResult.data === 'object' && authStateResult.data.hasPassword);
            
            if (hasPassword) {
              // User exists and has password - go to password flow
              setCurrentStep('password');
              return;
            }
          }
          
          // If no password or new user, proceed with OTP flow
          const otpResult = await generateOTP({
            mobile: formattedPhone,
            deviceId: deviceId,
            callback: () => {}
          });

          console.log('OTP Generate Result:', otpResult);
          
          // Store session ID from OTP response for verification
          let foundSessionId = null;
          if (otpResult?.sessionId) {
            foundSessionId = otpResult.sessionId;
          } else if (otpResult?.data?.sessionId) {
            foundSessionId = otpResult.data.sessionId;
          } else if (otpResult?.auth?.sessionId) {
            foundSessionId = otpResult.auth.sessionId;
          } else if (otpResult?.data?.auth?.sessionId) {
            foundSessionId = otpResult.data.auth.sessionId;
          }
          
          if (foundSessionId) {
            setSessionId(foundSessionId);
            console.log('SessionId found and set:', foundSessionId);
          } else {
            console.warn('No sessionId found in OTP response:', otpResult);
          }
          
          setCurrentStep('otp');
          setOtpTimer(60); // Start 60-second countdown
          // Set default OTP for testing
          setFormData(prev => ({ ...prev, otp: '999999' }));
          
        } catch (error: any) {
          setError(error.message || 'Không thể kiểm tra trạng thái tài khoản. Vui lòng thử lại.');
        }
        
      } else if (currentStep === 'otp') {
        try {
          const formattedPhone = formatPhoneNumber(formData.mobile);
          
          await verifyOTP({
            mobile: formattedPhone,
            deviceId: deviceId,
            sessionId: sessionId,
            otp: formData.otp,
            callback: () => {}
          });
          
          console.log('[LoginPage] OTP verification successful!');
          // OTP verification successful - navigate to home  
          navigate('/');
          
        } catch (error: any) {
          console.error('[LoginPage] OTP verification failed:', error);
          setError(error.message || 'Mã OTP không chính xác. Vui lòng thử lại.');
        }
        
      } else if (currentStep === 'password') {
        try {
          const formattedPhone = formatPhoneNumber(formData.mobile);
          
          await signinWithPassword({
            mobile: formattedPhone,
            deviceId: deviceId,
            password: formData.password
          });
          
          console.log('[LoginPage] Password signin successful!');
          // Password signin successful - navigate to home
          navigate('/');
          
        } catch (error: any) {
          console.error('[LoginPage] Password signin failed:', error);
          setError(error.message || 'Mật khẩu không chính xác. Vui lòng thử lại.');
        }
      }
      
    } catch (err: any) {
      setError(err.message || 'Đã có lỗi xảy ra. Vui lòng thử lại.');
      console.error('Auth error:', err);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    // Handle OTP input with auto-formatting
    if (field === 'otp') {
      const numericValue = value.replace(/\D/g, '').slice(0, 6);
      setFormData(prev => ({ ...prev, [field]: numericValue }));
    } else if (field === 'mobile') {
      // Handle phone number input
      const numericValue = value.replace(/\D/g, '');
      setFormData(prev => ({ ...prev, [field]: numericValue }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleBack = () => {
    if (currentStep === 'otp' || currentStep === 'password') {
      setCurrentStep('phone');
      setFormData(prev => ({ ...prev, otp: '', password: '' }));
      setError('');
    } else {
      navigate('/');
    }
  };

  const handleResendOTP = async () => {
    if (otpTimer > 0) return;
    
    try {
      setError('');
      const formattedPhone = formatPhoneNumber(formData.mobile);
      
      const otpResult = await generateOTP({
        mobile: formattedPhone,
        deviceId: deviceId,
        callback: () => {}
      });
      
      console.log('Resend OTP Result:', otpResult);
      
      // Try different possible sessionId locations for resend
      let foundSessionId = null;
      if (otpResult?.sessionId) {
        foundSessionId = otpResult.sessionId;
      } else if (otpResult?.data?.sessionId) {
        foundSessionId = otpResult.data.sessionId;
      } else if (otpResult?.auth?.sessionId) {
        foundSessionId = otpResult.auth.sessionId;
      } else if (otpResult?.data?.auth?.sessionId) {
        foundSessionId = otpResult.data.auth.sessionId;
      }
      
      if (foundSessionId) {
        setSessionId(foundSessionId);
        console.log('Resend SessionId found and set:', foundSessionId);
      } else {
        console.warn('No sessionId found in resend OTP response:', otpResult);
      }
      
      setOtpTimer(60);
      // Set default OTP for testing
      setFormData(prev => ({ ...prev, otp: '999999' }));
      
    } catch (error: any) {
      setError(error.message || 'Không thể gửi lại mã OTP. Vui lòng thử lại.');
    }
  };

  const getTitle = () => {
    switch (currentStep) {
      case 'phone': return 'Đăng nhập';
      case 'otp': return 'Xác thực OTP';
      case 'password': return 'Nhập mật khẩu';
    }
  };

  const getSubmitText = () => {
    const isLoading = loadingStates.otpGenerate === 'loading' || 
                     loadingStates.otpVerify === 'loading' || 
                     loadingStates.login === 'loading';
                     
    if (isLoading) return 'Đang xử lý...';
    
    switch (currentStep) {
      case 'phone': return 'Gửi mã OTP';
      case 'otp': return 'Xác thực';
      case 'password': return 'Đăng nhập';
    }
  };

  const formatPhoneDisplay = () => {
    const phone = formData.mobile;
    if (!phone) return '';
    
    if (phone.startsWith('84')) {
      return `+${phone}`;
    } else {
      return `+84${phone}`;
    }
  };

  // Back icon
  const BackIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M15 18L9 12L15 6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  return (
    <PageAny className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <NavigationHeader
        title={getTitle()}
        leftIcon={BackIcon}
        onLeftClick={handleBack}
      />

      <div className="p-4 max-w-md mx-auto">
        {/* Welcome Message */}
        <div className="text-center mb-8 mt-8">
          <div className="w-20 h-20 bg-[#F65D79] rounded-full flex items-center justify-center mx-auto mb-4">
            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" className="text-white">
              <path
                d="M20 3.33334C28.4667 3.33334 35.3333 10.2 35.3333 18.6667C35.3333 27.1333 28.4667 34 20 34C11.5333 34 4.66667 27.1333 4.66667 18.6667C4.66667 10.2 11.5333 3.33334 20 3.33334Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M20 13.3333C17.7909 13.3333 16 15.1242 16 17.3333V21.3333C16 23.5425 17.7909 25.3333 20 25.3333C22.2091 25.3333 24 23.5425 24 21.3333V17.3333C24 15.1242 22.2091 13.3333 20 13.3333Z"
                stroke="currentColor"
                strokeWidth="2"
              />
              <path
                d="M13.3333 28.6667H26.6667"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-[#1A1818] mb-2">
            {currentStep === 'phone' && 'Chào mừng trở lại'}
            {currentStep === 'otp' && 'Nhập mã OTP'}
            {currentStep === 'password' && 'Nhập mật khẩu'}
          </h2>
          
          <p className="text-[#9A9A9A]">
            {currentStep === 'phone' && 'Nhập số điện thoại để đăng nhập'}
            {currentStep === 'otp' && `Mã OTP đã được gửi đến ${formatPhoneDisplay()}`}
            {currentStep === 'password' && 'Nhập mật khẩu để đăng nhập'}
          </p>
        </div>

        {/* Main Form */}
        <Card className="p-6 mb-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            
            {/* Phone Number Step */}
            {currentStep === 'phone' && (
              <div>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#9A9A9A]">
                    +84
                  </div>
                  <Input
                    type="tel"
                    placeholder="Số điện thoại"
                    value={formData.mobile}
                    onChange={(e) => handleInputChange('mobile', e.target.value)}
                    error={formErrors.mobile}
                    disabled={loadingStates.otpGenerate === 'loading'}
                    className="pl-12"
                  />
                </div>
                {formErrors.mobile && (
                  <p className="text-red-500 text-sm mt-1">{formErrors.mobile}</p>
                )}
              </div>
            )}

            {/* OTP Step */}
            {currentStep === 'otp' && (
              <div>
                <Input
                  type="tel"
                  placeholder="Nhập mã OTP 6 số"
                  value={formData.otp}
                  onChange={(e) => handleInputChange('otp', e.target.value)}
                  error={formErrors.otp}
                  disabled={loadingStates.otpVerify === 'loading'}
                  maxLength={6}
                  className="text-center text-2xl tracking-widest"

                />
                {formErrors.otp && (
                  <p className="text-red-500 text-sm mt-1">{formErrors.otp}</p>
                )}
                
                {/* Resend OTP */}
                <div className="text-center mt-4">
                  {otpTimer > 0 ? (
                    <p className="text-[#9A9A9A] text-sm">
                      Gửi lại mã sau {otpTimer}s
                    </p>
                  ) : (
                    <button
                      type="button"
                      onClick={handleResendOTP}
                      className="text-[#F65D79] text-sm hover:underline"
                      disabled={loadingStates.otpGenerate === 'loading'}
                    >
                      Gửi lại mã OTP
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Password Step */}
            {currentStep === 'password' && (
              <div>
                <Input
                  type="password"
                  placeholder="Nhập mật khẩu"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  error={formErrors.password}
                  disabled={loadingStates.login === 'loading'}
                />
                {formErrors.password && (
                  <p className="text-red-500 text-sm mt-1">{formErrors.password}</p>
                )}
              </div>
            )}

            {/* Global error */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* Submit button */}
            <Button
              type="submit"
              className="w-full"
              disabled={
                loadingStates.otpGenerate === 'loading' || 
                loadingStates.otpVerify === 'loading' || 
                loadingStates.login === 'loading'
              }
            >
              {getSubmitText()}
            </Button>
          </form>
        </Card>

        {/* Additional Info */}
        {currentStep === 'otp' && (
          <div className="text-center">
            <p className="text-[#9A9A9A] text-sm mb-2">
              Không nhận được mã?
            </p>
            <button
              type="button"
              onClick={() => setCurrentStep('phone')}
              className="text-[#F65D79] font-medium hover:underline text-sm"
            >
              Thay đổi số điện thoại
            </button>
          </div>
        )}

        {currentStep === 'password' && (
          <div className="text-center">
            <button
              type="button"
              onClick={() => setCurrentStep('phone')}
              className="text-[#F65D79] font-medium hover:underline text-sm"
            >
              Đăng nhập bằng OTP
            </button>
          </div>
        )}
      </div>
    </PageAny>
  );
};

export default LoginPage;