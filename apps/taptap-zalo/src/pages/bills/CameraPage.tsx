import React from 'react';
import { Page } from "zmp-ui";
import { openMediaPicker } from 'zmp-sdk/apis';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import { 
  NavigationHeader, 
  CameraError,
  CameraSuccessState,
  CameraPreviewStep,
  LoadingSpinner,
  BackIcon,
  useCameraCapture
} from '@taptap/shared';

const PageAny = Page as any;

const CameraPage: React.FC = () => {
  const { navigate, goBack, goHome } = useZaloNavigation();
  
  // Use extracted hook with Zalo-specific navigation handlers
  const {
    state,
    capturedPhoto,
    isProcessing,
    processingStatus,
    userStatus,
    remainingSnaps,
    previewUserStatus,
    handleCapture,
    handleCameraError,
    handleShowInfo,
    handleShowCapturedReceipts,
    handleBack,
    handleComplete,
    handleGoHome,
  } = useCameraCapture({
    onNavigate: (path: string) => navigate(path),
    onNavigateBack: (steps?: number) => goBack(),
  });

  const handleOpenCamera = async () => {
    try {
      // Use <PERSON>alo's openMediaPicker with zcamera_photo type for camera capture
      const result = await openMediaPicker({
        serverUploadUrl: "https://api.taptap.com.vn/upload/media", // This would be your backend URL
        type: "zcamera_photo", // Opens camera directly for photo capture
        maxSelectItem: 1
      });

      if (result && result.data) {
        const uploadedData = JSON.parse(result.data);
        if (uploadedData && uploadedData.url) {
          handleCapture(uploadedData.url);
        }
      }
    } catch (error) {
      console.error('Camera error:', error);
      handleCameraError({
        type: 'capture_error',
        message: 'Không thể chụp ảnh. Vui lòng thử lại.'
      });
    }
  };

  const handleSelectFromGallery = async () => {
    try {
      // Use Zalo's openMediaPicker for selecting from gallery
      const result = await openMediaPicker({
        serverUploadUrl: "https://api.taptap.com.vn/upload/media", // This would be your backend URL
        type: "photo", // Opens photo gallery
        maxSelectItem: 1
      });

      if (result && result.data) {
        const uploadedData = JSON.parse(result.data);
        if (uploadedData && uploadedData.url) {
          handleCapture(uploadedData.url);
        }
      }
    } catch (error) {
      console.error('Gallery error:', error);
      handleCameraError({
        type: 'capture_error',
        message: 'Không thể chọn ảnh. Vui lòng thử lại.'
      });
    }
  };

  return (
    <PageAny className="w-full h-screen relative overflow-hidden" style={{ backgroundColor: state === 'success' ? '#EFF3F6' : 'white' }}>
      {/* Navigation Header */}
      <div className="absolute top-0 left-0 right-0 z-10">
        <NavigationHeader
          title="Chụp hóa đơn"
          leftIcon={<BackIcon />}
          onLeftClick={handleBack}
          variant="ZaloMiniApp"
          className={state === 'success' ? 'bg-white border-b border-[#ECECEC]' : 'bg-white border-b border-[#ECECEC]'}
          textClassName={state === 'success' ? 'text-[#1A1818]' : 'text-[#1A1818]'}
        />
      </div>

      {/* Main Content */}
      {state === 'camera' && (
        <div className="flex flex-col items-center justify-center h-full px-6 pt-20">
          <div className="text-center mb-8">
            <div className="text-6xl mb-4">📷</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Chụp hóa đơn</h2>
            <p className="text-gray-600 mb-2">Chụp ảnh hóa đơn để tích điểm VUI</p>
            {remainingSnaps > 0 && (
              <p className="text-sm text-blue-600 font-medium">
                Còn {remainingSnaps} lượt chụp hôm nay
              </p>
            )}
          </div>

          <div className="w-full space-y-4">
            <button
              onClick={handleOpenCamera}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-4 px-6 rounded-xl transition-colors flex items-center justify-center gap-3"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M23 19a2 2 0 01-2 2H3a2 2 0 01-2-2V8a2 2 0 012-2h4l2-3h6l2 3h4a2 2 0 012 2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <circle cx="12" cy="13" r="4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Chụp hóa đơn
            </button>

            <button
              onClick={handleSelectFromGallery}
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-4 px-6 rounded-xl transition-colors flex items-center justify-center gap-3"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <circle cx="8.5" cy="8.5" r="1.5" fill="currentColor"/>
                <polyline points="21 15 16 10 5 21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Chọn từ thư viện
            </button>

            <button
              onClick={handleShowCapturedReceipts}
              className="w-full bg-white border-2 border-gray-300 hover:border-gray-400 text-gray-700 font-medium py-4 px-6 rounded-xl transition-colors flex items-center justify-center gap-3"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <polyline points="14 2 14 8 20 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <polyline points="10 9 9 9 8 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Xem hóa đơn đã chụp
            </button>
          </div>

          <div className="mt-8 text-center">
            <button
              onClick={handleShowInfo}
              className="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center gap-1"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="12" y1="16" x2="12" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="12" y1="8" x2="12.01" y2="8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Hướng dẫn chụp hóa đơn
            </button>
          </div>
        </div>
      )}

      {state === 'preview' && capturedPhoto && (
        <CameraPreviewStep
          capturedPhoto={capturedPhoto}
          remainingSnaps={remainingSnaps}
          userStatus={previewUserStatus}
          onBack={handleBack}
          onComplete={handleComplete}
        />
      )}

      {state === 'success' && (
        <div className="absolute top-[92px] left-0 right-0 bottom-0">
          <CameraSuccessState
            onGoHome={handleGoHome}
            onViewCapturedReceipts={handleShowCapturedReceipts}
          />
        </div>
      )}

      {/* Processing Overlay */}
      {isProcessing && (
        <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-20">
          <div className="bg-white rounded-xl p-6 mx-4 text-center max-w-sm">
            <div className="mb-4">
              <LoadingSpinner />
            </div>
            <div className="text-gray-800 font-semibold text-lg mb-2">
              Đang xử lý hóa đơn
            </div>
            <div className="text-gray-600 text-sm leading-relaxed">
              {processingStatus || 'Vui lòng đợi trong giây lát...'}
            </div>
            <div className="mt-4 bg-gray-100 rounded-lg p-3">
              <div className="text-xs text-gray-500">
                💡 Quá trình này có thể mất 10-30 giây
              </div>
            </div>
          </div>
        </div>
      )}
    </PageAny>
  );
};

export default CameraPage;