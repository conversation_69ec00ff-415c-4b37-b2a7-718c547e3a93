import React, { useState, useMemo, useEffect } from 'react';
import { Page } from "zmp-ui";
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import {
  <PERSON><PERSON><PERSON>er,
  SwiperTabContainer,
  Bill<PERSON>ard,
  BillListSkeleton,
  useOCRStore,
  mapOCRTransactionToBillCard,
  EmptyBillState,
  BackIcon,
} from '@taptap/shared';
import type { SwiperTabContainerItem, BillCardProps, OCRTransactionStatus } from '@taptap/shared';
import ZaloBottomNavigation from '../../components/navigation/ZaloBottomNavigation';

const PageAny = Page as any;

// Tab configuration
const TAB_CONFIG = {
  'waiting': { id: 'waiting', label: 'Đang chờ', status: 'PROCESSING' as OCRTransactionStatus },
  'completed': { id: 'completed', label: 'Thành công', status: 'COMPLETED' as OCRTransactionStatus },
  'rejected': { id: 'rejected', label: 'Từ chối', status: 'REJECTED' as OCRTransactionStatus },
};

const BillsPage: React.FC = () => {
  const { navigate, goBack, goHome, goToCamera } = useZaloNavigation();
  const [activeTab, setActiveTab] = useState('waiting');

  // Get current tab configuration
  const currentTabConfig = TAB_CONFIG[activeTab as keyof typeof TAB_CONFIG];
  
  // Use OCR store
  const {
    getTransactionsForStatus,
    getMetaForStatus,
    isLoadingForStatus,
    getTotalCountForStatus,
    error,
    fetchTransactions,
    fetchStatistics,
    setCurrentStatus,
    clearError,
  } = useOCRStore();

  // Get data for current status
  const transactions = getTransactionsForStatus(currentTabConfig.status) || [];
  const meta = getMetaForStatus(currentTabConfig.status);
  const loading = isLoadingForStatus(currentTabConfig.status);

  // Convert OCR transactions to BillCardProps
  const billCards = useMemo(() => {
    return transactions.map(mapOCRTransactionToBillCard);
  }, [transactions]);

  // Custom Badge Component for Bill tabs
  const BillTabBadge: React.FC<{ count: number }> = ({ count }) => (
    <div 
      className="flex items-center justify-center px-0.5 h-4 min-w-[16px] bg-[#F65D79] rounded-full ml-0.5"
      style={{ fontFamily: 'Archia' }}
    >
      <span className="text-[10px] font-semibold text-white leading-[16px]">
        {count}
      </span>
    </div>
  );

  // Generate tab items for swiper with custom badge
  const tabItems: SwiperTabContainerItem[] = useMemo(() => {
    return Object.values(TAB_CONFIG).map(config => {
      // Use meta count for badges
      const count = getTotalCountForStatus(config.status);
      
      return {
        id: config.id,
        label: config.label,
        badgeText: count !== undefined && count > 0 ? count.toString() : undefined,
        badge: count !== undefined && count > 0,
        customBadge: count !== undefined && count > 0 ? <BillTabBadge count={count} /> : undefined,
      };
    });
  }, [getTotalCountForStatus]);

  // Load initial data
  useEffect(() => {
    // Fetch statistics on component mount
    fetchStatistics();
    
    if (currentTabConfig) {
      setCurrentStatus(currentTabConfig.status);
      fetchTransactions(currentTabConfig.status, { page: 1, size: 10 });
    }
  }, [currentTabConfig, fetchTransactions, fetchStatistics, setCurrentStatus]);


  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    clearError(); // Clear any previous errors
    
    // Fetch new data for the selected tab
    const newTabConfig = TAB_CONFIG[tabId as keyof typeof TAB_CONFIG];
    if (newTabConfig) {
      setCurrentStatus(newTabConfig.status);
      fetchTransactions(newTabConfig.status, { page: 1, size: 10 });
      
      // Refresh statistics to keep badges up to date
      fetchStatistics();
    }
  };

  const handleBillClick = (bill: BillCardProps) => {
    // Navigate to bill detail
    navigate(`/bills/${bill.id}`);
  };

  const handleBack = () => {
    goBack();
  };

  // Helper functions for empty state
  const getEmptyDescription = (tabId: string): string => {
    switch (tabId) {
      case 'completed':
        return 'Sau khi xác nhận, các hoá đơn được tích điểm sẽ được ghi nhận tại đây.';
      case 'waiting':
        return 'Hoá đơn bạn chụp sẽ xếp hàng chờ xác nhận tại đây.';
      case 'rejected':
        return 'Các hoá đơn không được tích điểm sẽ được ghi nhận tại đây.';
      default:
        return 'Khi chụp hoá đơn & tích VUI, trạng thái hoá đơn của bạn sẽ được cập nhật tại đây.';
    }
  };

  const getActionClick = (tabId: string): (() => void) | undefined => {
    switch (tabId) {
      case 'waiting':
      case 'rejected':
        return () => goToCamera();
      default:
        return () => goHome();
    }
  };

  const getActionText = (tabId: string): string | undefined => {
    switch (tabId) {
      case 'waiting':
      case 'rejected':
        return 'Chụp hoá đơn ngay';
      default:
        return 'Khám phá ngay';
    }
  };

  return (
    <PageAny className="w-full min-h-screen bg-[#EFF3F6] flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title="Hóa đơn đã chụp"
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        variant="ZaloMiniApp"
        className="bg-white border-b border-[#ECECEC]"
      />

      {/* Swiper Tab Container */}
      <SwiperTabContainer
        tabs={tabItems}
        activeTabId={activeTab}
        onTabChange={handleTabChange}
        containerClassName="flex-1"
        contentClassName="bg-[#EFF3F6]"
      >
        <div className="h-full px-4 py-4 pb-20 overflow-y-auto">
          {/* Loading State */}
          {loading && (
            <BillListSkeleton count={5} />
          )}

          {/* Error State */}
          {error && !loading && (
            <EmptyBillState
              title="Có lỗi xảy ra"
              description={error}
              mascotName="error"
              onActionClick={() => {
                clearError();
                fetchTransactions(currentTabConfig.status, { page: 1, size: 10 });
              }}
              actionText="Thử lại"
            />
          )}

          {/* Bill List */}
          {!loading && !error && billCards.length > 0 && (
            <div className="space-y-3">
              {billCards.map((bill) => (
                <BillCard
                  key={bill.id}
                  {...bill}
                  onClick={() => handleBillClick(bill)}
                />
              ))}
            </div>
          )}

          {/* Empty State */}
          {!loading && !error && billCards.length === 0 && (
            <EmptyBillState
              title="Chưa có hoá đơn nào"
              description={getEmptyDescription(activeTab)}
              mascotName="meditate"
              onActionClick={getActionClick(activeTab)}
              actionText={getActionText(activeTab)}
            />
          )}

          {/* Pagination Info */}
          {!loading && !error && meta && meta.totalRows > 0 && (
            <div className="mt-4 text-center text-sm text-[#9A9A9A]">
              Hiển thị {billCards.length} trong tổng số {meta.totalRows} hóa đơn
            </div>
          )}
        </div>
      </SwiperTabContainer>

      {/* Bottom Navigation */}
      <ZaloBottomNavigation activeTab="profile" />
    </PageAny>
  );
};

export default BillsPage;