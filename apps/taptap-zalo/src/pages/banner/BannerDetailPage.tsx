import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Page } from 'zmp-ui';
import { WebView, NavigationHeader, LoadingSpinner } from '@taptap/shared';
import { bannerService } from '@taptap/shared';
import type { BannerData } from '@taptap/shared';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';

const PageAny = Page as any;

const BannerDetailPage: React.FC = () => {
  const { bannerId } = useParams<{ bannerId: string }>();
  const { navigate, goBack } = useZaloNavigation();
  const [banner, setBanner] = useState<BannerData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBannerDetails = async () => {
      if (!bannerId) {
        setError('Banner ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const bannerData = await bannerService.getBannerDetail(bannerId);
        
        if (bannerData) {
          setBanner(bannerData);
        } else {
          setError('Banner not found');
        }
      } catch (err) {
        setError('Failed to load banner');
        console.error('Banner fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchBannerDetails();
  }, [bannerId]);

  // Handle deep links and external URLs
  const getWebViewUrl = (url: string): string => {
    // If it's a taptapvui:// deep link, we might need to handle it differently
    if (url.startsWith('taptapvui://')) {
      // For now, return a web equivalent or handle the deep link
      console.log('Deep link detected in Zalo:', url);
      // You might want to parse the deep link and redirect accordingly
      return '#'; // Placeholder
    }

    // For regular HTTP/HTTPS URLs
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // For relative URLs
    if (url.startsWith('/')) {
      return window.location.origin + url;
    }

    return url;
  };

  const handleBack = () => {
    goBack();
  };

  if (loading) {
    return (
      <PageAny className="min-h-screen bg-white">
        <NavigationHeader 
          title="Loading..."
          showBackButton={true}
          onBackClick={handleBack}
        />
        <div className="flex-1 flex items-center justify-center" style={{ height: 'calc(100vh - 48px)' }}>
          <LoadingSpinner />
        </div>
      </PageAny>
    );
  }

  if (error || !banner) {
    return (
      <PageAny className="min-h-screen bg-white">
        <NavigationHeader 
          title="Error"
          showBackButton={true}
          onBackClick={handleBack}
        />
        <div className="flex-1 flex flex-col items-center justify-center p-4" style={{ height: 'calc(100vh - 48px)' }}>
          <div className="text-center">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              {error || 'Banner not found'}
            </h2>
            <p className="text-gray-600 mb-4">
              The banner you're looking for is not available.
            </p>
            <button
              onClick={handleBack}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </PageAny>
    );
  }

  // Handle deep links - redirect to appropriate pages
  if (banner.url.startsWith('taptapvui://')) {
    // Parse deep link and redirect
    if (banner.url.includes('survey_campaign')) {
      const urlParams = new URLSearchParams(banner.url.split('?')[1] || '');
      const surveyId = urlParams.get('id');
      if (surveyId) {
        // Redirect to survey page
        navigate(`/survey/${surveyId}`);
        return null;
      }
    }
    
    // If we can't handle the deep link, show error
    return (
      <PageAny className="min-h-screen bg-white">
        <NavigationHeader 
          title={banner.name}
          showBackButton={true}
          onBackClick={handleBack}
        />
        <div className="flex-1 flex flex-col items-center justify-center p-4" style={{ height: 'calc(100vh - 48px)' }}>
          <div className="text-center">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              Deep Link Not Supported
            </h2>
            <p className="text-gray-600 mb-4">
              This banner contains a deep link that cannot be opened in Zalo Mini App.
            </p>
            <p className="text-sm text-gray-500 mb-4 font-mono bg-gray-100 p-2 rounded">
              {banner.url}
            </p>
            <button
              onClick={handleBack}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </PageAny>
    );
  }

  const webViewUrl = getWebViewUrl(banner.url);
  const showHeader = !banner.isHideWebviewHeader;

  return (
    <PageAny className="min-h-screen bg-white">
      {/* Navigation Header - only show if not hidden */}
      {showHeader && (
        <NavigationHeader 
          title={banner.name}
          showBackButton={true}
          onBackClick={handleBack}
          className="border-b border-gray-200"
        />
      )}
      
      {/* WebView Content */}
      <div 
        className="flex-1" 
        style={{ 
          height: showHeader ? 'calc(100vh - 48px)' : '100vh' 
        }}
      >
        <WebView 
          url={webViewUrl}
          title={banner.name}
          width="100%"
          height="100%"
          loading="eager"
          onLoad={() => {
            console.log('Banner webview loaded in Zalo:', banner.name);
          }}
          onError={() => {
            console.error('Banner webview failed to load in Zalo:', banner.url);
          }}
        />
      </div>
    </PageAny>
  );
};

export default BannerDetailPage;