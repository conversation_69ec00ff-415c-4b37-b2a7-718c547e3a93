import React, { useState, useEffect } from 'react';
import { Page } from "zmp-ui";
import { 
  NavigationHeader, 
  ScrollableCarousel,
  SearchBar,
  SectionHeader,
  RewardThumbnailCard,
  ServiceCategoryButton,
  RewardItem,
  rewardService,
  RewardItemType,
  BackIcon
} from '@taptap/shared';
import type { RewardSuggestionCollection, RewardCollectionItem } from '@taptap/shared';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';

const PageAny = Page as any;

// Default icon for categories - used by ServiceCategoryButton
const DefaultCategoryIcon = '/src/assets/icons/Menu.svg';

export const ExchangePage: React.FC = () => {
  const { navigate, goBack } = useZaloNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  
  // API state
  const [rewardCollections, setRewardCollections] = useState<RewardSuggestionCollection[]>([]);
  const [categoryCollections, setCategoryCollections] = useState<RewardCollectionItem[]>([]);
  const [loadingCategoryCollections, setLoadingCategoryCollections] = useState(false);
  
  // Carousel state for category buttons
  const [activeSlideIndex, setActiveSlideIndex] = useState(0);

  // Load APIs
  useEffect(() => {
    const loadData = async () => {
      // Load category collections for ServiceCategoryButton
      setLoadingCategoryCollections(true);
      try {
        const categoryResponse = await rewardService.getRewardCollection();
        console.log('Category Collections API response:', categoryResponse);
        if (categoryResponse.status.success && categoryResponse.data) {
          setCategoryCollections(categoryResponse.data);
        }
      } catch (error) {
        console.error('Error loading category collections:', error);
      } finally {
        setLoadingCategoryCollections(false);
      }

      // Load reward suggestions 
      try {
        const suggestResponse = await rewardService.getRewardCollectionSuggestion();
        console.log('Reward Collections API response:', suggestResponse);
        if (suggestResponse.status.success && suggestResponse.data) {
          setRewardCollections(suggestResponse.data);
        }
      } catch (error) {
        console.error('Error loading reward collections:', error);
      }
    };
    
    loadData();
  }, []);

  const handleBack = () => {
    goBack();
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Handle carousel slide change
  const handleSlideChange = (index: number) => {
    setActiveSlideIndex(index);
  };

  // Simple dot indicators (instead of image-based indicators)
  const renderDotIndicators = (totalSlides: number) => {
    if (totalSlides <= 1) return null;
    
    return (
      <div className="flex justify-center mt-3 gap-2">
        {Array.from({ length: totalSlides }, (_, index) => (
          <div
            key={index}
            className={`w-2 h-2 rounded-full transition-colors ${
              index === activeSlideIndex ? 'bg-[#F65D79]' : 'bg-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  return (
    <PageAny className="w-full min-h-screen bg-[#fff] relative">
      {/* Header with Wave Background matching Figma */}
      <div className="relative bg-[#F65D79] pb-4">
        {/* Navigation Header */}
        <NavigationHeader
          title="Đổi thưởng"
          leftIcon={<BackIcon />}
          onLeftClick={handleBack}
          className="!bg-[#F65D79] border-none text-white"
        />
        
        {/* Search Bar */}
        <div className="px-4 py-2">
          <SearchBar
            value={searchQuery}
            onChange={handleSearch}
            placeholder="Tìm ưu đãi, thương hiệu..."
            showSearchIcon={true}
            showClearButton={true}
            autoFocus={false}
            onClear={() => setSearchQuery('')}
            onSearch={handleSearch}
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 px-4 py-4 space-y-4 mb-20">
        {/* Search Results Info */}
        {searchQuery.trim() && (
          <div className="mb-4">
            <p className="text-sm text-[#9A9A9A]">
              Tìm kiếm cho &ldquo;{searchQuery}&rdquo;
            </p>
          </div>
        )}

        {/* Services Section - Carousel with dots for category navigation */}
        {!searchQuery.trim() && categoryCollections.length > 0 && (
          <div>
            {(() => {
              // Add "Tất cả" as first item
              const allCategoriesWithAll = [
                // {
                //   id: 'all',
                //   name: 'Tất cả',
                //   code: 'all',
                //   image: DefaultCategoryIcon,
                //   count: 0
                // },
                ...categoryCollections
              ];
              
              // Group items into pages of 8 (2 rows x 4 items per page)
              const itemsPerPage = 8;
              const pages = [];
              for (let i = 0; i < allCategoriesWithAll.length; i += itemsPerPage) {
                pages.push(allCategoriesWithAll.slice(i, i + itemsPerPage));
              }
              
              return (
                <>
                  <ScrollableCarousel
                    showPagination={false}
                    spaceBetween={0}
                    slidesPerView={1}
                    freeMode={false}
                    centeredSlides={true}
                    onSlideChange={handleSlideChange}
                    className="px-4"
                  >
                    {pages.map((pageItems, pageIndex) => {
                      // Split page items into 2 rows of 4
                      const firstRow = pageItems.slice(0, 4);
                      const secondRow = pageItems.slice(4, 8);
                      
                      return (
                        <div key={pageIndex} className="w-full space-y-3">
                          {/* First Row */}
                          <div className="flex justify-between gap-2">
                            {firstRow.map((collection) => (
                              <ServiceCategoryButton
                                key={collection.id}
                                collection={collection}
                                onClick={(code) => navigate(code === 'all' ? '/exchange/all-rewards' : `/exchange/all-rewards?collectionCode=${code}`)}
                              />
                            ))}
                            {/* Fill empty slots if needed */}
                            {Array.from({ length: 4 - firstRow.length }, (_, index) => (
                              <div key={`empty-row1-${index}`} className="flex-1 max-w-[80px]" />
                            ))}
                          </div>
                          
                          {/* Second Row */}
                          {secondRow.length > 0 && (
                            <div className="flex justify-between gap-2">
                              {secondRow.map((collection) => (
                                <ServiceCategoryButton
                                  key={collection.id}
                                  collection={collection}
                                  onClick={(code) => navigate(code === 'all' ? '/exchange/all-rewards' : `/exchange/all-rewards?collectionCode=${code}`)}
                                />
                              ))}
                              {/* Fill empty slots if needed */}
                              {Array.from({ length: 4 - secondRow.length }, (_, index) => (
                                <div key={`empty-row2-${index}`} className="flex-1 max-w-[80px]" />
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </ScrollableCarousel>
                  
                  {/* Dot Indicators */}
                  {renderDotIndicators(pages.length)}
                </>
              );
            })()}
          </div>
        )}

        {/* Loading state for categories */}
        {!searchQuery.trim() && loadingCategoryCollections && (
          <div className="space-y-3">
            <div className="flex justify-between gap-2">
              {Array.from({ length: 4 }, (_, index) => (
                <div key={index} className="flex flex-col items-center gap-2 p-3 flex-1 max-w-[80px]">
                  <div className="w-9 h-9 bg-gray-200 rounded-lg animate-pulse" />
                  <div className="w-12 h-3 bg-gray-200 rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Reward Collections from API */}
        {!searchQuery.trim() && rewardCollections.length > 0 && (
          <div className="space-y-6">
            {rewardCollections.slice(0, 2).map((collection) => (
              <div key={collection.collectionId} className="space-y-4">
                <SectionHeader
                  title={collection.name}
                  actionText={collection.rewardDetails.length > 4 ? "Tất cả" : undefined}
                  onActionClick={() => navigate(`/rewards/collection/${collection.collectionCode}`)}
                />
                
                {/* Reward Items using ScrollableCarousel */}
                <ScrollableCarousel
                  slidesPerView="auto"
                  spaceBetween={12}
                  freeMode={true}
                  className="px-4"
                >
                  {collection.rewardDetails.slice(0, 6).map((reward: RewardItemType) => (
                    <RewardItem
                      key={reward.id}
                      imageUrl={reward.image1}
                      logoUrl={reward.merchant?.logo || ''}
                      storeName={reward.merchant?.name || ''}
                      promotion={reward.name}
                      salePrice={reward.isVuiPoint ? (reward.issueVUIPoint || 0) : (reward.issueBrandCurrencyPoint || 0)}
                      originalPrice={reward.isVuiPoint ? (reward.issueVUIPoint || 0) : (reward.issueBrandCurrencyPoint || 0)}
                      onClick={() => navigate(`/rewards/${reward.id}`)}
                    />
                  ))}
                </ScrollableCarousel>
              </div>
            ))}
          </div>
        )}
      </div>
    </PageAny>
  );
};

export default ExchangePage;