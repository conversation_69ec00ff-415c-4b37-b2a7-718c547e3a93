import React, { useEffect } from 'react';
import { Page } from "zmp-ui";
import { useZaloNavigation } from '../../hooks/useZaloNavigation';

const PageAny = Page as any;

/**
 * RewardsPage acts as a redirect page.
 * When accessed directly at /rewards, it redirects to /my-rewards.
 * This maintains compatibility with any old links or direct navigation to /rewards.
 * The bottom navigation now directly navigates to /my-rewards or /exchange.
 */
const RewardsPage: React.FC = () => {
  const { navigate } = useZaloNavigation();

  useEffect(() => {
    // Redirect to my-rewards page by default
    // This page shouldn't normally be accessed directly since
    // bottom navigation now goes directly to /my-rewards or /exchange
    navigate('/my-rewards');
  }, [navigate]);

  return (
    <PageAny className="min-h-screen bg-[#EFF3F6]">
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F65D79] mx-auto mb-4"></div>
          <p className="text-[#9A9A9A]"><PERSON><PERSON>uy<PERSON> hướng...</p>
        </div>
      </div>
    </PageAny>
  );
};

export default RewardsPage;