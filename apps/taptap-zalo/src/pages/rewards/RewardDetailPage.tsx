import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Page } from "zmp-ui";
import {
  NavigationHeader,
  Button,
  ImageGallery,
  GalleryImage,
  SectionInfo,
  ContactInfo,
  SectionHowToUse,
  RewardDetailHeader,
  StoreNearbySection,
  BottomPurchaseSection,
  RewardsAPI,
  Dialog,
} from '@taptap/shared';
import type { RewardItemType } from '@taptap/shared';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';

// Import the downloaded images (fallback logos and coin icon)
import dashiKitchenLogo from '@shared/assets/images/logo-dashi-kitchen.png';
import rosieVintageLogo from '@shared/assets/images/logo-rosie-vintage.png';
import coinIcon from '@shared/assets/images/coin-icon-32px.svg';

const PageAny = Page as any;

const RewardDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { navigate, goBack } = useZaloNavigation();
  const [reward, setReward] = useState<RewardItemType | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  useEffect(() => {
    const fetchRewardDetail = async () => {
      if (!id) {
        setError('ID phần thưởng không hợp lệ');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Call the Rewards API to get reward detail
        const response = await RewardsAPI.getRewardDetail(id);
        // Handle BaseResponse wrapper
        const rewardData = response.data || response;
        setReward(rewardData);
      } catch (err: any) {
        setError(err?.message || 'Không thể tải thông tin phần thưởng');
      } finally {
        setLoading(false);
      }
    };

    fetchRewardDetail();
  }, [id]);

  const handleBack = () => {
    goBack();
  };

  const handleBrandClick = () => {
    if (reward?.merchant?.id || reward?.merchantId) {
      navigate(`/merchant/${reward.merchant?.id || reward.merchantId}`);
    }
  };

  const handleUseReward = async () => {
    if (!reward || actionLoading) return;

    try {
      setActionLoading(true);
      
      let response;
      
      // Check if this is a flash sale reward
      if (reward.isFlashSale || reward.campaignId) {
        response = await RewardsAPI.purchaseFlashSaleReward(
          reward.id,
          reward.campaignId,
          1
        );
      } else {
        response = await RewardsAPI.purchaseReward(reward.id, 1);
      }

      // Handle the response
      if (response?.data?.voucherId) {
        alert(`Đổi thành công!\nMã voucher: ${response.data.voucherCode || response.data.voucherId}\nĐiểm đã sử dụng: ${response.data.pointsUsed}`);
        
        // Close the dialog
        setShowConfirmDialog(false);
        
        // Optionally navigate to my rewards page
        // navigate('/my-rewards');
      } else {
        alert('Đổi thành công! Vui lòng kiểm tra ví voucher của bạn.');
        setShowConfirmDialog(false);
      }
      
    } catch (error: unknown) {
      console.error('Error purchasing reward:', error);
      
      const errorResponse = error as { status?: number; message?: string };
      
      // Handle specific error cases
      if (errorResponse?.status === 400) {
        alert('Không đủ điểm để đổi phần thưởng này');
      } else if (errorResponse?.status === 409) {
        alert('Phần thưởng đã hết hoặc bạn đã đạt giới hạn đổi');
      } else if (errorResponse?.status === 404) {
        alert('Phần thưởng không tồn tại hoặc đã kết thúc');
      } else {
        alert(`Lỗi: ${errorResponse?.message || 'Không thể đổi phần thưởng'}`);
      }
      
      setShowConfirmDialog(false);
    } finally {
      setActionLoading(false);
    }
  };

  // Convert string array to GalleryImage format
  const getGalleryImages = (images: string[]): GalleryImage[] => {
    return images.map((image, index) => ({
      id: `image-${index}`,
      src: image,
      alt: `${reward?.merchantName || 'Reward'} image ${index + 1}`,
      thumbnail: image, // Use same image for thumbnail
    }));
  };

  // Get fallback logo based on merchant name
  const getFallbackLogo = (merchantName?: string): string => {
    if (merchantName?.toLowerCase().includes('dashi')) {
      return dashiKitchenLogo;
    }
    if (merchantName?.toLowerCase().includes('rosie')) {
      return rosieVintageLogo;
    }
    if (merchantName?.toLowerCase().includes('phúc long')) {
      return dashiKitchenLogo; // Could add specific Phúc Long logo
    }
    return dashiKitchenLogo; // default fallback
  };

  // Format expiry date 
  const formatExpiryDate = (dateString?: string): string => {
    if (!dateString) return 'Không có hạn sử dụng';
    
    try {
      // Handle different date formats from API
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN');
    } catch {
      return dateString;
    }
  };

  // Prepare contact info from API response
  const getContactInfo = (): ContactInfo => {
    if (!reward) return { website: '', phone: '', email: '', facebook: '', zalo: '' };
    
    return {
      website: reward.merchant?.contact?.websiteURL || reward.merchant?.onlineStore?.websiteURL || '',
      phone: reward.merchant?.contact?.phone || '',
      email: reward.merchant?.contact?.email || '',
      facebook: reward.merchant?.contact?.fanpage || '',
      zalo: reward.merchant?.contact?.zaloOA || '',
    };
  };

  // Back icon
  const BackIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M15 18L9 12L15 6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  if (loading) {
    return (
      <PageAny className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Chi tiết ưu đãi"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
        />
        <div className="flex items-center justify-center pt-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F65D79] mx-auto mb-4"></div>
            <p className="text-[#9A9A9A]">Đang tải...</p>
          </div>
        </div>
      </PageAny>
    );
  }

  if (error || !reward) {
    return (
      <PageAny className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Chi tiết ưu đãi"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
        />
        <div className="flex items-center justify-center pt-20">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error || 'Phần thưởng không tìm thấy'}</p>
            <Button onClick={handleBack} variant="outline">
              Quay lại
            </Button>
          </div>
        </div>
      </PageAny>
    );
  }

  return (
    <PageAny className="min-h-screen bg-[#EFF3F6]">
      {/* Navigation Header with green background */}
      <div className="bg-[#0DC98B]">
        <NavigationHeader
          title="Chi tiết ưu đãi"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
          className="text-white"
        />
      </div>

      <div className="max-w-md mx-auto pb-6 relative">

        {/* Reward Detail Header */}
        <div className="flex justify-center">
          <RewardDetailHeader
            title={reward.name}
            brandName={reward.merchant?.name || reward.merchantName || 'Thương hiệu'}
            brandLogo={reward.merchant?.logo || reward.merchantLogo || getFallbackLogo(reward.merchant?.name || reward.merchantName)}
            expiryDate={formatExpiryDate(reward.endTime || reward.validTo)}
            pointsValue={`${reward.issueBrandCurrencyPoint || reward.pointsCost?.toLocaleString() || 0} điểm`}
            bannerBg={reward.image1}
            onBrandClick={handleBrandClick}
          />
        </div>

        {/* How to Use Section */}
        <div className="m-4">
          <SectionHowToUse className="w-full" />
        </div>

        {/* Section Info */}
        <div className="m-4">
          <SectionInfo
            description={reward.termsConditions || reward.description || reward.terms?.join('\n') || ''}
            isHtmlContent={!!((reward.termsConditions || reward.description) && /<[^>]*>/.test(reward.termsConditions || reward.description || ''))}
            contactInfo={getContactInfo()}
            onWebsiteClick={() => {
              const website = getContactInfo().website;
              if (website) {
                const url = website.startsWith('http') ? website : `https://${website}`;
                window.open(url, '_blank');
              }
            }}
            onPhoneClick={() => {
              const phone = getContactInfo().phone;
              if (phone) {
                window.open(`tel:${phone}`, '_self');
              }
            }}
            onEmailClick={() => {
              const email = getContactInfo().email;
              if (email) {
                window.open(`mailto:${email}`, '_self');
              }
            }}
            onEmailCopy={() => {
              const email = getContactInfo().email;
              if (email) {
                navigator.clipboard.writeText(email);
                alert('Email đã được sao chép vào clipboard');
              }
            }}
            onFacebookClick={() => {
              const facebook = getContactInfo().facebook;
              if (facebook) {
                const url = facebook.startsWith('http') ? facebook : `https://${facebook}`;
                window.open(url, '_blank');
              }
            }}
            onZaloClick={() => {
              const zalo = getContactInfo().zalo;
              if (zalo) {
                const url = zalo.startsWith('http') ? zalo : `https://${zalo}`;
                window.open(url, '_blank');
              }
            }}
          />
        </div>

        {/* Image Gallery */}
        {(reward.image1 || reward.imageUrl || reward.thumbnailUrl) && (
          <div className="m-4">
            <ImageGallery
              title="Hình ảnh"
              images={getGalleryImages([
                reward.image1 || reward.imageUrl || reward.thumbnailUrl!
              ].filter(Boolean))}
              className="w-full"
            />
          </div>
        )}

        {/* Store Nearby Section */}
        {reward?.merchant?.offlineStores && Array.isArray(reward.merchant.offlineStores) && reward.merchant.offlineStores.length > 0 && (
          <div className="m-4 mb-24">
            <StoreNearbySection
              stores={reward.merchant.offlineStores.map((store: { 
                id?: string; 
                name?: string; 
                storeName?: string; 
                address?: string; 
                distance?: string; 
                phone?: string; 
                phoneNumber?: string 
              }, index: number) => ({
                id: store.id || `store-${index}`,
                storeName: store.name || store.storeName || '',
                address: store.address || '',
                distance: store.distance || '',
                phoneNumber: store.phone || store.phoneNumber || ''
              }))}
              totalStoresCount={reward.merchant.offlineStores.length}
            onStoreCall={(store) => {
              if (store.phoneNumber) {
                window.open(`tel:${store.phoneNumber}`, '_self');
              }
            }}
            onStoreMap={(store) => {
              // In a real app, this would open maps with the store location
              alert(`Xem ${store.storeName} trên bản đồ`);
            }}
            onShowAllStores={() => {
              // Navigate to stores list page
              alert(`Hiển thị tất cả ${reward?.merchant?.offlineStores?.length || 0} cửa hàng`);
            }}
            className="w-full"
            />
          </div>
        )}
      </div>

      {/* Bottom Purchase Section */}
      <BottomPurchaseSection
        currentPrice={reward.issueBrandCurrencyPoint || reward.issueVUIPoint || reward.pointsCost || 0}
        originalPrice={reward.originalPrice}
        currencyLabel={reward.brandCurrency?.name || "điểm"}
        coinIcon={reward.brandCurrency?.logo || coinIcon}
        buttonText="Đổi ngay"
        loading={actionLoading}
        onPurchaseClick={() => setShowConfirmDialog(true)}
      />
      
      {/* Confirmation Dialog */}
      <Dialog
        variant="reward"
        isOpen={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        title="Đổi lấy ưu đãi này"
        vuiPoints={reward.isVuiPoint ? 950 : undefined} // Replace with actual VUI points from API
        brandCurrencyPoints={reward.isBrandCurrencyPoint ? 1200 : undefined} // Replace with actual brand currency points from API
        brandCurrencyLogo={reward.brandCurrency?.logo}
        brandCurrencyName={reward.brandCurrency?.name}
        // Legacy support - fallback for older usage
        currentPoints={(!reward.isVuiPoint && !reward.isBrandCurrencyPoint) ? 950 : undefined}
        requiredPoints={reward.issueBrandCurrencyPoint || reward.issueVUIPoint || reward.pointsCost || 0}
        coinIcon={reward.brandCurrency?.logo || coinIcon}
        confirmText="Xác nhận"
        confirmLoading={actionLoading}
        onConfirm={handleUseReward}
      />
    </PageAny>
  );
};

export default RewardDetailPage;