import React, { useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { 
  TierProgress, 
  TierNoRank,
  LoadingSpinner,
  NavigationHeader,
  Card
} from '@taptap/shared';
import { useMembershipStore, membershipHelpers } from '@taptap/shared';
import imgVoucher from '../../assets/images/imgVoucher.png';
import imgClock from '../../assets/images/imgClock.png';
import imgBgVoucher from '../../assets/images/imgBgVoucher.png';

const MembershipOverview: React.FC = () => {
  const { merchantCode } = useParams<{ merchantCode: string }>();
  const navigate = useNavigate();
  
  const {
    tierDetail,
    loading,
    error,
    stateTier,
    fetchTierDetail,
    registerTier
  } = useMembershipStore();

  useEffect(() => {
    if (merchantCode) {
      fetchTierDetail(merchantCode);
    }
  }, [merchantCode, fetchTierDetail]);

  const handleRegisterTier = async () => {
    if (merchantCode) {
      const success = await registerTier(merchantCode);
      if (success) {
        // Refresh tier details after successful registration
        await fetchTierDetail(merchantCode);
      }
      // Return the success status for the TierNoRank component to handle
      return success;
    }
    return false;
  };

  const onBenefitsClick = () => {
    navigate(`/membership/${merchantCode}/benefits`);
  };

  const onHistoryClick = () => {
    navigate(`/membership/${merchantCode}/history`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={() => merchantCode && fetchTierDetail(merchantCode)}
            className="px-4 py-2 bg-primary text-white rounded-lg"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  const calculateProgressData = () => {
    if (!tierDetail || !tierDetail.merchantTiers) return null;
    
    const listProgressTier = membershipHelpers.getListProgressTier(
      tierDetail.tierLevel,
      tierDetail.merchantTiers,
      3
    );
    
    const totalEarnNeeded = membershipHelpers.totalEarnCurrency(
      listProgressTier.map(tier => ({
        value: tier.conditions[0]?.value || 0
      })),
      tierDetail.current[0]?.value || 0
    );
    
    return {
      listProgressTier,
      totalEarnNeeded
    };
  };

  const progressData = calculateProgressData();

  return (
    <div className="min-h-screen bg-[#F7CC15]">
      <div className="max-w-[414px] mx-auto min-h-screen">
        <NavigationHeader 
          title="Hạng thành viên"
          showBackButton={true}
          onBackClick={() => navigate(-1)}
        />

        {/* Content */}
        <div className="bg-[#F7CC15]">
          <div className="p-4 space-y-4">
              {/* Avatar/Logo Section */}
              {tierDetail?.merchantLogo && (
                <div className="flex justify-center pt-4">
                  <div className="w-20 h-20 rounded-full bg-white p-2 overflow-hidden">
                    <img 
                      src={tierDetail.merchantLogo} 
                      alt={tierDetail.merchantName}
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>
              )}

              {/* Tier Status Section */}
              <div className="bg-[#FFE082] rounded-xl p-3">
                {stateTier === 'noRank' && (
                  <TierNoRank
                    merchantName={tierDetail?.merchantName}
                    onRegister={handleRegisterTier}
                    loading={loading}
                    backgroundColor={tierDetail?.merchantBackgroundColor}
                  />
                )}

                {(stateTier === 'hasRankTier' || stateTier === 'hasRankAndVoucher' || stateTier === 'maxTier') && tierDetail && (
                  <>
                    {/* Tier Name Badge */}
                    <div className="flex justify-center -mt-6 mb-3">
                      <div className="bg-white px-4 py-1 rounded-full">
                        <span className="font-semibold text-gray-900">
                          {tierDetail.tierName}
                        </span>
                      </div>
                    </div>

                    {/* Progress or Max Level Message */}
                    {stateTier === 'maxTier' ? (
                      <div className="text-center py-2">
                        <p className="font-semibold text-gray-900">
                          Max level rồi, tận hưởng ưu đãi thôi
                        </p>
                      </div>
                    ) : progressData && (
                      <>
                        <div className="text-center mb-4">
                          <p className="text-sm text-gray-700">
                            Tích thêm{' '}
                            <span className="font-bold text-base">
                              {progressData.totalEarnNeeded}
                            </span>{' '}
                            {tierDetail.currencyLogo && (
                              <img 
                                src={tierDetail.currencyLogo} 
                                alt="currency" 
                                className="inline-block w-4 h-4 mx-1"
                              />
                            )}
                            để nâng hạng bạn nhé
                          </p>
                        </div>

                        {/* Progress Bar */}
                        <TierProgress
                          currentValue={tierDetail.current[0]?.value || 0}
                          tiers={progressData.listProgressTier.map(tier => ({
                            value: tier.conditions[0]?.value || 0,
                            tierName: tier.tierName
                          }))}
                          progressColor={tierDetail.tierProgressBarColor}
                          currencyLogo={tierDetail.currencyLogo}
                          currencyName={tierDetail.currencyName}
                          tierName={tierDetail.tierName}
                          totalEarnNeeded={progressData.totalEarnNeeded}
                          merchantName={tierDetail.merchantName}
                          onActionClick={() => navigate(`/merchant/${merchantCode}`)}
                          actionText="Kiếm thêm điểm"
                        />
                      </>
                    )}
                  </>
                )}
              </div>

              {/* Voucher Card Section */}
              {stateTier === 'hasRankAndVoucher' && tierDetail?.voucherCode && (
                <div 
                  className="relative flex bg-white border border-[#cacaca] rounded-[14px] h-[165px] mt-[14px] cursor-pointer transition-opacity duration-[250ms] hover:opacity-90"
                  onClick={() => navigate(`/voucher/${tierDetail.voucherCodeId}`)}
                >
                  {/* Left Content */}
                  <div className="flex-[0.65] flex flex-col">
                    <div className="flex items-center pt-[14px] pb-[5px] pl-[18px]">
                      <div className="text-[10px] font-bold text-[#9a9a9a] uppercase tracking-[0.7px] leading-[18px] truncate" style={{ fontFamily: 'Archia' }}>
                        {tierDetail.merchantName || 'TAPTAP demo'}
                      </div>
                    </div>
                    <div className="flex-1 flex items-center justify-between pl-[18px]">
                      <div className="text-[17px] font-semibold text-[#1A1818] leading-[25px] line-clamp-2" style={{ fontFamily: 'Archia', display: '-webkit-box', WebkitBoxOrient: 'vertical', WebkitLineClamp: 2 }}>
                        Ưu đãi hạng thành viên {tierDetail.tierName}
                      </div>
                    </div>
                  </div>
                  
                  {/* Right Image */}
                  <div className="flex-[0.45] relative overflow-hidden">
                    <div 
                      className="absolute inset-0 bg-contain bg-center bg-no-repeat"
                      style={{
                        backgroundImage: `url(${imgBgVoucher})`
                      }}
                    />
                    <img 
                      src={imgBgVoucher} 
                      alt=""
                      className="absolute inset-0 w-full h-full opacity-0"
                      draggable={false}
                    />
                  </div>
                </div>
              )}

              {/* Merchant Info Section */}
              {tierDetail?.merchantDescription && (
                <div className="bg-white rounded-xl p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Về {tierDetail.merchantName}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {tierDetail.merchantDescription}
                  </p>
                </div>
              )}

              {/* Action Cards */}
              <div className="space-y-3">
                {/* Benefits Card */}
                <Card 
                  className="p-4 cursor-pointer hover:shadow-md transition-shadow"
                  onClick={onBenefitsClick}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-lg flex items-center justify-center overflow-hidden">
                        <img 
                          src={imgVoucher} 
                          alt="Benefits" 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <h3 className="text-[14px] font-semibold text-[#1A1818]">
                          Quyền lợi hạng thành viên
                        </h3>
                        <p className="text-[14px] text-[#5A5A5A]">
                          Khám phá ưu đãi của mỗi hạng
                        </p>
                      </div>
                    </div>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path 
                        d="M9.5 5L16 12L9.5 19" 
                        stroke="#1A1818" 
                        strokeWidth="1.5" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </Card>

                {/* History Card */}
                <Card 
                  className="p-4 cursor-pointer hover:shadow-md transition-shadow"
                  onClick={onHistoryClick}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-lg flex items-center justify-center overflow-hidden">
                        <img 
                          src={imgClock} 
                          alt="History" 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <h3 className="text-[14px] font-semibold text-[#1A1818]">
                          Lịch sử tích - đổi
                        </h3>
                        <p className="text-[14px] text-[#5A5A5A]">
                          Lịch sử tích - đổi <span className="font-semibold text-[#F87A91]">{tierDetail?.currencyName || 'điểm'}</span> của bạn
                        </p>
                      </div>
                    </div>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path 
                        d="M9.5 5L16 12L9.5 19" 
                        stroke="#1A1818" 
                        strokeWidth="1.5" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </Card>
              </div>
            </div>
        </div>
      </div>
    </div>
  );
};

export default MembershipOverview;