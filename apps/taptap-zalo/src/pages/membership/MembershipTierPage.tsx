import React from 'react';
import { useNavigate } from 'react-router-dom';
import { MembershipTierPage as SharedMembershipTierPage } from '../MembershipTier/MembershipTierPage';

const MembershipTierPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBenefitsClick = () => {
    console.log('Navigate to membership benefits');
    // TODO: Navigate to membership benefits page
  };

  const handleHistoryClick = () => {
    console.log('Navigate to points history');
    // TODO: Navigate to points history page
  };

  return (
    <SharedMembershipTierPage
      currentTier="GOLD"
      currentPoints={720}
      pointsToNextTier={80}
      expiringPoints={80}
      expiryDate="30/06/2025"
      avatarSrc="/src/assets/images/gold-tier-avatar.png"
      onBenefitsClick={handleBenefitsClick}
      onHistoryClick={handleHistoryClick}
    />
  );
};

export default MembershipTierPage;