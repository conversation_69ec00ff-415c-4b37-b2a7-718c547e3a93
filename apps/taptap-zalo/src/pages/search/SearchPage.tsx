import React, { useState, useEffect, useRef } from 'react';
import { Page } from "zmp-ui";
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import { SearchKeywordChip, NavigationHeader, SearchBar, SearchAPI, RewardListCard, MerchantItem, merchantAPI } from '@taptap/shared';
import type { Merchant, MerchantItemData } from '@taptap/shared';
import backIcon from '../../../../../shared/assets/icons/icon-24px-outline-back.svg';

const PageAny = Page as any;

const SearchPage: React.FC = () => {
  const { navigate, goBack } = useZaloNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<{
    rewards: any[];
    merchants: any[];
  }>({ rewards: [], merchants: [] });
  const [showSearchResults, setShowSearchResults] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // API data states
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [popularKeywords, setPopularKeywords] = useState<string[]>([]);
  const [topKeywords, setTopKeywords] = useState<string[]>([]);
  const [topRewards, setTopRewards] = useState<any[]>([]);
  const [topMerchants, setTopMerchants] = useState<Merchant[]>([]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Fetch search data on component mount
  useEffect(() => {
    const fetchSearchData = async () => {
      setLoading(true);
      try {
        // Fetch all search data in parallel
        const [historyData, rewardsData, merchantsData] = await Promise.all([
          SearchAPI.getSearchHistory(),
          SearchAPI.getTopRewards(8),
          SearchAPI.getTopMerchants()
        ]);

        // Set search history data
        setRecentSearches(historyData.data.history || []);
        setPopularKeywords(historyData.data.popular || []);
        setTopKeywords(historyData.data.topKeyword || []);

        // Set rewards and merchants data
        setTopRewards(rewardsData.data || []);
        setTopMerchants(merchantsData.data || []);
      } catch (error) {
        console.error('Failed to fetch search data:', error);
        // Fallback to empty arrays on error
        setRecentSearches([]);
        setPopularKeywords([]);
        setTopKeywords([]);
        setTopRewards([]);
        setTopMerchants([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSearchData();
  }, []);

  const handleBack = () => {
    goBack();
  };

  const handleSearchChange = async (value: string) => {
    setSearchQuery(value);
    
    if (value.trim().length > 0) {
      // Combine all available keywords for suggestions
      const allKeywords = [...recentSearches, ...popularKeywords, ...topKeywords];
      
      // Filter suggestions based on input
      const filtered = allKeywords.filter(suggestion =>
        suggestion.toLowerCase().includes(value.toLowerCase())
      );
      
      // Remove duplicates and limit to 6 suggestions
      const uniqueFiltered = Array.from(new Set(filtered)).slice(0, 6);
      setSuggestions(uniqueFiltered);
      setShowSuggestions(true);
      
      // Clear previous timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      
      // Set debounce timeout for API calls (300ms delay)
      searchTimeoutRef.current = setTimeout(async () => {
        setSearchLoading(true);
        setShowSearchResults(true);
        try {
          const [rewardsResponse, merchantsResponse] = await Promise.all([
            SearchAPI.searchRewards(value),
            SearchAPI.searchMerchants(value)
          ]);
          
          setSearchResults({
            rewards: Array.isArray(rewardsResponse.data) ? rewardsResponse.data : [],
            merchants: Array.isArray(merchantsResponse.data) ? merchantsResponse.data : []
          });
        } catch (error) {
          console.error('Search error:', error);
          setSearchResults({ rewards: [], merchants: [] });
        } finally {
          setSearchLoading(false);
        }
      }, 300);
    } else {
      // Clear timeout if input is empty
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      setSuggestions([]);
      setShowSuggestions(false);
      setShowSearchResults(false);
      setSearchResults({ rewards: [], merchants: [] });
    }
  };

  const handleSearch = (query: string) => {
    if (!query.trim()) return;

    // Add to recent searches
    const updatedRecent = [query, ...recentSearches.filter(item => item !== query)].slice(0, 8);
    setRecentSearches(updatedRecent);

    // Navigate to search results
    navigate(`/search/results?q=${encodeURIComponent(query)}`);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch(searchQuery);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion);
    setShowSuggestions(false);
    handleSearch(suggestion);
  };

  const handleKeywordClick = (keyword: string) => {
    setSearchQuery(keyword);
    handleSearch(keyword);
  };

  const handleRecentSearchClick = (query: string) => {
    setSearchQuery(query);
    handleSearch(query);
  };

  const handleRewardClick = (reward: any) => {
    // Navigate to reward detail page
    navigate(`/rewards/${reward.id}`);
  };

  const handleMerchantClick = (merchant: MerchantItemData) => {
    // Navigate to merchant detail page
    navigate(`/merchant/${merchant.id}`);
  };

  const handleMerchantFavoriteToggle = async (merchant: MerchantItemData) => {
    try {
      if (!merchant.code) {
        console.warn('Merchant code is missing, cannot toggle favorite');
        return;
      }
      
      const action = merchant.isFavorite ? 'ADD' : 'REMOVE';
      const response = await merchantAPI.toggleFavorite([merchant.code], action);
      
      if (response.status.success) {
        // Update the merchant in search results
        setSearchResults(prev => ({
          ...prev,
          merchants: prev.merchants.map(m => 
            m.id === merchant.id ? { ...m, isFavorite: merchant.isFavorite } : m
          )
        }));
        
        // Update top merchants if applicable
        setTopMerchants(prev => 
          prev.map(m => 
            m.id === merchant.id ? { ...m, isFavorite: merchant.isFavorite } : m
          )
        );
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  return (
    <PageAny className="min-h-screen">
      {/* Navigation Header */}
      <NavigationHeader
        title="Tìm kiếm"
        leftIcon={<img src={backIcon} alt="Back" className="w-6 h-6" />}
        onLeftClick={handleBack}
        variant="ZaloMiniApp"
        className="bg-white"
      />

      {/* Search Bar */}
      <div className="bg-white px-4 py-2">
        <form onSubmit={handleSubmit}>
          <SearchBar
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder="Tìm kiếm..."
            showSearchIcon={true}
            showClearButton={true}
            autoFocus={true}
            onClear={() => {
              setSearchQuery('');
              setSuggestions([]);
              setShowSuggestions(false);
            }}
            onSearch={handleSearch}
          />
        </form>
      </div>

      {/* Main Content */}
      <div className="">
        {/* Search Results when typing */}
        {showSearchResults && searchQuery.trim().length > 0 && (
          <div className="bg-white">
            {searchLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F65D79]"></div>
              </div>
            ) : (
              <>
                {/* Search Results - Rewards */}
                {searchResults.rewards.length > 0 && (
                  <div className="mb-4">
                    <div className="px-4 pt-4 pb-3">
                      <h2 className="text-[16px] font-[600] text-[#1A1818] leading-[22px]">Ưu đãi ({searchResults.rewards.length})</h2>
                    </div>
                    <div className="px-4 pb-4">
                      <div className="space-y-3">
                        {searchResults.rewards.map((reward: any) => (
                          <RewardListCard
                            key={reward.id}
                            id={reward.id}
                            brand={reward.merchant?.name || reward.brandName || ''}
                            title={reward.name}
                            points={reward.issueVUIPoint || reward.issueBrandCurrencyPoint || 0}
                            logoSrc={reward.merchant?.logo || ''}
                            imageSrc={reward.image1 || ''}
                            available={true}
                            onClick={() => navigate(`/rewards/${reward.id}`)}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Search Results - Merchants */}
                {searchResults.merchants.length > 0 && (
                  <div className="mb-4">
                    <div className="px-4 pt-4 pb-3">
                      <h2 className="text-[16px] font-[600] text-[#1A1818] leading-[22px]">Thương hiệu ({searchResults.merchants.length})</h2>
                    </div>
                    <div className="px-4 pb-4">
                      <div className="grid grid-cols-2 gap-3">
                        {searchResults.merchants.slice(0, 6).map((merchant) => (
                          <MerchantItem
                            key={merchant.id}
                            merchant={{
                              id: merchant.id,
                              name: merchant.name,
                              logo: merchant.logo || '',
                              bannerImage: merchant.banner || '',
                              offersCount: merchant.numberOfReward || 0,
                              isFavorite: merchant.isFavorite
                            }}
                            onClick={() => navigate(`/merchant/${merchant.id}`)}
                            onFavoriteToggle={handleMerchantFavoriteToggle}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* No Results */}
                {searchResults.rewards.length === 0 && searchResults.merchants.length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-[14px] text-[#9A9A9A]">Không tìm thấy kết quả phù hợp</p>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* Search Suggestions */}
        {!showSearchResults && showSuggestions && suggestions.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm bg-[#FFF]">
            <div className="py-2">
              {suggestions.map((suggestion, index) => (
                <SearchKeywordChip
                  key={index}
                  keyword={suggestion}
                  type="suggestion"
                  onClick={handleSuggestionClick}
                />
              ))}
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F65D79]"></div>
          </div>
        )}

        {/* Top Keywords */}
        {!loading && !showSuggestions && !showSearchResults && topKeywords.length > 0 && (
          <div className='px-4 py-4 space-y-6 bg-[#FFF] mb-4'>
            <h2 className="text-[16px] font-[600] text-[#1A1818] mb-3 leading-[22px]">Từ khóa hàng đầu</h2>
            <div className="flex flex-wrap gap-2">
              {topKeywords.map((keyword, index) => (
                <SearchKeywordChip
                  key={index}
                  keyword={keyword}
                  type="hot"
                  onClick={handleKeywordClick}
                />
              ))}
            </div>
          </div>
        )}

        {/* Recent Searches */}
        {!loading && !showSuggestions && !showSearchResults && recentSearches.length > 0 && (
          <div className='px-4 py-4 space-y-6 bg-[#FFF] mb-4'>
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-[16px] font-[600] text-[#1A1818] leading-[22px]">Tìm kiếm gần đây</h2>
            </div>
            <div className="flex flex-wrap gap-2">
              {recentSearches.map((search, index) => (
                <SearchKeywordChip
                  key={index}
                  keyword={search}
                  type="recent"
                  onClick={handleRecentSearchClick}
                />
              ))}
            </div>
          </div>
        )}

        {/* Popular Keywords */}
        {!loading && !showSuggestions && !showSearchResults && popularKeywords.length > 0 && (
          <div className='px-4 py-4 space-y-6 bg-[#FFF] mb-4'>
            <h2 className="text-[16px] font-[600] text-[#1A1818] mb-3 leading-[22px]">Từ khóa nổi bật</h2>
            <div className="flex flex-wrap gap-2">
              {popularKeywords.map((keyword, index) => (
                <SearchKeywordChip
                  key={index}
                  keyword={keyword}
                  type="hot"
                  onClick={handleKeywordClick}
                />
              ))}
            </div>
          </div>
        )}

        {/* Top Rewards */}
        {!loading && !showSuggestions && !showSearchResults && topRewards.length > 0 && (
          <div className='bg-[#FFF] mb-4'>
            <div className="px-4 pt-4 pb-3">
              <h2 className="text-[16px] font-[600] text-[#1A1818] leading-[22px]">Ưu đãi</h2>
            </div>
            <div className="px-4">
              <div className="space-y-3">
                {topRewards.map((reward) => (
                  <RewardListCard
                    key={reward.id}
                    id={reward.id}
                    brand={reward.merchant?.name || reward.brandName || ''}
                    title={reward.name}
                    points={reward.issueVUIPoint || reward.issueBrandCurrencyPoint || 320}
                    logoSrc={reward.merchant?.logo || ''}
                    imageSrc={reward.image1 || ''}
                    available={true}
                    onClick={() => handleRewardClick(reward)}
                  />
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Top Merchants */}
        {!loading && !showSuggestions && !showSearchResults && topMerchants.length > 0 && (
          <div className='bg-[#FFF] mb-4'>
            <div className="px-4 pt-4 pb-3">
              <h2 className="text-[16px] font-[600] text-[#1A1818] leading-[22px]">Thương hiệu</h2>
            </div>
            <div className="px-4 pb-4">
              <div className="grid grid-cols-2 gap-3">
                {topMerchants.slice(0, 6).map((merchant) => (
                  <MerchantItem
                    key={merchant.id}
                    merchant={{
                      id: merchant.id,
                      name: merchant.name,
                      logo: merchant.logo || '',
                      bannerImage: merchant.banner || '',
                      offersCount: merchant.numberOfReward || 0,
                      isFavorite: merchant.isFavorite
                    }}
                    onClick={handleMerchantClick}
                    onFavoriteToggle={handleMerchantFavoriteToggle}
                  />
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && !showSuggestions && !showSearchResults && topKeywords.length === 0 && recentSearches.length === 0 && popularKeywords.length === 0 && topRewards.length === 0 && topMerchants.length === 0 && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h3 className="text-[16px] font-[600] text-[#1A1818] mb-2 leading-[22px]">
              Tìm kiếm những gì bạn cần
            </h3>
            <p className="text-[12px] text-[#9A9A9A] leading-[18px]">
              Khám phá hàng ngàn thương hiệu và phần thưởng hấp dẫn
            </p>
          </div>
        )}
      </div>
    </PageAny>
  );
};

export default SearchPage;