import React, { useState, useEffect } from 'react';
import { Page } from "zmp-ui";
import { useSearchParams } from 'react-router-dom';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';
import {
  SearchResultsLayout,
  NavigationHeader,
  SearchResultsCounter,
  TabNavigation,
  RewardListCard,
  MerchantResultsSection,
  Button,
  SearchAPI,
  merchantAPI,
  resizeImage,
} from '@taptap/shared';
import type { RewardResult, MerchantResult, TabItem } from '@taptap/shared';
import backIcon from '../../../../../shared/assets/icons/icon-24px-outline-back.svg';

const PageAny = Page as any;

interface DealResult {
  id: string;
  title: string;
  brand: string;
  description: string;
  image: string;
  discount: string;
  originalPrice: number;
  discountPrice: number;
  expiryDate: string;
}

const SearchResultsPage: React.FC = () => {
  const { navigate, goBack } = useZaloNavigation();
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q') || '';
  
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [sortBy, setSortBy] = useState('relevance');
  const [rewards, setRewards] = useState<RewardResult[]>([]);
  const [merchants, setMerchants] = useState<MerchantResult[]>([]);
  const [deals, setDeals] = useState<DealResult[]>([]);

  // Search result counts
  const rewardCount = rewards.length;
  const merchantCount = merchants.length;
  const dealCount = deals.length;
  const totalCount = rewardCount + merchantCount + dealCount;

  // Tab navigation items
  const tabs: TabItem[] = [
    { id: 'all', label: 'Tất cả', count: totalCount },
    { id: 'rewards', label: 'Phần thưởng', count: rewardCount },
    { id: 'merchants', label: 'Thương hiệu', count: merchantCount },
    { id: 'deals', label: 'Ưu đãi', count: dealCount },
  ];

  useEffect(() => {
    const fetchSearchResults = async () => {
      setLoading(true);
      try {
        // Fetch search results from API
        const [rewardsResponse, merchantsResponse] = await Promise.all([
          SearchAPI.searchRewards(query),
          SearchAPI.searchMerchants(query)
        ]);

        // Process reward results
        const rewardResults: RewardResult[] = (rewardsResponse.data || []).map((reward: any) => ({
          id: reward.id,
          title: reward.name,
          brand: reward.merchant?.name || reward.brandName || '',
          points: reward.issueVUIPoint || reward.issueBrandCurrencyPoint || 0,
          image: reward.image1 || '',
          brandLogo: reward.merchant?.logo || '',
          expiryDate: reward.endDate || '',
          category: reward.category || 'Khác',
        }));

        // Process merchant results
        const merchantResults: MerchantResult[] = (merchantsResponse.data || []).map((merchant: any) => ({
          id: merchant.id,
          name: merchant.name,
          logo: merchant.logo || '',
          banner: merchant.banner || '',
          offersCount: merchant.numberOfReward || 0,
          category: merchant.category || 'Khác',
          location: merchant.address || '',
          rating: merchant.rating || 0,
          isFavorite: merchant.isFavorite || false,
        }));

        setRewards(rewardResults);
        setMerchants(merchantResults);
        setDeals([]); // No specific deals API yet
      } catch (error) {
        console.error('Failed to fetch search results:', error);
        setRewards([]);
        setMerchants([]);
        setDeals([]);
      } finally {
        setLoading(false);
      }
    };

    if (query) {
      fetchSearchResults();
    } else {
      setLoading(false);
    }
  }, [query]);

  const handleRewardClick = (reward: RewardResult) => {
    navigate(`/rewards/${reward.id}`);
  };

  const handleMerchantClick = (merchant: MerchantResult) => {
    navigate(`/merchant/${merchant.id}`);
  };

  const handleDealClick = (deal: DealResult) => {
    // Navigate to deal detail page
    navigate(`/deal/${deal.id}`);
  };

  const handleSortChange = (newSortBy: string) => {
    setSortBy(newSortBy);
    // Implement sorting logic based on sortBy value
  };

  const handleBack = () => {
    goBack();
  };

  const handleToggleFavorite = async (merchantId: string) => {
    try {
      const merchant = merchants.find(m => m.id === merchantId);
      if (!merchant) return;

      const action = merchant.isFavorite ? 'REMOVE' : 'ADD';
      const response = await merchantAPI.toggleFavorite([merchantId], action);
      
      if (response.status.success) {
        setMerchants(prev => 
          prev.map(m => 
            m.id === merchantId 
              ? { ...m, isFavorite: !m.isFavorite }
              : m
          )
        );
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  // Filter results based on active tab
  const getFilteredContent = () => {
    switch (activeTab) {
      case 'rewards':
        return (
          <div className="p-4">
            {rewards.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Không có phần thưởng nào được tìm thấy</p>
              </div>
            ) : (
              <div className="space-y-3">
                {rewards.map(reward => (
                  <RewardListCard
                    key={reward.id}
                    id={reward.id}
                    brand={reward.brand || ''}
                    title={reward.title}
                    points={0}
                    logoSrc="/shared/assets/images/placeholder-logo.png"
                    imageSrc={reward.imageUrl || '/shared/assets/images/placeholder-reward.png'}
                    discountPercent={0}
                    available={true}
                    expiry={reward.expiryDate || false}
                    onClick={() => handleRewardClick(reward)}
                  />
                ))}
              </div>
            )}
          </div>
        );
      case 'merchants':
        return (
          <MerchantResultsSection
            merchants={merchants}
            onMerchantClick={handleMerchantClick}
            onToggleFavorite={handleToggleFavorite}
            sortBy={sortBy}
            onSortChange={handleSortChange}
          />
        );
      case 'deals':
        return (
          <div className="p-4">
            {deals.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Không có ưu đãi nào được tìm thấy</p>
              </div>
            ) : (
              <div className="space-y-4">
                {deals.map(deal => (
                  <div
                    key={deal.id}
                    className="bg-white rounded-lg shadow-sm p-4 cursor-pointer"
                    onClick={() => handleDealClick(deal)}
                  >
                    <div className="flex gap-3">
                      <img
                        src={resizeImage(deal.image, { width: 160, height: 160, quality: 85, fit: 'cover' })}
                        alt={deal.title}
                        className="w-20 h-20 rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <h3 className="font-semibold text-sm">{deal.title}</h3>
                        <p className="text-xs text-gray-500 mt-1">{deal.brand}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-red-500 font-bold">{deal.discount}</span>
                          <span className="text-xs text-gray-400">Hết hạn {deal.expiryDate}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      case 'all':
      default:
        return (
          <div className="pb-20">
            {rewards.length > 0 && (
              <div className="mb-6">
                <div className="px-4 py-3 bg-gray-50">
                  <h3 className="font-semibold text-sm">Phần thưởng ({rewards.length})</h3>
                </div>
                <div className="px-4 space-y-3">
                  {rewards.slice(0, 3).map(reward => (
                    <RewardListCard
                      key={reward.id}
                      id={reward.id}
                      brand={reward.brand || ''}
                      title={reward.title}
                      points={0}
                      logoSrc="/shared/assets/images/placeholder-logo.png"
                      imageSrc={reward.imageUrl || '/shared/assets/images/placeholder-reward.png'}
                      discountPercent={0}
                      available={true}
                      expiry={reward.expiryDate || false}
                      onClick={() => handleRewardClick(reward)}
                    />
                  ))}
                </div>
                {rewards.length > 3 && (
                  <div className="px-4 mt-2">
                    <Button
                      label={`Xem tất cả ${rewards.length} phần thưởng`}
                      variant="outline"
                      size="sm"
                      fullWidth
                      onClick={() => setActiveTab('rewards')}
                    />
                  </div>
                )}
              </div>
            )}

            {merchants.length > 0 && (
              <div className="mb-6">
                <div className="px-4 py-3 bg-gray-50">
                  <h3 className="font-semibold text-sm">Thương hiệu ({merchants.length})</h3>
                </div>
                <MerchantResultsSection
                  merchants={merchants.slice(0, 3)}
                  onMerchantClick={handleMerchantClick}
                  onToggleFavorite={handleToggleFavorite}
                  sortBy={sortBy}
                  onSortChange={handleSortChange}
                />
                {merchants.length > 3 && (
                  <div className="px-4 mt-2">
                    <Button
                      label={`Xem tất cả ${merchants.length} thương hiệu`}
                      variant="outline"
                      size="sm"
                      fullWidth
                      onClick={() => setActiveTab('merchants')}
                    />
                  </div>
                )}
              </div>
            )}

            {deals.length > 0 && (
              <div className="mb-6">
                <div className="px-4 py-3 bg-gray-50">
                  <h3 className="font-semibold text-sm">Ưu đãi ({deals.length})</h3>
                </div>
                <div className="px-4 space-y-3">
                  {deals.slice(0, 3).map(deal => (
                    <div
                      key={deal.id}
                      className="bg-white rounded-lg shadow-sm p-4 cursor-pointer"
                      onClick={() => handleDealClick(deal)}
                    >
                      <div className="flex gap-3">
                        <img
                          src={deal.image}
                          alt={deal.title}
                          className="w-20 h-20 rounded-lg object-cover"
                        />
                        <div className="flex-1">
                          <h3 className="font-semibold text-sm">{deal.title}</h3>
                          <p className="text-xs text-gray-500 mt-1">{deal.brand}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <span className="text-red-500 font-bold">{deal.discount}</span>
                            <span className="text-xs text-gray-400">Hết hạn {deal.expiryDate}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {deals.length > 3 && (
                  <div className="px-4 mt-2">
                    <Button
                      label={`Xem tất cả ${deals.length} ưu đãi`}
                      variant="outline"
                      size="sm"
                      fullWidth
                      onClick={() => setActiveTab('deals')}
                    />
                  </div>
                )}
              </div>
            )}

            {totalCount === 0 && !loading && (
              <div className="text-center py-12">
                <p className="text-gray-500">Không tìm thấy kết quả nào cho "{query}"</p>
              </div>
            )}
          </div>
        );
    }
  };

  return (
    <PageAny className="min-h-screen bg-gray-50">
      <NavigationHeader
        title="Kết quả tìm kiếm"
        leftIcon={<img src={backIcon} alt="Back" className="w-6 h-6" />}
        onLeftClick={handleBack}
        variant="ZaloMiniApp"
        className="bg-white"
      />

      {loading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F65D79]"></div>
        </div>
      ) : (
        <>
          {/* Search Query and Results Count */}
          <SearchResultsCounter
            query={query}
            totalCount={totalCount}
          />

          {/* Tab Navigation */}
          <TabNavigation
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Results Content */}
          <SearchResultsLayout>
            {getFilteredContent()}
          </SearchResultsLayout>
        </>
      )}
    </PageAny>
  );
};

export default SearchResultsPage;