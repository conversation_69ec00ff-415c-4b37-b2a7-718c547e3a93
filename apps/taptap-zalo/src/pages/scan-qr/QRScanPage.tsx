import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import QrScanner from 'qr-scanner';
import { ChevronLeftIcon, InfoIcon, StoreIcon, FlashIcon, CameraIcon } from './icons';
import './scan-qr.css';

interface QRScanPageProps {
  hideFooterOverlay?: boolean;
}

const QRScanPage: React.FC<QRScanPageProps> = ({ hideFooterOverlay = false }) => {
  const navigate = useNavigate();
  const videoRef = useRef<HTMLVideoElement>(null);
  const scannerRef = useRef<QrScanner | null>(null);
  const [hasPermission, setHasPermission] = useState<'initial' | boolean>('initial');
  const [isScanning, setIsScanning] = useState(false);
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [merchantCount] = useState(12); // Mock data - replace with actual store data

  useEffect(() => {
    const initCamera = async () => {
      await checkCameraPermission();
    };
    initCamera();
    return () => {
      stopScanner();
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const checkCameraPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      stream.getTracks().forEach(track => track.stop());
      setHasPermission(true);
      initializeScanner();
    } catch (error) {
      console.error('Camera permission denied:', error);
      setHasPermission(false);
    }
  };

  const initializeScanner = async () => {
    if (!videoRef.current) return;

    try {
      const scanner = new QrScanner(
        videoRef.current,
        (result) => handleQRData(result.data),
        {
          preferredCamera: 'environment',
          highlightScanRegion: true,
          highlightCodeOutline: true,
          maxScansPerSecond: 5,
        }
      );
      
      scannerRef.current = scanner;
      await scanner.start();
      setIsScanning(true);
    } catch (error) {
      console.error('Failed to start scanner:', error);
      setHasPermission(false);
    }
  };

  const stopScanner = () => {
    if (scannerRef.current) {
      scannerRef.current.stop();
      scannerRef.current.destroy();
      scannerRef.current = null;
    }
    setIsScanning(false);
  };

  const handleQRData = useCallback((qrString: string) => {
    if (!qrString) return;
    
    // Stop scanning temporarily to prevent multiple reads
    stopScanner();
    
    // Parse QR data
    try {
      const url = new URL(qrString);
      const type = url.searchParams.get('t');
      
      switch (type) {
        case '3': // E-commerce/QSR QR
          handleEcommerceQR(qrString);
          break;
        case '4': // Scan-and-earn gift cards
          handleScanAndEarnQR(qrString);
          break;
        case '5': // Dynamic QR codes
          handleDynamicQR(qrString);
          break;
        case '6': // Offline wheel of fortune
          handleWheelOfFortuneQR(qrString);
          break;
        default:
          showErrorModal('QR code không hợp lệ');
          setTimeout(() => initializeScanner(), 2000);
      }
    } catch (error) {
      console.error('Invalid QR code:', error);
      showErrorModal('QR code không hợp lệ');
      setTimeout(() => initializeScanner(), 2000);
    }
  }, []);

  const handleEcommerceQR = (qrString: string) => {
    // Navigate to ecommerce flow
    navigate('/ecommerce/qr', { state: { qrData: qrString } });
  };

  const handleScanAndEarnQR = (qrString: string) => {
    // Navigate to scan and earn flow
    navigate('/earn/scan-result', { state: { qrData: qrString } });
  };

  const handleDynamicQR = (qrString: string) => {
    try {
      const url = new URL(qrString);
      const qrSessionId = url.searchParams.get('cc');
      
      if (!qrSessionId || qrSessionId === '') {
        showErrorModal('QR code không hợp lệ');
        setTimeout(() => initializeScanner(), 2000);
        return;
      }

      // Show loading state
      console.log('Processing Dynamic QR with sessionId:', qrSessionId);
      
      // Call Dynamic QR API
      processDynamicQR(qrSessionId);
      
    } catch (error) {
      console.error('Error processing Dynamic QR:', error);
      showErrorModal('QR code không hợp lệ');
      setTimeout(() => initializeScanner(), 2000);
    }
  };

  const processDynamicQR = async (qrSessionId: string) => {
    try {
      // This would normally call your API service
      console.log('Calling Dynamic QR API with sessionId:', qrSessionId);
      
      // Mock API response handling - replace with actual API call
      const mockResponse = {
        status: {
          success: true,
          code: 200,
          message: 'Success'
        },
        data: {
          // QR processing result
        }
      };

      if (!mockResponse.status.success) {
        handleDynamicQRError(mockResponse.status.code, mockResponse.status.message);
        return;
      }

      // Success case - navigate to result page or show success modal
      showSuccessModal('QR code đã được xử lý thành công!');
      setTimeout(() => {
        navigate('/'); // Navigate to home or appropriate page
      }, 2000);

    } catch (error) {
      console.error('Dynamic QR API error:', error);
      showErrorModal('Có lỗi xảy ra khi xử lý QR code. Vui lòng thử lại.');
      setTimeout(() => initializeScanner(), 2000);
    }
  };

  const handleDynamicQRError = (errorCode: number, message: string) => {
    let errorTitle = 'Có lỗi xảy ra';
    let errorContent = message || 'Vui lòng thử lại.';

    switch (errorCode) {
      case 2130101: // duplicated
        errorTitle = 'QR đã được sử dụng';
        errorContent = 'QR code này đã được quét trước đó.';
        break;
      case 2130102: // expired
        errorTitle = 'QR hết hạn';
        errorContent = 'QR code này đã hết hạn sử dụng.';
        break;
      case 2130103: // gift error
        errorTitle = 'Lỗi phần thưởng';
        errorContent = 'Có lỗi xảy ra với phần thưởng. Vui lòng liên hệ hỗ trợ.';
        break;
      case 2130104: // wrong code
        errorTitle = 'Mã không đúng';
        errorContent = 'QR code không chính xác.';
        break;
      case 2130105: // day limit
        errorTitle = 'Đã đạt giới hạn ngày';
        errorContent = 'Bạn đã đạt giới hạn quét QR trong ngày.';
        break;
      case 2130106: // week limit
        errorTitle = 'Đã đạt giới hạn tuần';
        errorContent = 'Bạn đã đạt giới hạn quét QR trong tuần.';
        break;
      case 2130107: // month limit
        errorTitle = 'Đã đạt giới hạn tháng';
        errorContent = 'Bạn đã đạt giới hạn quét QR trong tháng.';
        break;
      default:
        errorTitle = 'Có lỗi xảy ra';
        errorContent = 'Vui lòng thử lại sau.';
        break;
    }

    // Show error modal with specific message
    showErrorModal(`${errorTitle}: ${errorContent}`);
    setTimeout(() => initializeScanner(), 3000);
  };

  const handleWheelOfFortuneQR = (qrString: string) => {
    // Navigate to wheel of fortune
    navigate('/games/wheel-of-fortune', { state: { qrData: qrString } });
  };

  const showErrorModal = (message: string) => {
    // Show error modal - integrate with your modal system
    alert(message);
  };

  const showSuccessModal = (message: string) => {
    // Show success modal - integrate with your modal system
    alert(message);
  };

  const goToMerchant = () => {
    navigate('/merchants/qr-scan');
  };

  const goToInstruction = () => {
    navigate('/scan-qr/instructions');
  };

  const onPressBack = () => {
    navigate(-1);
  };

  const toggleFlash = async () => {
    if (scannerRef.current) {
      try {
        await scannerRef.current.toggleFlash();
        setFlashEnabled(!flashEnabled);
      } catch (error) {
        console.error('Flash not supported:', error);
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black">
      {/* Header Overlay */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-transparent">
        <div className="flex items-center justify-between p-4 pt-safe">
          <button
            onClick={onPressBack}
            className="text-white p-2"
            aria-label="Back"
          >
            <ChevronLeftIcon />
          </button>
          <h1 className="text-white text-lg font-semibold">Quét mã QR</h1>
          <div className="w-10" /> {/* Spacer for centering */}
        </div>
      </div>

      {/* Camera View */}
      {hasPermission === true && (
        <>
          <video
            ref={videoRef}
            className="absolute inset-0 w-full h-full object-cover"
            playsInline
            muted
          />
          
          {/* Scan Region Overlay */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative">
              {/* Scan Frame */}
              <div className="w-[200px] h-[200px] relative">
                {/* Corner borders */}
                <div className="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-primary rounded-tl-lg" />
                <div className="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-primary rounded-tr-lg" />
                <div className="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-primary rounded-bl-lg" />
                <div className="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-primary rounded-br-lg" />
                
                {/* Scanning animation line */}
                {isScanning && (
                  <div className="absolute inset-x-2 top-2 h-0.5 bg-primary animate-scan" />
                )}
              </div>
              
              {/* Dark overlay around scan region */}
              <div className="fixed inset-0 -z-10">
                <div className="absolute inset-0 bg-black/60" />
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[200px] h-[200px] bg-transparent" />
              </div>
            </div>
          </div>
        </>
      )}

      {/* Permission Denied View */}
      {hasPermission === false && (
        <div className="absolute inset-0 flex items-center justify-center px-8">
          <div className="text-center">
            <div className="mb-4">
              <div className="w-20 h-20 mx-auto bg-white/10 rounded-full flex items-center justify-center">
                <CameraIcon />
              </div>
            </div>
            <h2 className="text-white text-xl font-semibold mb-2">
              Cần quyền truy cập Camera
            </h2>
            <p className="text-white/70 mb-6">
              Vui lòng cho phép TapTap truy cập camera để quét mã QR
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-primary text-white px-6 py-3 rounded-full font-medium"
            >
              Thử lại
            </button>
          </div>
        </div>
      )}

      {/* Footer Overlay */}
      {!hideFooterOverlay && hasPermission === true && (
        <div className="absolute bottom-0 left-0 right-0 z-10 pb-safe">
          <div className="flex justify-between items-start px-8 pb-6">
            {/* Merchants Button */}
            <button
              onClick={goToMerchant}
              className="flex flex-col items-center gap-1 text-white"
            >
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <StoreIcon />
              </div>
              <span className="text-xs font-medium">
                Áp dụng tại ({merchantCount})
              </span>
            </button>

            {/* Flash Button */}
            {/* <button
              onClick={toggleFlash}
              className="flex flex-col items-center gap-1 text-white"
            >
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <FlashIcon filled={flashEnabled} />
              </div>
              <span className="text-xs font-medium">
                {flashEnabled ? 'Tắt đèn' : 'Bật đèn'}
              </span>
            </button> */}

            {/* Instructions Button */}
            <button
              onClick={goToInstruction}
              className="flex flex-col items-center gap-1 text-white"
            >
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <InfoIcon />
              </div>
              <span className="text-xs font-medium">
                Hướng dẫn
              </span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default QRScanPage;