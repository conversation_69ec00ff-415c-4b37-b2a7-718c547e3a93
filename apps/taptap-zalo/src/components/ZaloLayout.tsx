import React from 'react';
import { useZaloNavigation } from '../hooks/useZaloNavigation';
import ZaloBottomNavigation from './navigation/ZaloBottomNavigation';

interface ZaloLayoutProps {
  children: React.ReactNode;
  showBottomNavigation?: boolean;
}

export const ZaloLayout: React.FC<ZaloLayoutProps> = ({ 
  children, 
  showBottomNavigation = true 
}) => {
  const { pathname } = useZaloNavigation();
  const currentPath = pathname;

  // Determine active tab based on current path - following Web app logic
  const getActiveTab = () => {
    const path = currentPath;

    // Home tab
    if (path === "/") return "home";
    if (path.startsWith("/search")) return "home";
    if (path.startsWith("/news")) return "home";
    if (path.startsWith("/missions")) return "home";
    if (path.startsWith("/deals")) return "home";
    if (path.startsWith("/merchants")) return "home";
    if (path.startsWith("/flash-sale")) return "home";
    if (path.startsWith("/member-code")) return "home";
    if (path.startsWith("/gift-code")) return "home";
    if (path.startsWith("/qr-payment")) return "home";
    if (path.startsWith("/payment-history")) return "home";
    if (path.startsWith("/bills")) return "home";
    if (path.startsWith("/camera")) return "home";
    if (path.startsWith("/contact")) return "home";

    // Voucher/Rewards tab - user's rewards and vouchers
    if (path.startsWith("/my-rewards")) return "voucher";
    if (path.startsWith("/membership")) return "voucher";

    // Exchange tab - reward catalog and exchange
    if (path.startsWith("/exchange")) return "exchange";
    if (path.startsWith("/reward/")) return "exchange";
    // Note: /rewards path is now deprecated, use /my-rewards or /exchange

    // Games tab
    if (path.startsWith("/games")) return "games";

    // Profile tab - user account and messages
    if (path.startsWith("/profile")) return "profile";
    if (path.startsWith("/messages")) return "profile";
    if (path.startsWith("/inbox")) return "profile";

    // Default to home for unmatched routes
    return "home";
  };

  // Routes that should hide the bottom navigation
  const routesWithoutBottomNav = [
    '/login',
    '/intro',
    '/camera',
    '/bills/camera',
  ];

  const shouldShowBottomNav = showBottomNavigation && 
    !routesWithoutBottomNav.includes(currentPath);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Main Content */}
      <main className={`flex-1 ${shouldShowBottomNav ? 'pb-20' : ''}`}>
        {children}
      </main>

      {/* Bottom Navigation - Conditionally rendered */}
      {shouldShowBottomNav && (
        <ZaloBottomNavigation activeTab={getActiveTab()} />
      )}
    </div>
  );
};