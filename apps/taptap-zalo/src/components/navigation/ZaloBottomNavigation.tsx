import React from 'react';
import { NavigationTab, HomeIcon, GetPointIcon, GiftExchangeIcon, GamingIcon, AccountIcon, cn } from '@taptap/shared';
import { useZaloNavigation } from '../../hooks/useZaloNavigation';

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
}

interface ZaloBottomNavigationProps {
  activeTab?: string;
}

const defaultItems: NavigationItem[] = [
  {
    id: 'home',
    label: 'Trang chủ',
    path: '/',
    icon: <HomeIcon className="w-8 h-8" />
  },
  {
    id: 'voucher',
    label: 'Ưu đãi',
    path: '/my-rewards',
    icon: <GetPointIcon className="w-8 h-8" />
  },
  {
    id: 'exchange',
    label: 'Đổi thưởng',
    path: '/exchange',
    icon: <GiftExchangeIcon className="w-8 h-8" />
  },
  {
    id: 'games',
    label: 'VUI chơi',
    path: '/games',
    icon: <GamingIcon className="w-8 h-8" />
  },
  {
    id: 'profile',
    label: 'T<PERSON><PERSON> khoản',
    path: '/profile',
    icon: <AccountIcon className="w-8 h-8" />
  }
];

const ZaloBottomNavigation: React.FC<ZaloBottomNavigationProps> = ({ activeTab = 'home' }) => {
  const { navigate } = useZaloNavigation();

  const handleItemClick = (itemId: string) => {
    const item = defaultItems.find(i => i.id === itemId);
    if (item) {
      navigate(item.path);
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      <div className="max-w-md mx-auto">
        <nav 
          className={cn('flex flex-row justify-center bg-white')}
          style={{ boxShadow: '0 -5px 24px 0 rgba(26, 24, 24, 0.10)' }}
        >
          {defaultItems.map((item) => {
            const isActive = activeTab === item.id;
            let icon = item.icon;
            
            // Update icons to show active state
            if (item.id === 'home') {
              icon = <HomeIcon className="w-8 h-8" active={isActive} />;
            } else if (item.id === 'voucher') {
              icon = <GetPointIcon className="w-8 h-8" active={isActive} />;
            } else if (item.id === 'exchange') {
              icon = <GiftExchangeIcon className="w-8 h-8" active={isActive} />;
            } else if (item.id === 'games') {
              icon = <GamingIcon className="w-8 h-8" active={isActive} />;
            } else if (item.id === 'profile') {
              icon = <AccountIcon className="w-8 h-8" active={isActive} />;
            }
            
            return (
              <NavigationTab
                key={item.id}
                icon={icon}
                label={item.label}
                isActive={isActive}
                onClick={() => handleItemClick(item.id)}
              />
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default ZaloBottomNavigation;