import React from 'react';
// Import image từ shared assets (vẫn dùng được shared assets)
import entranceImage from '@taptap/shared/assets/images/zalo/zalo-entrance-illustration-4f3f27.png';

export interface ZaloEntranceProps {
  onLinkPhone?: () => void;
  onDecline?: () => void;
  className?: string;
}

/**
 * ZaloEntrance - Entrance screen for Zalo Mini App
 * Shows phone number linking consent screen with illustration
 * This component is Zalo-specific, no platform detection needed
 */
export const ZaloEntrance: React.FC<ZaloEntranceProps> = ({
  onLinkPhone,
  onDecline,
  className = '',
}) => {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative overflow-hidden ${className}`}>
      {/* Background decorative stars */}
      <div className="absolute inset-0">
        {/* Top left stars */}
        <div className="absolute top-8 left-8 w-4 h-4 text-yellow-400">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
        </div>
        <div className="absolute top-16 left-20 w-2 h-2 text-yellow-300">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
        </div>
        
        {/* Top right stars */}
        <div className="absolute top-12 right-12 w-3 h-3 text-yellow-400">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
        </div>
        
        {/* Middle left stars */}
        <div className="absolute top-1/3 left-6 w-2 h-2 text-yellow-300">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
        </div>
        
        {/* Middle right stars */}
        <div className="absolute top-1/2 right-8 w-2 h-2 text-yellow-300">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
        </div>
        
        {/* Bottom stars */}
        <div className="absolute bottom-20 left-12 w-3 h-3 text-yellow-400">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
        </div>
        <div className="absolute bottom-32 right-16 w-2 h-2 text-yellow-300">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
        </div>
      </div>

      {/* Main content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 py-8">
        {/* Illustration */}
        <div className="mb-8">
          <img 
            src={entranceImage} 
            alt="TapTap Entrance Illustration"
            className="w-64 h-64 object-contain"
          />
        </div>

        {/* Login content */}
        <div className="text-center max-w-sm mx-auto">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Chào mừng đến với TapTap!
          </h1>
          
          <p className="text-gray-600 text-sm mb-8 leading-relaxed">
            Để sử dụng đầy đủ các tính năng của TapTap, 
            chúng tôi cần quyền truy cập số điện thoại của bạn 
            để tạo tài khoản và đồng bộ dữ liệu.
          </p>

          {/* Action buttons */}
          <div className="space-y-3">
            <button
              onClick={onLinkPhone}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 shadow-md"
            >
              Liên kết số điện thoại
            </button>
            
            <button
              onClick={onDecline}
              className="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors duration-200"
            >
              Từ chối
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZaloEntrance;