import { getSystemInfo } from "zmp-sdk";
import {
  AnimationRout<PERSON>,
  App,
  Route,
  SnackbarProvider,
  ZMPRouter,
} from "zmp-ui";
import { AppProps } from "zmp-ui/app";
import { ZaloLayout } from './ZaloLayout';

// Import pages
import HomePage from "../pages/home/<USER>";
import LoginPage from "../pages/auth/LoginPage";
import ProfilePage from "../pages/profile/ProfilePage";

// Search Pages
import SearchPage from "../pages/search/SearchPage";
import SearchResultsPage from "../pages/search/SearchResultsPage";

// Reward Pages
import RewardsPage from "../pages/rewards/RewardsPage";
import MyRewardsPage from "../pages/rewards/MyRewardsPage";
import ExchangePage from "../pages/rewards/ExchangePage";
import RewardDetailPage from "../pages/rewards/RewardDetailPage";

// Game Pages
import GamesPage from "../pages/games/GamesPage";
import GameDetailPage from "../pages/games/GameDetailPage";
import GamesHistoryPage from "../pages/games/GamesHistoryPage";
import MissionHistoryPage from "../pages/games/MissionHistoryPage";
import WheelGamePage from "../pages/games/WheelGamePage";

// Merchant Pages
import MerchantListPage from "../pages/merchant/MerchantListPage";
import MerchantDetailPage from "../pages/merchant/MerchantDetailPage";

// Mission Pages  
import MissionsPage from "../pages/missions/MissionsPage";
import MissionDetailPage from "../pages/missions/MissionDetailPage";

// Flash Sale Pages
import FlashSalePage from "../pages/flash-sale/FlashSalePage";

// Bill Pages
import CameraPage from "../pages/bills/CameraPage";
import BillsPage from "../pages/bills/BillsPage";

// Profile Related Pages
import TransactionHistoryPage from "../pages/transaction-history/TransactionHistoryPage";
import ContactUsPage from "../pages/contact/ContactUsPage";
import MyPointPage from "../pages/point/MyPointPage";
import InboxListPage from "../pages/inbox/InboxListPage";
import InboxDetailPage from "../pages/inbox/InboxDetailPage";
import TierMemberListPage from "../pages/membership/TierMemberListPage";

// Intro Pages
import IntroPage from "../pages/intro/IntroPage";

// Banner Pages
import BannerDetailPage from "../pages/banner/BannerDetailPage";

// News Pages
import NewsDetailPage from "../pages/news/NewsDetailPage";

const AppAny = App as any;
const SnackbarProviderAny = SnackbarProvider as any;
const ZMPRouterAny = ZMPRouter as any;
const AnimationRoutesAny = AnimationRoutes as any;
const RouteAny = Route as any;

const Layout = () => {
  return (
    <AppAny theme={getSystemInfo().zaloTheme as AppProps["theme"]}>
      <SnackbarProviderAny>
        <ZMPRouterAny>
          <ZaloLayout>
            <AnimationRoutesAny>
              {/* Main Pages */}
              <RouteAny path="/" element={<HomePage />}></RouteAny>
              
              {/* Auth Pages */}
              <RouteAny path="/login" element={<LoginPage />}></RouteAny>
              
              {/* Bottom Navigation Pages */}
              <RouteAny path="/profile" element={<ProfilePage />}></RouteAny>
              
              {/* Reward Pages */}
              <RouteAny path="/rewards" element={<RewardsPage />}></RouteAny>
              <RouteAny path="/my-rewards" element={<MyRewardsPage />}></RouteAny>
              <RouteAny path="/my-rewards/:id" element={<RewardDetailPage />}></RouteAny>
              <RouteAny path="/exchange" element={<ExchangePage />}></RouteAny>
              <RouteAny path="/rewards/:id" element={<RewardDetailPage />}></RouteAny>
              <RouteAny path="/reward/:id" element={<RewardDetailPage />}></RouteAny>
              
              {/* Banner Pages */}
              <RouteAny path="/banner/:bannerId" element={<BannerDetailPage />}></RouteAny>
              
              {/* News Pages */}
              <RouteAny path="/news/:id" element={<NewsDetailPage />}></RouteAny>
              
              {/* Game Pages */}
              <RouteAny path="/games" element={<GamesPage />}></RouteAny>
              <RouteAny path="/games/:id" element={<GameDetailPage />}></RouteAny>
              <RouteAny path="/games/history" element={<GamesHistoryPage />}></RouteAny>
              <RouteAny path="/games/missions/history" element={<MissionHistoryPage />}></RouteAny>
              <RouteAny path="/games/missions/:id" element={<MissionHistoryPage />}></RouteAny>
              <RouteAny path="/games/wheel/:id" element={<WheelGamePage />}></RouteAny>
              
              {/* Merchant Pages */}
              <RouteAny path="/merchants" element={<MerchantListPage />}></RouteAny>
              <RouteAny path="/merchant/:id" element={<MerchantDetailPage />}></RouteAny>
              
              {/* Mission Pages */}
              <RouteAny path="/missions" element={<MissionsPage />}></RouteAny>
              <RouteAny path="/missions/:id" element={<MissionDetailPage />}></RouteAny>
              
              {/* Flash Sale Pages */}
              <RouteAny path="/flash-sale" element={<FlashSalePage />}></RouteAny>
              
              {/* Action Category Pages */}
              <RouteAny path="/camera" element={<CameraPage />}></RouteAny>
              <RouteAny path="/bills" element={<BillsPage />}></RouteAny>
              
              {/* Search Pages */}
              <RouteAny path="/search" element={<SearchPage />}></RouteAny>
              <RouteAny path="/search/results" element={<SearchResultsPage />}></RouteAny>
              
              {/* Profile Related Pages */}
              <RouteAny path="/transaction-history" element={<TransactionHistoryPage />}></RouteAny>
              <RouteAny path="/contact" element={<ContactUsPage />}></RouteAny>
              <RouteAny path="/point" element={<MyPointPage />}></RouteAny>
              <RouteAny path="/inbox" element={<InboxListPage />}></RouteAny>
              <RouteAny path="/inbox/:id" element={<InboxDetailPage />}></RouteAny>
              <RouteAny path="/membership/tier-list" element={<TierMemberListPage />}></RouteAny>
              
              {/* Intro Pages */}
              <RouteAny path="/intro" element={<IntroPage />}></RouteAny>
            </AnimationRoutesAny>
          </ZaloLayout>
        </ZMPRouterAny>
      </SnackbarProviderAny>
    </AppAny>
  );
};
export default Layout;
