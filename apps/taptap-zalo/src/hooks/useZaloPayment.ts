import { useCallback } from 'react';
import { Payment } from 'zmp-sdk/apis';

interface PaymentOptions {
  amount: number;
  desc: string;
  orderId: string;
  extraData?: Record<string, any>;
}

/**
 * Hook for Zalo Mini App payment operations
 */
export const useZaloPayment = () => {
  const createPayment = useCallback(async (options: PaymentOptions) => {
    try {
      const result = await Payment.purchase({
        amount: options.amount,
        desc: options.desc,
        method: 'zalo_pay',
      });
      return result;
    } catch (error) {
      console.error('Error creating payment:', error);
      throw error;
    }
  }, []);

  return {
    createPayment,
  };
};