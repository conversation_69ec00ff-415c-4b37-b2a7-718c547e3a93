/**
 * Zalo authentication hook
 * Uses web auth mechanism with Zalo-specific features
 */

import { useState, useEffect, useCallback } from 'react';
import { zaloAuthService, type ZaloUserInfo } from '../services/zaloAuth';
import { type User, type LoginRequest, useAuthStore } from '@taptap/shared';
import { useZaloNavigation } from './useZaloNavigation';

export function useZaloAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [zaloUser, setZaloUser] = useState<ZaloUserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { navigate } = useZaloNavigation();
  const { setToken, logout: clearAuth } = useAuthStore();
  
  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('[useZaloAuth] Initializing auth state...');
        setLoading(true);
        const currentUser = await zaloAuthService.getCurrentUser();
        console.log('[useZaloAuth] Current user from service:', currentUser);
        setUser(currentUser);
        
        // Get Zalo user info if available
        if (currentUser) {
          try {
            const zaloUserInfo = await zaloAuthService.getZaloUserInfo();
            console.log('[useZaloAuth] Zalo user info:', zaloUserInfo);
            setZaloUser(zaloUserInfo);
          } catch (err) {
            console.warn('[useZaloAuth] Failed to get Zalo user info:', err);
          }
        }
      } catch (err) {
        console.error('[useZaloAuth] Init failed:', err);
        setError('Failed to initialize authentication');
      } finally {
        setLoading(false);
      }
    };
    
    initAuth();
  }, []);
  
  // Login using web mechanism
  const login = useCallback(async (credentials: LoginRequest) => {
    try {
      console.log('[useZaloAuth] Login attempt with credentials:', credentials);
      setLoading(true);
      setError(null);
      
      const userData = await zaloAuthService.login(credentials);
      console.log('[useZaloAuth] Login successful, user data:', userData);
      setUser(userData);
      
      // Update shared auth store
      // Note: For now we get token from sessionStorage as web auth stores it there
      
      // Try to get Zalo user info if available
      try {
        const zaloUserInfo = await zaloAuthService.getZaloUserInfo();
        console.log('[useZaloAuth] Got Zalo user info after login:', zaloUserInfo);
        setZaloUser(zaloUserInfo);
      } catch (err) {
        console.warn('[useZaloAuth] Failed to get Zalo user info:', err);
      }
      
      // Navigate to home
      console.log('[useZaloAuth] Navigating to home...');
      navigate('/');
      
      return userData;
    } catch (err: any) {
      console.error('[useZaloAuth] Login failed:', err);
      const errorMessage = err.message || 'Login failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [navigate]);
  
  // Logout
  const logout = useCallback(async () => {
    try {
      setLoading(true);
      await zaloAuthService.logout();
      setUser(null);
      setZaloUser(null);
      clearAuth();
    } catch (err) {
      console.error('[useZaloAuth] Logout failed:', err);
      // Force logout even on error
      setUser(null);
      setZaloUser(null);
      clearAuth();
    } finally {
      setLoading(false);
    }
  }, [clearAuth]);
  
  // Check authentication status
  const isAuthenticated = useCallback(() => {
    return zaloAuthService.isAuthenticated();
  }, []);
  
  // Refresh user data
  const refreshUser = useCallback(async () => {
    try {
      setLoading(true);
      const currentUser = await zaloAuthService.getCurrentUser();
      setUser(currentUser);
      
      if (currentUser) {
        try {
          const zaloUserInfo = await zaloAuthService.getZaloUserInfo();
          setZaloUser(zaloUserInfo);
        } catch (err) {
          console.warn('[useZaloAuth] Failed to refresh Zalo user info:', err);
        }
      }
      
      return currentUser;
    } catch (err) {
      console.error('[useZaloAuth] Refresh failed:', err);
      setError('Failed to refresh user data');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Get phone number (Zalo-specific feature)
  const getPhoneNumber = useCallback(async (): Promise<string | null> => {
    try {
      const phoneNumber = await zaloAuthService.getPhoneNumber();
      return phoneNumber;
    } catch (err) {
      console.error('[useZaloAuth] Get phone number failed:', err);
      setError('Failed to get phone number');
      return null;
    }
  }, []);
  
  // Request permissions (Zalo-specific feature)
  const requestPermissions = useCallback(async (scopes: string[]): Promise<boolean> => {
    try {
      const result = await zaloAuthService.requestPermissions(scopes);
      if (!result) {
        setError('Permission denied');
      }
      return result;
    } catch (err) {
      console.error('[useZaloAuth] Permission request failed:', err);
      setError('Failed to request permissions');
      return false;
    }
  }, []);
  
  // Check OA follow status (Zalo-specific feature)
  const checkOAFollow = useCallback(async (): Promise<boolean> => {
    try {
      return await zaloAuthService.checkOAFollowStatus();
    } catch (err) {
      console.error('[useZaloAuth] Check OA follow failed:', err);
      return false;
    }
  }, []);
  
  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Check if user has specific role (placeholder for future implementation)
  const hasRole = useCallback((role: string): boolean => {
    return false; // TODO: Add roles field to User type
  }, []);
  
  // Check if user has specific permission (placeholder for future implementation) 
  const hasPermission = useCallback((permission: string): boolean => {
    return false; // TODO: Add permissions field to User type
  }, []);
  
  // Get user display name
  const getDisplayName = useCallback((): string => {
    if (zaloUser) return zaloUser.name;
    if (user) return user.displayName || user.email || 'User';
    return 'Guest';
  }, [user, zaloUser]);
  
  // Get user avatar URL
  const getAvatarUrl = useCallback((): string => {
    if (zaloUser && zaloUser.avatar) return zaloUser.avatar;
    if (user && user.avatar) return user.avatar;
    return '/assets/images/default-avatar.png';
  }, [user, zaloUser]);
  
  // Check if user email is verified (placeholder for future implementation)
  const isEmailVerified = useCallback((): boolean => {
    return false; // TODO: Add email verification field to User type
  }, []);
  
  // Check if user phone is verified (placeholder for future implementation)
  const isPhoneVerified = useCallback((): boolean => {
    return false; // TODO: Add phone verification field to User type
  }, []);
  
  // Get user tier/level (placeholder for future implementation)
  const getUserTier = useCallback((): string => {
    return 'SILVER'; // TODO: Add tier field to User type
  }, []);
  
  // Get user points (placeholder for future implementation) 
  const getUserPoints = useCallback((): number => {
    return 0; // TODO: Add points field to User type
  }, []);

  return {
    // State
    user,
    zaloUser,
    loading,
    error,
    isAuthenticated: isAuthenticated(),
    
    // Actions
    login,
    logout,
    refreshUser,
    clearError,
    
    // Zalo-specific features
    getPhoneNumber,
    requestPermissions,
    checkOAFollow,
    
    // User utilities
    hasRole,
    hasPermission,
    getDisplayName,
    getAvatarUrl,
    isEmailVerified,
    isPhoneVerified,
    getUserTier,
    getUserPoints,
  };
}