import { useCallback } from 'react';
import { chooseImage } from 'zmp-sdk/apis';

interface CameraOptions {
  count?: number;
  sourceType?: ('album' | 'camera')[];
  cameraType?: 'back' | 'front';
}

/**
 * Hook for Zalo Mini App camera operations
 */
export const useZaloCamera = () => {
  const chooseImages = useCallback(async (options: CameraOptions = {}) => {
    try {
      const result = await chooseImage({
        count: options.count || 1,
        sourceType: options.sourceType || ['album', 'camera'],
        cameraType: options.cameraType,
      });
      return result;
    } catch (error) {
      console.error('Error choosing images:', error);
      throw error;
    }
  }, []);

  const takePhoto = useCallback(async (cameraType: 'back' | 'front' = 'back') => {
    try {
      const result = await chooseImage({
        count: 1,
        sourceType: ['camera'],
        cameraType,
      });
      return result;
    } catch (error) {
      console.error('Error taking photo:', error);
      throw error;
    }
  }, []);

  return {
    chooseImages,
    takePhoto,
  };
};