/**
 * Zalo navigation hook
 * Provides navigation utilities for Zalo Mini App
 */

import { useNavigate as useZMPNavigate, useLocation } from 'react-router-dom';
import { useCallback } from 'react';
import { zaloNavigationService } from '../services/zaloNavigation';

export function useZaloNavigation() {
  const navigate = useZMPNavigate();
  const location = useLocation();

  // Navigation methods
  const goBack = useCallback(() => {
    zaloNavigationService.back();
  }, []);

  const goHome = useCallback(() => {
    navigate('/');
  }, [navigate]);

  const goToProfile = useCallback(() => {
    navigate('/profile');
  }, [navigate]);

  const goToEarn = useCallback(() => {
    navigate('/earn');
  }, [navigate]);

  const goToRewards = useCallback(() => {
    navigate('/rewards');
  }, [navigate]);

  const goToGames = useCallback(() => {
    navigate('/games');
  }, [navigate]);

  const goToExchange = useCallback(() => {
    navigate('/exchange');
  }, [navigate]);

  const goToCamera = useCallback(() => {
    navigate('/camera');
  }, [navigate]);

  const goToQRPayment = useCallback(() => {
    navigate('/qr-payment');
  }, [navigate]);

  const goToBills = useCallback(() => {
    navigate('/bills');
  }, [navigate]);

  const goToSearch = useCallback((query?: string) => {
    if (query) {
      navigate(`/search/results?q=${encodeURIComponent(query)}`);
    } else {
      navigate('/search');
    }
  }, [navigate]);

  const goToFlashSale = useCallback(() => {
    navigate('/flash-sale');
  }, [navigate]);

  const goToMissions = useCallback(() => {
    navigate('/missions');
  }, [navigate]);

  const goToMyRewards = useCallback(() => {
    navigate('/my-rewards');
  }, [navigate]);

  const goToMerchant = useCallback((merchantId: string) => {
    navigate(`/merchants/${merchantId}`);
  }, [navigate]);

  const goToReward = useCallback((rewardId: string) => {
    navigate(`/rewards/${rewardId}`);
  }, [navigate]);

  const goToGame = useCallback((gameId: string) => {
    navigate(`/games/${gameId}`);
  }, [navigate]);

  const goToNews = useCallback((newsId: string) => {
    navigate(`/news/${newsId}`);
  }, [navigate]);

  const openExternal = useCallback(async (url: string) => {
    await zaloNavigationService.openExternal(url);
  }, []);

  const reload = useCallback(() => {
    zaloNavigationService.reload();
  }, []);

  // Query param utilities
  const getQueryParam = useCallback((key: string): string | null => {
    const params = new URLSearchParams(location.search);
    return params.get(key);
  }, [location.search]);

  const setQueryParam = useCallback((key: string, value: string) => {
    const params = new URLSearchParams(location.search);
    params.set(key, value);
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });
  }, [location, navigate]);

  const removeQueryParam = useCallback((key: string) => {
    const params = new URLSearchParams(location.search);
    params.delete(key);
    const newSearch = params.toString();
    navigate(`${location.pathname}${newSearch ? `?${newSearch}` : ''}`, { replace: true });
  }, [location, navigate]);

  const clearQueryParams = useCallback(() => {
    navigate(location.pathname, { replace: true });
  }, [location.pathname, navigate]);

  // History utilities
  const canGoBack = useCallback(() => {
    return zaloNavigationService.canGoBack();
  }, []);

  const getHistory = useCallback(() => {
    return zaloNavigationService.getHistory();
  }, []);

  const clearHistory = useCallback(() => {
    zaloNavigationService.clearHistory();
  }, []);

  const getCurrentPath = useCallback(() => {
    return zaloNavigationService.getCurrentPath();
  }, []);

  return {
    // Navigation methods
    navigate,
    goBack,
    goHome,
    goToProfile,
    goToEarn,
    goToRewards,
    goToGames,
    goToExchange,
    goToCamera,
    goToQRPayment,
    goToBills,
    goToSearch,
    goToFlashSale,
    goToMissions,
    goToMyRewards,
    goToMerchant,
    goToReward,
    goToGame,
    goToNews,
    openExternal,
    reload,
    
    // Location info
    location,
    pathname: location.pathname,
    search: location.search,
    hash: location.hash,
    state: location.state,
    
    // Query params
    getQueryParam,
    setQueryParam,
    removeQueryParam,
    clearQueryParams,
    
    // History
    canGoBack,
    getHistory,
    clearHistory,
    getCurrentPath,
  };
}