import { useCallback } from 'react';
import { nativeStorage } from 'zmp-sdk/apis';

/**
 * Hook for Zalo Mini App storage operations using nativeStorage
 */
export const useZaloStorage = () => {
  const setStorageItem = useCallback((key: string, value: any) => {
    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      nativeStorage.setItem(key, stringValue);
    } catch (error) {
      console.error('Error setting storage:', error);
    }
  }, []);

  const getStorageItem = useCallback((key: string) => {
    try {
      return nativeStorage.getItem(key);
    } catch (error) {
      console.error('Error getting storage:', error);
      return null;
    }
  }, []);

  const getStorageItemAsJson = useCallback(<T = any>(key: string): T | null => {
    try {
      const value = nativeStorage.getItem(key);
      if (value === null) return null;
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Error getting storage as JSON:', error);
      return null;
    }
  }, []);

  const removeStorageItem = useCallback((key: string) => {
    try {
      nativeStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing storage:', error);
    }
  }, []);

  const clearStorage = useCallback(() => {
    try {
      nativeStorage.clear();
    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  }, []);

  const getStorageDetails = useCallback(() => {
    try {
      return nativeStorage.getStorageInfo();
    } catch (error) {
      console.error('Error getting storage info:', error);
      return null;
    }
  }, []);

  return {
    setItem: setStorageItem,
    getItem: getStorageItem,
    getItemAsJson: getStorageItemAsJson,
    removeItem: removeStorageItem,
    clear: clearStorage,
    getStorageInfo: getStorageDetails,
  };
};