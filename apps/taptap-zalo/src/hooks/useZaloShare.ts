import { useCallback } from 'react';
import { openShareSheet } from 'zmp-sdk/apis';

interface ShareTextOptions {
  text: string;
  description?: string;
  autoParseLink?: boolean;
}

interface ShareImageOptions {
  imageUrls: string[];
}

interface ShareLinkOptions {
  url: string;
  title?: string;
  description?: string;
}

/**
 * Hook for Zalo Mini App sharing operations
 */
export const useZaloShare = () => {
  const shareText = useCallback(async (options: ShareTextOptions) => {
    try {
      const result = await openShareSheet({
        type: 'text',
        data: {
          text: options.text,
          description: options.description,
          autoParseLink: options.autoParseLink || false,
        },
      });
      return result;
    } catch (error) {
      console.error('Error sharing text:', error);
      throw error;
    }
  }, []);

  const shareImages = useCallback(async (options: ShareImageOptions) => {
    try {
      const result = await openShareSheet({
        type: 'image',
        data: {
          imageUrls: options.imageUrls,
        },
      });
      return result;
    } catch (error) {
      console.error('Error sharing images:', error);
      throw error;
    }
  }, []);

  const shareLink = useCallback(async (options: ShareLinkOptions) => {
    try {
      const result = await openShareSheet({
        type: 'link',
        data: {
          link: options.url,
          chatOnly: false,
        },
      });
      return result;
    } catch (error) {
      console.error('Error sharing link:', error);
      throw error;
    }
  }, []);

  return {
    shareText,
    shareImages,
    shareLink,
  };
};