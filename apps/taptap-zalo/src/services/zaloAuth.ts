/**
 * Zalo authentication service
 * Uses web login mechanism with Zalo-specific storage handling
 */

import { webAuthService, type User } from '../../../web/src/services/webAuth';
import { type LoginRequest } from '@taptap/shared';
import { getUserInfo, getPhoneNumber, authorize } from 'zmp-sdk';

interface ZaloUserInfo {
  id: string;
  name: string;
  avatar: string;
  idByOA?: string;
  followedOA?: boolean;
}

interface ZaloPhoneNumber {
  number: string;
  token: string;
}

class ZaloAuthService {
  private cachedUserInfo: ZaloUserInfo | null = null;

  /**
   * Get Zalo user info
   */
  async getZaloUserInfo(): Promise<ZaloUserInfo> {
    try {
      if (this.cachedUserInfo) {
        return this.cachedUserInfo;
      }

      const userInfo = await getUserInfo({
        avatarType: 'large'
      });
      
      this.cachedUserInfo = userInfo as ZaloUserInfo;
      return this.cachedUserInfo;
    } catch (error) {
      console.error('[<PERSON>alo<PERSON><PERSON>] Failed to get user info:', error);
      throw new Error('Failed to get Zalo user info');
    }
  }

  /**
   * Get user phone number (requires permission)
   */
  async getPhoneNumber(): Promise<string> {
    try {
      // Request permission first
      const authResult = await authorize({
        scopes: ['scope.userPhonenumber']
      });

      if (!authResult || authResult.errorCode !== 0) {
        throw new Error('Permission denied');
      }

      const phoneData = await getPhoneNumber() as ZaloPhoneNumber;
      return phoneData.number;
    } catch (error) {
      console.error('[ZaloAuth] Failed to get phone number:', error);
      throw new Error('Failed to get phone number');
    }
  }

  /**
   * Login using web mechanism (same as web)
   */
  async login(credentials: LoginRequest): Promise<User> {
    try {
      console.log('[ZaloAuth] Starting login with credentials:', credentials);
      // Use the same web login mechanism
      const user = await webAuthService.login(credentials);
      console.log('[ZaloAuth] Login successful, user:', user);
      return user;
    } catch (error) {
      console.error('[ZaloAuth] Login failed:', error);
      throw error;
    }
  }

  /**
   * Logout using web mechanism
   */
  async logout(): Promise<void> {
    try {
      // Clear cached Zalo data
      this.cachedUserInfo = null;
      
      // Clear browser storage manually (like web logout but without redirect)
      sessionStorage.removeItem('taptap_auth_token');
      sessionStorage.removeItem('taptap_auth_user');
      localStorage.removeItem('taptap_refresh_token');
      
      // Call backend logout if we have a token
      const token = sessionStorage.getItem('taptap_auth_token');
      if (token) {
        try {
          await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
        } catch (error) {
          console.error('[ZaloAuth] Backend logout failed:', error);
        }
      }
      
      // Navigate to Zalo home instead of web login
      window.location.href = '/';
    } catch (error) {
      console.error('[ZaloAuth] Logout failed:', error);
      // Force redirect to Zalo home on error
      window.location.href = '/';
    }
  }

  /**
   * Get current user using web mechanism
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const user = await webAuthService.getCurrentUser();
      console.log('[ZaloAuth] getCurrentUser result:', user);
      return user;
    } catch (error) {
      console.error('[ZaloAuth] Get current user failed:', error);
      return null;
    }
  }

  /**
   * Check if user is authenticated using web mechanism
   */
  isAuthenticated(): boolean {
    const isAuth = webAuthService.isAuthenticated();
    console.log('[ZaloAuth] isAuthenticated check:', isAuth);
    return isAuth;
  }

  /**
   * Request permissions
   */
  async requestPermissions(scopes: string[]): Promise<boolean> {
    try {
      const result = await authorize({ scopes });
      return result && result.errorCode === 0;
    } catch (error) {
      console.error('[ZaloAuth] Permission request failed:', error);
      return false;
    }
  }

  /**
   * Check if user follows OA (Official Account)
   */
  async checkOAFollowStatus(): Promise<boolean> {
    try {
      const userInfo = await this.getZaloUserInfo();
      return userInfo.followedOA || false;
    } catch (error) {
      console.error('[ZaloAuth] Failed to check OA follow status:', error);
      return false;
    }
  }
}

// Export singleton instance
export const zaloAuthService = new ZaloAuthService();

// Export types
export type { ZaloUserInfo, ZaloPhoneNumber, User, LoginRequest };