/**
 * Zalo storage service
 * Wraps ZMP SDK storage functionality
 */

import { getStorage, setStorage, removeStorage } from 'zmp-sdk';

class ZaloStorageService {
  private prefix = 'taptap_';
  private memoryCache = new Map<string, any>();

  /**
   * Set item in Zalo storage
   */
  async setItem<T>(key: string, value: T): Promise<void> {
    const prefixedKey = this.prefix + key;
    
    try {
      await setStorage({
        key: prefixedKey,
        value: JSON.stringify(value),
        fail: (error) => {
          console.error('[ZaloStorage] Failed to set item:', error);
          // Fallback to memory cache
          this.memoryCache.set(prefixedKey, value);
        }
      });
      
      // Also cache in memory
      this.memoryCache.set(prefixedKey, value);
    } catch (error) {
      console.error('[ZaloStorage] Set item error:', error);
      // Fallback to memory cache
      this.memoryCache.set(prefixedKey, value);
    }
  }

  /**
   * Get item from Zalo storage
   */
  async getItem<T>(key: string): Promise<T | null> {
    const prefixedKey = this.prefix + key;
    
    try {
      // Check memory cache first
      if (this.memoryCache.has(prefixedKey)) {
        return this.memoryCache.get(prefixedKey);
      }
      
      const result = await getStorage({
        keys: [prefixedKey],
        fail: (error) => {
          console.error('[ZaloStorage] Failed to get item:', error);
        }
      });
      
      if (result && result[prefixedKey]) {
        const value = JSON.parse(result[prefixedKey]);
        // Update memory cache
        this.memoryCache.set(prefixedKey, value);
        return value;
      }
      
      return null;
    } catch (error) {
      console.error('[ZaloStorage] Get item error:', error);
      // Try memory cache as fallback
      return this.memoryCache.get(prefixedKey) || null;
    }
  }

  /**
   * Remove item from Zalo storage
   */
  async removeItem(key: string): Promise<void> {
    const prefixedKey = this.prefix + key;
    
    try {
      await removeStorage({
        keys: [prefixedKey],
        fail: (error) => {
          console.error('[ZaloStorage] Failed to remove item:', error);
        }
      });
      
      // Also remove from memory cache
      this.memoryCache.delete(prefixedKey);
    } catch (error) {
      console.error('[ZaloStorage] Remove item error:', error);
      this.memoryCache.delete(prefixedKey);
    }
  }

  /**
   * Get multiple items
   */
  async getMultiple<T>(keys: string[]): Promise<Record<string, T>> {
    const prefixedKeys = keys.map(key => this.prefix + key);
    
    try {
      const result = await getStorage({
        keys: prefixedKeys,
        fail: (error) => {
          console.error('[ZaloStorage] Failed to get multiple items:', error);
        }
      });
      
      const items: Record<string, T> = {};
      
      for (const key of keys) {
        const prefixedKey = this.prefix + key;
        if (result && result[prefixedKey]) {
          items[key] = JSON.parse(result[prefixedKey]);
          // Update memory cache
          this.memoryCache.set(prefixedKey, items[key]);
        } else if (this.memoryCache.has(prefixedKey)) {
          items[key] = this.memoryCache.get(prefixedKey);
        }
      }
      
      return items;
    } catch (error) {
      console.error('[ZaloStorage] Get multiple error:', error);
      
      // Fallback to memory cache
      const items: Record<string, T> = {};
      for (const key of keys) {
        const prefixedKey = this.prefix + key;
        if (this.memoryCache.has(prefixedKey)) {
          items[key] = this.memoryCache.get(prefixedKey);
        }
      }
      return items;
    }
  }

  /**
   * Clear all TapTap items from storage
   */
  async clear(): Promise<void> {
    try {
      // Get all keys with our prefix
      const allKeys = Array.from(this.memoryCache.keys()).filter(key => 
        key.startsWith(this.prefix)
      );
      
      if (allKeys.length > 0) {
        await removeStorage({
          keys: allKeys,
          fail: (error) => {
            console.error('[ZaloStorage] Failed to clear storage:', error);
          }
        });
      }
      
      // Clear memory cache
      this.memoryCache.clear();
    } catch (error) {
      console.error('[ZaloStorage] Clear error:', error);
      this.memoryCache.clear();
    }
  }

  /**
   * Check if storage is available
   */
  isStorageAvailable(): boolean {
    return typeof setStorage === 'function' && typeof getStorage === 'function';
  }

  /**
   * Get storage info
   */
  getStorageInfo(): {
    available: boolean;
    cacheSize: number;
  } {
    return {
      available: this.isStorageAvailable(),
      cacheSize: this.memoryCache.size
    };
  }

  /**
   * Set with expiry
   */
  async setWithExpiry<T>(key: string, value: T, expiryMinutes: number): Promise<void> {
    const data = {
      value,
      expiry: Date.now() + (expiryMinutes * 60 * 1000)
    };
    await this.setItem(key, data);
  }

  /**
   * Get with expiry check
   */
  async getWithExpiry<T>(key: string): Promise<T | null> {
    const data = await this.getItem<{ value: T; expiry: number }>(key);
    
    if (!data) return null;
    
    if (Date.now() > data.expiry) {
      await this.removeItem(key);
      return null;
    }
    
    return data.value;
  }

  /**
   * Export data for backup
   */
  async exportData(): Promise<string> {
    const allKeys = Array.from(this.memoryCache.keys()).filter(key => 
      key.startsWith(this.prefix)
    );
    
    const data: Record<string, any> = {};
    
    for (const key of allKeys) {
      const cleanKey = key.replace(this.prefix, '');
      data[cleanKey] = this.memoryCache.get(key);
    }
    
    return JSON.stringify(data, null, 2);
  }

  /**
   * Import data from backup
   */
  async importData(jsonData: string): Promise<void> {
    try {
      const data = JSON.parse(jsonData);
      
      for (const [key, value] of Object.entries(data)) {
        await this.setItem(key, value);
      }
    } catch (error) {
      console.error('[ZaloStorage] Failed to import data:', error);
      throw new Error('Invalid backup data format');
    }
  }
}

// Export singleton instance
export const zaloStorageService = new ZaloStorageService();