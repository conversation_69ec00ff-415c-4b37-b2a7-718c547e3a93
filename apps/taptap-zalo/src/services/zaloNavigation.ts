/**
 * Zalo navigation service
 * Wraps ZMP SDK navigation functionality
 */

import { openWebview } from 'zmp-sdk';

class ZaloNavigationService {
  private history: string[] = [];
  private maxHistorySize = 50;

  /**
   * Navigate to a route within the mini app
   */
  to(path: string, options?: { replace?: boolean; state?: any }): void {
    // Add to history
    if (!options?.replace) {
      this.addToHistory(path);
    }

    // Use ZMP Router navigation
    // Note: This assumes ZMP Router is configured in the app
    const event = new CustomEvent('zmp-route-change', {
      detail: { path, state: options?.state }
    });
    window.dispatchEvent(event);
  }

  /**
   * Go back in history
   */
  back(): void {
    if (this.history.length > 1) {
      this.history.pop(); // Remove current
      const previous = this.history[this.history.length - 1];
      this.to(previous, { replace: true });
    } else {
      // Fallback to home
      this.to('/', { replace: true });
    }
  }

  /**
   * Navigate to home
   */
  home(): void {
    this.to('/', { replace: true });
  }

  /**
   * Navigate to login
   */
  login(): void {
    this.to('/login');
  }

  /**
   * Navigate to profile
   */
  profile(): void {
    this.to('/profile');
  }


  /**
   * Navigate to rewards
   */
  rewards(): void {
    this.to('/rewards');
  }

  /**
   * Navigate to games
   */
  games(): void {
    this.to('/games');
  }


  /**
   * Navigate to camera (bill scan)
   */
  camera(): void {
    this.to('/camera');
  }


  /**
   * Navigate to bills
   */
  bills(): void {
    this.to('/bills');
  }

  /**
   * Navigate to search
   */
  search(query?: string): void {
    if (query) {
      this.to(`/search/results?q=${encodeURIComponent(query)}`);
    } else {
      this.to('/search');
    }
  }

  /**
   * Open external URL in webview
   */
  async openExternal(url: string): Promise<void> {
    try {
      await openWebview({
        url,
        config: {
          style: 'normal',
          leftButton: 'back'
        }
      });
    } catch (error) {
      console.error('[ZaloNavigation] Failed to open webview:', error);
      // Fallback to window.open
      window.open(url, '_blank');
    }
  }

  /**
   * Close webview (fallback to history back)
   */
  async closeWebview(): Promise<void> {
    try {
      // Use history back as fallback since closeWebview is not available
      this.back();
    } catch (error) {
      console.error('[ZaloNavigation] Failed to close webview:', error);
    }
  }

  /**
   * Get current path
   */
  getCurrentPath(): string {
    return window.location.pathname;
  }

  /**
   * Get navigation history
   */
  getHistory(): string[] {
    return [...this.history];
  }

  /**
   * Clear navigation history
   */
  clearHistory(): void {
    this.history = [this.getCurrentPath()];
  }

  /**
   * Add to history
   */
  private addToHistory(path: string): void {
    this.history.push(path);
    
    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize);
    }
  }

  /**
   * Check if can go back
   */
  canGoBack(): boolean {
    return this.history.length > 1;
  }

  /**
   * Replace current URL
   */
  replaceUrl(path: string): void {
    window.history.replaceState(null, '', path);
  }

  /**
   * Reload current page
   */
  reload(): void {
    window.location.reload();
  }

  /**
   * Navigate to merchant detail
   */
  merchantDetail(merchantId: string): void {
    this.to(`/merchants/${merchantId}`);
  }

  /**
   * Navigate to reward detail
   */
  rewardDetail(rewardId: string): void {
    this.to(`/rewards/${rewardId}`);
  }

  /**
   * Navigate to game detail
   */
  gameDetail(gameId: string): void {
    this.to(`/games/${gameId}`);
  }

  /**
   * Navigate to news detail
   */
  newsDetail(newsId: string): void {
    this.to(`/news/${newsId}`);
  }

  /**
   * Navigate to flash sale
   */
  flashSale(): void {
    this.to('/flash-sale');
  }

  /**
   * Navigate to missions
   */
  missions(): void {
    this.to('/missions');
  }

  /**
   * Navigate to my rewards
   */
  myRewards(): void {
    this.to('/my-rewards');
  }
}

// Export singleton instance
export const zaloNavigationService = new ZaloNavigationService();