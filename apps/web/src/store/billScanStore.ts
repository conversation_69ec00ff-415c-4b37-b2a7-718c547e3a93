import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useEffect } from 'react';
import { 
  billScanService, 
  type BillConfigGlobal,
  type EarnMethodInfo,
  type SubMerchant,
  type Step
} from '@taptap/shared';

interface BillScanState {
  // Bill config state
  billConfig: BillConfigGlobal | null;
  isLoadingConfig: boolean;
  configError: string | null;
  
  // Earn method info state
  earnMethodInfo: EarnMethodInfo | null;
  isLoadingEarnInfo: boolean;
  earnInfoError: string | null;
  
  // Actions
  fetchBillConfig: () => Promise<void>;
  fetchEarnMethodInfo: () => Promise<void>;
  setBillConfig: (config: BillConfigGlobal | null) => void;
  setEarnMethodInfo: (info: EarnMethodInfo | null) => void;
  setLoadingConfig: (loading: boolean) => void;
  setConfigError: (error: string | null) => void;
  setLoadingEarnInfo: (loading: boolean) => void;
  setEarnInfoError: (error: string | null) => void;
}

export const useBillScanStore = create<BillScanState>()(
  devtools(
    (set, get) => ({
      // Initial state
      billConfig: null,
      isLoadingConfig: false,
      configError: null,
      earnMethodInfo: null,
      isLoadingEarnInfo: false,
      earnInfoError: null,
      
      // Actions
      fetchBillConfig: async () => {
        const { billConfig } = get();
        
        // Return cached config if already loaded
        if (billConfig) {
          return;
        }
        
        set({ isLoadingConfig: true, configError: null }, false, 'fetchBillConfig:start');
        
        try {
          const config = await billScanService.getBillConfigGlobal();
          set({ 
            billConfig: config, 
            isLoadingConfig: false,
            configError: config ? null : 'Failed to load bill configuration'
          }, false, 'fetchBillConfig:success');
        } catch (error) {
          set({ 
            isLoadingConfig: false, 
            configError: 'Failed to load bill configuration'
          }, false, 'fetchBillConfig:error');
          console.error('Error fetching bill config:', error);
        }
      },
      
      fetchEarnMethodInfo: async () => {
        const { earnMethodInfo } = get();
        
        // Return cached info if already loaded
        if (earnMethodInfo) {
          return;
        }
        
        set({ isLoadingEarnInfo: true, earnInfoError: null }, false, 'fetchEarnMethodInfo:start');
        
        try {
          const info = await billScanService.getEarnMethodInfo();
          set({ 
            earnMethodInfo: info, 
            isLoadingEarnInfo: false,
            earnInfoError: info ? null : 'Failed to load earn method information'
          }, false, 'fetchEarnMethodInfo:success');
        } catch (error) {
          set({ 
            isLoadingEarnInfo: false, 
            earnInfoError: 'Failed to load earn method information'
          }, false, 'fetchEarnMethodInfo:error');
          console.error('Error fetching earn method info:', error);
        }
      },
      
      setBillConfig: (billConfig) => set({ billConfig }, false, 'setBillConfig'),
      
      setEarnMethodInfo: (earnMethodInfo) => set({ earnMethodInfo }, false, 'setEarnMethodInfo'),
      
      setLoadingConfig: (isLoadingConfig) => set({ isLoadingConfig }, false, 'setLoadingConfig'),
      
      setConfigError: (configError) => set({ configError }, false, 'setConfigError'),
      
      setLoadingEarnInfo: (isLoadingEarnInfo) => set({ isLoadingEarnInfo }, false, 'setLoadingEarnInfo'),
      
      setEarnInfoError: (earnInfoError) => set({ earnInfoError }, false, 'setEarnInfoError'),
    }),
    {
      name: 'bill-scan-store', // Store name for dev tools
    }
  )
);

// Selectors for commonly used state
export const useBillConfig = () => useBillScanStore((state) => ({
  billConfig: state.billConfig,
  isLoadingConfig: state.isLoadingConfig,
  configError: state.configError,
  fetchBillConfig: state.fetchBillConfig,
}));

export const useEarnMethodInfo = () => useBillScanStore((state) => ({
  earnMethodInfo: state.earnMethodInfo,
  isLoadingEarnInfo: state.isLoadingEarnInfo,
  earnInfoError: state.earnInfoError,
  fetchEarnMethodInfo: state.fetchEarnMethodInfo,
}));

export const useBillScanMerchants = () => {
  const store = useBillScanStore((state) => ({
    merchants: state.earnMethodInfo?.subMerchants || [],
    isLoading: state.isLoadingEarnInfo,
    error: state.earnInfoError,
    fetchEarnMethodInfo: state.fetchEarnMethodInfo,
  }));

  // Auto-fetch on mount if not loaded
  useEffect(() => {
    store.fetchEarnMethodInfo();
  }, []);

  return {
    merchants: store.merchants,
    isLoading: store.isLoading,
    error: store.error,
  };
};

export const useBillScanSteps = () => {
  const store = useBillScanStore((state) => ({
    steps: state.earnMethodInfo?.steps || [],
    isLoading: state.isLoadingEarnInfo,
    error: state.earnInfoError,
    fetchEarnMethodInfo: state.fetchEarnMethodInfo,
  }));

  // Auto-fetch on mount if not loaded
  useEffect(() => {
    store.fetchEarnMethodInfo();
  }, []);

  return {
    steps: store.steps,
    isLoading: store.isLoading,
    error: store.error,
  };
};