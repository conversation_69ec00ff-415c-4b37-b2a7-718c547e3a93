import React, { useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore, LoadingSpinner } from '@taptap/shared';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { token, profile } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();
  const hasRedirectedRef = useRef(false);

  // Check if user is authenticated based on token
  const isAuthenticated = !!token;
  
  useEffect(() => {
    // Only redirect once and only if not authenticated
    if (!isAuthenticated && !hasRedirectedRef.current) {
      console.log('ProtectedRoute: User not authenticated, redirecting to login');
      hasRedirectedRef.current = true;
      navigate('/login', { state: { from: location.pathname }, replace: true });
    }
  }, [isAuthenticated, navigate, location.pathname]);

  // If user is authenticated, render protected content
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Show loading while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center">
      <LoadingSpinner />
    </div>
  );
};

export default ProtectedRoute;