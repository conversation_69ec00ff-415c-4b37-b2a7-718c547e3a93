import React from 'react';
import { NavigationTab, HomeIcon, GetPointIcon, GiftExchangeIcon, GamingIcon, AccountIcon, cn } from '@taptap/shared';

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href?: string;
}

interface BottomNavigationProps {
  items?: NavigationItem[];
  activeItemId: string;
  onItemClick: (itemId: string) => void;
  className?: string;
}

// Message icon for inbox/messages
const MessageIcon: React.FC<{ className?: string; active?: boolean }> = ({ className, active = false }) => (
  <svg className={className} width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path 
      d="M4 5.33H20V18.67H4L4 5.33Z" 
      stroke={active ? "#1A1818" : "#9A9A9A"} 
      strokeWidth="1.2"
      fill={active ? "#F7CC15" : "none"}
    />
    <path 
      d="M5.33 8H18.67V16H5.33V8Z" 
      stroke={active ? "#1A1818" : "#9A9A9A"} 
      strokeWidth="1.2"
    />
  </svg>
);

const defaultItems: NavigationItem[] = [
  {
    id: 'home',
    label: 'Trang chủ',
    icon: <HomeIcon className="w-8 h-8" />
  },
  {
    id: 'voucher',
    label: 'Ưu đãi',
    icon: <GetPointIcon className="w-8 h-8" />
  },
  {
    id: 'exchange',
    label: 'Đổi thưởng',
    icon: <GiftExchangeIcon className="w-8 h-8" />
  },
  {
    id: 'games',
    label: 'VUI chơi',
    icon: <GamingIcon className="w-8 h-8" />
  },
  {
    id: 'profile',
    label: 'Tài khoản',
    icon: <AccountIcon className="w-8 h-8" />
  }
];

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  items = defaultItems,
  activeItemId,
  onItemClick,
  className
}) => {
  return (
    <nav 
      className={cn('flex flex-row justify-center bg-white', className)}
      style={{ boxShadow: '0 -5px 24px 0 rgba(26, 24, 24, 0.10)' }}
    >
      {items.map((item) => {
        const isActive = activeItemId === item.id;
        let icon = item.icon;
        
        // Update icons to show active state
        if (item.id === 'home') {
          icon = <HomeIcon className="w-8 h-8" active={isActive} />;
        } else if (item.id === 'voucher') {
          icon = <GetPointIcon className="w-8 h-8" active={isActive} />;
        } else if (item.id === 'exchange') {
          icon = <GiftExchangeIcon className="w-8 h-8" active={isActive} />;
        } else if (item.id === 'games') {
          icon = <GamingIcon className="w-8 h-8" active={isActive} />;
        } else if (item.id === 'profile') {
          icon = <AccountIcon className="w-8 h-8" active={isActive} />;
        }
        
        return (
          <NavigationTab
            key={item.id}
            icon={icon}
            label={item.label}
            isActive={isActive}
            onClick={() => onItemClick(item.id)}
          />
        );
      })}
    </nav>
  );
};

export default BottomNavigation;