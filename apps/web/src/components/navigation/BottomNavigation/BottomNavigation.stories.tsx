import type { Meta, StoryObj } from '@storybook/react';
import { BottomNavigation } from './BottomNavigation';
import { useState } from 'react';

const meta: Meta<typeof BottomNavigation> = {
  title: 'UI/BottomNavigation',
  component: BottomNavigation,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const BottomNavigationWithState = () => {
  const [activeTab, setActiveTab] = useState('home');
  
  return (
    <div className="min-h-screen bg-gray-100 flex flex-col justify-end">
      <BottomNavigation
        activeItemId={activeTab}
        onItemClick={setActiveTab}
      />
    </div>
  );
};

export const Default: Story = {
  render: () => <BottomNavigationWithState />,
};

export const WithHomeActive: Story = {
  args: {
    activeItemId: 'home',
    onItemClick: (id) => console.log('Clicked:', id),
  },
};

export const WithVoucherActive: Story = {
  args: {
    activeItemId: 'voucher',
    onItemClick: (id) => console.log('Clicked:', id),
  },
};

export const WithExchangeActive: Story = {
  args: {
    activeItemId: 'exchange',
    onItemClick: (id) => console.log('Clicked:', id),
  },
};