import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { appPath } from "@taptap/shared";
import { BottomNavigation } from "./navigation";

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();

  const getActiveTab = () => {
    const path = location.pathname;

    // Home tab
    if (path === "/") return "home";
    if (path.startsWith("/search")) return "home";
    if (path.startsWith("/news")) return "home";
    if (path.startsWith("/missions")) return "home";
    if (path.startsWith("/deals")) return "home";
    if (path.startsWith("/merchants")) return "home";
    if (path.startsWith("/flash-sale")) return "home";
    if (path.startsWith("/member-code")) return "home";
    if (path.startsWith("/gift-code")) return "home";
    if (path.startsWith("/qr-payment")) return "home";
    if (path.startsWith("/payment-history")) return "home";
    if (path.startsWith("/bill-scan")) return "home";
    if (path.startsWith("/contact")) return "home";

    // Voucher/Rewards tab - user's rewards and vouchers
    if (path.startsWith("/my-rewards")) return "voucher";
    if (path.startsWith("/membership")) return "voucher";

    // Exchange tab - reward catalog and exchange
    if (path.startsWith("/exchange")) return "exchange";
    if (path.startsWith("/reward/")) return "exchange";
    if (path.startsWith("/rewards/")) return "exchange";

    // Games tab
    if (path.startsWith("/games")) return "games";

    // Profile tab - user account and messages
    if (path.startsWith("/profile")) return "profile";
    if (path.startsWith("/messages")) return "profile";
    if (path.startsWith("/inbox")) return "profile";

    // Default to home for unmatched routes
    return "home";
  };

  const handleTabClick = (tabId: string) => {
    switch (tabId) {
      case "home":
        navigate("/");
        break;
      case "voucher":
        navigate("/my-rewards");
        break;
      case "exchange":
        navigate("/exchange");
        break;
      case "games":
        navigate("/games");
        break;
      case "profile":
        navigate("/profile");
        break;
    }
  };

  // Routes that should hide the bottom navigation
  const routesWithoutBottomNav = [
    "/login",
    "/auth",
    "/bill-scan",
    "/bill-scan/camera",
    "/bill-scan/info",
    "/scan-qr",
    "/scan-qr/instructions",
    "/gift-code",
    "/contact",
    "/inbox",
    "/my-rewards",
  ];
  // Check if current route should hide bottom nav
  const shouldShowBottomNav =
    !routesWithoutBottomNav.includes(location.pathname) &&
    !location.pathname.startsWith("/rewards/") &&
    !location.pathname.startsWith("/games/wheel/") &&
    !location.pathname.startsWith(appPath.merchants()) &&
    !location.pathname.startsWith("/merchant") &&
    !location.pathname.startsWith(appPath.favorites()) &&
    !location.pathname.startsWith("/games/missions/") &&
    !location.pathname.startsWith("/reward/") &&
    !location.pathname.match(/^\/bill-scan\/[a-f0-9]{24}$/) && // Hide bottom nav for bill detail pages
    !location.pathname.match(/^\/inbox\/[a-f0-9]{24}$/) && // Hide bottom nav for inbox detail pages
    !location.pathname.match(/^\/my-rewards\/[^/]+$/) && // Hide bottom nav for my-reward detail pages
    location.pathname !== "/transaction-history"; // Hide bottom nav for transaction history page

  return (
    <div className="max-w-md mx-auto min-h-screen bg-gray-50 flex flex-col">
      {/* Main Content */}
      <main className={`flex-1 ${shouldShowBottomNav ? "pb-20" : ""}`}>
        {children}
      </main>

      {/* Bottom Navigation - Conditionally rendered */}
      {shouldShowBottomNav && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white z-navbar">
          <BottomNavigation
            activeItemId={getActiveTab()}
            onItemClick={handleTabClick}
          />
        </div>
      )}
    </div>
  );
};
