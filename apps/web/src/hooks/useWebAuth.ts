/**
 * Web authentication hook
 * Provides auth utilities with Web-specific features
 */

import { useState, useEffect, useCallback } from 'react';
import { webAuthService, type User, type LoginCredentials } from '../services/webAuth';
import { useWebNavigation } from './useWebNavigation';
import { useAuthStore } from '@taptap/shared';

export function useWebAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { navigateToReturnUrl } = useWebNavigation();
  const { setToken, clearAuth } = useAuthStore();
  
  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      try {
        setLoading(true);
        const currentUser = await webAuthService.getCurrentUser();
        setUser(currentUser);
        
        // Set up auto refresh if user is authenticated
        if (currentUser) {
          webAuthService.setupAutoRefresh();
        }
      } catch (err) {
        console.error('[useWebAuth] Init failed:', err);
        setError('Failed to initialize authentication');
      } finally {
        setLoading(false);
      }
    };
    
    initAuth();
  }, []);
  
  // Login
  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      setLoading(true);
      setError(null);
      
      const userData = await webAuthService.login(credentials);
      setUser(userData);
      
      // Update shared auth store
      if (userData.token) {
        setToken(userData.token);
      }
      
      // Request notification permission after login
      await webAuthService.requestNotificationPermission();
      
      // Navigate to return URL or home
      navigateToReturnUrl('/');
      
      return userData;
    } catch (err: any) {
      const errorMessage = err.message || 'Login failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [setToken, navigateToReturnUrl]);
  
  // Logout
  const logout = useCallback(async () => {
    try {
      setLoading(true);
      await webAuthService.logout();
      setUser(null);
      clearAuth();
    } catch (err) {
      console.error('[useWebAuth] Logout failed:', err);
      // Force logout even on error
      setUser(null);
      clearAuth();
    } finally {
      setLoading(false);
    }
  }, [clearAuth]);
  
  // Check authentication status
  const isAuthenticated = useCallback(() => {
    return webAuthService.isAuthenticated();
  }, []);
  
  // Refresh user data
  const refreshUser = useCallback(async () => {
    try {
      setLoading(true);
      const currentUser = await webAuthService.getCurrentUser();
      setUser(currentUser);
      return currentUser;
    } catch (err) {
      console.error('[useWebAuth] Refresh failed:', err);
      setError('Failed to refresh user data');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Check if user has specific role
  const hasRole = useCallback((role: string): boolean => {
    if (!user) return false;
    return user.roles?.includes(role) || false;
  }, [user]);
  
  // Check if user has specific permission
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user) return false;
    return user.permissions?.includes(permission) || false;
  }, [user]);
  
  // Get user display name
  const getDisplayName = useCallback((): string => {
    if (!user) return 'Guest';
    return user.displayName || user.email || 'User';
  }, [user]);
  
  // Get user avatar URL
  const getAvatarUrl = useCallback((): string => {
    if (!user || !user.avatar) {
      // Return default avatar
      return '/assets/images/default-avatar.png';
    }
    return user.avatar;
  }, [user]);
  
  // Check if user email is verified
  const isEmailVerified = useCallback((): boolean => {
    return user?.emailVerified || false;
  }, [user]);
  
  // Check if user phone is verified
  const isPhoneVerified = useCallback((): boolean => {
    return user?.phoneVerified || false;
  }, [user]);
  
  // Get user tier/level
  const getUserTier = useCallback((): string => {
    return user?.tier || 'SILVER';
  }, [user]);
  
  // Get user points
  const getUserPoints = useCallback((): number => {
    return user?.points || 0;
  }, [user]);
  
  return {
    // State
    user,
    loading,
    error,
    isAuthenticated: isAuthenticated(),
    
    // Actions
    login,
    logout,
    refreshUser,
    clearError,
    
    // User utilities
    hasRole,
    hasPermission,
    getDisplayName,
    getAvatarUrl,
    isEmailVerified,
    isPhoneVerified,
    getUserTier,
    getUserPoints,
  };
}