/**
 * Web navigation hook
 * Provides navigation utilities with React Router integration
 */

import { useNavigate, useLocation, useParams, useSearchParams } from 'react-router-dom';
import { useEffect, useCallback } from 'react';
import { webNavigationService } from '../services/webNavigation';

export function useWebNavigation() {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Initialize navigation service
  useEffect(() => {
    webNavigationService.init(navigate);
  }, [navigate]);
  
  // Navigation methods
  const goBack = useCallback(() => {
    webNavigationService.back();
  }, []);
  
  const goHome = useCallback(() => {
    webNavigationService.home();
  }, []);
  
  const goToLogin = useCallback((returnUrl?: string) => {
    webNavigationService.login(returnUrl || location.pathname);
  }, [location.pathname]);
  
  const goToProfile = useCallback(() => {
    webNavigationService.profile();
  }, []);
  
  const goToGame = useCallback((gameId: string) => {
    webNavigationService.gameDetail(gameId);
  }, []);
  
  const goToReward = useCallback((rewardId: string) => {
    webNavigationService.rewardDetail(rewardId);
  }, []);
  
  const goToMerchant = useCallback((merchantId: string) => {
    webNavigationService.merchantDetail(merchantId);
  }, []);
  
  const goToMembership = useCallback((merchantCode?: string) => {
    webNavigationService.membership(merchantCode);
  }, []);
  
  const goToSearch = useCallback((query?: string) => {
    webNavigationService.search(query);
  }, []);
  
  const goToFlashSale = useCallback(() => {
    webNavigationService.flashSale();
  }, []);
  
  const goToMissions = useCallback(() => {
    webNavigationService.missions();
  }, []);
  
  const goToBillScan = useCallback(() => {
    webNavigationService.billScanCamera();
  }, []);
  
  const goToQRScanner = useCallback(() => {
    webNavigationService.qrScanner();
  }, []);
  
  const goToMyRewards = useCallback(() => {
    webNavigationService.myRewards();
  }, []);
  
  const goToInbox = useCallback((messageId?: string) => {
    webNavigationService.inbox(messageId);
  }, []);
  
  const goToNews = useCallback((newsId?: string) => {
    webNavigationService.news(newsId);
  }, []);
  
  const goToPointHistory = useCallback((type?: 'vui' | 'brand', currencyCode?: string) => {
    webNavigationService.pointHistory(type, currencyCode);
  }, []);
  
  const goToTransactionHistory = useCallback(() => {
    webNavigationService.transactionHistory();
  }, []);
  
  const goToContact = useCallback(() => {
    webNavigationService.contact();
  }, []);
  
  const goToDeals = useCallback(() => {
    webNavigationService.deals();
  }, []);
  
  const openExternal = useCallback((url: string, target?: '_blank' | '_self') => {
    webNavigationService.openExternal(url, target);
  }, []);
  
  const reload = useCallback(() => {
    webNavigationService.reload();
  }, []);
  
  // Query param utilities
  const getQueryParam = useCallback((key: string): string | null => {
    return searchParams.get(key);
  }, [searchParams]);
  
  const setQueryParam = useCallback((key: string, value: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set(key, value);
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);
  
  const removeQueryParam = useCallback((key: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.delete(key);
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);
  
  const clearQueryParams = useCallback(() => {
    setSearchParams(new URLSearchParams());
  }, [setSearchParams]);
  
  // History utilities
  const canGoBack = useCallback(() => {
    return webNavigationService.canGoBack();
  }, []);
  
  const getHistory = useCallback(() => {
    return webNavigationService.getHistory();
  }, []);
  
  const clearHistory = useCallback(() => {
    webNavigationService.clearHistory();
  }, []);
  
  // Browser back button handler
  useEffect(() => {
    const cleanup = webNavigationService.setupBrowserBackButton();
    return cleanup;
  }, []);
  
  // Return URL handling
  const getReturnUrl = useCallback((): string | null => {
    const state = location.state as { returnUrl?: string } | null;
    return state?.returnUrl || null;
  }, [location.state]);
  
  const navigateToReturnUrl = useCallback((defaultPath = '/') => {
    const returnUrl = getReturnUrl();
    navigate(returnUrl || defaultPath, { replace: true });
  }, [getReturnUrl, navigate]);
  
  return {
    // Navigation methods
    navigate,
    goBack,
    goHome,
    goToLogin,
    goToProfile,
    goToGame,
    goToReward,
    goToMerchant,
    goToMembership,
    goToSearch,
    goToFlashSale,
    goToMissions,
    goToBillScan,
    goToQRScanner,
    goToMyRewards,
    goToInbox,
    goToNews,
    goToPointHistory,
    goToTransactionHistory,
    goToContact,
    goToDeals,
    openExternal,
    reload,
    
    // Location info
    location,
    pathname: location.pathname,
    search: location.search,
    hash: location.hash,
    state: location.state,
    params,
    
    // Query params
    searchParams,
    getQueryParam,
    setQueryParam,
    removeQueryParam,
    clearQueryParams,
    
    // History
    canGoBack,
    getHistory,
    clearHistory,
    
    // Return URL
    getReturnUrl,
    navigateToReturnUrl,
  };
}