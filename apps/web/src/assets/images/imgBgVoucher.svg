<svg width="200" height="165" viewBox="0 0 200 165" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="200" height="165" fill="url(#gradient)"/>
  <circle cx="150" cy="82" r="60" fill="white" opacity="0.1"/>
  <circle cx="180" cy="30" r="40" fill="white" opacity="0.08"/>
  <circle cx="170" cy="135" r="35" fill="white" opacity="0.06"/>
  <path d="M20 100 Q 80 80, 140 100 T 200 100" stroke="white" stroke-width="2" opacity="0.1"/>
  <path d="M0 130 Q 60 110, 120 130 T 200 130" stroke="white" stroke-width="2" opacity="0.08"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFE082;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F7CC15;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>