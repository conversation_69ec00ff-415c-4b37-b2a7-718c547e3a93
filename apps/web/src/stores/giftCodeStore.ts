import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { giftCodeApi, type ScanAndEarnResponse } from '@taptap/shared';
import { validateGiftCode, getGiftCodeErrorMessage } from '@taptap/shared';
import { useAuthStore } from '@taptap/shared';

interface GiftCodeState {
  code: string;
  loading: boolean;
  submitting: boolean;
  error: string | null;
  success: boolean;
  description: string;
  loadingDescription: boolean;
  lastSubmittedCode: string | null;
  giftData: any | null;

  // Actions
  setCode: (code: string) => void;
  clearError: () => void;
  reset: () => void;
  fetchDescription: () => Promise<void>;
  submitGiftCode: () => Promise<void>;
}

const DEFAULT_DESCRIPTION = '';

export const useGiftCodeStore = create<GiftCodeState>()(
  devtools(
    (set, get) => ({
      code: '',
      loading: false,
      submitting: false,
      error: null,
      success: false,
      description: DEFAULT_DESCRIPTION,
      loadingDescription: false,
      lastSubmittedCode: null,
      giftData: null,

      setCode: (code: string) => {
        set({ 
          code: code.toUpperCase(), 
          error: null 
        });
      },

      clearError: () => {
        set({ error: null });
      },

      reset: () => {
        set({
          code: '',
          loading: false,
          submitting: false,
          error: null,
          success: false,
          lastSubmittedCode: null,
          giftData: null,
        });
      },

      fetchDescription: async () => {
        set({ loadingDescription: true });
        
        try {
          const response = await giftCodeApi.getGiftCodeDescription('REDEEM_CODE');
          
          if (response?.data?.description) {
            // Parse HTML to extract text content
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = response.data.description;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';
            
            // Clean up the text: remove extra spaces and line breaks
            const cleanedText = textContent
              .replace(/\s+/g, ' ')
              .trim();
            
            set({ description: cleanedText || DEFAULT_DESCRIPTION });
          }
        } catch (error) {
          console.error('Failed to fetch gift code description:', error);
          // Keep default description on error
        } finally {
          set({ loadingDescription: false });
        }
      },

      submitGiftCode: async () => {
        const state = get();
        const { code } = state;

        // Validate code
        const validation = validateGiftCode(code);
        if (!validation.isValid) {
          set({ error: validation.error });
          return;
        }

        // Get mobile number from auth store profile
        const authStore = useAuthStore.getState();
        const mobile = authStore.profile?.mobile || '';

        if (!mobile) {
          set({ error: 'Vui lòng đăng nhập để sử dụng mã đổi quà' });
          return;
        }

        set({ 
          submitting: true, 
          error: null,
          success: false 
        });

        try {
          const response = await giftCodeApi.submitGiftCode({
            mobile,
            voucherCode: code,
          });
          console.log('[submitGiftCode]',response)
          if (response?.status?.success) {
            set({
              success: true,
              lastSubmittedCode: code,
              giftData: response.data,
              submitting: false,
            });

            // Navigate to gift listing page after success
            // This will be handled by the component
          } else {
            // Use message from API if available, otherwise fallback to error code mapping
            const errorMessage = response?.status?.message || getGiftCodeErrorMessage(response?.status?.code);
            set({
              error: errorMessage,
              submitting: false,
            });
          }
        } catch (error: any) {
          console.error('Gift code submission error:', error);
          
          // Handle API error response
          const errorStatus = error?.response?.data?.status;
          // Use message from API if available, otherwise fallback to error code mapping
          const errorMessage = error?.message || 
            (errorStatus?.code ? getGiftCodeErrorMessage(errorStatus.code) : 'Có lỗi xảy ra, vui lòng thử lại');
          
          set({
            error: errorMessage,
            submitting: false,
          });
        }
      },
    }),
    {
      name: 'gift-code-store',
    }
  )
);