import React, { Suspense } from "react";
import { Routes, Route } from "react-router-dom";
import { Layout } from "./components/Layout";
import { LoadingSpinner } from "@taptap/shared";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { ProtectedRoute } from "./components/ProtectedRoute";
import { ScrollToTop } from "./components/ScrollToTop";

// Lazy load pages for better performance
const HomePage = React.lazy(() => import("./pages/home/<USER>"));
const LoginPage = React.lazy(() => import("./pages/auth/LoginPage"));
// const LoginMockPage = React.lazy(() => import("./pages/auth/LoginMockPage"));
import LoginMockPage from "./pages/auth/LoginMockPage";
const GameDetailPage = React.lazy(() => import("./pages/games/GameDetailPage"));
const ProfilePage = React.lazy(() => import("./pages/user/ProfilePage"));
const GamesPage = React.lazy(() => import("./pages/games/GamesPage"));
const GamesHistoryPage = React.lazy(
  () => import("./pages/games/GamesHistoryPage")
);
const MissionHistoryPage = React.lazy(
  () => import("./pages/games/MissionHistoryPage")
);
const CameraPage = React.lazy(() => import("./pages/bill-scan/CameraPage"));
const ExchangePage = React.lazy(
  () => import("./pages/reward-category/ExchangePage")
);
const FlashSalePage = React.lazy(
  () => import("./pages/flash-sale/FlashSalePage")
);
const GiftCodePage = React.lazy(() => import("./pages/gift-code"));
const MemberCodePage = React.lazy(() => import("./pages/user/MemberCode"));
const RewardDetailPage = React.lazy(
  () => import("./pages/reward-detail/RewardDetailPage")
);
const SearchPage = React.lazy(() => import("./pages/search/SearchPage"));
const SearchResultsPage = React.lazy(
  () => import("./pages/search/SearchResultsPage")
);
const QRPaymentPage = React.lazy(
  () => import("./pages/missions/QRPaymentPage")
);
const MerchantDetailPage = React.lazy(
  () => import("./pages/merchant/MerchantDetailPage")
);
const AllRewardsPage = React.lazy(
  () => import("./pages/reward-category/AllRewardsPage")
);
const CategoryDetailPage = React.lazy(
  () => import("./pages/reward-category/CategoryDetailPage")
);
const CategoryCollectionPage = React.lazy(
  () => import("./pages/content/CategoryCollectionPage")
);
const BannerDetailPage = React.lazy(
  () => import("./pages/banner/BannerDetailPage")
);
const MissionsPage = React.lazy(() => import("./pages/missions/MissionsPage"));
const MissionDetailPage = React.lazy(
  () => import("./pages/missions/MissionDetailPage")
);
const NewsListPage = React.lazy(() => import("./pages/news/NewsListPage"));
const NewsDetailPage = React.lazy(() => import("./pages/news/NewsDetailPage"));

// News Route Handler - shows NewsDetailPage if url query exists, otherwise NewsListPage
const NewsRouteHandler: React.FC = () => {
  const location = window.location;
  const searchParams = new URLSearchParams(location.search);
  const urlParam = searchParams.get("url");

  // If URL query parameter exists, show NewsDetailPage
  if (urlParam) {
    return <NewsDetailPage />;
  }

  // Otherwise show NewsListPage
  return <NewsListPage />;
};
const CapturedReceiptsPage = React.lazy(
  () => import("./pages/bill-scan/CapturedReceiptsPage")
);
const InboxListPage = React.lazy(() => import("./pages/inbox/InboxListPage"));
const InboxDetailPage = React.lazy(
  () => import("./pages/inbox/InboxDetailPage")
);
const MyRewardsPage = React.lazy(
  () => import("./pages/my-rewards/MyRewardsPage")
);
const MyRewardDetailPage = React.lazy(
  () => import("./pages/my-rewards/MyRewardDetailPage")
);
const MembershipTierPage = React.lazy(
  () => import("./pages/membership/MembershipTierPage")
);
const MembershipOverview = React.lazy(
  () => import("./pages/membership/MembershipOverview")
);
const MembershipBenefits = React.lazy(
  () => import("./pages/membership/MembershipBenefits")
);
const MembershipDetails = React.lazy(
  () => import("./pages/membership/MembershipDetails")
);
const TierMemberListPage = React.lazy(
  () => import("./pages/membership/TierMemberListPage")
);
const BillListPage = React.lazy(() => import("./pages/bill-scan/BillListPage"));
const BillScanDetailPage = React.lazy(
  () => import("./pages/bill-scan/BillScanDetailPage")
);
const BillScanInfoPage = React.lazy(
  () => import("./pages/bill-scan/BillScanInfoPage")
);
const ContactUsPage = React.lazy(() => import("./pages/contact/ContactUsPage"));
const NotFoundPage = React.lazy(() => import("./pages/errors/NotFoundPage"));
const WheelGamePage = React.lazy(() => import("./pages/games/WheelGamePage"));
const QRScanPage = React.lazy(() => import("./pages/scan-qr"));
const QRScanInstructionPage = React.lazy(
  () => import("./pages/scan-qr/QRScanInstructionPage")
);
const TransactionHistoryPage = React.lazy(
  () => import("./pages/transaction-history/TransactionHistoryPage")
);
const MyPointPage = React.lazy(() => import("./pages/point/MyPointPage"));
const MyVUIPointPage = React.lazy(() => import("./pages/point/MyVUIPointPage"));
const BrandCurrencyDetailPage = React.lazy(
  () => import("./pages/point/BrandCurrencyDetailPage")
);
const MerchantListPage = React.lazy(
  () => import("./pages/merchant/MerchantListPage")
);
const AllStoresPage = React.lazy(
  () => import("./pages/merchant/AllStoresPage")
);
const RewardListPage = React.lazy(
  () => import("./pages/reward-list/RewardListPage")
);

const FavoritesPage = React.lazy(
  () => import("./pages/favorites/FavoritesPage")
);

function App() {
  return (
    <ErrorBoundary>
      <ScrollToTop />
      <Layout>
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <HomePage />
                </ProtectedRoute>
              }
            />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/auth" element={<LoginMockPage />} />
            <Route path="/games" element={<GamesPage />} />
            <Route path="/games/history" element={<GamesHistoryPage />} />
            <Route
              path="/games/missions/history"
              element={<MissionHistoryPage />}
            />
            <Route path="/games/wheel/:id" element={<WheelGamePage />} />
            <Route path="/games/:id" element={<GameDetailPage />} />
            <Route path="/exchange" element={<ExchangePage />} />
            <Route
              path="/flash-sale"
              element={
                <ProtectedRoute>
                  <FlashSalePage />
                </ProtectedRoute>
              }
            />
            <Route path="/gift-code" element={<GiftCodePage />} />
            <Route path="/member-code" element={<MemberCodePage />} />
            <Route path="/reward/:id" element={<RewardDetailPage />} />
            <Route path="/rewards/:id" element={<RewardDetailPage />} />
            <Route path="/search" element={<SearchPage />} />
            <Route path="/search/results" element={<SearchResultsPage />} />
            <Route path="/qr-payment" element={<QRPaymentPage />} />
            <Route
              path="/bill-scan/captured"
              element={<CapturedReceiptsPage />}
            />

            {/* merchant routes */}
            <Route path="/merchants" element={<MerchantListPage />} />
            <Route path="/merchants/:code" element={<MerchantDetailPage />} />
            <Route path="/merchant/:code" element={<MerchantDetailPage />} />
            <Route path="/merchants/:code/stores" element={<AllStoresPage />} />

            <Route path="/favorites" element={<FavoritesPage />} />

            <Route path="/exchange/all-rewards" element={<AllRewardsPage />} />
            <Route
              path="/exchange/super-deals"
              element={<CategoryCollectionPage />}
            />
            <Route
              path="/exchange/category/:category"
              element={<CategoryDetailPage />}
            />
            <Route
              path="/rewards-by-brand/:brandCode"
              element={<RewardListPage />}
            />
            <Route
              path="/exchange/collection/:category"
              element={<CategoryCollectionPage />}
            />
            <Route path="/missions" element={<MissionsPage />} />
            <Route path="/games/missions/:id" element={<MissionDetailPage />} />
            <Route
              path="/news"
              element={
                // Show NewsDetailPage if url query exists, otherwise NewsListPage
                <NewsRouteHandler />
              }
            />
            <Route path="/news/:id" element={<NewsDetailPage />} />
            <Route path="/inbox" element={<InboxListPage />} />
            <Route path="/inbox/:id" element={<InboxDetailPage />} />
            <Route path="/my-rewards" element={<MyRewardsPage />} />
            <Route path="/my-rewards/:id" element={<MyRewardDetailPage />} />
            <Route path="/banner/:bannerId" element={<BannerDetailPage />} />
            <Route path="/membership" element={<MembershipTierPage />} />
            <Route
              path="/membership/tier-list"
              element={<TierMemberListPage />}
            />
            <Route
              path="/membership/details/:merchantCode"
              element={<MembershipDetails />}
            />
            <Route
              path="/membership/:merchantCode"
              element={<MembershipOverview />}
            />
            <Route
              path="/membership/:merchantCode/benefits"
              element={<MembershipBenefits />}
            />
            
            <Route path="/deals/:id" element={<MerchantDetailPage />} />
            <Route path="/flash-sale/:id" element={<MerchantDetailPage />} />
            <Route path="/contact" element={<ContactUsPage />} />

            {/* Bill Scan Route Group */}
            <Route path="/bill-scan" element={<BillListPage />} />
            <Route path="/bill-scan/bills" element={<BillListPage />} />
            <Route path="/bill-scan/camera" element={<CameraPage />} />
            <Route
              path="/bill-scan/captured"
              element={<CapturedReceiptsPage />}
            />
            <Route path="/bill-scan/info" element={<BillScanInfoPage />} />
            <Route path="/bill-scan/:id" element={<BillScanDetailPage />} />

            {/* QR Scanner Routes */}
            <Route path="/scan-qr" element={<QRScanPage />} />
            <Route
              path="/scan-qr/instructions"
              element={<QRScanInstructionPage />}
            />

            <Route path="/profile" element={<ProfilePage />} />
            <Route
              path="/transaction-history"
              element={<TransactionHistoryPage />}
            />

            {/* Point Pages */}
            <Route path="/point" element={<MyPointPage />} />
            <Route path="/point/vui" element={<MyVUIPointPage />} />
            <Route
              path="/point/brand/:currencyCode"
              element={<BrandCurrencyDetailPage />}
            />

            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Suspense>
      </Layout>
    </ErrorBoundary>
  );
}

export default App;
