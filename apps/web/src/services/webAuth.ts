/**
 * Web-specific authentication service
 * Wraps shared auth service with Web-specific features
 */

import { authService as sharedAuthService, type User, type LoginCredentials } from '@taptap/shared';

class WebAuthService {
  /**
   * Web-specific login with browser features
   */
  async login(credentials: LoginCredentials): Promise<User> {
    try {
      // Call shared auth service
      const user = await sharedAuthService.login(credentials);
      
      // Web-specific: Store auth state in sessionStorage for tab persistence
      if (user && user.token) {
        sessionStorage.setItem('taptap_auth_token', user.token);
        sessionStorage.setItem('taptap_auth_user', JSON.stringify(user));
      }
      
      // Web-specific: Set up auto-logout on tab close if remember me is false
      if (!credentials.rememberMe) {
        window.addEventListener('beforeunload', this.handleTabClose);
      }
      
      return user;
    } catch (error) {
      // Web-specific error handling
      console.error('[WebAuth] Login failed:', error);
      
      // Show browser notification if permission granted
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('Login Failed', {
          body: 'Please check your credentials and try again.',
          icon: '/favicon.ico'
        });
      }
      
      throw error;
    }
  }
  
  /**
   * Web-specific logout
   */
  async logout(): Promise<void> {
    try {
      // Clear browser storage
      sessionStorage.removeItem('taptap_auth_token');
      sessionStorage.removeItem('taptap_auth_user');
      localStorage.removeItem('taptap_refresh_token');
      
      // Remove event listeners
      window.removeEventListener('beforeunload', this.handleTabClose);
      
      // Call shared logout
      await sharedAuthService.logout();
      
      // Web-specific: Redirect to login
      window.location.href = '/login';
    } catch (error) {
      console.error('[WebAuth] Logout failed:', error);
      // Force redirect even on error
      window.location.href = '/login';
    }
  }
  
  /**
   * Get current user from browser storage
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      // Try to get from shared service first
      const user = await sharedAuthService.getCurrentUser();
      if (user) return user;
      
      // Fallback to browser storage
      const storedUser = sessionStorage.getItem('taptap_auth_user');
      if (storedUser) {
        return JSON.parse(storedUser);
      }
      
      return null;
    } catch (error) {
      console.error('[WebAuth] Get current user failed:', error);
      return null;
    }
  }
  
  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = sessionStorage.getItem('taptap_auth_token');
    return !!token;
  }
  
  /**
   * Request browser notification permission
   */
  async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('[WebAuth] Browser does not support notifications');
      return false;
    }
    
    if (Notification.permission === 'granted') {
      return true;
    }
    
    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    
    return false;
  }
  
  /**
   * Handle tab close for session management
   */
  private handleTabClose = (): void => {
    // Clear session data if remember me is not enabled
    if (!localStorage.getItem('taptap_remember_me')) {
      sessionStorage.clear();
    }
  };
  
  /**
   * Set up auto token refresh for Web
   */
  setupAutoRefresh(): void {
    // Check token expiry every minute
    setInterval(async () => {
      const token = sessionStorage.getItem('taptap_auth_token');
      if (!token) return;
      
      try {
        // Check if token is about to expire (within 5 minutes)
        const tokenPayload = this.parseJwt(token);
        const expiryTime = tokenPayload.exp * 1000;
        const currentTime = Date.now();
        const timeUntilExpiry = expiryTime - currentTime;
        
        if (timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0) {
          // Refresh token
          await sharedAuthService.refreshToken();
        }
      } catch (error) {
        console.error('[WebAuth] Auto refresh failed:', error);
      }
    }, 60000); // Check every minute
  }
  
  /**
   * Parse JWT token
   */
  private parseJwt(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('[WebAuth] Failed to parse JWT:', error);
      return {};
    }
  }
}

// Export singleton instance
export const webAuthService = new WebAuthService();

// Export types from shared
export type { User, LoginCredentials } from '@taptap/shared';