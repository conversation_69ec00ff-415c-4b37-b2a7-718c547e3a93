/**
 * Web-specific API service
 * Provides API utilities with caching and offline support
 */

import { getHttpClient } from '@taptap/shared';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

class WebApiService {
  private cache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, Promise<any>>();
  private offlineQueue: Array<() => Promise<any>> = [];
  private isOnline = navigator.onLine;
  
  constructor() {
    this.setupNetworkListeners();
    this.setupPeriodicCacheCleanup();
  }
  
  /**
   * GET request with caching
   */
  async get<T>(
    url: string,
    options?: {
      cache?: boolean;
      cacheTime?: number; // in minutes
      forceRefresh?: boolean;
    }
  ): Promise<T> {
    const cacheKey = `GET:${url}`;
    const shouldCache = options?.cache !== false;
    const cacheTime = options?.cacheTime || 5; // Default 5 minutes
    
    // Check cache first
    if (shouldCache && !options?.forceRefresh) {
      const cached = this.getFromCache<T>(cacheKey);
      if (cached) {
        console.log(`[WebApi] Cache hit for ${url}`);
        return cached;
      }
    }
    
    // Check if request is already pending (deduplication)
    const pending = this.pendingRequests.get(cacheKey);
    if (pending) {
      console.log(`[WebApi] Reusing pending request for ${url}`);
      return pending;
    }
    
    // Make request
    const request = this.makeRequest<T>(async () => {
      const httpClient = getHttpClient();
      return await httpClient.get(url);
    });
    
    // Store as pending
    this.pendingRequests.set(cacheKey, request);
    
    try {
      const data = await request;
      
      // Cache the result
      if (shouldCache) {
        this.setCache(cacheKey, data, cacheTime);
      }
      
      return data;
    } finally {
      // Clear pending request
      this.pendingRequests.delete(cacheKey);
    }
  }
  
  /**
   * POST request with offline queue
   */
  async post<T>(
    url: string,
    data?: any,
    options?: {
      queue?: boolean; // Queue if offline
    }
  ): Promise<T> {
    if (!this.isOnline && options?.queue) {
      return this.queueRequest(() => this.post<T>(url, data, { queue: false }));
    }
    
    return this.makeRequest<T>(async () => {
      const httpClient = getHttpClient();
      return await httpClient.post(url, data);
    });
  }
  
  /**
   * PUT request with offline queue
   */
  async put<T>(
    url: string,
    data?: any,
    options?: {
      queue?: boolean;
    }
  ): Promise<T> {
    if (!this.isOnline && options?.queue) {
      return this.queueRequest(() => this.put<T>(url, data, { queue: false }));
    }
    
    return this.makeRequest<T>(async () => {
      const httpClient = getHttpClient();
      return await httpClient.put(url, data);
    });
  }
  
  /**
   * DELETE request
   */
  async delete<T>(url: string): Promise<T> {
    // Clear related cache
    this.clearCacheByPattern(`GET:${url}`);
    
    return this.makeRequest<T>(async () => {
      const httpClient = getHttpClient();
      return await httpClient.delete(url);
    });
  }
  
  /**
   * PATCH request
   */
  async patch<T>(url: string, data?: any): Promise<T> {
    // Clear related cache
    this.clearCacheByPattern(`GET:${url}`);
    
    return this.makeRequest<T>(async () => {
      const httpClient = getHttpClient();
      return await httpClient.patch(url, data);
    });
  }
  
  /**
   * Upload file with progress
   */
  async uploadFile(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);
    
    const httpClient = getHttpClient();
    
    return httpClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }
  
  /**
   * Download file
   */
  async downloadFile(url: string, filename?: string): Promise<void> {
    const httpClient = getHttpClient();
    
    const response = await httpClient.get(url, {
      responseType: 'blob',
    });
    
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }
  
  /**
   * Make request with error handling
   */
  private async makeRequest<T>(request: () => Promise<T>): Promise<T> {
    try {
      return await request();
    } catch (error: any) {
      // Handle network errors
      if (!this.isOnline) {
        throw new Error('No internet connection. Please check your network.');
      }
      
      // Handle specific error codes
      if (error.response) {
        switch (error.response.status) {
          case 401:
            // Unauthorized - redirect to login
            window.location.href = '/login';
            throw new Error('Session expired. Please login again.');
          
          case 403:
            throw new Error('You do not have permission to perform this action.');
          
          case 404:
            throw new Error('The requested resource was not found.');
          
          case 429:
            throw new Error('Too many requests. Please try again later.');
          
          case 500:
            throw new Error('Server error. Please try again later.');
          
          default:
            throw error;
        }
      }
      
      throw error;
    }
  }
  
  /**
   * Get from cache
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }
  
  /**
   * Set cache
   */
  private setCache<T>(key: string, data: T, cacheTimeMinutes: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + (cacheTimeMinutes * 60 * 1000),
    };
    
    this.cache.set(key, entry);
  }
  
  /**
   * Clear cache by pattern
   */
  private clearCacheByPattern(pattern: string): void {
    const keys = Array.from(this.cache.keys());
    keys.forEach(key => {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    });
  }
  
  /**
   * Clear all cache
   */
  clearCache(): void {
    this.cache.clear();
    console.log('[WebApi] Cache cleared');
  }
  
  /**
   * Queue request for offline execution
   */
  private async queueRequest<T>(request: () => Promise<T>): Promise<T> {
    console.log('[WebApi] Request queued for offline execution');
    
    return new Promise((resolve, reject) => {
      this.offlineQueue.push(async () => {
        try {
          const result = await request();
          resolve(result);
          return result;
        } catch (error) {
          reject(error);
          throw error;
        }
      });
    });
  }
  
  /**
   * Process offline queue
   */
  private async processOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) return;
    
    console.log(`[WebApi] Processing ${this.offlineQueue.length} queued requests`);
    
    const queue = [...this.offlineQueue];
    this.offlineQueue = [];
    
    for (const request of queue) {
      try {
        await request();
      } catch (error) {
        console.error('[WebApi] Failed to process queued request:', error);
      }
    }
  }
  
  /**
   * Setup network listeners
   */
  private setupNetworkListeners(): void {
    window.addEventListener('online', () => {
      console.log('[WebApi] Network online');
      this.isOnline = true;
      this.processOfflineQueue();
    });
    
    window.addEventListener('offline', () => {
      console.log('[WebApi] Network offline');
      this.isOnline = false;
    });
  }
  
  /**
   * Setup periodic cache cleanup
   */
  private setupPeriodicCacheCleanup(): void {
    // Clean expired cache every 5 minutes
    setInterval(() => {
      const now = Date.now();
      const keys = Array.from(this.cache.keys());
      
      keys.forEach(key => {
        const entry = this.cache.get(key);
        if (entry && now > entry.expiry) {
          this.cache.delete(key);
        }
      });
    }, 5 * 60 * 1000);
  }
  
  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    entries: Array<{ key: string; size: number; age: number }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      size: JSON.stringify(entry.data).length,
      age: Date.now() - entry.timestamp,
    }));
    
    const totalSize = entries.reduce((sum, entry) => sum + entry.size, 0);
    
    return {
      size: totalSize,
      entries,
    };
  }
}

// Export singleton instance
export const webApiService = new WebApiService();