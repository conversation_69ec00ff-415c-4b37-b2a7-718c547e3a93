/**
 * Web-specific storage service
 * Provides localStorage and sessionStorage utilities with fallbacks
 */

class WebStorageService {
  private prefix = 'taptap_';
  
  /**
   * Set item in localStorage with JSON serialization
   */
  setLocal<T>(key: string, value: T, expiryMinutes?: number): void {
    try {
      const prefixedKey = this.prefix + key;
      const data = {
        value,
        timestamp: Date.now(),
        expiry: expiryMinutes ? Date.now() + (expiryMinutes * 60 * 1000) : null
      };
      localStorage.setItem(prefixedKey, JSON.stringify(data));
    } catch (error) {
      console.error('[WebStorage] Failed to set localStorage:', error);
      // Fallback to memory storage if localStorage is full
      this.memoryStorage.set(key, value);
    }
  }
  
  /**
   * Get item from localStorage with expiry check
   */
  getLocal<T>(key: string): T | null {
    try {
      const prefixedKey = this.prefix + key;
      const stored = localStorage.getItem(prefixedKey);
      
      if (!stored) {
        // Check memory fallback
        return this.memoryStorage.get(key) || null;
      }
      
      const data = JSON.parse(stored);
      
      // Check expiry
      if (data.expiry && Date.now() > data.expiry) {
        localStorage.removeItem(prefixedKey);
        return null;
      }
      
      return data.value;
    } catch (error) {
      console.error('[WebStorage] Failed to get from localStorage:', error);
      return this.memoryStorage.get(key) || null;
    }
  }
  
  /**
   * Remove item from localStorage
   */
  removeLocal(key: string): void {
    const prefixedKey = this.prefix + key;
    localStorage.removeItem(prefixedKey);
    this.memoryStorage.delete(key);
  }
  
  /**
   * Clear all TapTap items from localStorage
   */
  clearLocal(): void {
    const keysToRemove: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key));
    this.memoryStorage.clear();
  }
  
  /**
   * Set item in sessionStorage
   */
  setSession<T>(key: string, value: T): void {
    try {
      const prefixedKey = this.prefix + key;
      sessionStorage.setItem(prefixedKey, JSON.stringify(value));
    } catch (error) {
      console.error('[WebStorage] Failed to set sessionStorage:', error);
      this.memoryStorage.set(key, value);
    }
  }
  
  /**
   * Get item from sessionStorage
   */
  getSession<T>(key: string): T | null {
    try {
      const prefixedKey = this.prefix + key;
      const stored = sessionStorage.getItem(prefixedKey);
      
      if (!stored) {
        return this.memoryStorage.get(key) || null;
      }
      
      return JSON.parse(stored);
    } catch (error) {
      console.error('[WebStorage] Failed to get from sessionStorage:', error);
      return this.memoryStorage.get(key) || null;
    }
  }
  
  /**
   * Remove item from sessionStorage
   */
  removeSession(key: string): void {
    const prefixedKey = this.prefix + key;
    sessionStorage.removeItem(prefixedKey);
    this.memoryStorage.delete(key);
  }
  
  /**
   * Clear all TapTap items from sessionStorage
   */
  clearSession(): void {
    const keysToRemove: string[] = [];
    
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => sessionStorage.removeItem(key));
  }
  
  /**
   * Get storage size info
   */
  async getStorageInfo(): Promise<{
    used: number;
    quota: number;
    percentage: number;
  }> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      const used = estimate.usage || 0;
      const quota = estimate.quota || 0;
      const percentage = quota > 0 ? (used / quota) * 100 : 0;
      
      return { used, quota, percentage };
    }
    
    // Fallback calculation
    let totalSize = 0;
    for (const key in localStorage) {
      if (key.startsWith(this.prefix)) {
        totalSize += localStorage[key].length + key.length;
      }
    }
    
    return {
      used: totalSize,
      quota: 5 * 1024 * 1024, // Assume 5MB limit
      percentage: (totalSize / (5 * 1024 * 1024)) * 100
    };
  }
  
  /**
   * Check if storage is available
   */
  isStorageAvailable(type: 'localStorage' | 'sessionStorage'): boolean {
    try {
      const storage = window[type];
      const testKey = '__storage_test__';
      storage.setItem(testKey, 'test');
      storage.removeItem(testKey);
      return true;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Memory storage fallback for when localStorage/sessionStorage is unavailable
   */
  private memoryStorage = new Map<string, any>();
  
  /**
   * Export data for backup
   */
  exportData(): string {
    const data: Record<string, any> = {};
    
    // Export localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.prefix)) {
        const value = localStorage.getItem(key);
        if (value) {
          data[key] = JSON.parse(value);
        }
      }
    }
    
    return JSON.stringify(data, null, 2);
  }
  
  /**
   * Import data from backup
   */
  importData(jsonData: string): void {
    try {
      const data = JSON.parse(jsonData);
      
      Object.entries(data).forEach(([key, value]) => {
        if (key.startsWith(this.prefix)) {
          localStorage.setItem(key, JSON.stringify(value));
        }
      });
    } catch (error) {
      console.error('[WebStorage] Failed to import data:', error);
      throw new Error('Invalid backup data format');
    }
  }
  
  /**
   * Set up storage event listener for cross-tab communication
   */
  onStorageChange(callback: (key: string, newValue: any, oldValue: any) => void): () => void {
    const handler = (event: StorageEvent) => {
      if (event.key && event.key.startsWith(this.prefix)) {
        const key = event.key.replace(this.prefix, '');
        const newValue = event.newValue ? JSON.parse(event.newValue) : null;
        const oldValue = event.oldValue ? JSON.parse(event.oldValue) : null;
        callback(key, newValue, oldValue);
      }
    };
    
    window.addEventListener('storage', handler);
    
    // Return cleanup function
    return () => window.removeEventListener('storage', handler);
  }
}

// Export singleton instance
export const webStorageService = new WebStorageService();