/**
 * Web-specific navigation service
 * Provides navigation utilities and route management
 */

import { NavigateFunction } from 'react-router-dom';

class WebNavigationService {
  private navigate: NavigateFunction | null = null;
  private history: string[] = [];
  private maxHistorySize = 50;
  
  /**
   * Initialize navigation with React Router's navigate function
   */
  init(navigate: NavigateFunction): void {
    this.navigate = navigate;
  }
  
  /**
   * Navigate to a route
   */
  to(path: string, options?: { replace?: boolean; state?: any }): void {
    if (!this.navigate) {
      console.error('[WebNavigation] Navigation not initialized');
      return;
    }
    
    // Add to history
    if (!options?.replace) {
      this.addToHistory(path);
    }
    
    this.navigate(path, options);
  }
  
  /**
   * Go back in history
   */
  back(): void {
    if (!this.navigate) {
      console.error('[WebNavigation] Navigation not initialized');
      return;
    }
    
    if (this.history.length > 1) {
      this.history.pop(); // Remove current
      const previous = this.history[this.history.length - 1];
      this.navigate(previous, { replace: true });
    } else {
      // Fallback to home if no history
      this.navigate('/', { replace: true });
    }
  }
  
  /**
   * Navigate to home
   */
  home(): void {
    this.to('/', { replace: true });
  }
  
  /**
   * Navigate to login
   */
  login(returnUrl?: string): void {
    const state = returnUrl ? { returnUrl } : undefined;
    this.to('/login', { state });
  }
  
  /**
   * Navigate to profile
   */
  profile(): void {
    this.to('/profile');
  }
  
  /**
   * Navigate to game detail
   */
  gameDetail(gameId: string): void {
    this.to(`/games/${gameId}`);
  }
  
  /**
   * Navigate to reward detail
   */
  rewardDetail(rewardId: string): void {
    this.to(`/rewards/${rewardId}`);
  }
  
  /**
   * Navigate to merchant detail
   */
  merchantDetail(merchantId: string): void {
    this.to(`/merchants/${merchantId}`);
  }
  
  /**
   * Navigate to membership
   */
  membership(merchantCode?: string): void {
    if (merchantCode) {
      this.to(`/membership/${merchantCode}`);
    } else {
      this.to('/membership');
    }
  }
  
  /**
   * Navigate to search with query
   */
  search(query?: string): void {
    if (query) {
      this.to(`/search/results?q=${encodeURIComponent(query)}`);
    } else {
      this.to('/search');
    }
  }
  
  /**
   * Navigate to exchange category
   */
  exchangeCategory(category: string): void {
    this.to(`/exchange/category/${category}`);
  }
  
  /**
   * Navigate to flash sale
   */
  flashSale(): void {
    this.to('/flash-sale');
  }
  
  /**
   * Navigate to missions
   */
  missions(): void {
    this.to('/missions');
  }
  
  /**
   * Navigate to bill scan camera
   */
  billScanCamera(): void {
    this.to('/bill-scan/camera');
  }
  
  /**
   * Navigate to QR scanner
   */
  qrScanner(): void {
    this.to('/scan-qr');
  }
  
  /**
   * Navigate to my rewards
   */
  myRewards(): void {
    this.to('/my-rewards');
  }
  
  /**
   * Navigate to inbox
   */
  inbox(messageId?: string): void {
    if (messageId) {
      this.to(`/inbox/${messageId}`);
    } else {
      this.to('/inbox');
    }
  }
  
  /**
   * Navigate to news
   */
  news(newsId?: string): void {
    if (newsId) {
      this.to(`/news/${newsId}`);
    } else {
      this.to('/news');
    }
  }
  
  /**
   * Navigate to point history
   */
  pointHistory(type?: 'vui' | 'brand', currencyCode?: string): void {
    if (type === 'vui') {
      this.to('/point/vui');
    } else if (type === 'brand' && currencyCode) {
      this.to(`/point/brand/${currencyCode}`);
    } else {
      this.to('/point');
    }
  }
  
  /**
   * Navigate to transaction history
   */
  transactionHistory(): void {
    this.to('/transaction-history');
  }
  
  /**
   * Navigate to contact us
   */
  contact(): void {
    this.to('/contact');
  }
  
  /**
   * Navigate to deals
   */
  deals(): void {
    this.to('/deals');
  }
  
  /**
   * Open external URL
   */
  openExternal(url: string, target: '_blank' | '_self' = '_blank'): void {
    window.open(url, target);
  }
  
  /**
   * Get current path
   */
  getCurrentPath(): string {
    return window.location.pathname;
  }
  
  /**
   * Get navigation history
   */
  getHistory(): string[] {
    return [...this.history];
  }
  
  /**
   * Clear navigation history
   */
  clearHistory(): void {
    this.history = [this.getCurrentPath()];
  }
  
  /**
   * Add to history
   */
  private addToHistory(path: string): void {
    this.history.push(path);
    
    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize);
    }
  }
  
  /**
   * Check if can go back
   */
  canGoBack(): boolean {
    return this.history.length > 1;
  }
  
  /**
   * Set up browser back button handler
   */
  setupBrowserBackButton(handler?: () => boolean): () => void {
    const handlePopState = (event: PopStateEvent) => {
      // Allow custom handler to prevent default behavior
      if (handler && !handler()) {
        event.preventDefault();
        return;
      }
      
      // Update history based on browser navigation
      const currentPath = this.getCurrentPath();
      const lastPath = this.history[this.history.length - 1];
      
      if (currentPath !== lastPath) {
        this.addToHistory(currentPath);
      }
    };
    
    window.addEventListener('popstate', handlePopState);
    
    // Return cleanup function
    return () => window.removeEventListener('popstate', handlePopState);
  }
  
  /**
   * Reload current page
   */
  reload(): void {
    window.location.reload();
  }
  
  /**
   * Replace current URL without navigation
   */
  replaceUrl(path: string): void {
    window.history.replaceState(null, '', path);
  }
}

// Export singleton instance
export const webNavigationService = new WebNavigationService();