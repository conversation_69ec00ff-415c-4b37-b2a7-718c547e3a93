import React from 'react';

export const EmptyState: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-6 bg-white">
      {/* Mascot/Illustration placeholder */}
      <div className="w-32 h-32 rounded-full bg-gradient-to-br from-[#F65D79]/20 to-[#FF7425]/20 flex items-center justify-center mb-6">
        <div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#F65D79]/40 to-[#FF7425]/40 flex items-center justify-center">
          <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
            <path 
              d="M9 5H7C5.89543 5 5 5.89543 5 7V18C5 19.1046 5.89543 20 7 20H17C18.1046 20 19 19.1046 19 18V7C19 5.89543 18.1046 5 17 5H15" 
              stroke="#F65D79" 
              strokeWidth="2" 
              strokeLinecap="round"
            />
            <path 
              d="M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5V6H9V5Z" 
              stroke="#F65D79" 
              strokeWidth="2"
            />
            <path 
              d="M9 12H15" 
              stroke="#F65D79" 
              strokeWidth="2" 
              strokeLinecap="round"
            />
            <path 
              d="M9 16H12" 
              stroke="#F65D79" 
              strokeWidth="2" 
              strokeLinecap="round"
            />
          </svg>
        </div>
      </div>
      
      <h3 
        className="text-base font-bold text-[#1A1818] mb-2"
        style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
      >
        Chưa có lịch sử giao dịch
      </h3>
      <p 
        className="text-sm text-[#9A9A9A] text-center max-w-xs"
        style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
      >
        Các giao dịch tích điểm và đổi thưởng của bạn sẽ hiển thị tại đây
      </p>
    </div>
  );
};