import React from 'react';
import { IHistoryPoint } from '../hooks/useTransactionHistory';
import moment from 'moment';
import 'moment/locale/vi';
import SolidCoinIcon from '@shared/assets/icons/solid-coin.svg';
import IncreaseIcon from '@shared/assets/icons/increase-icon.svg';
import DecreaseIcon from '@shared/assets/icons/decrease-icon.svg';
import VUIIcon from '@shared/assets/icons/vui.png';

moment.locale('vi');

interface TransactionItemProps {
  transaction: IHistoryPoint;
  isLast?: boolean;
}

export const TransactionItem: React.FC<TransactionItemProps> = ({ transaction, isLast = false }) => {
  const isIncrease = transaction.adjustedType === 'ISSUE';
  const pointFormatter = new Intl.NumberFormat('vi-VN').format(Math.abs(transaction.adjustedPoint));
  const displayPoint = isIncrease ? `+ ${pointFormatter}` : `- ${pointFormatter}`;
  
  const formatDateTime = (dateString: string) => {
    return moment(dateString).format('HH:mm • DD.MM.YYYY');
  };

  const getPointColor = () => {
    return isIncrease ? '#0DC98B' : '#F65D79';
  };

  const renderCurrencyLogo = () => {
    if (transaction.currencyCode === 'VUI') {
      return <VUILogo />;
    }
    if (transaction.logo) {
      return (
        <img 
          src={transaction.logo} 
          alt={transaction.currencyCode}
          className="w-4 h-4 rounded-full"
        />
      );
    }
    return null;
  };

  return (
    <div 
      className={`flex items-center px-4 py-4 bg-white ${!isLast ? 'border-b border-[#F0F0F0]' : ''}`}
    >
      {/* Icon Container */}
      <div className="relative w-9 h-9 flex items-center justify-center">
        <img src={SolidCoinIcon} alt="Coin" className="w-6 h-6" />
        <div className="absolute -bottom-0.5 -right-0.5">
          <img 
            src={isIncrease ? IncreaseIcon : DecreaseIcon} 
            alt={isIncrease ? "Increase" : "Decrease"}
            className="w-[18px] h-[18px]"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 ml-2 mr-6">
        <p 
          className="text-sm text-[#1A1818] leading-5 line-clamp-2"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          {transaction.description?.note || 'Giao dịch'}
        </p>
      </div>

      {/* Points and Date */}
      <div className="flex flex-col items-end">
        <div className="flex items-center gap-1.5">
          <span 
            className="text-sm font-bold"
            style={{ 
              color: getPointColor(),
              fontFamily: 'Archia, system-ui, sans-serif'
            }}
          >
            {displayPoint}
          </span>
          {renderCurrencyLogo()}
        </div>
        <p 
          className="text-xs text-[#9A9A9A] mt-1"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          {formatDateTime(transaction.createdAt)}
        </p>
      </div>
    </div>
  );
};

const VUILogo: React.FC = () => (
  <img src={VUIIcon} alt="VUI" className="w-4 h-4" />
);