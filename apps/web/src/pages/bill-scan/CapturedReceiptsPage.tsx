import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  NavigationHeader,
  Card,
  Button,
  BackIcon,
} from '@taptap/shared';

interface CapturedReceipt {
  id: string;
  storeName: string;
  amount: number;
  date: string;
  status: 'processing' | 'approved' | 'rejected';
  points?: number;
  imageUrl?: string;
}

export const CapturedReceiptsPage: React.FC = () => {
  const navigate = useNavigate();
  const [receipts] = useState<CapturedReceipt[]>([
    {
      id: '1',
      storeName: 'Rosie Vintage',
      amount: 450000,
      date: '2024-01-15',
      status: 'approved',
      points: 450,
      imageUrl: '/images/receipt-1.jpg',
    },
    {
      id: '2',
      storeName: 'Mori Boutique',
      amount: 320000,
      date: '2024-01-14',
      status: 'processing',
      imageUrl: '/images/receipt-2.jpg',
    },
    {
      id: '3',
      storeName: 'Dashi Kitchen',
      amount: 250000,
      date: '2024-01-13',
      status: 'approved',
      points: 250,
      imageUrl: '/images/receipt-3.jpg',
    },
    {
      id: '4',
      storeName: 'Coffee House',
      amount: 85000,
      date: '2024-01-12',
      status: 'rejected',
      imageUrl: '/images/receipt-4.jpg',
    },
  ]);

  const handleReceiptClick = (receiptId: string) => {
    console.log('Clicked receipt:', receiptId);
    navigate(`/bill-scan/${receiptId}`);
  };

  const handleCaptureNew = () => {
    navigate('/bill-scan/camera');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600';
      case 'processing':
        return 'text-yellow-600';
      case 'rejected':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Đã duyệt';
      case 'processing':
        return 'Đang xử lý';
      case 'rejected':
        return 'Bị từ chối';
      default:
        return 'Không xác định';
    }
  };


  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <NavigationHeader
        title="Hóa đơn đã chụp"
        leftIcon={<BackIcon />}
        onLeftClick={() => navigate(-1)}
      />

      {/* Stats */}
      <div className="p-4">
        <div className="grid grid-cols-3 gap-3 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-3 text-center">
            <div className="text-xl font-bold text-green-600">
              {receipts.filter(r => r.status === 'approved').length}
            </div>
            <div className="text-xs text-gray-600">Đã duyệt</div>
          </div>
          <div className="bg-white rounded-lg shadow-sm p-3 text-center">
            <div className="text-xl font-bold text-yellow-600">
              {receipts.filter(r => r.status === 'processing').length}
            </div>
            <div className="text-xs text-gray-600">Đang xử lý</div>
          </div>
          <div className="bg-white rounded-lg shadow-sm p-3 text-center">
            <div className="text-xl font-bold text-[#F65D79]">
              {receipts.reduce((total, r) => total + (r.points || 0), 0)}
            </div>
            <div className="text-xs text-gray-600">Tổng điểm</div>
          </div>
        </div>

        {/* Capture New Receipt Button */}
        <Button
          onClick={handleCaptureNew}
          className="w-full mb-6"
          variant="primary"
        >
          Chụp hóa đơn mới
        </Button>
      </div>

      {/* Receipts List */}
      <div className="px-4">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">
          Danh sách hóa đơn
        </h2>
        
        {receipts.length === 0 ? (
          <Card className="p-6 text-center">
            <div className="text-gray-500 mb-4">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-sm">Chưa có hóa đơn nào được chụp</p>
            </div>
            <Button onClick={handleCaptureNew} variant="primary">
              Chụp hóa đơn đầu tiên
            </Button>
          </Card>
        ) : (
          <div className="space-y-4">
            {receipts.map((receipt) => (
              <Card
                key={receipt.id}
                className="p-4 cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleReceiptClick(receipt.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-800">
                        {receipt.storeName}
                      </h3>
                      <span className={`text-sm font-medium ${getStatusColor(receipt.status)}`}>
                        {getStatusText(receipt.status)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-600">
                        {receipt.amount.toLocaleString('vi-VN')} VND
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(receipt.date).toLocaleDateString('vi-VN')}
                      </p>
                    </div>
                    {receipt.points && (
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          +{receipt.points} điểm
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="ml-4">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default CapturedReceiptsPage;