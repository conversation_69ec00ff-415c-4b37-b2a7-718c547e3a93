import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  CameraInterface, 
  NavigationHeader, 
  CameraSuccessState,
  CameraPreviewStep,
  LoadingSpinner,
  Toast,
  BackIcon,
  useCameraCapture,
} from '@taptap/shared';

export const CameraPage: React.FC = () => {
  const navigate = useNavigate();

  // Use extracted hook for camera capture functionality
  const {
    state,
    capturedPhoto,
    isProcessing,
    processingStatus,
    userStatus,
    loadingUserStatus,
    handleCapture,
    handleToggleFlash,
    handleCameraError,
    handleShowInfo,
    handleShowCapturedReceipts,
    handleBack,
    handleComplete,
    handleGoHome,
    remainingSnaps,
    previewUserStatus,
    toast,
    hideToast,
  } = useCameraCapture({
    onNavigate: (path: string) => navigate(path),
    onNavigateBack: (steps = 1) => navigate(-steps),
  });

  return (
    <div className="w-full h-screen relative overflow-hidden" style={{ backgroundColor: state === 'success' ? '#EFF3F6' : 'black' }}>
      {/* Navigation Header */}
      <div className="absolute top-0 left-0 right-0 z-10">
        <NavigationHeader
          title="Chụp hóa đơn"
          leftIcon={<BackIcon />}
          onLeftClick={handleBack}
          variant="ZaloMiniApp"
          className={state === 'success' ? 'bg-white border-b border-[#ECECEC]' : 'bg-transparent border-none'}
          textClassName={state === 'success' ? 'text-[#1A1818]' : 'text-white'}
        />
      </div>

      {/* Main Content */}
      {state === 'camera' && (
        <>
          {/* Camera Interface with Carousel Dots */}
          <div className="relative w-full h-full">
            <CameraInterface
              onCapture={handleCapture}
              onToggleFlash={handleToggleFlash}
              onShowInfo={handleShowInfo}
              onShowCapturedReceipts={handleShowCapturedReceipts}
              onError={handleCameraError}
              capturedCount={0}
              className="w-full h-full"
              facingMode="environment"
              currentStep={1}
              snapPerDay={userStatus?.snapPerDay || 0}
            />
          </div>
        </>
      )}

      {state === 'preview' && capturedPhoto && (
        <CameraPreviewStep
          capturedPhoto={capturedPhoto}
          remainingSnaps={remainingSnaps}
          userStatus={previewUserStatus}
          onBack={handleBack}
          onComplete={handleComplete}
        />
      )}

      {state === 'success' && (
        <div className="absolute top-[92px] left-0 right-0 bottom-0">
          <CameraSuccessState
            onGoHome={handleGoHome}
            onViewCapturedReceipts={handleShowCapturedReceipts}
          />
        </div>
      )}

      {/* Processing Overlay */}
      {isProcessing && (
        <div className="absolute inset-0 bg-black/70 flex items-center justify-center z-20">
          <div className="bg-white rounded-xl p-6 mx-4 text-center max-w-sm">
            <div className="mb-4">
              <LoadingSpinner />
            </div>
            <div className="text-gray-800 font-semibold text-lg mb-2">
              Đang xử lý hóa đơn
            </div>
            <div className="text-gray-600 text-sm leading-relaxed">
              {processingStatus || 'Vui lòng đợi trong giây lát...'}
            </div>
            <div className="mt-4 bg-gray-100 rounded-lg p-3">
              <div className="text-xs text-gray-500">
                💡 Quá trình này có thể mất 10-30 giây
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Toast notifications */}
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default CameraPage;