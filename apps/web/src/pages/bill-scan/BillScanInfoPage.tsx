import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { NavigationHeader, Button, ScrollableTabContainer, type ScrollableTabItem, MerchantListCard, formatBillScanEarnRate, BackIcon } from '@taptap/shared';
import { useBillConfig, useBillScanMerchants, useBillScanSteps } from '../../store/billScanStore';
import billOCRGuide from '../../assets/images/bill-ocr-guide-6e297c.png';

type InfoTab = 'thuong-hieu' | 'huong-dan' | 'gioi-han';

export const BillScanInfoPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<InfoTab>('thuong-hieu');
  const [showAllMerchants, setShowAllMerchants] = useState(false);
  const { billConfig, fetchBillConfig } = useBillConfig();
  const { merchants, isLoading: isMerchantsLoading } = useBillScanMerchants();
  const { steps } = useBillScanSteps();

  useEffect(() => {
    fetchBillConfig();
  }, [fetchBillConfig]);


  const handleBack = () => {
    navigate(-1);
  };

  const handleStartCapture = () => {
    navigate('/bill-scan/camera');
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId as InfoTab);
  };

  const handleViewAllMerchants = () => {
    setShowAllMerchants(!showAllMerchants);
  };

  // Display only first 6 merchants initially, or all if showAllMerchants is true
  const displayedMerchants = showAllMerchants ? merchants : merchants.slice(0, 6);

  // Create tabs with content
  const tabs: ScrollableTabItem[] = [
    {
      id: 'thuong-hieu',
      label: 'Thương hiệu',
      content: (
        <div className="mb-4">
          <div className="bg-white">
            {/* Section Header */}
            <div className="px-4 py-3 border-b border-[#ECECEC]">
              <h2 className="text-[#1A1818] font-bold text-base" style={{ fontFamily: 'Archia' }}>
                Thương hiệu
              </h2>
            </div>
            
            {/* Merchants List */}
            <div className="px-4">
              {isMerchantsLoading ? (
                <div className="py-8 text-center text-gray-500">Đang tải...</div>
              ) : merchants.length > 0 ? (
                displayedMerchants.map((merchant) => (
                  <MerchantListCard
                    key={merchant.id}
                    id={merchant.id}
                    name={merchant.name}
                    logoSrc={merchant.logo}
                    earnRate={formatBillScanEarnRate(merchant.earnRate)}
                    onClick={(id) => {
                      console.log('Merchant clicked:', id);
                      // Navigate to merchant detail page
                      navigate(`/merchant/${merchant.code || id}`);
                    }}
                  />
                ))
              ) : (
                <div className="py-8 text-center text-gray-500">Không có thương hiệu nào</div>
              )}
            </div>
            
            {/* View All Button - Only show if there are more than 6 merchants */}
            {merchants.length > 6 && (
              <div className="px-4 py-3">
                <button 
                  className="w-full h-11 bg-white border border-[#CACACA] rounded-lg flex items-center justify-center"
                  onClick={handleViewAllMerchants}
                >
                  <span className="text-[#1A1818] font-semibold text-sm" style={{ fontFamily: 'Archia' }}>
                    {showAllMerchants 
                      ? 'Thu gọn' 
                      : `Xem tất cả ${merchants.length} thương hiệu`
                    }
                  </span>
                </button>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      id: 'huong-dan',
      label: 'Hướng dẫn',
      content: (
        <div className="mb-4">
          {/* Section Header */}
          <div className="bg-white flex items-center px-4 h-12">
            <h2 className="text-[#1A1818] font-bold text-base" style={{ fontFamily: 'Archia' }}>
              Hướng dẫn
            </h2>
          </div>
          
          {/* Instructions Content */}
          <div className="bg-white">
            {steps.length > 0 ? (
              steps.map((step, index) => (
                <div key={step.index} className="px-4" style={{ paddingTop: index === 0 ? '4px' : '24px', paddingBottom: index === steps.length - 1 ? '16px' : '0' }}>
                  <div className={index < steps.length - 1 ? 'border-b border-[#ECECEC] pb-6' : ''}>
                    <h3 className="text-[#1A1818] font-bold text-sm mb-1" style={{ fontFamily: 'Archia', lineHeight: '22px' }}>
                      {step.title}
                    </h3>
                    <div className="text-[#1A1818] text-sm" style={{ fontFamily: 'Archia', lineHeight: '22px', whiteSpace: 'pre-line' }}>
                      {step.description}
                    </div>
                    {step.image && (
                      <div className="w-full mt-2">
                        <img 
                          src={step.image} 
                          alt={step.title} 
                          className="w-full h-auto"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              // Fallback to hardcoded steps if API data not available
              <>
                {/* Step 1 */}
                <div className="px-4 pt-1">
                  <div className="border-b border-[#ECECEC] pb-6">
                    <h3 className="text-[#1A1818] font-bold text-sm mb-1" style={{ fontFamily: 'Archia', lineHeight: '22px' }}>
                      Bước 1: Tận hưởng dịch vụ
                    </h3>
                    <p className="text-[#1A1818] text-sm" style={{ fontFamily: 'Archia', lineHeight: '22px' }}>
                      Tại những cửa hàng có áp dụng tích VUI khi chụp hóa đơn.
                    </p>
                  </div>
                </div>

                {/* Step 2 */}
                <div className="px-4 pt-6">
                  <div className="flex flex-col gap-2">
                    <h3 className="text-[#1A1818] font-bold text-sm" style={{ fontFamily: 'Archia', lineHeight: '22px' }}>
                      Bước 2: Chụp hóa đơn
                    </h3>
                    <div className="text-[#1A1818] text-sm mb-2" style={{ fontFamily: 'Archia', lineHeight: '22px', whiteSpace: 'pre-line' }}>
                      Sau khi thanh toán, chụp hóa đơn bằng app trong vòng 48-72h (tùy thương hiệu)
                      
                      Chụp thẳng và rõ hóa đơn như hình:
                      
                      Lưu ý: Hóa đơn chính chủ, không chụp lại từ thiết bị khác, không chỉnh sửa.
                    </div>
                    <div className="w-full">
                      <img 
                        src={billOCRGuide} 
                        alt="Bill OCR image" 
                        className="w-full h-auto"
                      />
                    </div>
                  </div>
                </div>

                {/* Step 3 */}
                <div className="px-4 pt-6">
                  <h3 className="text-[#1A1818] font-bold text-sm mb-1" style={{ fontFamily: 'Archia', lineHeight: '22px' }}>
                    Bước 3. Xác nhận
                  </h3>
                  <p className="text-[#1A1818] text-sm" style={{ fontFamily: 'Archia', lineHeight: '22px' }}>
                    Kiểm tra chất lượng hình chụp và &quot;Xác nhận&quot;.
                  </p>
                </div>

                {/* Step 4 */}
                <div className="px-4 pt-6 pb-4">
                  <h3 className="text-[#1A1818] font-bold text-sm mb-1" style={{ fontFamily: 'Archia', lineHeight: '22px' }}>
                    Bước 4. VUI về
                  </h3>
                  <p className="text-[#1A1818] text-sm" style={{ fontFamily: 'Archia', lineHeight: '22px' }}>
                    VUI sẽ về sau 48h kể từ khi chụp thành công.
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      )
    },
    {
      id: 'gioi-han',
      label: 'Giới hạn',
      content: (
        <div className="">
          <div className="bg-white ">
            {/* Section Header */}
            <div className="px-4 py-3 border-b border-[#ECECEC]">
              <h2 className="text-[#1A1818] font-bold text-base" style={{ fontFamily: 'Archia' }}>
                Giới hạn tối đa
              </h2>
            </div>
            
            {/* Limits List */}
            <div className="p-4 space-y-4">
              {/* Số lần chụp hóa đơn */}
              <div className="border-b border-[#ECECEC] pb-4">
                <div className="flex gap-2">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="flex-shrink-0 mt-1">
                    <path d="M3 9a2 2 0 012-2h1l1.64-1.64A2 2 0 019.05 4.63h5.9a2 2 0 011.41.73L18 7h1a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="#1A1818" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <circle cx="12" cy="13" r="3" stroke="#1A1818" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <div className="flex-1">
                    <h3 className="text-[#1A1818] font-bold text-sm mb-2" style={{ fontFamily: 'Archia' }}>
                      Số lần chụp hóa đơn
                    </h3>
                    <div className="space-y-2">
                      <div>
                        <p className="text-[#1A1818] font-semibold text-sm mb-1" style={{ fontFamily: 'Archia' }}>
                          Với mỗi tài khoản
                        </p>
                        <p className="text-[#5A5A5A] text-sm" style={{ fontFamily: 'Archia' }}>
                          Tối đa {billConfig?.limitSnapPerDay || 20} lần/ngày
                        </p>
                      </div>
                      <div>
                        <p className="text-[#1A1818] font-semibold text-sm mb-1" style={{ fontFamily: 'Archia' }}>
                          Tại mỗi thương hiệu
                        </p>
                        <p className="text-[#5A5A5A] text-sm" style={{ fontFamily: 'Archia' }}>
                          Tuỳ theo Điều kiện tích điểm ở từng thương hiệu.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Số điểm được tích */}
              <div className="border-b border-[#ECECEC] pb-4">
                <div className="flex gap-2">
                  <div className="w-6 h-6 flex-shrink-0 mt-1">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="9" stroke="#1A1818" strokeWidth="1.5"/>
                      <path d="M9 10L10.5 14L12 10L13.5 14L15 10" stroke="#1A1818" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-[#1A1818] font-bold text-sm mb-2" style={{ fontFamily: 'Archia' }}>
                      Số điểm được tích
                    </h3>
                    <div className="space-y-2">
                      <div>
                        <p className="text-[#1A1818] font-semibold text-sm mb-1" style={{ fontFamily: 'Archia' }}>
                          Với mỗi tài khoản
                        </p>
                        <p className="text-[#5A5A5A] text-sm leading-relaxed" style={{ fontFamily: 'Archia' }}>
                          Trong 1 ngày: tối đa {billConfig?.pointPerDay?.toLocaleString('vi-VN') || '50.000'} VUI<br/>
                          Trong 7 ngày: tối đa {billConfig?.pointPerWeek?.toLocaleString('vi-VN') || '150.000'} VUI<br/>
                          Trong 30 ngày: tối đa {billConfig?.pointPerMonth?.toLocaleString('vi-VN') || '500.000'} VUI
                        </p>
                      </div>
                      <div>
                        <p className="text-[#1A1818] font-semibold text-sm mb-1" style={{ fontFamily: 'Archia' }}>
                          Tại mỗi thương hiệu
                        </p>
                        <p className="text-[#5A5A5A] text-sm" style={{ fontFamily: 'Archia' }}>
                          Tuỳ theo Điều kiện tích điểm ở từng thương hiệu.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Thời gian chụp hóa đơn */}
              <div>
                <div className="flex gap-2">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" className="flex-shrink-0 mt-1">
                    <circle cx="12" cy="12" r="9" stroke="#1A1818" strokeWidth="1.5"/>
                    <path d="M12 6v6l4 2" stroke="#1A1818" strokeWidth="1.5" strokeLinecap="round"/>
                  </svg>
                  <div className="flex-1">
                    <h3 className="text-[#1A1818] font-bold text-sm mb-2" style={{ fontFamily: 'Archia' }}>
                      Thời gian chụp hóa đơn
                    </h3>
                    <p className="text-[#5A5A5A] text-sm" style={{ fontFamily: 'Archia' }}>
                      Theo Điều kiện tích điểm ở từng thương hiệu.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="w-full min-h-screen bg-[#EFF3F6] flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title="Chụp hóa đơn"
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        variant="ZaloMiniApp"
        className="bg-white border-b border-[#ECECEC]"
        textClassName="text-[#1A1818]"
      />

      {/* SwiperTabContainer with content */}
      <div className="flex-1 overflow-hidden">
        <ScrollableTabContainer
          tabs={tabs}
          activeTabId={activeTab}
          onTabChange={handleTabChange}
          className="shadow-sm h-full"
          containerClassName="h-full"
          contentClassName="bg-[#EFF3F6]"
        />
      </div>

      {/* Bottom Action Button */}
      <div className="sticky bottom-0 bg-white px-4 py-4 border-t border-[#ECECEC]" style={{ boxShadow: '0px 10px 24px 0px rgba(26, 24, 24, 0.2)' }}>
        <Button
          variant="primary"
          size="large"
          onClick={handleStartCapture}
          className="w-full h-11 bg-[#F65D79] rounded-lg flex items-center justify-center gap-2"
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path
              d="M13.33 5H15C15.44 5 15.78 5.34 15.78 5.78V14.22C15.78 14.66 15.44 15 15 15H5C4.56 15 4.22 14.66 4.22 14.22V5.78C4.22 5.34 4.56 5 5 5H6.67L8 3.33H12L13.33 5ZM10 13.33C11.84 13.33 13.33 11.84 13.33 10C13.33 8.16 11.84 6.67 10 6.67C8.16 6.67 6.67 8.16 6.67 10C6.67 11.84 8.16 13.33 10 13.33Z"
              fill="white"
            />
          </svg>
          <span className="text-white font-semibold text-sm" style={{ fontFamily: 'Archia' }}>
            Bắt đầu chụp hóa đơn
          </span>
        </Button>
      </div>
    </div>
  );
};

export default BillScanInfoPage;