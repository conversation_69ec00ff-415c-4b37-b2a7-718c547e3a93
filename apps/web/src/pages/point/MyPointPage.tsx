import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  NavigationHeader,
  CardVUIPoint,
  CardBrandPoint,
  CardBrandPointSkeleton,
  Mascot,
  useBrandCurrencyList,
  BackIcon,
} from '@taptap/shared';
import { BottomNavigation, type NavigationItem } from '../../components/navigation';
import { useAuthStore } from '@taptap/shared';

const SKELETON = Array.from({ length: 6 }, (_, i) => i);

export const MyPointPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile } = useAuthStore();
  const { brandCurrencyList, loading } = useBrandCurrencyList();

  useEffect(() => {
    // Track page view
    console.log('My Point page viewed');
  }, []);

  const handleBack = () => {
    navigate(-1);
  };

  const handleHistoryClick = () => {
    navigate('/transaction-history');
  };

  const onPressCardVuiPoint = () => {
    navigate('/point/vui');
  };

  const renderHistoryEntry = () => (
    <>
      <div className="w-8"></div>
      <button
        onClick={handleHistoryClick}
        className="text-sm text-[#F65D79] font-semibold min-w-[48px] text-right"
        style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
      >
        Lịch sử
      </button>
    </>
  );

  // Navigation items for bottom navigation
  const navigationItems: NavigationItem[] = [
    {
      id: 'home',
      label: 'Trang chủ',
      icon: 'home',
      href: '/'
    },
    {
      id: 'voucher',
      label: 'Ưu đãi',
      icon: 'voucher',
      href: '/exchange'
    },
    {
      id: 'exchange',
      label: 'Đổi thưởng',
      icon: 'gift',
      href: '/exchange'
    },
    {
      id: 'games',
      label: 'VUI chơi',
      icon: 'game',
      href: '/games'
    },
    {
      id: 'profile',
      label: 'Tài khoản',
      icon: 'user',
      href: '/profile'
    }
  ];

  return (
    <div className="w-full min-h-screen bg-[#EFF3F6] flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title="Kho điểm của bạn"
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        rightComponent={renderHistoryEntry()}
        className="bg-white border-b border-[#ECECEC]"
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto pb-20">
        <div className="bg-[#EFF3F6] pt-6 pb-12 flex flex-col items-center px-4">
          {/* Rich Mascot */}
          <Mascot 
            name="rich" 
            className="w-[122px] h-[88px] mb-4"
          />

          {/* VUI Point Card */}
          <CardVUIPoint
            point={profile?.loyaltyPoint || 0}
            onPress={onPressCardVuiPoint}
          />

          {/* Brand Currencies Container */}
          <div className="w-full mt-6 py-4">
            <div className="flex flex-wrap justify-start">
              {loading
                ? SKELETON.map(item => (
                    <CardBrandPointSkeleton key={item} index={item} />
                  ))
                : brandCurrencyList.map((item, index) => {
                    const onPress = () => {
                      console.log('Brand currency clicked:', item.currencyCode);
                      navigate(`/point/brand/${item.currencyCode}`, {
                        state: {
                          brand: item.name,
                          currencyCode: item.currencyCode,
                        }
                      });
                    };

                    return (
                      <CardBrandPoint
                        data={item}
                        key={item.currencyCode}
                        index={index}
                        onPress={onPress}
                      />
                    );
                  })}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 w-full z-50">
        <BottomNavigation
          items={navigationItems}
          activeItemId="profile"
          onItemClick={(itemId) => {
            const item = navigationItems.find(nav => nav.id === itemId);
            if (item?.href) {
              navigate(item.href);
            }
          }}
        />
      </div>
    </div>
  );
};

export default MyPointPage;