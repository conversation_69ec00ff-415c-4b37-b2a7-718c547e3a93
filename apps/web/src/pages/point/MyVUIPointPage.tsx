import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  NavigationHeader,
  PointHistoryItem,
  LoadingSpinner,
  usePointVUIHistory,
  ActionHistory,
  BackIcon,
} from '@taptap/shared';
import { BottomNavigation, type NavigationItem } from '../../components/navigation';
import { useAuthStore } from '@taptap/shared';

export const MyVUIPointPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile } = useAuthStore();
  
  const {
    data: listHistoryPoints,
    isLoading,
    isError,
    onLoadMore,
  } = usePointVUIHistory(ActionHistory.GET_HISTORY_BY_MONTH, 'VUI');

  const topTenHistory = listHistoryPoints.slice(0, 10);

  const handleBack = () => {
    navigate(-1);
  };

  const navigateOnEarnVui = () => {
    navigate('/earn');
  };


  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const renderHeader = () => (
    <>
      <div className="bg-[#F7CC15] px-6 py-6 text-center">
        <div 
          className="text-lg font-semibold text-[#1A1818] mb-4"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          VUI của bạn
        </div>

        <div className="mb-4">
          <div className="bg-[#ffefb3] rounded-full px-4 py-3 flex items-center justify-center gap-3 mx-auto max-w-[200px]">
            <div className="w-10 h-10 rounded-full bg-gradient-to-b from-[#F8DF63] to-[#FED932] flex items-center justify-center">
              <span className="text-xs font-bold text-[#594009]">V</span>
            </div>
            <div 
              className="text-xl font-bold text-[#1A1818]"
              style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
            >
              {formatNumber(profile?.loyaltyPoint || 0)}
            </div>
          </div>
        </div>

        {profile?.expirySchedule?.map((value, index) => (
          <div 
            key={index} 
            className="text-sm text-[#1A1818] mb-1"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            {formatNumber(value.points || 0)} VUI sẽ hết hạn vào {formatDate(value.expiryDate || '')}
          </div>
        ))}

        {/* <button
          onClick={navigateOnEarnVui}
          className="flex items-center justify-center gap-2 mt-4 mx-auto"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="#1A1818" strokeWidth="2"/>
            <path d="M9,12l2,2 4,-4" stroke="#1A1818" strokeWidth="2" fill="none"/>
          </svg>
          <span 
            className="text-sm text-[#1b1818] font-medium underline"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            Làm sao tích VUI?
          </span>
        </button> */}
      </div>
      
      <div 
        className="px-4 py-3 bg-white border-b border-[#ECECEC] text-lg font-bold text-[#1A1818]"
        style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
      >
        Giao dịch gần đây
      </div>
    </>
  );

  const renderEmpty = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-16">
          <LoadingSpinner />
        </div>
      );
    }
    
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <div className="text-6xl mb-4">🧘</div>
        <div 
          className="text-lg font-medium text-[#1A1818] text-center"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          Chưa có lịch sử điểm nào
        </div>
      </div>
    );
  };

  // Navigation items for bottom navigation
  const navigationItems: NavigationItem[] = [
    {
      id: 'home',
      label: 'Trang chủ',
      icon: 'home',
      href: '/'
    },
    {
      id: 'voucher',
      label: 'Ưu đãi',
      icon: 'voucher',
      href: '/exchange'
    },
    {
      id: 'exchange',
      label: 'Đổi thưởng',
      icon: 'gift',
      href: '/exchange'
    },
    {
      id: 'games',
      label: 'VUI chơi',
      icon: 'game',
      href: '/games'
    },
    {
      id: 'profile',
      label: 'Tài khoản',
      icon: 'user',
      href: '/profile'
    }
  ];

  if (isError) {
    return (
      <div className="w-full min-h-screen bg-[#EFF3F6] flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😵</div>
          <div 
            className="text-lg font-medium text-[#1A1818]"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            Có lỗi xảy ra
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-white flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title="Kho điểm VUI"
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        className="bg-white border-b border-[#ECECEC]"
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto pb-20">
        {/* Header Section */}
        {renderHeader()}

        {/* History List */}
        <div className="bg-white">
          {topTenHistory.length === 0 ? (
            renderEmpty()
          ) : (
            <>
              {topTenHistory.map((item, index) => (
                <PointHistoryItem
                  key={item.id}
                  item={item}
                  index={index}
                  isLast={index === topTenHistory.length - 1}
                />
              ))}
              
              {/* Load More Button */}
              {listHistoryPoints.length > 10 && (
                <div className="p-4 border-t border-[#ECECEC]">
                  <button
                    onClick={onLoadMore}
                    className="w-full py-3 text-[#F65D79] font-semibold text-center"
                    style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Đang tải...' : 'Xem thêm'}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 w-full z-50">
        <BottomNavigation
          items={navigationItems}
          activeItemId="profile"
          onItemClick={(itemId) => {
            const item = navigationItems.find(nav => nav.id === itemId);
            if (item?.href) {
              navigate(item.href);
            }
          }}
        />
      </div>
    </div>
  );
};

export default MyVUIPointPage;