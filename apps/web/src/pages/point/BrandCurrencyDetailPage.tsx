import React from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import {
  NavigationHeader,
  PointHistoryItem,
  LoadingSpinner,
  useBrandCurrencyInfo,
  useBrandCurrencyHistory,
  ActionHistory,
  Mascot,
  BackIcon,
} from '@taptap/shared';
import { BottomNavigation, type NavigationItem } from '../../components/navigation';

interface LocationState {
  brand?: string;
  currencyCode?: string;
}

export const BrandCurrencyDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { currencyCode } = useParams<{ currencyCode: string }>();
  const location = useLocation();
  const state = location.state as LocationState;
  
  const { brandInfo } = useBrandCurrencyInfo({
    currencyCode: currencyCode || '',
  });

  const {
    listHistoryBrand,
    isLoading,
    isError,
    onLoadMore,
  } = useBrandCurrencyHistory(
    ActionHistory.GET_HISTORY_BY_MONTH,
    currencyCode || '',
  );

  const brandName = state?.brand || brandInfo?.name || '';
  const titleBrand = `${brandName} của bạn`;

  const handleBack = () => {
    navigate(-1);
  };

  const navigateToMerchant = () => {
    // Navigate to merchant detail page for earning methods
    console.log('Navigate to merchant:', brandInfo?.merchantCode);
    // navigate(`/merchant/${brandInfo?.merchantCode}/earn`);
  };


  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const renderBrandInfo = () => (
    <div className="bg-[#F7CC15] px-6 py-6">
      <div 
        className="text-lg font-semibold text-[#1A1818] mb-4 text-center"
        style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
      >
        {titleBrand}
      </div>

      <div className="mb-4">
        <div className="bg-[#FED932] rounded-full px-4 py-3 flex items-center justify-center gap-3 mx-auto max-w-[200px]">
          {brandInfo?.logo && (
            <img
              src={brandInfo.logo}
              alt={brandInfo.name}
              className="w-10 h-10 rounded-full object-cover"
            />
          )}
          <div 
            className="text-xl font-bold text-[#1A1818]"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            {formatNumber(brandInfo?.totalBalance || 0)}
          </div>
        </div>
      </div>

      {brandInfo?.details?.map((expire, index) => (
        <div 
          key={index} 
          className="text-sm text-[#1A1818] mb-1 text-center"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          {formatNumber(expire?.point || 0)} {brandInfo.currencyCode} sẽ hết hạn vào {formatDate(expire.date || '')}
        </div>
      ))}

      <button
        onClick={navigateToMerchant}
        className="flex items-center justify-center gap-2 mt-4 mx-auto"
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <circle cx="12" cy="12" r="10" stroke="#1A1818" strokeWidth="2"/>
          <path d="M9,12l2,2 4,-4" stroke="#1A1818" strokeWidth="2" fill="none"/>
        </svg>
        <span 
          className="text-sm text-[#F65D79] font-medium underline"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          Làm thế nào để kiếm điểm?
        </span>
      </button>
    </div>
  );

  const renderEmpty = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-16">
          <LoadingSpinner />
        </div>
      );
    }
    
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <Mascot name="meditate" className="w-20 h-20 mb-4" />
        <div 
          className="text-lg font-medium text-[#1A1818] text-center"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          Chưa có lịch sử giao dịch nào
        </div>
      </div>
    );
  };

  // Navigation items for bottom navigation
  const navigationItems: NavigationItem[] = [
    {
      id: 'home',
      label: 'Trang chủ',
      icon: 'home',
      href: '/'
    },
    {
      id: 'voucher',
      label: 'Ưu đãi',
      icon: 'voucher',
      href: '/exchange'
    },
    {
      id: 'exchange',
      label: 'Đổi thưởng',
      icon: 'gift',
      href: '/exchange'
    },
    {
      id: 'games',
      label: 'VUI chơi',
      icon: 'game',
      href: '/games'
    },
    {
      id: 'profile',
      label: 'Tài khoản',
      icon: 'user',
      href: '/profile'
    }
  ];

  if (isError) {
    return (
      <div className="w-full min-h-screen bg-[#EFF3F6] flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😵</div>
          <div 
            className="text-lg font-medium text-[#1A1818]"
            style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
          >
            Có lỗi xảy ra
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-white flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title={titleBrand}
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        className="bg-white border-b border-[#ECECEC]"
      />

      {/* Content */}
      <div className="flex-1 overflow-y-auto pb-20">
        {/* Brand Info Section */}
        {renderBrandInfo()}

        {/* History Section */}
        <div 
          className="px-4 py-3 bg-white border-b border-[#ECECEC] text-lg font-bold text-[#1A1818]"
          style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
        >
          Lịch sử giao dịch
        </div>

        {/* History List */}
        <div className="bg-white">
          {listHistoryBrand.length === 0 ? (
            renderEmpty()
          ) : (
            <>
              {listHistoryBrand.map((item, index) => (
                <PointHistoryItem
                  key={item.id}
                  item={item}
                  index={index}
                  isLast={index === listHistoryBrand.length - 1}
                />
              ))}
              
              {/* Load More Button */}
              {!isLoading && listHistoryBrand.length > 0 && (
                <div className="p-4 border-t border-[#ECECEC]">
                  <button
                    onClick={onLoadMore}
                    className="w-full py-3 text-[#F65D79] font-semibold text-center"
                    style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Đang tải...' : 'Xem thêm'}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 w-full z-50">
        <BottomNavigation
          items={navigationItems}
          activeItemId="profile"
          onItemClick={(itemId) => {
            const item = navigationItems.find(nav => nav.id === itemId);
            if (item?.href) {
              navigate(item.href);
            }
          }}
        />
      </div>
    </div>
  );
};

export default BrandCurrencyDetailPage;