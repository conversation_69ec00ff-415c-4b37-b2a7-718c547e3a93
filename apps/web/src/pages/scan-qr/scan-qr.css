/* QR Scanner Animation Styles */
@keyframes scan {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(190px);
  }
  100% {
    transform: translateY(0);
  }
}

.animate-scan {
  animation: scan 2s ease-in-out infinite;
}

/* Scanner overlay styles */
.scanner-overlay {
  background: radial-gradient(
    ellipse at center,
    transparent 0%,
    transparent 30%,
    rgba(0, 0, 0, 0.6) 30.1%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

/* QR Code frame corners */
.qr-frame-corner {
  position: absolute;
  width: 40px;
  height: 40px;
  border-color: var(--primary-color, #FF6B35);
}

.qr-frame-corner.top-left {
  top: 0;
  left: 0;
  border-top: 4px solid;
  border-left: 4px solid;
  border-top-left-radius: 12px;
}

.qr-frame-corner.top-right {
  top: 0;
  right: 0;
  border-top: 4px solid;
  border-right: 4px solid;
  border-top-right-radius: 12px;
}

.qr-frame-corner.bottom-left {
  bottom: 0;
  left: 0;
  border-bottom: 4px solid;
  border-left: 4px solid;
  border-bottom-left-radius: 12px;
}

.qr-frame-corner.bottom-right {
  bottom: 0;
  right: 0;
  border-bottom: 4px solid;
  border-right: 4px solid;
  border-bottom-right-radius: 12px;
}

/* Camera permission icon animation */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.camera-permission-icon {
  animation: pulse 2s ease-in-out infinite;
}

/* Flash button animation */
@keyframes flash-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
}

.flash-active {
  animation: flash-pulse 1.5s infinite;
}

/* Loading spinner for QR processing */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.qr-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: rotate 1s linear infinite;
}

/* Success checkmark animation */
@keyframes checkmark {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.checkmark-circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #4CAF50;
  fill: none;
  animation: checkmark 0.6s ease-in-out forwards;
}

.checkmark-check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  stroke: #4CAF50;
  animation: checkmark 0.3s ease-in-out 0.3s forwards;
}

/* Error shake animation */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

.error-shake {
  animation: shake 0.6s;
}

/* Fade in animation for modals */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Tab indicator animation */
@keyframes slideIn {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

.tab-indicator {
  transform-origin: left;
  animation: slideIn 0.3s ease-out;
}

/* QR Code generation animation */
@keyframes qrGenerate {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(-5deg);
  }
  50% {
    transform: scale(1.05) rotate(2deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

.qr-code-generate {
  animation: qrGenerate 0.5s ease-out;
}

/* Button hover effects */
.btn-3d {
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-3d:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.btn-3d:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Mobile-specific styles */
@media (max-width: 640px) {
  .qr-frame {
    width: min(80vw, 280px);
    height: min(80vw, 280px);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .scanner-overlay {
    background: radial-gradient(
      ellipse at center,
      transparent 0%,
      transparent 30%,
      rgba(0, 0, 0, 0.8) 30.1%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .animate-scan,
  .camera-permission-icon,
  .flash-active,
  .qr-loading-spinner,
  .checkmark-circle,
  .checkmark-check,
  .error-shake,
  .fade-in,
  .tab-indicator,
  .qr-code-generate {
    animation: none;
  }
  
  .btn-3d {
    transition: none;
  }
}