import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ChevronLeftIcon } from './icons';
import QRScanPage from './QRScanPage';
import PaymentQRPage from './PaymentQRPage';

type TabType = 'payment' | 'scan';

const QRPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const defaultTab = (searchParams.get('tab') as TabType) || 'scan';
  const [activeTab, setActiveTab] = useState<TabType>(defaultTab);

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSearchParams({ tab });
  };

  const onPressBack = () => {
    navigate(-1);
  };

  return (
    <div className="fixed inset-0 bg-white">
      {/* Header with Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={onPressBack}
            className="p-2"
            aria-label="Back"
          >
            <ChevronLeftIcon />
          </button>
          <h1 className="text-lg font-semibold">QR Code</h1>
          <div className="w-10" />
        </div>
        
        {/* Tab Navigation */}
        <div className="flex border-t border-gray-100">
          <button
            onClick={() => handleTabChange('payment')}
            className={`flex-1 py-3 px-4 text-center font-medium transition-colors relative ${
              activeTab === 'payment'
                ? 'text-primary'
                : 'text-gray-500'
            }`}
          >
            Mã thanh toán
            {activeTab === 'payment' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary" />
            )}
          </button>
          <button
            onClick={() => handleTabChange('scan')}
            className={`flex-1 py-3 px-4 text-center font-medium transition-colors relative ${
              activeTab === 'scan'
                ? 'text-primary'
                : 'text-gray-500'
            }`}
          >
            Quét QR
            {activeTab === 'scan' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary" />
            )}
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="absolute inset-0 top-[108px]">
        {activeTab === 'payment' ? (
          <PaymentQRPage />
        ) : (
          <QRScanPage />
        )}
      </div>
    </div>
  );
};

export default QRPage;