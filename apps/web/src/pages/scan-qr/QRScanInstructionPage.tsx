import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { NavigationHeader, Button, ScrollableTabContainer, type ScrollableTabItem, MerchantListCard, useGiftCardStore, BackIcon } from '@taptap/shared';
import qrScanIcon from '../../assets/icons/qr-scan-icon.svg';

type InfoTab = 'thuong-hieu' | 'huong-dan';

export const QRScanInstructionPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<InfoTab>('thuong-hieu');
  const [showAllMerchants, setShowAllMerchants] = useState(false);
  const { merchants, steps, isLoading: isMerchantsLoading, fetchGiftCardInfo } = useGiftCardStore();
  
  useEffect(() => {
    fetchGiftCardInfo();
  }, [fetchGiftCardInfo]);



  const handleBack = () => {
    navigate(-1);
  };

  const handleStartScan = () => {
    navigate('/scan-qr');
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId as InfoTab);
  };

  const handleViewAllMerchants = () => {
    setShowAllMerchants(!showAllMerchants);
  };

  // Display only first 6 merchants initially, or all if showAllMerchants is true
  const displayedMerchants = showAllMerchants ? merchants : merchants.slice(0, 6);

  // Create tabs with content
  const tabs: ScrollableTabItem[] = [
    {
      id: 'thuong-hieu',
      label: 'Thương hiệu',
      content: (
        <div className="mb-4">
          <div className="bg-white">
            {/* Section Header */}
            <div className="px-4 py-3 border-b border-[#ECECEC]">
              <h2 className="text-[#1A1818] font-bold text-base" style={{ fontFamily: 'Archia' }}>
                Thương hiệu
              </h2>
            </div>

            {/* Merchants List */}
            <div className="px-4">
              {isMerchantsLoading ? (
                <div className="py-8 text-center text-gray-500">Đang tải...</div>
              ) : merchants.length > 0 ? (
                displayedMerchants.map((merchant) => {
                  // Extract text from HTML description
                  const earnRateText = merchant.earnRate?.description 
                    ? merchant.earnRate.description.replace(/<[^>]*>/g, '').trim()
                    : '';
                  
                  return (
                    <MerchantListCard
                      key={merchant.id}
                      id={merchant.id}
                      name={merchant.name}
                      logoSrc={merchant.logo}
                      earnRate={earnRateText}
                      onClick={(id) => {
                        console.log('Merchant clicked:', id);
                        // Navigate to merchant detail page
                        navigate(`/merchant/${merchant.code || id}`);
                      }}
                    />
                  );
                })
              ) : (
                <div className="py-8 text-center text-gray-500">Không có thương hiệu nào</div>
              )}
            </div>

            {/* View All Button - Only show if there are more than 6 merchants */}
            {merchants.length > 6 && (
              <div className="px-4 py-3">
                <button
                  className="w-full h-11 bg-white border border-[#CACACA] rounded-lg flex items-center justify-center"
                  onClick={handleViewAllMerchants}
                >
                  <span className="text-[#1A1818] font-semibold text-sm" style={{ fontFamily: 'Archia' }}>
                    {showAllMerchants
                      ? 'Thu gọn'
                      : `Xem tất cả ${merchants.length} thương hiệu`
                    }
                  </span>
                </button>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      id: 'huong-dan',
      label: 'Hướng dẫn',
      content: (
        <div className="mb-4">
          {/* Section Header */}
          <div className="bg-white flex items-center px-4 h-12">
            <h2 className="text-[#1A1818] font-bold text-base" style={{ fontFamily: 'Archia' }}>
              Hướng dẫn
            </h2>
          </div>

          {/* Instructions Content */}
          <div className="bg-white">
            <div className="px-4 pb-4">
              <div className="flex flex-col gap-3">
                {steps.sort((a, b) => a.index - b.index).map((step, index) => (
                  <div key={step.index} className="relative">
                    {/* Divider line - show for first item */}
                    {index === 0 && (
                      <div className="absolute top-0 left-0 right-0 h-px bg-[#ECECEC]" />
                    )}

                    <div className={`flex flex-col gap-1 ${index === 0 ? 'pt-4' : 'pt-3'} pb-3`}>
                      <h3 className="text-[#1A1818] font-bold text-sm" style={{ fontFamily: 'Archia', fontSize: '14px', lineHeight: '22px' }}>
                        {step.title}
                      </h3>
                      <p className="text-[#5A5A5A] text-sm" style={{ fontFamily: 'Archia', fontSize: '14px', lineHeight: '22px' }}>
                        {step.description}
                      </p>
                      {step.image && (
                        <div className="w-full mt-2">
                          <img 
                            src={step.image} 
                            alt={step.title} 
                            className="w-full h-auto rounded-lg"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="w-full min-h-screen bg-[#EFF3F6] flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title="Quét mã QR"
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        variant="ZaloMiniApp"
        className="bg-white border-b border-[#ECECEC]"
        textClassName="text-[#1A1818]"
      />

      {/* SwiperTabContainer with content */}
      <div className="flex-1 overflow-hidden">
        <ScrollableTabContainer
          tabs={tabs}
          activeTabId={activeTab}
          onTabChange={handleTabChange}
          className="shadow-sm h-full"
          containerClassName="h-full"
          contentClassName="bg-[#EFF3F6]"
        />
      </div>

      {/* Bottom Action Button */}
      <div className="sticky bottom-0 bg-white px-4 py-4 border-t border-[#ECECEC]" style={{ boxShadow: '0px 10px 24px 0px rgba(26, 24, 24, 0.2)' }}>
        <Button
          variant="primary"
          size="large"
          onClick={handleStartScan}
          className="w-full h-11 bg-[#F65D79] rounded-lg flex items-center justify-center gap-2"
        >
          <img src={qrScanIcon} alt="QR Scan" width="18" height="18" />
          <span className="text-white font-semibold text-sm" style={{ fontFamily: 'Archia' }}>
            Bắt đầu quét mã QR
          </span>
        </Button>
      </div>
    </div>
  );
};

export default QRScanInstructionPage;