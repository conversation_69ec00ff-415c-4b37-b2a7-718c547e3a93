import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeftIcon, CopyIcon, ShareIcon, DownloadIcon } from './icons';
import QRCode from 'qrcode';

interface PaymentQRPageProps {
  userId?: string;
  userName?: string;
  phoneNumber?: string;
}

const PaymentQRPage: React.FC<PaymentQRPageProps> = ({
  userId = '123456789',
  userName = 'Nguyễn Văn A',
  phoneNumber = '0901234567'
}) => {
  const navigate = useNavigate();
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    generateQRCode();
  }, [userId]);

  const generateQRCode = async () => {
    try {
      // Generate payment QR data
      const qrData = JSON.stringify({
        type: 'payment',
        userId,
        userName,
        phoneNumber,
        timestamp: Date.now()
      });

      // Generate QR code image
      const url = await QRCode.toDataURL(qrData, {
        width: 280,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      setQrCodeUrl(url);
    } catch (error) {
      console.error('Failed to generate QR code:', error);
    }
  };

  const handleCopyCode = () => {
    navigator.clipboard.writeText(userId);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Mã thanh toán TapTap',
          text: `Mã thanh toán của ${userName}: ${userId}`,
          url: window.location.href
        });
      } catch (error) {
        console.error('Share failed:', error);
      }
    } else {
      handleCopyCode();
    }
  };

  const handleDownload = () => {
    if (!qrCodeUrl) return;
    
    const link = document.createElement('a');
    link.download = `taptap-payment-qr-${userId}.png`;
    link.href = qrCodeUrl;
    link.click();
  };

  const onPressBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-primary/10 to-white">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={onPressBack}
            className="p-2"
            aria-label="Back"
          >
            <ChevronLeftIcon />
          </button>
          <h1 className="text-lg font-semibold">Mã thanh toán</h1>
          <div className="w-10" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6">
        {/* User Info Card */}
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mr-3">
              <span className="text-primary font-bold text-lg">
                {userName.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h2 className="font-semibold text-gray-900">{userName}</h2>
              <p className="text-sm text-gray-500">{phoneNumber}</p>
            </div>
          </div>
          
          {/* User ID */}
          <div className="bg-gray-50 rounded-lg p-3 flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-500 mb-1">Mã khách hàng</p>
              <p className="font-mono font-semibold">{userId}</p>
            </div>
            <button
              onClick={handleCopyCode}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
              aria-label="Copy code"
            >
              <CopyIcon />
            </button>
          </div>
          
          {copied && (
            <p className="text-sm text-green-600 mt-2 text-center">
              Đã sao chép mã!
            </p>
          )}
        </div>

        {/* QR Code Card */}
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
          <h3 className="text-center font-semibold mb-4">
            Quét mã để thanh toán
          </h3>
          
          {qrCodeUrl ? (
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-white rounded-xl border-2 border-gray-100">
                <img
                  src={qrCodeUrl}
                  alt="Payment QR Code"
                  className="w-[280px] h-[280px]"
                />
              </div>
            </div>
          ) : (
            <div className="flex justify-center mb-4">
              <div className="w-[280px] h-[280px] bg-gray-100 rounded-xl animate-pulse" />
            </div>
          )}
          
          <p className="text-center text-sm text-gray-500">
            Đưa mã này cho nhân viên thu ngân để thanh toán
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={handleShare}
            className="flex-1 bg-white border border-gray-200 text-gray-700 py-3 px-4 rounded-xl font-medium flex items-center justify-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <ShareIcon />
            Chia sẻ
          </button>
          <button
            onClick={handleDownload}
            className="flex-1 bg-primary text-white py-3 px-4 rounded-xl font-medium flex items-center justify-center gap-2 hover:bg-primary/90 transition-colors"
          >
            <DownloadIcon />
            Tải xuống
          </button>
        </div>

        {/* Instructions */}
        <div className="mt-6 bg-blue-50 rounded-xl p-4">
          <h4 className="font-semibold text-blue-900 mb-2">Hướng dẫn sử dụng:</h4>
          <ul className="space-y-1 text-sm text-blue-800">
            <li>• Đưa mã QR cho nhân viên để quét</li>
            <li>• Hoặc cung cấp mã khách hàng để nhập thủ công</li>
            <li>• Kiểm tra thông tin trước khi xác nhận thanh toán</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PaymentQRPage;