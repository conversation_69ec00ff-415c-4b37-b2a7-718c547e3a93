import React from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useChallengeDetail, resizeImage } from "@taptap/shared";
import { Icon } from "@taptap/shared";
import {
  buildAccumulatedBrandsFromConditions,
  ICondition,
} from "./utils/helper";
import { BrandLogoWeb } from "./BrandLogo";
import { iconsAssets } from "../../assets/icons";
import { FloatButtonWeb } from "./FloatButton";
import { getTimeRemaining as getTimeRemainingHelper } from "@taptap/shared";

// Local lightweight types to avoid any casts
type ChallengeUserProgressItem = {
  brandId: string;
  logoLink: string;
  validTransactions?: unknown[];
};

// Note: FloatButtonWeb expects a challenge-like object; we pass `any` at usage site to avoid tight coupling here

// Helper component để format description với style
const FormattedDescription: React.FC<{ description: string }> = ({
  description,
}) => {
  // Tạo HTML với style được format
  const createFormattedHTML = (desc: string) => {
    return desc;
  };

  return (
    <>
      <div
        className="space-y-3 text-sm leading-relaxed style-description"
        dangerouslySetInnerHTML={{
          __html: createFormattedHTML(description),
        }}
      />
      <style>{`
      .style-description { color: #000; }
      .style-description p { margin: 0.5rem 0; line-height: 1.6; }
      .style-description h1,
      .style-description h2,
      .style-description h3,
      .style-description h4,
      .style-description h5,
      .style-description h6 { font-weight: 700; margin: 0.75rem 0 0.5rem; color: #111827; }
      .style-description h1 { font-size: 1.5rem; }
      .style-description h2 { font-size: 1.25rem; }
      .style-description h3 { font-size: 1.125rem; }
      .style-description a { color: #0ea5e9; text-decoration: underline; }
      .style-description ul { list-style: disc; padding-left: 1.25rem; margin: 0.5rem 0; }
      .style-description ol { list-style: decimal; padding-left: 1.25rem; margin: 0.5rem 0; }
      .style-description li { margin: 0.25rem 0; }
      .style-description strong { font-weight: 600; }
      .style-description em { font-style: italic; }
      .style-description img { max-width: 100%; height: auto; border-radius: 0.5rem; }
      .style-description blockquote { border-left: 4px solid #e5e7eb; color: #4b5563; padding-left: 1rem; margin: 0.75rem 0; }
      .style-description table { width: 100%; border-collapse: collapse; margin: 0.75rem 0; }
      .style-description th, .style-description td { border: 1px solid #e5e7eb; padding: 0.5rem; text-align: left; }
      .style-description hr { border: none; border-top: 1px solid #e5e7eb; margin: 1rem 0; }
      .style-description code { background: #f3f4f6; border-radius: 4px; padding: 0.1rem 0.3rem; font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; }
    `}</style>
    </>
  );
};

export const MissionDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { challenge, loading, error, refetch } = useChallengeDetail(id || "");

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="animate-pulse">
          <div className="h-16 bg-teal-500"></div>
          <div className="h-64 bg-teal-500"></div>
          <div className="h-96 bg-white"></div>
        </div>
      </div>
    );
  }

  if (error || !challenge) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">
            Không thể tải thông tin nhiệm vụ
          </div>
          <button
            onClick={() => navigate(-1)}
            className="px-6 py-2 bg-teal-500 text-white rounded-lg"
          >
            Quay lại
          </button>
        </div>
      </div>
    );
  }

  const getTimeRemaining = () => getTimeRemainingHelper(challenge);

  const getProgress = () => {
    if (!challenge.userProgress) return { current: 0, max: 1, percent: 0 };
    return {
      current: challenge.userProgress.currentProgress || 0,
      max: challenge.userProgress.maxProgress || 1,
      percent: challenge.userProgress.percentProgress || 0,
    };
  };

  const progress = getProgress();
  const hasJoined = Boolean(challenge.userProgress?.hadJoinChallenge);

  const brandsEx = buildAccumulatedBrandsFromConditions(
    challenge.conditions as ICondition[],
    (challenge.userProgress?.progress as
      | ChallengeUserProgressItem[]
      | undefined) || []
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mission Overview Section */}
      <div
        className="text-white px-4 pb-6 pt-8 relative"
        style={{
          backgroundColor: challenge.hexBackgroundColor,
          color: challenge.challengeNameColor,
        }}
      >
        <div className="absolute top-4 left-4">
          <button
            onClick={() => navigate(-1)}
            className="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors flex items-center justify-center"
          >
            <Icon
              name="arrow-left"
              className="w-6 h-6 text-black flex items-center justify-center"
            />
          </button>
        </div>

        <div className="w-full h-48 rounded-lg mb-4 overflow-hidden flex items-center justify-center">
          <img
            src={resizeImage(challenge.challengeImage, { width: 400, height: 400, quality: 85, fit: 'cover' })}
            alt={challenge.displayChallengeName}
            className="w-full h-full object-contain max-h-[200px] max-w-[200px] mx-auto"
          />
        </div>

        <div className="text-center mb-4">
          <div className="text-sm opacity-80 mb-2">NHIỆM VỤ</div>
          <h2 className="text-xl font-bold leading-tight">
            {challenge.displayChallengeName}
          </h2>
        </div>

        <div className="flex flex-col items-center justify-between">
          {hasJoined &&
            challenge.isDisplayRemainingTime &&
            challenge.endDate && (
              <button className="mb-3 ml-4 px-4 py-2 bg-white bg-opacity-20 rounded-lg text-sm font-medium">
                Còn {getTimeRemaining()}
              </button>
            )}

          {hasJoined && (
            <div className="flex-1  w-[50vw] mx-auto mb-8 relative">
              <div className="flex items-center space-x-2 mb-2 h-6">
                <div className="absolute top-0 left-0 text-sm font-medium bg-white text-black rounded-full px-2 py-1">
                  {progress.current}/{progress.max}
                </div>
              </div>
              <div className="w-full bg-white rounded-full h-2">
                <div
                  className="bg-white h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress.percent}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
        <div className="h-[12px] absolute bottom-0 left-0 bg-white w-full mt-[-1rem] rounded-t-[14px] z-10" />
      </div>

      {/* Instructions Section */}
      {challenge.description && (
        <div className="bg-white px-4 pb-6 mb-2">
          <h3 className="text-lg font-semibold mb-4">Hướng dẫn</h3>

          <FormattedDescription description={challenge.description || ""} />
        </div>
      )}

      {/* Already Performed At Section */}
      {brandsEx.length > 0 && (
        <div className="bg-white px-4 pb-6 mb-2">
          <h3 className="text-lg font-semibold mb-4">Đã thực hiện tại</h3>
          <div className="flex space-x-3">
            {brandsEx.map((item) => (
              <BrandLogoWeb
                key={item.brandId}
                item={item}
                isExpiredItem={false}
                trophySrc={iconsAssets.outlineChallenge}
              />
            ))}
          </div>
        </div>
      )}

      {/* Mission Rewards Section */}
      <div className="bg-white px-4 pb-6 pt-2 mb-2">
        <div className="flex flex-col items-start justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center">
            <Icon name="gift" className="w-5 h-5 mr-2" />
            Quà tặng nhiệm vụ
          </h3>
          <button className="ml-8 px-4 py-1 bg-orange-500 text-white rounded-[16px] mt-2 text-sm font-medium flex items-center">
            <Icon name="droplet" className="w-4 h-4 mr-2" />
            Quà còn lại: {challenge.packageGift?.remain || 0}
          </button>
        </div>

        <p className="pl-8 text-sm text-gray-700 mb-4">
          {challenge.packageGift?.content}
        </p>

        <div className="space-y-3">
          {challenge.packageGift?.gifts?.map((item, index) => {
            return (
              <div
                className={`flex items-center space-x-3 border-b border-gray-200 pb-4 ${
                  index === challenge.packageGift?.gifts?.length - 1
                    ? "border-b-0"
                    : ""
                }`}
                key={item.giftImageLink}
              >
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <img
                    src={resizeImage(item.giftImageLink, { width: 64, height: 64, quality: 85, fit: 'cover' })}
                    alt={item.giftContent}
                    className="w-full h-full object-contain rounded-full"
                  />
                </div>
                <span className="text-sm font-medium text-[#23B082]">
                  {item.giftContent}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Notes Section */}
      {challenge.note && (
        <div className="bg-white px-4 pt-2 pb-6">
          <h3 className="text-lg font-semibold mb-4">Lưu ý</h3>
          <div
            className="space-y-3 text-sm text-gray-700"
            dangerouslySetInnerHTML={{ __html: challenge.note }}
          />
        </div>
      )}

      {/* Bottom Button */}
      <div className="fixed bottom-0 left-0 right-0 bg-white px-4 pt-3 pb-[max(env(safe-area-inset-bottom),1rem)] shadow-[0_-4px_12px_rgba(0,0,0,0.04)]">
        { }
        <FloatButtonWeb
          challenge={challenge as unknown as any}
          refetch={refetch}
        />
        {/* <Button
          variant="primary"
          size="large"
          fullWidth
          className="rounded-xl text-base leading-none h-14 shadow-md"
          loading={isJoining}
          disabled={isJoining}
          onClick={async () => {
            if (hasJoined) {
              navigate("/member-code");
              return;
            }
            if (!id || isJoining) return;
            setIsJoining(true);
            try {
              const ok = await entertainmentService.joinChallenge(id);
              if (ok) {
                await refetch();
              }
            } finally {
              setIsJoining(false);
            }
          }}
        >
          {hasJoined ? "Tham gia ngay" : "Chấp nhận nhiệm vụ"}
        </Button> */}
      </div>
      <div className="h-[88px] w-full" />
    </div>
  );
};

export default MissionDetailPage;
