// types rút gọn cho ví dụ
type IChallengeBrand = {
  brandId: string;
  logoLink: string;
  validTransactions?: unknown[];
};

export type ICondition = {
  conditionType: "BRAND" | "OPEN_LINK" | string;
  transactionsWithinBrands?: number;
  transactionEachBrand?: number;
  brands?: IChallengeBrand[];
};

type IChallengeUserProgress = IChallengeBrand;

enum EConditionChalengeType {
  EARN_POINT = "EARN_POINT",
  REDEEM_POINT = "REDEEM_POINT",
}

const isNumberSafe = (n: unknown): n is number =>
  typeof n === "number" && !Number.isNaN(n);

function selectBrandCondition(
  conditions?: ICondition[]
): ICondition | undefined {
  if (!Array.isArray(conditions)) return undefined;
  return conditions.find((c) => c.conditionType === "BRAND");
}

function getNumberOfAccumulating(condition?: ICondition): number {
  if (!condition) return 0;
  if (isNumberSafe(condition.transactionsWithinBrands)) {
    return condition.transactionsWithinBrands!;
  }
  if (
    isNumberSafe(condition.transactionEachBrand) &&
    Array.isArray(condition.brands)
  ) {
    return condition.brands!.length;
  }
  return 0;
}

export function buildAccumulatedBrandsFromConditions(
  conditions: ICondition[] | undefined,
  challengeBrands: IChallengeUserProgress[] = [],
  conditionChallengeType?: EConditionChalengeType
): IChallengeBrand[] {
  // bỏ qua nếu là challenge earn/redeem point
  if (
    conditionChallengeType === EConditionChalengeType.EARN_POINT ||
    conditionChallengeType === EConditionChalengeType.REDEEM_POINT
  ) {
    return [];
  }

  const condition = selectBrandCondition(conditions);
  const targetCount = getNumberOfAccumulating(condition);
  if (!condition || condition.conditionType !== "BRAND" || !targetCount) {
    return [];
  }

  const result: IChallengeBrand[] = [];

  for (const b of challengeBrands) {
    const count = b?.validTransactions?.length ?? 0;
    if (count > 0) {
      for (let i = 0; i < count && result.length < targetCount; i += 1) {
        result.push({
          brandId: b.brandId,
          logoLink: b.logoLink,
          validTransactions: b.validTransactions,
        });
        if (isNumberSafe(condition.transactionEachBrand)) break;
      }
    }
    if (result.length >= targetCount) break;
  }

  while (result.length < targetCount) {
    const idx = result.length;
    result.push({ brandId: `placeholder-${idx}`, logoLink: "" });
  }

  return result;
}
