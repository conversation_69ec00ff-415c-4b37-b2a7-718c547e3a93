import React from "react";

type IChallengeBrand = {
  brandId?: string;
  logoLink?: string;
  validTransactions?: unknown[];
};

type Props = {
  item: IChallengeBrand;
  isExpiredItem: boolean;
  trophySrc: string; // đường dẫn icon trophy placeholder
  size?: number; // mặc định 48
  trophySize?: number; // mặc định 32
};

const COLORS = {
  primaryPink: "#ff2d55",
  green: "#34c759",
  grey3: "#c7c7cc",
  grey6: "#f3f3f3",
};

export const BrandLogoWeb: React.FC<Props> = ({
  item,
  isExpiredItem,
  trophySrc,
  size = 48,
  trophySize = 32,
}) => {
  const isCompleted = Boolean(
    item?.validTransactions && (item.validTransactions as unknown[])?.length > 0
  );

  const borderColor = isExpiredItem
    ? COLORS.grey3
    : isCompleted
    ? COLORS.green
    : COLORS.primaryPink;

  const borderStyle = isCompleted ? ("solid" as const) : ("dashed" as const);

  return (
    <div
      style={{
        width: size,
        height: size,
        borderRadius: size / 2,
        margin: "0 5px 12px 5px",
        backgroundColor: COLORS.grey6,
        borderWidth: 1,
        borderStyle,
        borderColor,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        overflow: "hidden",
      }}
    >
      {isCompleted ? (
        <img
          src={item?.logoLink || ""}
          alt=""
          style={{
            width: size,
            height: size,
            borderRadius: size / 2,
            objectFit: "contain",
            filter: isExpiredItem ? "grayscale(1)" : "none",
          }}
        />
      ) : (
        <img
          src={trophySrc}
          alt=""
          style={{
            width: trophySize,
            height: trophySize,
            objectFit: "contain",
            filter: isExpiredItem ? "grayscale(1)" : "none",
          }}
        />
      )}
    </div>
  );
};
