import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  WheelGame,
  WheelGift,
  NavigationHeader,
  BackIcon,
  LoadingSpinner,
} from "@taptap/shared";
import { entertainmentService } from "@taptap/shared";
import type {
  WheelDetail,
  WheelGift as ApiWheelGift,
} from "@taptap/shared/services/api/entertainmentService";

const WheelGamePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [remainingSpins, setRemainingSpins] = useState(0);
  const [wheelData, setWheelData] = useState<WheelDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const convertGifts = useCallback((apiGifts: ApiWheelGift[]): WheelGift[] => {
    return apiGifts.map((gift) => ({
      name: gift.name,
      image: gift.logoUrl || "https://picsum.photos/seed/default/100/100", // Fallback image nếu không có logo
    }));
  }, []);

  const gifts = useMemo(() => {
    return convertGifts(wheelData?.wheel.giftDetails || []);
  }, [wheelData, convertGifts]);

  console.log("gifts", gifts);

  // Call API spinWheel thay vì mock
  const handleSpin = async () => {
    if (!id) return;
    try {
      // Gọi API spinWheel
      const spinResult = await entertainmentService.spinWheel({ wheelId: id });

      console.log("spinResult", spinResult);

      if (spinResult?.status?.success && spinResult?.data) {
        // Tìm index của gift trong danh sách gifts
        const giftIndex = spinResult?.data?.index;

        // Cập nhật remaining spins
        setRemainingSpins((prev) => prev - 1);

        return {
          giftIndex: giftIndex >= 0 ? giftIndex : 0,
          state:
            spinResult.data.name === "Chúc may mắn lần sau"
              ? ("no-gift" as const)
              : ("gift" as const),
          name: spinResult.data.name,
          image: spinResult.data.logoUrl || undefined,
        };
      } else {
        console.log("spinResult", spinResult);
        return null;
      }
    } catch (error) {
      console.error("Failed to spin wheel:", error);
      return null;
    }
  };

  // Fetch wheel data from API
  useEffect(() => {
    const fetchWheelData = async () => {
      if (!id) return;

      try {
        setLoading(true);
        setError(null);
        const data = await entertainmentService.getWheel(id);
        console.log("data", data);
        if (data) {
          setWheelData(data);
          setRemainingSpins(data?.user?.spinTurns || 0);
        } else {
          setError("Không thể tải thông tin wheel");
        }
      } catch (err) {
        console.error("Failed to fetch wheel data:", err);
        setError("Có lỗi xảy ra khi tải dữ liệu");
      } finally {
        setLoading(false);
      }
    };

    fetchWheelData();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !wheelData) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Vui Chơi - Trò chơi"
          leftIcon={<BackIcon />}
          onLeftClick={() => navigate(-1)}
          className="border-b border-[#ECECEC]"
        />
        <div className="flex flex-col items-center justify-center p-8">
          <p className="text-red-500 text-center">
            {error || "Không tìm thấy thông tin wheel"}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <NavigationHeader
        title="Vui Chơi - Trò chơi"
        leftIcon={<BackIcon />}
        onLeftClick={() => navigate(-1)}
        className="border-b border-[#ECECEC]"
      />
      <div className="flex flex-col items-center justify-center">
        <WheelGame
          wheelData={wheelData}
          gifts={gifts}
          remainingSpins={remainingSpins}
          onSpin={handleSpin as any}
        />
      </div>
    </div>
  );
};

export default WheelGamePage;
