import React from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON>Header,
  BackIcon,
  useChallenges,
  ChallengeV3,
} from "@taptap/shared";

// Import default mission thumbnail fallback
import defaultThumbnail from "../../../../../shared/assets/images/<EMAIL>";

interface MissionHistoryCard {
  item: ChallengeV3;
  onMissionClick: (challenge: ChallengeV3) => void;
}

const MissionHistoryCardComponent: React.FC<MissionHistoryCard> = ({
  item,
  onMissionClick,
}) => {
  const getStatusFromChallenge = (challenge: ChallengeV3) => {
    const state = challenge.userProgress?.state;
    switch (state) {
      case "SUCCESSFULLY":
      case "NOT_RECEIVED_YET":
        return "received";
      case "FAILED":
        return "lost";
      case "RELEASING":
        return "releasing";
      default:
        return "received";
    }
  };

  const getBackgroundColor = () => {
    return item.hexBackgroundColor || "#14A197";
  };

  const getStatusInfo = () => {
    const status = getStatusFromChallenge(item);
    switch (status) {
      case "received":
        return {
          text: "Đã nhận quà!",
          iconBg: "#0DC98B",
          icon: (
            <svg width="8" height="6" viewBox="0 0 8 6" fill="none">
              <path
                d="M1 3L3 5L7 1"
                stroke="white"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      case "lost":
        return {
          text: "Quà tặng thất lạc",
          iconBg: "#FF7425",
          icon: (
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <rect width="16" height="16" rx="8" fill="white" />
              <path
                d="M8 3V8L11 11"
                stroke="#FF7425"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      case "releasing":
        return {
          text: "Quà đang đến...",
          iconBg: "#0DC98B",
          icon: (
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <rect width="16" height="16" rx="8" fill="white" />
              <path
                d="M8 6V10L11 13"
                stroke="#0DC98B"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      default:
        return {
          text: "Đã nhận quà!",
          iconBg: "#0DC98B",
          icon: null,
        };
    }
  };

  const statusInfo = getStatusInfo();
  const title = item.displayChallengeName || item.title || "Nhiệm vụ";
  const thumbnailSrc = item.challengeImage || defaultThumbnail;

  const handleCardClick = () => {
    onMissionClick(item);
  };

  return (
    <div
      className="w-full h-[128px] rounded-xl flex items-center p-2 gap-2 relative overflow-hidden cursor-pointer transition-transform hover:scale-[1.02] active:scale-[0.98]"
      style={{ backgroundColor: getBackgroundColor() }}
      onClick={handleCardClick}
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-[215px] h-[215px] opacity-20">
        <div className="w-full h-full bg-gradient-to-br from-white/20 to-transparent rounded-full transform rotate-12 translate-x-12 -translate-y-12"></div>
      </div>

      {/* Mission Thumbnail */}
      <div className="w-24 h-28 rounded-lg overflow-hidden flex-shrink-0">
        <img
          src={thumbnailSrc}
          alt={title}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Mission Content */}
      <div className="flex-1 min-w-0 flex flex-col gap-3">
        {/* Title and Rewards */}
        <div className="flex flex-col gap-1.5">
          <h3
            className="text-base font-semibold text-white leading-[22px] line-clamp-2"
            style={{ color: item.challengeNameColor }}
          >
            {title}
          </h3>

          {/* Rewards */}
          {item.giftDisplay && item.giftDisplay.length > 0 && (
            <div className="flex items-center gap-2 flex-wrap">
              {item.giftDisplay.map((reward, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 rounded-md px-1.5 py-1"
                >
                  <span className="text-xs font-semibold text-white">
                    {reward.giftTab}
                  </span>
                  <img
                    src={reward.logoLink}
                    alt="Brand"
                    className="w-4 h-4 rounded-full"
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Status and Action */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1.5">
            <div
              className="w-4 h-4 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: statusInfo.iconBg }}
            >
              {statusInfo.icon}
            </div>
            <span className="text-[10px] font-normal text-white leading-[16px]">
              {statusInfo.text}
            </span>
          </div>

          {/* Hidden button for layout consistency */}
          <div className="w-[85px] h-8 opacity-0"></div>
        </div>
      </div>
    </div>
  );
};

const MissionHistoryPage: React.FC = () => {
  const navigate = useNavigate();

  const { challenges, loading, error } = useChallenges({
    userProgressState: "ARCHIVE",
    limit: 20,
    autoFetch: true,
  });

  const handleMissionClick = (challenge: ChallengeV3) => {
    console.log("Mission clicked:", challenge._id);
    console.log("Challenge data:", challenge);
    // Navigate to mission detail page
    navigate(`/games/missions/${challenge._id}`);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Header */}
      <NavigationHeader
        title="Lịch sử"
        leftIcon={<BackIcon size="sm" />}
        onLeftClick={() => navigate(-1)}
        className="border-b border-[#ECECEC]"
      />

      {/* Content */}
      <div className="px-4 py-4">
        {loading && (
          <div className="text-sm text-gray-500">
            Đang tải lịch sử nhiệm vụ...
          </div>
        )}
        {error && (
          <div className="text-sm text-red-500">Có lỗi xảy ra: {error}</div>
        )}
        {!loading && !error && challenges.length === 0 && (
          <div className="text-sm text-gray-500">Chưa có lịch sử nhiệm vụ.</div>
        )}
        <div className="flex flex-col gap-2.5">
          {challenges.map((c) => (
            <MissionHistoryCardComponent
              key={c._id}
              item={c}
              onMissionClick={handleMissionClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default MissionHistoryPage;
