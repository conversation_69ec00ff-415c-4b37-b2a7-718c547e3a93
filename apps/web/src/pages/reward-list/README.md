# RewardListPage

Trang hiển thị danh sách phần thưởng theo brandCode.

## Features

- ✅ Hiển thị danh sách phần thưởng theo brandCode
- ✅ Sử dụng VoucherCard component để hiển thị thông tin reward
- ✅ Call API `rewardsAPI.getListRewardV2` với pagination
- ✅ Loading states và error handling
- ✅ Load more functionality
- ✅ Responsive design
- ✅ Navigation back button

## Usage

### URL Route

```
/rewards/brand/:brandCode
```

### Examples

```
/rewards/brand/STARBUCKS
/rewards/brand/KFC
/rewards/brand/CIRCLE_K
```

### Navigation

Có thể navigate đến trang này từ:

```tsx
import { useNavigate } from "react-router-dom";

const navigate = useNavigate();

// Navigate to reward list for a specific brand
navigate(`/rewards/brand/${brandCode}`);
```

### API Call

Trang này sử dụng API:

```tsx
const response = await rewardsAPI.getListRewardV2({
  brandCode: "STARBUCKS",
  mobileType: "running",
  offset: 0,
  limit: 10,
});
```

### Component Structure

```
RewardListPage
├── Header (with back button and title)
├── Stats (showing count and brandCode)
├── VoucherCard[] (list of rewards)
├── Load More Button
└── Error States
```

### Props Flow

```tsx
VoucherCard {
  reward: RewardItemType
  onClick: () => navigate(`/reward/${reward.id}`)
}
```

## Features Details

### Pagination

- Initial load: 10 items
- Load more: +10 items per click
- Auto-disable when no more items

### Error Handling

- Network errors
- Empty states
- Retry functionality

### Loading States

- Initial loading spinner
- Load more button loading
- Skeleton states

### Navigation

- Back button returns to previous page
- Clicking reward navigates to reward detail page

## API Response Structure

```typescript
interface RewardDetailResponse {
  totalItem: number;
  rewards: RewardItemType[];
}

interface RewardItemType {
  id: string;
  name: string;
  image1: string;
  merchant: {
    code: string;
    name: string;
    logo: string;
  };
  // ... other fields
}
```

## Styling

Sử dụng Tailwind CSS với:

- Gray-50 background
- White cards với shadow
- Hover effects
- Responsive spacing
- Custom fonts (Archia)

## Performance

- Lazy loading của PriceWeb component
- Efficient re-renders với proper key props
- Optimized API calls với offset/limit
- Image optimization với proper alt tags
