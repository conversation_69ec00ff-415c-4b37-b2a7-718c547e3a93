import React, { useState, useEffect, useRef, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { NavigationHeader, rewardService as rewardsAPI } from "@taptap/shared";
import { RewardItemType } from "@taptap/shared/services/api/rewards/types";
import { LoadingSpinner } from "@taptap/shared";
import { VoucherCard } from "@taptap/shared";

const RewardListPage: React.FC = () => {
  const { brandCode } = useParams<{ brandCode: string }>();
  const navigate = useNavigate();

  const [rewards, setRewards] = useState<RewardItemType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [offset, setOffset] = useState(0);

  const loadMoreTriggerRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const limit = 10;

  const fetchRewards = useCallback(
    async (currentOffset: number = 0, append: boolean = false) => {
      if (!brandCode) {
        setError("Brand code is required");
        setLoading(false);
        return;
      }

      try {
        if (currentOffset === 0) {
          setLoading(true);
        } else {
          setLoadingMore(true);
        }

        setError(null);

        const response = await rewardsAPI.getListRewardV2({
          brandCode,
          mobileType: "running",
          offset: currentOffset,
          limit,
        });

        if (response.status.success && response.data) {
          const newRewards = response.data.rewards || [];

          if (append) {
            setRewards((prev) => [...prev, ...newRewards]);
          } else {
            setRewards(newRewards);
          }

          // Check if there are more items to load
          setHasMore(currentOffset + limit < (response.data.totalItem || 0));
          setOffset(currentOffset + limit);
        } else {
          setError(response.status.message || "Failed to fetch rewards");
        }
      } catch (err) {
        console.error("Error fetching rewards:", err);
        setError(err instanceof Error ? err.message : "Unknown error occurred");
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [brandCode, limit]
  );

  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchRewards(offset, true);
    }
  }, [loadingMore, hasMore, offset, fetchRewards]);

  useEffect(() => {
    fetchRewards(0, false);
  }, [fetchRewards]);

  // Intersection Observer for infinite loading
  useEffect(() => {
    const trigger = loadMoreTriggerRef.current;
    if (!trigger) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !loadingMore && hasMore) {
          loadMore();
        }
      },
      {
        root: scrollContainerRef.current,
        rootMargin: "100px",
        threshold: 0.1,
      }
    );

    observer.observe(trigger);

    return () => {
      observer.unobserve(trigger);
    };
  }, [loadingMore, hasMore, loadMore]);

  const handleRewardClick = (reward: RewardItemType) => {
    navigate(`/reward/${reward.id}`);
  };

  if (loading && rewards.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error && rewards.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <svg
                className="w-16 h-16 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">
              Lỗi tải dữ liệu
            </h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => fetchRewards(0, false)}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
            >
              Thử lại
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="">
        {/* Header */}
        <div className="mb-6">
          <NavigationHeader
            title="Tất cả ưu đãi"
            leftIcon={
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            }
            onLeftClick={() => navigate(-1)}
          />
        </div>

        {/* Scrollable Content with Auto Load More */}
        <div
          ref={scrollContainerRef}
          className="flex-1 overflow-y-auto px-3"
          style={{ height: "calc(100vh - 150px)" }}
        >
          {/* Rewards Grid */}
          {rewards.length === 0 && !loading ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg
                  className="w-20 h-20 mx-auto"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 5l7 7-7 7"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">
                Không có phần thưởng
              </h3>
              <p className="text-gray-600">
                Hiện tại không có phần thưởng nào cho thương hiệu này.
              </p>
            </div>
          ) : (
            <>
              <div className="space-y-4">
                {rewards.map((reward) => (
                  <VoucherCard
                    key={reward.id}
                    reward={reward}
                    onClick={() => handleRewardClick(reward)}
                  />
                ))}
              </div>

              {/* Intersection Observer Trigger */}
              {hasMore && (
                <div
                  ref={loadMoreTriggerRef}
                  className="h-1 w-full"
                  aria-hidden="true"
                />
              )}

              {/* Loading indicator for infinite scroll */}
              {loadingMore && (
                <div className="flex justify-center items-center py-4">
                  <div className="text-gray-500 text-sm">Đang tải thêm...</div>
                </div>
              )}

              {/* End of list indicator */}
              {!hasMore && rewards.length > 0 && (
                <div className="flex justify-center items-center py-4">
                  <div className="text-gray-400 text-sm">Đã hết dữ liệu</div>
                </div>
              )}
            </>
          )}

          {/* Error message for load more */}
          {error && rewards.length > 0 && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-center">{error}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RewardListPage;
