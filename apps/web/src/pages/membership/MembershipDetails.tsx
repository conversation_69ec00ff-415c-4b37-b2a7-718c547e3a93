import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  TierCard,
  TierBenefitCard,
  SectionHeader,
  LoadingSpinner,
  BackButton,
  TabNavigation
} from '@taptap/shared';
import { useMembershipStore, membershipHelpers } from '@taptap/shared';

const MembershipDetails: React.FC = () => {
  const { merchantCode } = useParams<{ merchantCode: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = React.useState(0);
  
  const {
    tierDetail,
    tierRewards,
    loading,
    error,
    stateTier,
    fetchTierDetail,
    fetchTierRewards
  } = useMembershipStore();

  useEffect(() => {
    if (merchantCode) {
      fetchTierDetail(merchantCode);
      //fetchTierRewards(merchantCode);
    }
  }, [merchantCode]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={() => {
              if (merchantCode) {
                fetchTierDetail(merchantCode);
                fetchTierRewards(merchantCode);
              }
            }}
            className="px-4 py-2 bg-primary text-white rounded-lg"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 0, label: 'Thông tin hạng' },
    { id: 1, label: 'Quyền lợi' },
    { id: 2, label: 'Lịch sử' }
  ];

  const currentTierLevel = tierDetail?.tierLevel || 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-[414px] mx-auto bg-white min-h-screen">
        <div className="sticky top-0 bg-white z-10 border-b">
          <div className="flex items-center p-4">
            <BackButton onClick={() => navigate(-1)} />
            <h1 className="flex-1 text-center text-lg font-semibold">
              Chi tiết hạng thành viên
            </h1>
            <div className="w-10" />
          </div>
          
          <TabNavigation
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />
        </div>

        <div className="p-4">
          {activeTab === 0 && tierDetail && (
            <div className="space-y-4">
              <TierCard
                tierName={tierDetail.tierName}
                tierLevel={tierDetail.tierLevel}
                background={tierDetail.tierBackground}
                labelBackground={tierDetail.tierLabelBackground}
                merchantName={tierDetail.merchantName}
                voucherCode={tierDetail.voucherCode}
                isMaxRank={stateTier === 'maxTier'}
              />

              <div className="bg-gray-50 rounded-xl p-4">
                <h3 className="font-semibold text-gray-900 mb-3">
                  Thông tin điểm tích lũy
                </h3>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Điểm hiện tại</span>
                    <span className="font-semibold">
                      {tierDetail.current[0]?.value || 0} {tierDetail.currencyName}
                    </span>
                  </div>
                  
                  {stateTier !== 'maxTier' && tierDetail.merchantTiers && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Điểm cần để lên hạng</span>
                      <span className="font-semibold">
                        {membershipHelpers.totalEarnCurrency(
                          membershipHelpers.getListProgressTier(
                            tierDetail.tierLevel,
                            tierDetail.merchantTiers
                          ).map(tier => ({
                            value: tier.conditions[0]?.value || 0
                          })),
                          tierDetail.current[0]?.value || 0
                        )} {tierDetail.currencyName}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {tierDetail.merchantDescription && (
                <div className="bg-gray-50 rounded-xl p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Về {tierDetail.merchantName}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {tierDetail.merchantDescription}
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 1 && (
            <div className="space-y-4">
              {tierRewards.map((reward) => (
                <TierBenefitCard
                  key={reward.id}
                  level={reward.level}
                  name={reward.name}
                  background={reward.background}
                  logo={reward.logo}
                  conditions={reward.conditions}
                  benefits={reward.benefits}
                  offerName={reward.offerName}
                  description={reward.description}
                  isCurrentTier={reward.level === currentTierLevel}
                  isAchieved={reward.level <= currentTierLevel}
                />
              ))}

              {tierRewards.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-gray-500">
                    Chưa có thông tin quyền lợi
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 2 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg 
                  className="w-24 h-24 mx-auto"
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={1.5} 
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
                  />
                </svg>
              </div>
              <p className="text-gray-500">
                Chưa có lịch sử thay đổi hạng
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MembershipDetails;