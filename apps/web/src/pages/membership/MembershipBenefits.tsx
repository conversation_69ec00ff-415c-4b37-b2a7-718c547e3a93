import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  LoadingSpinner,
  BackButton,
  SwiperTabContainer
} from '@taptap/shared';
import type { SwiperTabContainerItem } from '@taptap/shared';
import { useMembershipStore } from '@taptap/shared';

const MembershipBenefits: React.FC = () => {
  const { merchantCode } = useParams<{ merchantCode: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('');
  
  const {
    tierDetail,
    tierRewards,
    loading,
    error,
    fetchTierRewards,
    fetchTierDetail
  } = useMembershipStore();


  useEffect(() => {
    if (merchantCode) {
      fetchTierRewards(merchantCode);
      if (!tierDetail) {
        fetchTierDetail(merchantCode);
      }
    }
  }, [merchantCode, tierDetail, fetchTierRewards, fetchTierDetail]);

  // Set initial active tab when tierRewards are loaded
  useEffect(() => {
    if (tierRewards.length > 0 && !activeTab) {
      setActiveTab(tierRewards[0].id);
    }
  }, [tierRewards, activeTab]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (error && tierRewards.length === 0) {
    return (
      <div className="min-h-screen bg-[#EFF3F6]">
        <div className="max-w-[414px] mx-auto bg-white min-h-screen">
          {/* Header */}
          <div className="sticky top-0 bg-white z-10 border-b">
            <div className="flex items-center p-4">
              <BackButton onClick={() => navigate(-1)} />
              <h1 className="flex-1 text-center text-lg font-semibold">
                Quyền lợi thành viên
              </h1>
              <div className="w-10" />
            </div>
          </div>

          {/* Tier Banner */}
          {tierDetail && (tierDetail.merchantBackgroundColor || tierDetail.tierBackground) && (
            <div 
              className="h-[170px] bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: (tierDetail.merchantBackgroundColor || tierDetail.tierBackground)?.startsWith('http') 
                  ? `url(${tierDetail.merchantBackgroundColor || tierDetail.tierBackground})`
                  : undefined,
                backgroundColor: (tierDetail.merchantBackgroundColor || tierDetail.tierBackground)?.startsWith('#') 
                  ? tierDetail.merchantBackgroundColor || tierDetail.tierBackground
                  : '#F7CC15'
              }}
            />
          )}

          {/* No rewards message - but don't show it */}
          <div className="p-4">
            <div className="text-center text-gray-500">
              {/* Intentionally empty - hide when no rewards */}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const activeTierReward = tierRewards.find(tier => tier.id === activeTab);
  const tierBannerUrl = tierDetail?.merchantBackgroundColor || tierDetail?.tierBackground;

  // Create tabs from tierRewards
  const tabs: SwiperTabContainerItem[] = tierRewards.map(tier => ({
    id: tier.id,
    label: tier.name
  }));

  return (
    <div className="min-h-screen bg-[#EFF3F6]">
      <div className="max-w-[414px] mx-auto bg-white min-h-screen">
        {/* Header */}
        <div className="sticky top-0 bg-white z-10 border-b">
          <div className="flex items-center p-4">
            <BackButton onClick={() => navigate(-1)} />
            <h1 className="flex-1 text-center text-lg font-semibold">
              Quyền lợi thành viên
            </h1>
            <div className="w-10" />
          </div>
        </div>

        {/* Tier Banner */}
        {tierBannerUrl && (
          <div 
            className="h-[170px] bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: tierBannerUrl.startsWith('http') || tierBannerUrl.startsWith('https')
                ? `url(${tierBannerUrl})`
                : undefined,
              backgroundColor: tierBannerUrl.startsWith('#') || !tierBannerUrl.startsWith('http')
                ? tierBannerUrl
                : '#F7CC15'
            }}
          />
        )}

        {/* Swiper Tab Container with Swipe Navigation */}
        {tierRewards.length > 0 && (
          <SwiperTabContainer
            tabs={tabs}
            activeTabId={activeTab}
            onTabChange={setActiveTab}
            contentClassName="bg-[#EFF3F6] flex-1"
            containerClassName="flex-1"
            scrollableTabs={true}
          >

            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto" style={{ minHeight: 'calc(100vh - 280px)' }}>
              {activeTierReward && (
                <div className="p-3 h-full">
                  {/* Tier Info Header */}
                  <div className="bg-white rounded-t-3xl p-3">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        Ưu đãi bao gồm
                      </h3>
                      
                      {/* Tier Range Badge */}
                      {activeTierReward.tierRange && (
                        <div className="flex items-center bg-yellow-100 rounded-full px-3 py-2">
                          <span className="text-xs text-gray-600 mr-1">
                            Tích đãi
                          </span>
                          <span className="text-sm font-semibold text-gray-900">
                            {activeTierReward.tierRange}
                          </span>
                          {activeTierReward.logo && (
                            <img 
                              src={activeTierReward.logo} 
                              alt="currency"
                              className="w-6 h-6 ml-1"
                            />
                          )}
                        </div>
                      )}
                    </div>

                    {/* Special Reward */}
                    {activeTierReward.offerName && activeTierReward.offerMerchantCode && (
                      <div className="mb-4 p-3 border-2 border-pink-200 rounded-lg bg-pink-50">
                        <div className="flex items-center gap-3">
                          {activeTierReward.offerMerchantLogo && (
                            <img 
                              src={activeTierReward.offerMerchantLogo}
                              alt="Special offer"
                              className="w-12 h-12 rounded-lg object-cover border-2 border-pink-300"
                            />
                          )}
                          <div className="flex-1">
                            <p className="text-sm font-semibold text-pink-800">
                              {activeTierReward.offerName}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Benefits List */}
                  <div className="bg-white rounded-b-3xl overflow-hidden">
                    {activeTierReward.benefits && activeTierReward.benefits.length > 0 ? (
                      <div className="divide-y divide-gray-100">
                        {activeTierReward.benefits.map((benefit, index) => (
                          <div key={index} className="flex items-center p-4">
                            {benefit.image && (
                              <img 
                                src={benefit.image}
                                alt="Benefit"
                                className="w-10 h-10 rounded-lg object-cover mr-3"
                              />
                            )}
                            <div className="flex-1">
                              <p className="text-sm text-gray-900 leading-relaxed">
                                {benefit.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-8 text-center text-gray-500">
                        <p>Chưa có thông tin quyền lợi cho hạng này</p>
                      </div>
                    )}
                  </div>

                  {/* Description */}
                  {activeTierReward.description && (
                    <div className="mt-4 p-4 bg-white rounded-xl">
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">
                        Mô tả
                      </h4>
                      <div 
                        className="text-sm text-gray-600 leading-relaxed"
                        dangerouslySetInnerHTML={{ __html: activeTierReward.description }}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </SwiperTabContainer>
        )}

      </div>
    </div>
  );
};

export default MembershipBenefits;