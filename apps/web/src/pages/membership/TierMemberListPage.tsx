import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  NavigationHeader,
  LoyaltyCard,
  Button,
  membershipApi,
  ITierMember,
  ITierClassDetail,
  membershipHelpers,
  IStateTier
} from '@taptap/shared';

const TierMemberListPage: React.FC = () => {
  const navigate = useNavigate();
  const [tierMembers, setTierMembers] = useState<ITierMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTierMembers();
  }, []);

  const fetchTierMembers = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all tier members with high limit
      const response = await membershipApi.getTierMemberList(0, 20);
      
      // Handle the new API response format
      const tierMemberData = response?.data || [];
      setTierMembers(tierMemberData);
    } catch (err: any) {
      setError(err?.message || 'Không thể tải danh sách thẻ thành viên');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleTierCardClick = (tierMember: ITierMember) => {
    // Navigate to membership details page with merchant code
    navigate(`/membership/details/${tierMember.merchantCode}`);
  };

  const getCardStatus = (tierMember: ITierMember): 'no-join' | 'without-rank' | 'with-rank' | 'max-rank' => {
    const stateTier = membershipHelpers.getStateTier(tierMember as ITierClassDetail);
    
    switch (stateTier) {
      case 'noRank':
        return 'no-join';
      case 'hasRankTier':
      case 'hasRankAndVoucher':
        return 'with-rank';
      case 'maxTier':
        return 'max-rank';
      default:
        return 'without-rank';
    }
  };

  const calculateProgress = (tierMember: ITierMember): number => {
    if (!tierMember.current?.[0] || !tierMember.merchantTiers?.length) return 0;
    
    const currentValue = tierMember.current[0].value;
    const listTier = tierMember.merchantTiers.map(tier => ({
      value: tier.conditions?.[0]?.value || 0,
      tierName: tier.tierName
    }));

    return membershipHelpers.calculateProgress(currentValue, listTier);
  };

  const getPointsToNextLevel = (tierMember: ITierMember): number => {
    if (!tierMember.current?.[0] || !tierMember.merchantTiers?.length) return 0;
    
    const currentValue = tierMember.current[0].value;
    const listTier = tierMember.merchantTiers.map(tier => ({
      value: tier.conditions?.[0]?.value || 0
    }));

    return membershipHelpers.totalEarnCurrency(listTier, currentValue);
  };

  // Back icon
  const BackIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M15 18L9 12L15 6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Thẻ thành viên"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
        />
        <div className="flex items-center justify-center pt-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F65D79] mx-auto mb-4"></div>
            <p className="text-[#9A9A9A]">Đang tải...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavigationHeader
          title="Thẻ thành viên"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
        />
        <div className="flex items-center justify-center pt-20">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={fetchTierMembers} variant="outline">
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#EFF3F6]">
      {/* Navigation Header */}
      <NavigationHeader
        title="Thẻ thành viên"
        leftIcon={BackIcon}
        onLeftClick={handleBack}
      />

      <div className="max-w-md mx-auto pb-6">
        {/* Tier Member Cards */}
        <div className="p-4 space-y-3">
          {tierMembers.map((tierMember, index) => (
            <div key={`${tierMember.merchantCode}-${index}`} className="flex justify-center">
              <LoyaltyCard
                status={getCardStatus(tierMember)}
                theme="default"
                brandName={tierMember.merchantName}
                userTitle={tierMember.tierName}
                pointsToNextLevel={getPointsToNextLevel(tierMember)}
                currentProgress={calculateProgress(tierMember)}
                maxProgress={100}
                badgeIconSrc={tierMember.tierLogo}
                onActionClick={() => handleTierCardClick(tierMember)}
              />
            </div>
          ))}

          {tierMembers.length === 0 && (
            <div className="text-center py-20">
              <p className="text-[#9A9A9A] mb-4">Chưa có thẻ thành viên nào</p>
              <Button onClick={() => navigate('/home')} variant="outline">
                Khám phá ưu đãi
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TierMemberListPage;