import React from 'react';
import { Mascot } from '@taptap/shared';

interface NoRewardComponentProps {
  tierName?: string;
}

const NoRewardComponent: React.FC<NoRewardComponentProps> = ({ tierName }) => {
  return (
    <div className="flex-1 flex flex-col items-center justify-center px-8">
      <div className="mb-6">
        <Mascot 
          name="sleep" 
          className="mx-auto"
          imageStyle={{ width: '120px', height: '90px' }}
        />
      </div>
      
      <div className="text-center">
        <h2 
          className="text-xl font-semibold text-black mb-2"
          style={{ fontFamily: 'Archia' }}
        >
          Chưa có ưu đãi
        </h2>
        
        <p 
          className="text-base text-black/70 leading-6"
          style={{ fontFamily: 'Archia' }}
        >
          {tierName ? 
            `Hạng ${tierName} chưa có ưu đãi đặc biệt. Hãy tích điểm để nâng hạng bạn nhé!` :
            'Chưa có ưu đãi đặc biệt. Hãy tích điểm để nhận ưu đãi bạn nhé!'
          }
        </p>
      </div>
    </div>
  );
};

export default NoRewardComponent;