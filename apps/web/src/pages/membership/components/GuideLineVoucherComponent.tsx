import React, { useState } from 'react';

interface GuideLineVoucherComponentProps {
  tnc: string;
}

const GuideLineVoucherComponent: React.FC<GuideLineVoucherComponentProps> = ({ tnc }) => {
  const [expanded, setExpanded] = useState(false);

  if (!tnc) return null;

  return (
    <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4">
      <div className="flex items-center justify-between mb-2">
        <h3 
          className="text-sm font-semibold text-black"
          style={{ fontFamily: 'Archia' }}
        >
          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n sử dụng
        </h3>
        <button
          onClick={() => setExpanded(!expanded)}
          className="p-1 rounded-full hover:bg-black/5 transition-colors"
        >
          <svg 
            width="16" 
            height="16" 
            viewBox="0 0 16 16" 
            fill="none"
            className={`transform transition-transform ${expanded ? 'rotate-180' : ''}`}
          >
            <path 
              d="M4 6L8 10L12 6" 
              stroke="#000" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>
      
      {expanded && (
        <div className="mt-3">
          <p 
            className="text-xs text-black/80 leading-5"
            style={{ fontFamily: 'Archia' }}
            dangerouslySetInnerHTML={{ __html: tnc }}
          />
        </div>
      )}
      
      {!expanded && (
        <p 
          className="text-xs text-black/80 leading-5"
          style={{ fontFamily: 'Archia' }}
        >
          Nhấn để xem chi tiết điều khoản sử dụng ưu đãi
        </p>
      )}
    </div>
  );
};

export default GuideLineVoucherComponent;