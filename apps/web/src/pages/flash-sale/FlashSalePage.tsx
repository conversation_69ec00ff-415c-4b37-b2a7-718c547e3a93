import React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { BottomNavigation } from '../../components/navigation';
import { 
  NavigationHeader,
  InformFlashSale,
  RewardListCard,
  LoadingSpinner,
  EmptyBillState,
  BackIcon,
  useFlashSale
} from '@taptap/shared';
import type { NavigationItem, RewardItemType } from '@taptap/shared';

export const FlashSalePage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Get params from URL
  const collectionCode = searchParams.get('collectionCode') || 'flash-sale';
  const collectionName = searchParams.get('collectionName') || 'Flash Sale';
  const hasFlashSale = searchParams.get('hasFlashSale') === 'true';
  const _status = searchParams.get('status') || '';
  
  // Use extracted hook
  const {
    flashSaleCampaign,
    rewardList,
    loading,
    error,
    currentDateTime,
    handleCountdownCallback,
    handleRewardClick,
    handleBack,
    isFlashSaleActive,
    isCountdownActive,
    hasTeasing,
    backgroundColor,
  } = useFlashSale({
    collectionCode,
    hasFlashSale,
    status: _status,
    onNavigate: (path: string) => navigate(path),
    onNavigateBack: (steps?: number) => navigate(steps ? -steps : -1),
  });

  // Navigation items for bottom navigation
  const navigationItems: NavigationItem[] = [
    {
      id: 'home',
      label: 'Trang chủ',
      icon: 'home',
      href: '/'
    },
    {
      id: 'voucher', 
      label: 'Ưu đãi',
      icon: 'voucher',
      href: '/exchange'
    },
    {
      id: 'exchange',
      label: 'Đổi thưởng', 
      icon: 'gift',
      href: '/exchange'
    },
    {
      id: 'games',
      label: 'VUI chơi',
      icon: 'game',
      href: '/games'
    },
    {
      id: 'profile',
      label: 'Tài khoản',
      icon: 'user', 
      href: '/profile'
    }
  ];

  return (
    <div className="w-full min-h-screen bg-[#EFF3F6] flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title={collectionName}
        leftIcon={<BackIcon />}
        onLeftClick={handleBack}
        className={isFlashSaleActive ? "bg-[#F65D79] text-white border-b border-[#F65D79]" : ""}
      />

      {/* Flash Sale Info Bar */}
      {(isFlashSaleActive || isCountdownActive || hasTeasing) && (
        <InformFlashSale
          campaign={flashSaleCampaign}
          currentDateTime={currentDateTime}
          callback={handleCountdownCallback}
          canPurchase={true}
        />
      )}

      {/* Main Content */}
      <div 
        className="flex-1 px-4 py-4 pb-20"
        style={{ backgroundColor }}
      >
        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-16">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <EmptyBillState
            title="Có lỗi xảy ra"
            description={error}
            mascotName="error"
            onActionClick={() => window.location.reload()}
            actionText="Thử lại"
          />
        )}

        {/* Flash Sale Content */}
        {!loading && !error && (
          <>
 

            {/* Rewards List */}
            {rewardList.length > 0 ? (
              <div className="space-y-3">
                {rewardList.map((reward) => {
                  // Flash sale pricing logic based on mobile implementation
                  const hasFlashSalePrice = reward.displayOnPrice && 
                    reward.issueVUIPointFlsOrPP && 
                    reward.issueVUIPointFlsOrPP !== reward.issueVUIPoint;
                  
                  return (
                    <RewardListCard
                      key={reward.id}
                      id={reward.id}
                      brand={reward.merchant?.name}
                      title={reward.name}
                      points={hasFlashSalePrice ? undefined : (reward.issueVUIPoint || 0)}
                      originalPoints={hasFlashSalePrice ? reward.issueVUIPoint : undefined}
                      flashSalePoints={hasFlashSalePrice ? reward.issueVUIPointFlsOrPP : undefined}
                      logoSrc={reward.merchant?.logo || '/shared/assets/images/placeholder-logo.png'}
                      imageSrc={reward.image1 || '/shared/assets/images/placeholder-reward.png'}
                      discountPercent={reward.discountPercentage || 0}
                      available={true}
                      expiry={false}
                      onClick={() => handleRewardClick(reward)}
                    />
                  );
                })}
              </div>
            ) : (
              <EmptyBillState
                title="Chưa có sản phẩm Flash Sale"
                description="Hiện tại chưa có sản phẩm nào trong chương trình Flash Sale. Hãy quay lại sau nhé!"
                mascotName="meditate"
                onActionClick={() => navigate('/exchange')}
                actionText="Xem sản phẩm khác"
              />
            )}
          </>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 w-full z-50">
        <BottomNavigation
          items={navigationItems}
          activeItemId="exchange"
          onItemClick={(itemId) => {
            const item = navigationItems.find(nav => nav.id === itemId);
            if (item?.href) {
              navigate(item.href);
            }
          }}
        />
      </div>
    </div>
  );
};

export default FlashSalePage;