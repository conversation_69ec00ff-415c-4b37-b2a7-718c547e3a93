import React, { useEffect, useState, useRef, useCallback } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { LoadingSpinner, StoreNearbySection } from "@taptap/shared";
import { merchantService as merchantAPI } from "@taptap/shared";
import type { OfflineStoreType } from "@taptap/shared";

const DEFAULT_LAT = 10.8231; // HCMC
const DEFAULT_LNG = 106.6297;
const STORES_PER_PAGE = 20;

const AllStoresPage: React.FC = () => {
  const { code } = useParams<{ code: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const search = new URLSearchParams(location.search);
  const merchantCode = code || search.get("merchantCode") || "";

  const [loading, setLoading] = useState(true);
  const [stores, setStores] = useState<OfflineStoreType[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentOffset, setCurrentOffset] = useState(0);

  // Ref for intersection observer
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Load stores function
  const loadStores = useCallback(
    async (offset: number, append: boolean = false) => {
      try {
        if (append) {
          setLoadingMore(true);
        } else {
          setLoading(true);
        }
        setError(null);

        let lat = DEFAULT_LAT;
        let lng = DEFAULT_LNG;
        try {
          await new Promise<void>((resolve) => {
            if (!navigator.geolocation) return resolve();
            navigator.geolocation.getCurrentPosition(
              (pos) => {
                lat = pos.coords.latitude;
                lng = pos.coords.longitude;
                resolve();
              },
              () => resolve(),
              { timeout: 5000, maximumAge: 10000 }
            );
          });
        } catch {
          // Ignore geolocation errors
        }

        const res = await merchantAPI.getMerchantStores({
          merchantCode: merchantCode,
          action: "NEAR_BY",
          latitude: lat,
          longitude: lng,
          radius: 999999999,
          offset: offset,
          limit: STORES_PER_PAGE,
        });

        if (res.status.success && res.data) {
          if (append) {
            setStores((prev) => [...prev, ...res.data]);
          } else {
            setStores(res.data);
          }

          // Check if there are more stores to load
          setHasMore(res.data.length === STORES_PER_PAGE);
          setCurrentOffset(offset + res.data.length);
        }
      } catch {
        setError("Không thể tải danh sách cửa hàng");
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [merchantCode]
  );

  // Initial load
  useEffect(() => {
    if (merchantCode) {
      loadStores(0, false);
    }
  }, [merchantCode, loadStores]);

  // Intersection observer for auto load more
  useEffect(() => {
    if (!loadMoreRef.current || !hasMore || loadingMore) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !loadingMore) {
          loadStores(currentOffset, true);
        }
      },
      {
        rootMargin: "100px", // Load more when 100px away from bottom
        threshold: 0.1,
      }
    );

    observerRef.current.observe(loadMoreRef.current);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loadingMore, currentOffset, loadStores]);

  if (!merchantCode) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-sm text-gray-600">Thiếu merchantCode</div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-[#EFF3F6]">
      <div className="bg-white px-4 py-3 flex items-center gap-2 border-b border-[#ECECEC]">
        <button onClick={() => navigate(-1)} className="p-1">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path
              d="M15 18L9 12L15 6"
              stroke="#222"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
        <h1 className="text-base font-semibold">Tất cả cửa hàng</h1>
      </div>

      <div className="my-3">
        <StoreNearbySection stores={stores} />
      </div>

      {/* Load more indicator */}
      {hasMore && (
        <div ref={loadMoreRef} className="px-4 py-4">
          {loadingMore ? (
            <div className="flex items-center justify-center">
              <LoadingSpinner />
              <span className="ml-2 text-sm text-gray-600">
                Đang tải thêm cửa hàng...
              </span>
            </div>
          ) : (
            <div className="text-center text-sm text-gray-500">
              Cuộn xuống để xem thêm
            </div>
          )}
        </div>
      )}

      {/* End of list indicator */}
      {!hasMore && stores.length > 0 && (
        <div className="px-4 py-4 text-center text-sm text-gray-500">
          Đã hiển thị tất cả {stores.length} cửa hàng
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="px-4 py-4 text-center text-sm text-red-500">
          {error}
        </div>
      )}
    </div>
  );
};

export default AllStoresPage;
