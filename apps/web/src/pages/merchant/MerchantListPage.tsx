import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON><PERSON><PERSON>er,
  SwiperTabContainer,
  Loading<PERSON><PERSON>ner,
  appPath,
  MerchantCard,
  BackIcon,
} from "@taptap/shared";
import { merchantService as merchantAPI } from "@taptap/shared";
import type {
  MerchantType,
  MerchantCollection as CollectionType,
  MerchantItemData,
  SwiperTabContainerItem,
} from "@taptap/shared";
import { iconsAssets } from "../../assets/icons";

const PAGE_LIMIT = 12;

// Cache for collections to prevent duplicate API calls in StrictMode
const collectionsCache: {
  data: CollectionType[] | null;
  loading: boolean;
  error: string | null;
  promise: Promise<any> | null;
} = {
  data: null,
  loading: false,
  error: null,
  promise: null
};

// Thinking in React: split data fetching into hooks and UI into small components

type MerchantGridProps = {
  merchants: MerchantType[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadingMore: boolean;
  onLoadMore: () => void;
  onItemClick: (merchant: MerchantType) => void;
  collectionCode: string;
};

const MerchantGrid: React.FC<MerchantGridProps> = ({
  merchants,
  loading,
  error,
  hasMore,
  loadingMore,
  onLoadMore,
  onItemClick,
  collectionCode,
}) => {
  const sentinelRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!hasMore) return;
    const target = sentinelRef.current;
    if (!target) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !loading && !loadingMore) {
          onLoadMore();
        }
      },
      { root: null, rootMargin: "200px 0px", threshold: 0.01 }
    );

    observer.observe(target);
    return () => observer.disconnect();
  }, [hasMore, loading, loadingMore, onLoadMore, merchants.length, collectionCode]);

  return (
    <div className="px-4 py-4">
      {loading && (
        <div className="w-full flex justify-center py-10">
          <LoadingSpinner />
        </div>
      )}

      {!loading && error && (
        <div className="text-center text-red-500 text-sm py-6">{error}</div>
      )}

      {!loading && !error && merchants.length === 0 && (
        <div className="text-center text-gray-500 text-sm py-6">
          Chưa có thương hiệu trong danh mục này
        </div>
      )}

      {!loading && !error && merchants.length > 0 && (
        <div className="grid grid-cols-2 gap-3">
          {merchants.map((item: MerchantType) => {
            return (
              <MerchantCard
                key={item.id}
                merchant={item as any}
                onClick={onItemClick}
              />
            );
          })}
        </div>
      )}

      {/* Infinite scroll sentinel */}
      {!loading && !error && merchants.length > 0 && (
        <div ref={sentinelRef} className="h-6" />
      )}

      {/* Loading more indicator */}
      {loadingMore && (
        <div className="w-full flex justify-center py-4">
          <LoadingSpinner />
        </div>
      )}
    </div>
  );
};

// Data hooks with global caching to prevent duplicate calls in StrictMode
const useMerchantCollections = () => {
  const [collections, setCollections] = useState<CollectionType[]>(collectionsCache.data || []);
  const [loading, setLoading] = useState<boolean>(!collectionsCache.data && !collectionsCache.error);
  const [error, setError] = useState<string | null>(collectionsCache.error);

  const refresh = useCallback(async () => {
    // If already loading, return existing promise
    if (collectionsCache.promise) {
      return collectionsCache.promise;
    }

    // If data already exists, no need to refetch
    if (collectionsCache.data) {
      setCollections(collectionsCache.data);
      setLoading(false);
      setError(null);
      return;
    }

    try {
      collectionsCache.loading = true;
      setLoading(true);
      setError(null);

      collectionsCache.promise = merchantAPI.getCollections();
      const res = await collectionsCache.promise;

      if (!res.status.success) throw new Error(res.status.message);
      
      const data = res.data || [];
      collectionsCache.data = data;
      collectionsCache.error = null;
      setCollections(data);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : "Không thể tải danh mục";
      collectionsCache.error = errorMsg;
      setError(errorMsg);
    } finally {
      collectionsCache.loading = false;
      collectionsCache.promise = null;
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    refresh();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return { collections, loading, error, refresh };
};

const useMerchantsByCollection = (collectionCode: string) => {
  const [merchants, setMerchants] = useState<MerchantType[]>([]);
  const [offset, setOffset] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPage = useCallback(
    async (startOffset: number, append: boolean) => {
      if (!collectionCode) return;
      try {
        if (append) setLoadingMore(true);
        else setLoading(true);
        setError(null);
        const res = await merchantAPI.getMerchantListByCollection({
          collectionCode,
          offset: startOffset,
          limit: PAGE_LIMIT,
        });
        if (!res.status.success) throw new Error(res.status.message);
        const list = res.data || [];
        setMerchants((prev) => (append ? [...prev, ...list] : list));
        setHasMore(list.length === PAGE_LIMIT);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Không thể tải danh sách thương hiệu"
        );
      } finally {
        if (append) setLoadingMore(false);
        else setLoading(false);
      }
    },
    [collectionCode]
  );

  useEffect(() => {
    // reset when collection changes
    setOffset(0);
    setMerchants([]);
    setHasMore(true);
    if (collectionCode) fetchPage(0, false);
  }, [collectionCode, fetchPage]);

  const loadMore = useCallback(() => {
    if (!hasMore || loadingMore) return;
    const nextOffset = offset + PAGE_LIMIT;
    setOffset(nextOffset);
    fetchPage(nextOffset, true);
  }, [hasMore, loadingMore, offset, fetchPage]);

  return { merchants, loading, loadingMore, error, hasMore, loadMore };
};

// Component for individual merchant tab content with lazy loading
const MerchantTabContent: React.FC<{
  collectionCode: string;
  onMerchantClick: (merchant: MerchantType) => void;
  isActive: boolean;
  hasBeenVisited: boolean;
}> = ({ collectionCode, onMerchantClick, isActive, hasBeenVisited }) => {
  // Only call API hook if tab is active or has been visited
  const shouldLoad = isActive || hasBeenVisited;
  
  const {
    merchants,
    loading,
    loadingMore,
    error,
    hasMore,
    loadMore,
  } = useMerchantsByCollection(shouldLoad ? collectionCode : '');

  if (!shouldLoad) {
    // Return placeholder for unvisited tabs
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-gray-500">Chạm để tải dữ liệu...</div>
      </div>
    );
  }

  return (
    <MerchantGrid
      merchants={merchants}
      loading={loading}
      error={error}
      hasMore={hasMore}
      loadingMore={loadingMore}
      onLoadMore={loadMore}
      onItemClick={onMerchantClick}
      collectionCode={collectionCode}
    />
  );
};

const MerchantListPage: React.FC = () => {
  const navigate = useNavigate();

  // Collections and selection state
  const {
    collections,
    loading: collectionsLoading,
    error: collectionsError,
  } = useMerchantCollections();
  const [selectedCollectionCode, setSelectedCollectionCode] =
    useState<string>("");
  
  // Track visited tabs for lazy loading
  const [visitedTabs, setVisitedTabs] = useState<Set<string>>(new Set());

  // Set default selected collection when loaded
  useEffect(() => {
    if (!selectedCollectionCode && collections.length > 0) {
      const firstCollection = collections[0].code;
      setSelectedCollectionCode(firstCollection);
      // Mark first tab as visited
      setVisitedTabs(new Set([firstCollection]));
    }
  }, [collections, selectedCollectionCode]);

  const handleBack = () => navigate(-1);
  
  const handleTabChange = useCallback((tabId: string) => {
    setSelectedCollectionCode(tabId);
    // Mark tab as visited when user switches to it
    setVisitedTabs(prev => new Set([...prev, tabId]));
  }, []);
  
  const handleMerchantClick = useCallback((merchant: MerchantType) => {
    // Convert MerchantType to code for navigation
    const merchantCode = (merchant as any).code || merchant.id;
    navigate(appPath.merchantDetail(merchantCode));
  }, [navigate]);

  const tabs: SwiperTabContainerItem[] = useMemo(() => {
    return collections
      .slice()
      .sort((a, b) => (a.index || 0) - (b.index || 0))
      .map((c) => ({ 
        id: c.code, 
        label: c.name,
        content: (
          <MerchantTabContent
            key={c.code}
            collectionCode={c.code}
            onMerchantClick={handleMerchantClick}
            isActive={selectedCollectionCode === c.code}
            hasBeenVisited={visitedTabs.has(c.code)}
          />
        )
      }));
  }, [collections, handleMerchantClick, selectedCollectionCode, visitedTabs]);

  if (collectionsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex justify-center items-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (collectionsError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white border-b border-[#ECECEC]">
          <NavigationHeader
            title="Thương hiệu tích điểm"
            leftIcon={<BackIcon />}
            onLeftClick={handleBack}
            className="h-[48px]"
          />
        </div>
        <div className="flex flex-col items-center justify-center py-8 px-4">
          <div className="text-red-500 mb-4">{collectionsError}</div>
          <button 
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <div className="bg-white border-b border-[#ECECEC]">
        <NavigationHeader
          title="Thương hiệu tích điểm"
          leftIcon={<BackIcon />}
          onLeftClick={handleBack}
          className="h-[48px]"
        />
      </div>

      {/* Swipeable Tabs Container */}
      <SwiperTabContainer
        tabs={tabs}
        activeTabId={selectedCollectionCode}
        onTabChange={handleTabChange}
        contentClassName="bg-gray-50 flex-1"
        containerClassName="flex-1"
        minSlideHeight="calc(100vh - 96px)"
        scrollableTabs={true}
      />

      {/* Floating Favorite Button - Bottom Right */}
      <button
        onClick={() => navigate(appPath.favorites())}
        className="fixed bottom-12 right-6 z-50 w-14 h-14 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-[#E54D6A] transition-colors duration-200"
        aria-label="Danh sách yêu thích"
      >
        <img src={iconsAssets.wishlist} alt="wishlist" className="w-6 h-6" />
      </button>
    </div>
  );
};

export default MerchantListPage;
