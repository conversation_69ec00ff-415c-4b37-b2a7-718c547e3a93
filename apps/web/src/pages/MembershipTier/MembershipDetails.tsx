import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  SwiperTabContainer,
  StatusScreen,
  useAuthStore,
  membershipApi,
  ITierMember,
} from '@taptap/shared';

// Import tab components
import RankRewardTab from './components/RankRewardTab';
import MemberCodeTab from './components/MemberCodeTab';

interface MembershipDetailsProps {}

const MembershipDetails: React.FC<MembershipDetailsProps> = () => {
  const { merchantCode } = useParams<{ merchantCode: string }>();
  
  // State management matching mobile
  const [loading, setLoading] = useState(true);
  const [status, setStatus] = useState<'NO_GIFT' | 'ERROR' | 'SUCCESS' | 'LOADING'>('LOADING');
  const [rewardDetailData, setRewardDetailData] = useState<{
    tierDetail?: ITierMember;
    voucherDetail?: any;
    balanceData?: any;
  }>({});
  const [activeTabId, setActiveTabId] = useState('rank_reward');

  // Fetch reward detail data on mount (matching mobile useFetchRewardDetail hook)
  useEffect(() => {
    if (!merchantCode) {
      setStatus('ERROR');
      setLoading(false);
      return;
    }

    fetchRewardDetail();
  }, [merchantCode]);

  const fetchRewardDetail = async () => {
    try {
      setLoading(true);
      setStatus('LOADING');
      
      // Use membership API service to get tier details
      const tierResponse = await membershipApi.getTierDetailWebview(merchantCode!);
      
      if (!tierResponse.status.success || !tierResponse.data) {
        setStatus('NO_GIFT');
        setRewardDetailData({});
        return;
      }
      
      const tierDetail = tierResponse.data;
      
      // Check gift status and fetch voucher details if available
      let voucherDetail = null;
      if (tierDetail.giftStatus === 'SUCCESS' && tierDetail.voucherCodeId) {
        try {
          const voucherResponse = await membershipApi.getVoucherDetail(tierDetail.voucherCodeId);
          if (voucherResponse.status.success) {
            voucherDetail = voucherResponse.data;
          }
        } catch (voucherError) {
          console.error('Failed to fetch voucher details:', voucherError);
        }
      }
      
      setStatus(tierDetail.giftStatus || 'NO_GIFT');
      setRewardDetailData({
        tierDetail,
        voucherDetail: voucherDetail || {
          name: tierDetail.tierName,
          tnc: 'Điều khoản sử dụng ưu đãi thành viên...'
        },
        balanceData: tierDetail.current?.[0] || {}
      });
      
    } catch (error) {
      console.error('Failed to fetch reward details:', error);
      setStatus('ERROR');
      setRewardDetailData({});
    } finally {
      setLoading(false);
    }
  };

  // Tab configuration matching mobile
  const tabs = [
    {
      id: 'rank_reward',
      label: 'Ưu đãi hạng',
      content: (
        <RankRewardTab
          loading={loading}
          status={status}
          rewardDetailData={rewardDetailData}
        />
      )
    },
    {
      id: 'member_code',
      label: 'Mã thành viên',
      content: (
        <MemberCodeTab
          rewardDetailData={rewardDetailData}
        />
      )
    }
  ];

  // Loading state matching mobile
  if (loading) {
    return (
      <div className="fixed inset-0 bg-[#F7CC15] z-50 flex items-center justify-center">
        <StatusScreen type="loading_error" />
      </div>
    );
  }

  // Special case: Show only MemberCode tab if NO_GIFT but has tier data
  if (status === 'NO_GIFT' && rewardDetailData.tierDetail && rewardDetailData.balanceData) {
    return (
      <div className="fixed inset-0 bg-[#F7CC15] z-50">
        <MemberCodeTab
          rewardDetailData={{
            tierDetail: rewardDetailData.tierDetail,
            balanceData: rewardDetailData.balanceData,
          }}
        />
      </div>
    );
  }

  // Main TabView layout matching mobile - full screen with bottom tabs
  return (
    <div className="fixed inset-0 bg-[#F7CC15] z-50 flex flex-col">
      {/* Content Area */}
      <div className="flex-1 overflow-hidden">
        {activeTabId === 'rank_reward' ? (
          <RankRewardTab
            status={status}
            rewardDetailData={rewardDetailData}
          />
        ) : (
          <MemberCodeTab
            rewardDetailData={rewardDetailData}
          />
        )}
      </div>

      {/* Bottom Tab Navigation - matching mobile TabBar position="bottom" */}
      <div className="bg-white border-t border-[#ECECEC]">
        <div className="flex h-[60px]">
          {tabs.map((tab) => {
            const isActive = tab.id === activeTabId;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTabId(tab.id)}
                className={`flex-1 flex flex-col items-center justify-center gap-1 py-2 transition-colors ${
                  isActive 
                    ? 'text-[#F65D79] border-b-2 border-[#F65D79]' 
                    : 'text-[#8E8E93]'
                }`}
              >
                {/* Tab Icon */}
                <div className="w-6 h-6 flex items-center justify-center">
                  {tab.id === 'rank_reward' ? (
                    <img 
                      src={`/shared/assets/icons/${isActive ? 'voucher' : 'voucher-grey'}.png`} 
                      alt="Voucher"
                      className="w-6 h-6"
                      onError={(e) => {
                        // Fallback to SVG if image not found
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling.style.display = 'block';
                      }}
                    />
                  ) : (
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <rect x="3" y="4" width="18" height="16" rx="2" stroke="currentColor" strokeWidth="1.5"/>
                      <path d="M7 8h10M7 12h6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                    </svg>
                  )}
                </div>
                
                {/* Tab Label */}
                <span className="text-xs font-medium">{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default MembershipDetails;