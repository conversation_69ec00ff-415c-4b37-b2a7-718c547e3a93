import React from 'react';
import { useNavigate } from 'react-router-dom';
import { NavigationHeader, Card } from '@taptap/shared';
import coinIcon from '../../../../shared/assets/icons/coin-icon.svg';

export interface MembershipTierPageProps {
  currentTier?: 'SILVER' | 'GOLD' | 'PLATINUM';
  currentPoints?: number;
  pointsToNextTier?: number;
  expiringPoints?: number;
  expiryDate?: string;
  avatarSrc?: string;
  onBackClick?: () => void;
  onBenefitsClick?: () => void;
  onHistoryClick?: () => void;
}

const TierBadge: React.FC<{ tier: string; isActive?: boolean }> = ({ tier, isActive }) => (
  <div className={`text-[10px] font-semibold ${isActive ? 'text-[#0E0E0E]' : 'text-[#0E0E0E]/50'}`}>
    {tier}
  </div>
);

const TierMilestone: React.FC<{ points: string | number; isActive?: boolean }> = ({ points, isActive }) => (
  <div className={`px-1 py-0.5 rounded-[20px] ${isActive ? 'bg-[#F87A91]' : 'bg-[#F87A91]/50'}`}>
    <span className="text-[12px] text-white">{points}</span>
  </div>
);

export const MembershipTierPage: React.FC<MembershipTierPageProps> = ({
  currentTier = 'SILVER',
  currentPoints = 720,
  pointsToNextTier = 80,
  expiringPoints = 80,
  expiryDate = '30/06/2025',
  avatarSrc = '/src/assets/images/gold-tier-avatar.png',
  onBackClick,
  onBenefitsClick,
  onHistoryClick,
}) => {
  const navigate = useNavigate();
  
  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      navigate(-1);
    }
  };

  const getTierProgress = () => {
    if (currentTier === 'SILVER') {
      return (currentPoints / 800) * 100;
    } else if (currentTier === 'GOLD') {
      return ((currentPoints - 800) / (3000 - 800)) * 100 + 33.33;
    }
    return 100;
  };

  const getCurrentTierLabel = () => {
    return currentTier === 'GOLD' ? 'Thành viên Vàng' : `Thành viên ${currentTier.toLowerCase()}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#08C09A] to-[#02D692]">
      {/* Navigation Header */}
      <NavigationHeader
        title="Hạng thành viên"
        showBackButton={true}
        onBackClick={handleBackClick}
        className="bg-white"
      />

      {/* Main Content */}
      <div className="px-4 pt-[31px]">
        {/* Avatar and Tier Badge */}
        <div className="flex flex-col items-center gap-2 mb-8">
          <img 
            src={avatarSrc} 
            alt="Tier Avatar" 
            className="w-[84px] h-[84px] rounded-full"
          />
          <div className="bg-white px-4 py-1 rounded-[20px] border border-[#F65D79]">
            <span className="text-[16px] font-semibold text-[#0E0E0E]">
              {getCurrentTierLabel()}
            </span>
          </div>
        </div>

        {/* Progress Card */}
        <div className="bg-white/80 rounded-xl p-4 mb-3">
          {/* Expiring Points Notice */}
          <div className="text-center mb-4">
            <p className="text-[12px] text-[#1A1818]">
              {expiringPoints} EPoint sẽ hết hạn vào {expiryDate}
            </p>
          </div>

          {/* Progress Text */}
          <div className="flex items-center justify-center gap-1 mb-4">
            <span className="text-[14px] text-[#1A1818]">Tích thêm</span>
            <span className="text-[16px] font-semibold text-black">{pointsToNextTier} điểm</span>
            <span className="text-[14px] text-[#1A1818]">để nâng hạng bạn nhé</span>
          </div>

          {/* Progress Bar */}
          <div className="relative">
            {/* Background Bar */}
            <div className="h-[7px] bg-white rounded-full relative">
              {/* Progress Fill */}
              <div 
                className="absolute left-0 top-0 h-full bg-[#F65D79] rounded-full transition-all duration-300"
                style={{ width: `${getTierProgress()}%` }}
              />
            </div>

            {/* Tier Markers */}
            <div className="absolute -top-6 left-0 right-0 flex justify-between px-3">
              <TierMilestone points="0" isActive={true} />
              <TierMilestone points="800" isActive={currentPoints >= 800} />
              <TierMilestone points="3k" isActive={currentPoints >= 3000} />
            </div>

            {/* Tier Labels */}
            <div className="flex justify-between mt-3 px-3">
              <TierBadge tier="SILVER" isActive={true} />
              <TierBadge tier="GOLD" isActive={currentPoints >= 800} />
              <TierBadge tier="PLATINUM" isActive={currentPoints >= 3000} />
            </div>

            {/* Current Points Indicator */}
            <div 
              className="absolute -top-2 flex items-center justify-center"
              style={{ left: `${getTierProgress()}%`, transform: 'translateX(-50%)' }}
            >
              <div className="relative">
                <svg width="33" height="21" viewBox="0 0 33 21" fill="none">
                  <path d="M0 0L33 0V21L16.5 14L0 21V0Z" fill="url(#gradient)" />
                  <defs>
                    <linearGradient id="gradient" x1="33" y1="21" x2="0" y2="0">
                      <stop stopColor="#D5D5D5" />
                      <stop offset="0.43" stopColor="#D5D5D5" />
                      <stop offset="1" stopColor="#FFFFFF" />
                    </linearGradient>
                  </defs>
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-[12px] font-semibold text-[#F87A91] mt-[-4px]">
                    {currentPoints}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Cards */}
        <div className="space-y-3">
          {/* Benefits Card */}
          <Card 
            className="p-4 cursor-pointer hover:shadow-md transition-shadow"
            onClick={onBenefitsClick}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  <img 
                    src="/src/assets/images/membership-benefits-icon-40503a.png" 
                    alt="Benefits" 
                    className="w-8 h-8"
                  />
                </div>
                <div>
                  <h3 className="text-[14px] font-semibold text-[#1A1818]">
                    Quyền lợi hạng thành viên
                  </h3>
                  <p className="text-[14px] text-[#5A5A5A]">
                    Khám phá ưu đãi của mỗi hạng
                  </p>
                </div>
              </div>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path 
                  d="M9.5 5L16 12L9.5 19" 
                  stroke="#1A1818" 
                  strokeWidth="1.5" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </Card>

          {/* History Card */}
          <Card 
            className="p-4 cursor-pointer hover:shadow-md transition-shadow"
            onClick={onHistoryClick}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  <img 
                    src="/src/assets/images/history-icon-4a5119.png" 
                    alt="History" 
                    className="w-8 h-8"
                  />
                </div>
                <div>
                  <h3 className="text-[14px] font-semibold text-[#1A1818]">
                    Lịch sử tích - đổi
                  </h3>
                  <p className="text-[14px] text-[#5A5A5A]">
                    Lịch sử tích - đổi <span className="font-semibold text-[#F87A91]">EPoint</span> của bạn
                  </p>
                </div>
              </div>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path 
                  d="M9.5 5L16 12L9.5 19" 
                  stroke="#1A1818" 
                  strokeWidth="1.5" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default MembershipTierPage;