import React from 'react';
import { Mascot } from '@taptap/shared';

type MascotName = 'rich' | 'meditate' | 'error' | 'cute' | 'welcome' | 'inbox' | 
                 'sleep' | 'love' | 'warning' | 'hunt' | 'cry' | 'login' | 
                 'congrat' | 'intro' | 'lostConnection';

interface MascotComponentProps {
  name: MascotName;
  className?: string;
}

const MascotComponent: React.FC<MascotComponentProps> = ({ name, className }) => {
  return (
    <Mascot 
      name={name} 
      className={className}
      imageStyle={{
        width: '99px',
        height: '72px'
      }}
    />
  );
};

export default MascotComponent;