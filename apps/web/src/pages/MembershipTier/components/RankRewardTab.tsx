import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  EnhancedBarcodeCard,
  ITierMember,
} from '@taptap/shared';
import NoRewardComponent from './NoRewardComponent';
import LostRewardComponent from './LostRewardComponent';
import GuideLineVoucherComponent from './GuideLineVoucherComponent';
import MascotComponent from './MascotComponent';

interface RankRewardTabProps {
  loading: boolean;
  status: 'NO_GIFT' | 'ERROR' | 'SUCCESS' | 'LOADING';
  rewardDetailData: {
    tierDetail?: ITierMember;
    voucherDetail?: {
      name: string;
      tnc: string;
    };
  };
}

const RankRewardTab: React.FC<RankRewardTabProps> = ({
  status,
  rewardDetailData: { tierDetail, voucherDetail }
}) => {
  const navigate = useNavigate();

  // Error states (matching mobile RewardError component)
  if (status !== 'SUCCESS' || !tierDetail?.current?.[0]) {
    return (
      <div className="min-h-screen bg-[#F7CC15] flex flex-col">
        {status === 'NO_GIFT' ? (
          <NoRewardComponent tierName={tierDetail?.tierName} />
        ) : (
          <LostRewardComponent tierName={tierDetail?.tierName} />
        )}
      </div>
    );
  }

  // Success state - show voucher details (matching mobile success layout)
  return (
    <div className="min-h-screen bg-[#F7CC15] overflow-y-auto">
      {/* Header Bar - web equivalent of HeaderBarCode */}
      <div className="flex items-center justify-between p-4 bg-transparent">
        <button
          onClick={() => navigate(-1)}
          className="p-2 rounded-full bg-white/20 backdrop-blur-sm"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
        </button>
      </div>

      {/* Tier Name (matching mobile reward_name style) */}
      <div className="text-center mt-4">
        <h1 
          className="text-2xl font-semibold text-black leading-8"
          style={{ fontFamily: 'Archia' }}
        >
          {tierDetail.tierName}
        </h1>
      </div>

      {/* Mascot Image (matching mobile mascot positioning) */}
      <div className="flex justify-center mt-3">
        <MascotComponent name="welcome" className="w-[99px] h-[72px]" />
      </div>

      {/* Barcode/Voucher Card (matching mobile Coupon component) */}
      <div className="px-4 mt-6">
        <EnhancedBarcodeCard
          brandName={voucherDetail?.name || tierDetail.tierName || 'Ưu đãi thành viên'}
          expiryDate=""
          barcodeValue={voucherDetail?.code || tierDetail.voucherCode || ''}
          className="bg-white rounded-xl"
        />
      </div>

      {/* Guide Text (matching mobile guide style) */}
      <div className="px-14 mt-5">
        <p 
          className="text-base text-black text-center leading-6"
          style={{ fontFamily: 'Archia' }}
        >
          Vui lòng xuất trình mã này khi thanh toán để nhận ưu đãi thành viên
        </p>
      </div>

      {/* Terms and Conditions (matching mobile GuideLineVoucher) */}
      <div className="mt-6 px-4">
        <GuideLineVoucherComponent tnc={voucherDetail?.tnc || ''} />
      </div>
    </div>
  );
};

export default RankRewardTab;