import React from 'react';
import { Mascot } from '@taptap/shared';

interface LostRewardComponentProps {
  tierName?: string;
}

const LostRewardComponent: React.FC<LostRewardComponentProps> = ({ tierName }) => {
  return (
    <div className="flex-1 flex flex-col items-center justify-center px-8">
      <div className="mb-6">
        <Mascot 
          name="cry" 
          className="mx-auto"
          imageStyle={{ width: '120px', height: '90px' }}
        />
      </div>
      
      <div className="text-center">
        <h2 
          className="text-xl font-semibold text-black mb-2"
          style={{ fontFamily: 'Archia' }}
        >
          Lỗi tải ưu đãi
        </h2>
        
        <p 
          className="text-base text-black/70 leading-6"
          style={{ fontFamily: 'Archia' }}
        >
          {tierName ? 
            `Không thể tải ưu đãi hạng ${tierName}. Vui lòng thử lại sau.` :
            'Không thể tải ưu đãi. Vui lòng thử lại sau.'
          }
        </p>
      </div>
    </div>
  );
};

export default LostRewardComponent;