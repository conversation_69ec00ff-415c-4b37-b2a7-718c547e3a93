import React from 'react';

interface SectionUpRankComponentProps {
  number: number;
  logoSource?: string;
  currencyName?: string;
}

const SectionUpRankComponent: React.FC<SectionUpRankComponentProps> = ({ 
  number, 
  logoSource, 
  currencyName 
}) => {
  return (
    <div className="flex flex-col items-center bg-white/60 backdrop-blur-sm rounded-xl p-4 min-w-[280px]">
      <div className="flex items-center gap-2 mb-2">
        <span 
          className="text-lg font-semibold text-black"
          style={{ fontFamily: 'Archia' }}
        >
          Tích thêm
        </span>
        <span 
          className="text-xl font-bold text-[#F65D79]"
          style={{ fontFamily: 'Archia' }}
        >
          {number.toLocaleString()}
        </span>
        {logoSource && (
          <img 
            src={logoSource} 
            alt="Currency logo" 
            className="w-5 h-5 rounded-full"
          />
        )}
      </div>
      
      <p 
        className="text-sm text-black text-center"
        style={{ fontFamily: 'Archia' }}
      >
        {currencyName ? `${currencyName} để nâng hạng` : 'điểm để nâng hạng'}
      </p>
    </div>
  );
};

export default SectionUpRankComponent;