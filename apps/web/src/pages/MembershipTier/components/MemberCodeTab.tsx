import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  EnhancedBarcodeCard,
  ITierMember,
  StatusScreen,
  useAuthStore,
} from '@taptap/shared';
import SectionUpRankComponent from './SectionUpRankComponent';

interface MemberCodeTabProps {
  rewardDetailData: {
    tierDetail?: ITierMember;
    balanceData?: {
      logo?: string;
      name?: string;
      code?: string;
    };
  };
}

const MemberCodeTab: React.FC<MemberCodeTabProps> = ({
  rewardDetailData: { tierDetail, balanceData }
}) => {
  const navigate = useNavigate();
  const { profile } = useAuthStore();

  // Error state matching mobile (isEmpty checks)
  if (!tierDetail && !balanceData) {
    return (
      <div className="min-h-screen bg-[#F7CC15] flex flex-col items-center justify-center">
        <div className="absolute top-0 left-0 right-0 p-4">
          <button
            onClick={() => navigate(-1)}
            className="p-2 rounded-full bg-white/20 backdrop-blur-sm"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
              <path d="M19 12H5M12 19l-7-7 7-7"/>
            </svg>
          </button>
        </div>
        <StatusScreen type="loading_error" />
      </div>
    );
  }

  // Calculate points needed for next tier (matching mobile logic)
  const calculatePointsToNextLevel = (): number => {
    if (!tierDetail?.current?.[0] || !tierDetail.merchantTiers?.length) return 0;
    
    const currentValue = tierDetail.current[0].value;
    const tierLevel = tierDetail.tierLevel || 0;
    
    // Get progress tiers (matching mobile getListProgressTier logic)
    const listRestTier = tierDetail.merchantTiers.filter(tier => 
      tier.tierLevel > tierLevel
    ).slice(0, 2); // Take up to 2 next tiers
    
    if (listRestTier.length === 0) return 0;
    
    const nextTierValue = listRestTier[0].conditions?.[0]?.value || 0;
    return Math.max(0, nextTierValue - currentValue);
  };

  const currencyName = tierDetail?.current?.[0]?.name || balanceData?.name;
  const totalNeedEarnCurrency = calculatePointsToNextLevel();
  const userMobile = (profile as any)?.phone || (profile as any)?.mobile || '0'; // Use user's mobile from profile

  return (
    <div className="min-h-screen bg-[#F7CC15] overflow-y-auto">
      {/* Header Bar */}
      <div className="flex items-center justify-between p-4 bg-transparent">
        <button
          onClick={() => navigate(-1)}
          className="p-2 rounded-full bg-white/20 backdrop-blur-sm"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
        </button>
      </div>

      {/* Member Code Title (matching mobile member_code style) */}
      <div className="text-center mt-2">
        <h1 
          className="text-2xl font-semibold text-black leading-8"
          style={{ fontFamily: 'Archia' }}
        >
          Mã thành viên
        </h1>
      </div>

      {/* Section Up Rank (matching mobile SectionUpRank) */}
      {totalNeedEarnCurrency > 0 && (
        <div className="mt-10 flex items-center justify-center px-4">
          <SectionUpRankComponent
            number={totalNeedEarnCurrency}
            logoSource={balanceData?.logo || (tierDetail?.current?.[0] as any)?.currencyLogo}
            currencyName={currencyName}
          />
        </div>
      )}

      {/* Barcode Section (matching mobile Barcode component) */}
      <div className="px-6 mt-4">
        <div className="bg-[#F7E066] rounded-xl p-4">
          <EnhancedBarcodeCard
            brandName="TapTap"
            expiryDate=""
            barcodeValue={userMobile}
            className="bg-transparent"
          />
        </div>
      </div>

      {/* Instruction Text (matching mobile barcodeInstruction style) */}
      <div className="px-14 mt-5">
        <p 
          className="text-base text-black text-center leading-6"
          style={{ fontFamily: 'Archia' }}
        >
          {currencyName ? 
            `Vui lòng xuất trình mã này khi thanh toán để tích ${currencyName}` :
            'Vui lòng xuất trình mã này khi thanh toán để tích điểm'
          }
        </p>
      </div>
    </div>
  );
};

export default MemberCodeTab;