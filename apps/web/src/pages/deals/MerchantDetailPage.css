/* HTML Content Styles for Merchant Information */
.merchant-info-html {
  font-size: 14px;
  line-height: 1.6;
  color: #4a4a4a;
}

.merchant-info-html h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1A1818;
  margin-top: 16px;
  margin-bottom: 8px;
}

.merchant-info-html h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1A1818;
  margin-top: 12px;
  margin-bottom: 6px;
}

.merchant-info-html p {
  color: #6b6b6b;
  margin-bottom: 12px;
}

.merchant-info-html strong {
  color: #1A1818;
  font-weight: 600;
}

.merchant-info-html ul, 
.merchant-info-html ol {
  padding-left: 20px;
  margin-bottom: 12px;
}

.merchant-info-html li {
  color: #6b6b6b;
  margin-bottom: 4px;
}

.merchant-info-html ul li {
  list-style-type: disc;
}

.merchant-info-html ol li {
  list-style-type: decimal;
}

.merchant-info-html code {
  background-color: #f0f4f8;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 13px;
  color: #F65D79;
  font-family: 'Courier New', monospace;
}

.merchant-info-html table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.merchant-info-html th {
  background-color: #f7f7f7;
  padding: 8px;
  text-align: left;
  font-weight: 600;
  border: 1px solid #e0e0e0;
  color: #1A1818;
}

.merchant-info-html td {
  padding: 8px;
  border: 1px solid #e0e0e0;
  color: #6b6b6b;
}

.merchant-info-html a {
  color: #F65D79;
  text-decoration: underline;
  cursor: pointer;
}

.merchant-info-html a:hover {
  color: #e44560;
}

.merchant-info-html img {
  max-width: 100%;
  height: auto;
  margin: 12px 0;
  border-radius: 8px;
}

.merchant-info-html blockquote {
  border-left: 4px solid #F65D79;
  padding-left: 16px;
  margin: 16px 0;
  color: #6b6b6b;
  font-style: italic;
}

.merchant-info-html hr {
  border: none;
  border-top: 1px solid #e0e0e0;
  margin: 20px 0;
}

.merchant-info-html pre {
  background-color: #f7f7f7;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  margin: 12px 0;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .merchant-info-html table {
    font-size: 12px;
  }
  
  .merchant-info-html th,
  .merchant-info-html td {
    padding: 6px;
  }
}