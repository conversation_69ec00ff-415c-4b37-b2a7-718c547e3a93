import React, {
  useEffect,
  useCallback,
  useRef,
  useMemo,
  memo,
} from "react";
import { useNavigate } from "react-router-dom";
import {
  useHomeStore,
  useAuthStore,
  HomeSectionCode,
  HomeConfig,
  SectionBanner,
  SectionNews,
  SectionFlashSale,
  SectionPopularMerchant,
  SectionEarnBy,
  LoadingSkeleton,
  ActionCategories,
  ActionItem,
  ProfileSearchHeader,
  SectionReward,
  MyRewardsSection,
  useActiveRewards,
  usePullToRefresh,
} from "@taptap/shared";

const HomePage: React.FC = memo(() => {
  const navigate = useNavigate();
  
  // Prevent duplicate API calls in React.StrictMode
  const homeConfigFetchedRef = useRef(false);

  const {
    sections,
    panels,
    loading,
    refreshAll,
    myRewards,
    rewardsByCollection,
    rewardsLoading,
    popularMerchants,
    news,
  } = useHomeStore();

  const { profile, currentAvatar, token, getCustomerProfile, getCurrentAvatar } =
    useAuthStore();

  // Fetch active rewards for MyRewardsSection
  const { activeRewards } = useActiveRewards();

  const handlePointsClick = useCallback(() => {
    navigate("/point");
  }, [navigate]);

  const handleSearch = useCallback(
    (searchValue: string) => {
      navigate(`/search?q=${encodeURIComponent(searchValue)}`);
    },
    [navigate]
  );

  const handleSearchClick = useCallback(() => {
    navigate("/search");
  }, [navigate]);

  const handleNotificationClick = useCallback(() => {
    navigate("/inbox");
  }, [navigate]);

  const handleAvatarClick = useCallback(() => {
    navigate("/profile");
  }, [navigate]);

  const handleMyRewardsClick = useCallback(() => {
    navigate("/my-rewards");
  }, [navigate]);

  const handleRewardClick = useCallback(
    (reward: any) => {
      navigate(`/my-rewards/${reward.id}`);
    },
    [navigate]
  );

  const handleBannerClick = useCallback(
    (bannerId: string, actionType: string, actionUrl: string) => {
      // console.log("🏠 HomePage handleBannerClick called:", {
      //   bannerId,
      //   actionType,
      //   actionUrl,
      // });

      switch (actionType) {
        case "internal":
          // console.log("🏠 Internal navigation to:", actionUrl);
          navigate(actionUrl);
          break;
        case "external":
          // console.log("🌐 External link to:", actionUrl);
          window.open(actionUrl, "_blank");
          break;
        case "deeplink":
          // console.log("🔗 Deep link:", actionUrl);
          if (actionUrl.startsWith("taptapvui://")) {
            // console.log("🏠 Navigating to banner detail for deep link");
            navigate(`/banner/${bannerId}`);
          } else {
            navigate(actionUrl);
          }
          break;
        case "banner":
          // console.log("🏠 Banner navigation to:", `/banner/${bannerId}`);
          navigate(`/banner/${bannerId}`);
          break;
        default:
          // console.log(
          //   "🏠 Default banner navigation to:",
          //   `/banner/${bannerId}`
          // );
          navigate(`/banner/${bannerId}`);
      }
    },
    [navigate]
  );

  // Stable timer callbacks to prevent excessive re-renders
  const handleTimerComplete = useCallback(() => {
    // Handle timer completion - currently just logging
  }, []);

  const handleRemindClick = useCallback(() => {
    // Handle remind me click - currently just logging
  }, []);

  // Handle panel action clicks based on directScreenCode
  const handlePanelClick = useCallback(
    (panel: any) => {
      const { directScreenCode, parameter } = panel;

      switch (directScreenCode) {
        case "scanQR":
          navigate("/scan-qr", { state: parameter });
          break;
        case "barcode":
          navigate("/member-code");
          break;
        case "billScan":
          navigate("/bill-scan/camera");
          break;
        case "inputCode":
          navigate("/gift-code");
          break;
        default:
          // console.log(`Unhandled panel action: ${directScreenCode}`, panel);
          break;
      }
    },
    [navigate]
  );

  // Transform active rewards to RewardItem format
  const rewardItems = useMemo(() => {
    return activeRewards.slice(0, 5).map((reward) => ({
      id: reward.codeId,
      brand: reward.merchantName,
      title: reward.name,
      expiry: reward.endTime
        ? new Date(reward.endTime).toLocaleDateString("vi-VN")
        : "Không giới hạn",
      logoSrc: reward.merchantLogo || "",
      bgSrc: reward.images || reward.thumbnails || "",
    }));
  }, [activeRewards]);

  // Convert panels to ActionItems
  const actionItems: ActionItem[] = useMemo(() => {
    if (!panels || panels.length === 0) {
      return [];
    }

    return panels
      .sort((a, b) => a.priority - b.priority) // Sort by priority
      .slice(0, 4) // Limit to 4 items for UI layout
      .map((panel) => ({
        id: panel.code,
        icon: panel.logo ? (
          <img
            src={panel.logo}
            alt={panel.name}
            className="w-9 h-9 object-contain"
          />
        ) : (
          <div className="w-9 h-9 bg-gray-100 rounded-lg"></div>
        ),
        label: panel.name.replace(/\s+/g, "\n"), // Convert spaces to line breaks for better display
        onClick: () => handlePanelClick(panel),
      }));
  }, [panels, handlePanelClick]);

  // Check authentication - redirect to login if not authenticated
  useEffect(() => {
    if (!token) {
      console.log('🔐 No token found, redirecting to login...');
      navigate('/auth/login');
      return;
    }
  }, [token, navigate]);

  useEffect(() => {
    // Only fetch if not already fetched (prevent StrictMode double calls)
    if (!homeConfigFetchedRef.current) {
      homeConfigFetchedRef.current = true;
      // Use refreshAll instead of fetchHomeConfig to get both config and section data
      refreshAll();
      getCustomerProfile(); // Fetch profile data on component mount
      getCurrentAvatar().catch((err) => {
        console.error("Failed to get current avatar:", err);
      }); // Fetch current avatar on component mount
    }
  }, []); // Empty dependency array - run only on mount

  // fetchSectionData is now handled by refreshAll() to prevent duplicate calls


  const handleRefresh = useCallback(async () => {
    console.log('🔄 HomePage refresh triggered');
    // Reset the fetch flag to allow refreshAll to work properly
    homeConfigFetchedRef.current = false;
    await refreshAll();
    homeConfigFetchedRef.current = true;
    console.log('🔄 HomePage refresh completed');
  }, [refreshAll]);

  // Use pull-to-refresh hook
  console.log('🔄 HomePage: Setting up usePullToRefresh hook...');
  const { isRefreshing, pullPosition } = usePullToRefresh({
    onRefresh: handleRefresh,
    maximumPullLength: 200,
    refreshThreshold: 60, // Lower threshold để dễ trigger
    isDisabled: false,
  });
  
  console.log('🔄 HomePage: usePullToRefresh state:', { isRefreshing, pullPosition });

  const renderSection = useCallback(
    (section: HomeConfig, index: number) => {
      const sectionIndex = index + 1;
      // Create unique key for each section based on code, collectionCode, and index
      const uniqueKey = `${section.code}-${
        section.collectionCode || "default"
      }-${index}`;

      switch (section.code) {
        case HomeSectionCode.BANNER:
          return (
            <SectionBanner
              key={uniqueKey}
              data={section}
              sectionIndex={sectionIndex}
              onBannerClick={handleBannerClick}
            />
          );

        case HomeSectionCode.NEWS:
          return (
            <SectionNews
              key={uniqueKey}
              data={section}
              sectionIndex={sectionIndex}
              news={news}
              onNewsClick={(newsItem) => {
                // If news has external link, use query parameter
                if (
                  newsItem.contentType === "External link" &&
                  newsItem.externalLink
                ) {
                  window.open(newsItem.externalLink, '_blank', 'noopener,noreferrer');
                  // navigate(
                  //   `/news?url=${encodeURIComponent(
                  //     newsItem.externalLink
                  //   )}&title=${encodeURIComponent(newsItem.title || "")}`
                  // );
                } else {
                  // Fallback to ID-based navigation with state data
                  navigate(`/news/${newsItem._id}`, {
                    state: { newsData: newsItem },
                  });
                }
              }}
              onViewMore={() => navigate("/news")}
            />
          );

        case HomeSectionCode.FLASH_SALE: {
          const collectionCode = section.collectionCode || "hat";
          const collectionName = section.name || "Flash Sale";

          // Get specific flash sale items for this collection
          const sectionFlashSaleItems =
            rewardsByCollection[collectionCode] || [];

          // console.log("🔥 FLASH_SALE Section Debug:", {
          //   uniqueKey,
          //   collectionCode,
          //   collectionName,
          //   sectionFlashSaleItemsCount: sectionFlashSaleItems.length,
          //   rewardsByCollectionKeys: Object.keys(rewardsByCollection),
          //   hasCollectionData: !!rewardsByCollection[collectionCode],
          //   section,
          // });

          return (
            <SectionFlashSale
              key={uniqueKey}
              data={section}
              sectionIndex={sectionIndex}
              flashSaleItems={sectionFlashSaleItems}
              onItemClick={(item) => navigate(`/rewards/${item.id}`)}
              onViewAll={() =>
                navigate(
                  `/flash-sale?collectionCode=${collectionCode}&collectionName=${encodeURIComponent(
                    collectionName
                  )}&hasFlashSale=true`
                )
              }
              onRemindClick={handleRemindClick}
              onTimerComplete={handleTimerComplete}
            />
          );
        }

        case HomeSectionCode.MERCHANT: {
          const collectionCode = section.collectionCode || "popular";
          // Each merchant section can have different collection-based merchants
          const sectionMerchants =
            collectionCode === "popular"
              ? popularMerchants
              : popularMerchants || [];

          return (
            <SectionPopularMerchant
              key={uniqueKey}
              data={section}
              sectionIndex={sectionIndex}
              popularMerchants={sectionMerchants}
              onMerchantClick={(merchant) =>
                navigate(`/merchants/${merchant.code}`)
              }
              onViewMore={() => navigate("/merchants")}
            />
          );
        }

        case HomeSectionCode.REWARD: {
          const collectionCode = section.collectionCode || "suggestion";
          const sectionRewards =
            collectionCode === "suggestion"
              ? myRewards
              : rewardsByCollection[collectionCode] || [];

          return (
            <SectionReward
              key={uniqueKey}
              data={section}
              sectionIndex={sectionIndex}
              rewards={sectionRewards}
              loading={rewardsLoading}
              error={null}
              onRewardClick={(reward) => navigate(`/rewards/${reward.id}`)}
              onViewMore={() => navigate("/exchange/all-rewards")}
            />
          );
        }

        // TODO game
        // case HomeSectionCode.PINNED_CHALLENGE:
        // case HomeSectionCode.CHALLENGE:
        // case HomeSectionCode.GAME:
        //   return (
        //     <SectionChallenge
        //       key={uniqueKey}
        //       data={section}
        //       sectionIndex={sectionIndex}
        //     />
        //   );

        case HomeSectionCode.EARN_BY:
          return (
            <SectionEarnBy
              key={uniqueKey}
              data={section}
              sectionIndex={sectionIndex}
              earnByOptions={undefined} // Will use default options
              onOptionClick={(option) => navigate(option.route)}
            />
          );

        // case HomeSectionCode.EARN:
        //   return (
        //     <SectionHowToEarn
        //       key={uniqueKey}
        //       data={section}
        //       sectionIndex={sectionIndex}
        //     />
        //   );

        default:
          return null;
      }
    },
    [
      navigate,
      news,
      popularMerchants,
      myRewards,
      rewardsByCollection,
      rewardsLoading,
      handleRemindClick,
      handleTimerComplete,
      handleBannerClick,
    ]
  );


  // Redirect to login if not authenticated
  if (!token) {
    return null; // Don't render anything while redirecting
  }

  if (loading && sections.length === 0) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="bg-gray-50 min-h-screen relative">
      {/* Pull-to-refresh indicator */}
      {(pullPosition > 0 || isRefreshing) && (
        <div
          className="fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-sm flex items-center justify-center z-50 transition-all duration-200"
          style={{
            height: `${isRefreshing ? 60 : pullPosition}px`,
            transform: `translateY(-${Math.max(0, 60 - (isRefreshing ? 60 : pullPosition))}px)`,
          }}
        >
          <div className="flex items-center gap-2 text-gray-600">
            {isRefreshing ? (
              <>
                <div className="w-4 h-4 border-2 border-gray-300 border-t-pink-500 rounded-full animate-spin" />
                <span className="text-sm font-medium">Đang làm mới...</span>
              </>
            ) : (
              <>
                <div
                  className="transition-transform duration-200"
                  style={{
                    transform: `rotate(${pullPosition >= 120 ? 180 : 0}deg)`,
                  }}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                  </svg>
                </div>
                <span className="text-sm font-medium">
                  {pullPosition >= 120 ? 'Thả để làm mới' : 'Kéo để làm mới'}
                </span>
              </>
            )}
          </div>
        </div>
      )}

      <div
        className="transition-transform duration-200"
        style={{
          transform: `translateY(${isRefreshing ? 60 : pullPosition}px)`,
        }}
      >
        {/* Profile Search Header */}
        <div className="bg-[#F65D79] pb-12">
          <ProfileSearchHeader
            userName={`${profile.firstname || profile.firstName || "Bạn"}`}
            avatarSrc={currentAvatar || undefined}
            onSearch={handleSearch}
            onSearchClick={handleSearchClick}
            onNotificationClick={handleNotificationClick}
            onAvatarClick={handleAvatarClick}
            hasNotification={true}
          />
        </div>

        {/* Action Categories */}
        <div className="-mt-16 pt-4">
          <ActionCategories
            points={profile.loyaltyPoint || profile.loyaltyPoints || 0}
            onPointsClick={handlePointsClick}
            actions={actionItems}
          />
        </div>

        {/* My Rewards Section */}
        <MyRewardsSection
          title="Ưu đãi của tôi"
          actionText="Tất cả"
          rewards={rewardItems}
          onActionClick={handleMyRewardsClick}
          onRewardClick={handleRewardClick}
        />


        {/* Dynamic Sections */}
        <div className="space-y-4">
          {sections.map((section, index) => renderSection(section, index))}
        </div>

        {/* Loading more indicator */}
        {loading && sections.length > 0 && (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-600"></div>
          </div>
        )}
      </div>
    </div>
  );
});

HomePage.displayName = "HomePage";

export default HomePage;
