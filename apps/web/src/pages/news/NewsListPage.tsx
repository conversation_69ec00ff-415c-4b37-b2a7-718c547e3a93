import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { newsAPI, NewsItem, resizeImage } from '@taptap/shared';
import { NewsCard } from '@taptap/shared';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';

const NewsListPage: React.FC = () => {
  const navigate = useNavigate();
  const [newsList, setNewsList] = useState<NewsItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNewsList = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await newsAPI.getNewsList();
        
        if (response.status.success && response.data) {
          setNewsList(response.data);
        } else {
          setError('Failed to load news');
        }
      } catch (err) {
        console.error('Error fetching news list:', err);
        setError('Failed to load news');
      } finally {
        setLoading(false);
      }
    };

    fetchNewsList();
  }, []);

  const handleBack = () => {
    navigate(-1);
  };

  const handleNewsClick = (newsItem: NewsItem) => {
    // If news has external link, use query parameter
    if (newsItem.contentType === 'External link' && newsItem.externalLink) {
      navigate(`/news?url=${encodeURIComponent(newsItem.externalLink)}&title=${encodeURIComponent(newsItem.title || '')}`);
    } else {
      // Fallback to ID-based navigation with state data
      navigate(`/news/${newsItem.id}`, { state: { newsData: newsItem } });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200">
        <div className="flex items-center justify-between px-4 py-3">
          <button onClick={handleBack} className="p-1">
            <ChevronLeftIcon className="h-6 w-6 text-gray-700" />
          </button>
          <h1 className="text-lg font-semibold">Tin tức</h1>
          <div className="w-6 h-6" />
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-4">
        {loading ? (
          <div className="grid grid-cols-1 gap-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="bg-white rounded-lg p-4 shadow-sm animate-pulse">
                <div className="flex gap-3">
                  <div className="w-24 h-24 bg-gray-200 rounded-lg flex-shrink-0" />
                  <div className="flex-1">
                    <div className="h-5 bg-gray-200 rounded mb-2" />
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-96">
            <p className="text-gray-500 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-white rounded-lg"
            >
              Thử lại
            </button>
          </div>
        ) : newsList.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-96">
            <p className="text-gray-500">Không có tin tức nào</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {newsList.map((newsItem) => (
              <div
                key={newsItem.id}
                className="bg-white rounded-lg p-4 shadow-sm cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleNewsClick(newsItem)}
              >
                <div className="flex gap-3">
                  {newsItem.imageUrl && (
                    <div className="w-24 h-24 flex-shrink-0 bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={resizeImage(newsItem.imageUrl, { width: 192, height: 192, quality: 85, fit: 'cover' })}
                        alt={newsItem.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 line-clamp-2 mb-1">
                      {newsItem.title}
                    </h3>
                    {newsItem.summary && (
                      <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                        {newsItem.summary}
                      </p>
                    )}
                    <div className="flex items-center gap-3 text-xs text-gray-500">
                      {newsItem.category && (
                        <span className="px-2 py-1 bg-gray-100 rounded">
                          {newsItem.category}
                        </span>
                      )}
                      {newsItem.publishedAt && (
                        <span>
                          {new Date(newsItem.publishedAt).toLocaleDateString('vi-VN')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NewsListPage;