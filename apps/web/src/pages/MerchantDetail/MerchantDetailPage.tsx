import React, { useState } from "react";
import {
  NavigationHeader,
  LoyaltyCard,
  StoreNearbySection,
  ImageGallery,
  BrandDetailSection,
  RoundedTabNavigation,
  cn,
  type RoundedTabItem,
  BackIcon,
} from "@taptap/shared";

export interface MerchantDetailPageProps {
  merchantName: string;
  merchantLogo?: string;
  backgroundImage?: string;
  earnRate?: string;
  isFavorite?: boolean;
  loyaltyCardData?: any;
  storeLocations?: any[];
  galleryImages?: any[];
  brandInfo?: {
    title: string;
    description: string;
    image?: string;
  };
  tabs?: RoundedTabItem[];
  onBack?: () => void;
  onToggleFavorite?: () => void;
  onTabChange?: (tabId: string) => void;
  className?: string;
}

export const MerchantDetailPage: React.FC<MerchantDetailPageProps> = ({
  merchantName,
  merchantLogo = "",
  backgroundImage = "",
  earnRate = "1 VUI = 1đ",
  isFavorite = false,
  loyaltyCardData,
  storeLocations = [],
  galleryImages = [],
  brandInfo,
  tabs = [
    { id: "vouchers", label: "Vouchers", isActive: true },
    { id: "earn", label: "Cách tích điểm", isActive: false },
    { id: "info", label: "Thông tin", isActive: false },
  ],
  onBack,
  onToggleFavorite,
  onTabChange,
  className,
}) => {
  const [activeTab, setActiveTab] = useState(
    tabs.find((tab) => tab.isActive)?.id || tabs[0]?.id
  );
  const [favorite, setFavorite] = useState(isFavorite);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onTabChange?.(tabId);
  };

  const handleToggleFavorite = () => {
    setFavorite(!favorite);
    onToggleFavorite?.();
  };

  return (
    <div className={cn("w-full min-h-screen bg-[#EFF3F6] relative", className)}>
      {/* Background Header with Image */}
      <div className="relative w-full h-[221px] overflow-hidden">
        {/* Background Image */}
        <img
          src={backgroundImage}
          alt={merchantName}
          className="w-full h-full object-cover"
        />

        {/* Gradient Overlay */}
        <div
          className="absolute inset-0"
          style={{
            background:
              "linear-gradient(180deg, rgba(0, 0, 0, 1) 0%, rgba(25, 24, 24, 0) 100%)",
          }}
        />

        {/* Navigation Header */}
        <div className="absolute top-0 left-0 right-0">
          <NavigationHeader
            title=""
            leftIcon={<BackIcon />}
            rightIcon={
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 5V19M5 12L19 12"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            }
            onLeftClick={onBack || (() => window.history.back())}
            className="bg-transparent border-none"
            textClassName="text-white"
          />
        </div>
      </div>

      {/* Merchant Info Card - Floating */}
      <div className="relative px-3 -mt-16 z-10">
        <div className="bg-white rounded-xl shadow-lg px-3 pt-3">
          {/* Merchant Logo - Positioned above card */}
          <div className="absolute -top-[25px] left-3">
            <div className="w-12 h-12 rounded-lg overflow-hidden bg-white shadow-sm">
              <img
                src={merchantLogo}
                alt={merchantName}
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Favorite Button */}
          <button
            onClick={handleToggleFavorite}
            className="absolute top-2 right-3 w-5 h-5 rounded flex items-center justify-center 2"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill={favorite ? "#F65D79" : "none"}
              stroke={favorite ? "#F65D79" : "#9A9A9A"}
              strokeWidth="1.5"
            >
              <path d="M8 14L6.5 12.7C3.4 9.9 1.5 8.2 1.5 6.1C1.5 4.6 2.6 3.5 4.1 3.5C5 3.5 5.9 3.9 6.4 4.6H9.6C10.1 3.9 11 3.5 11.9 3.5C13.4 3.5 14.5 4.6 14.5 6.1C14.5 8.2 12.6 9.9 9.5 12.7L8 14Z" />
            </svg>
          </button>

          {/* Merchant Name and Earn Rate */}
          <div className="flex items-center justify-between pt-2">
            <div className="flex-1">
              <h1
                className="text-lg font-bold text-[#1A1818] mb-1"
                style={{ fontFamily: "Archia, system-ui, sans-serif" }}
              >
                {merchantName}
              </h1>

              {/* Earn Rate */}
              <div className="flex items-center gap-1.5">
                <span className="text-xs text-[#9A9A9A]">Tỷ lệ:</span>
                <span
                  className="text-sm font-semibold text-[#F65D79]"
                  style={{ fontFamily: "Archia, system-ui, sans-serif" }}
                >
                  {earnRate}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="mt-6 bg-white">
        <div className="px-4 pt-3 pb-1">
          <RoundedTabNavigation
            tabs={tabs.map((tab) => ({
              ...tab,
              isActive: tab.id === activeTab,
            }))}
            onTabChange={handleTabChange}
          />
        </div>
      </div>

      {/* Content Sections */}
      <div className="mt-3 space-y-3">
        {/* Vouchers Section */}
        {activeTab === "vouchers" && (
          <div className="bg-white">
            <div className="px-4 py-6">
              <p className="text-center text-[#9A9A9A] text-sm">
                Vouchers sẽ được hiển thị ở đây
              </p>
            </div>
          </div>
        )}

        {/* How to Earn Section */}
        {activeTab === "earn" && loyaltyCardData && (
          <div className="px-4">
            <LoyaltyCard {...loyaltyCardData} theme="merchant-detail" />
          </div>
        )}

        {/* Store Nearby Section */}
        {storeLocations.length > 0 && (
          <StoreNearbySection
            title="Cửa hàng gần đây"
            stores={storeLocations}
            showAll={true}
          />
        )}

        {/* Brand Info Section */}
        {brandInfo && (
          <BrandDetailSection
            title={brandInfo.title}
            description={brandInfo.description}
            image={brandInfo.image}
          />
        )}

        {/* Gallery Section */}
        {galleryImages.length > 0 && (
          <div className="px-4">
            <ImageGallery images={galleryImages} title="Hình ảnh" />
          </div>
        )}
      </div>
    </div>
  );
};

export default MerchantDetailPage;
