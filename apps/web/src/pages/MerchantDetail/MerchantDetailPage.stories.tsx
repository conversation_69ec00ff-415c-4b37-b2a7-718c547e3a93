import type { Meta, StoryObj } from '@storybook/react';
import { MerchantDetailPage } from './MerchantDetailPage';

const meta: Meta<typeof MerchantDetailPage> = {
  title: 'UI Components/MerchantDetailPage',
  component: MerchantDetailPage,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Complete merchant detail page with background image, floating info card, tabs, and content sections.',
      },
    },
  },
  argTypes: {
    onBack: { action: 'back clicked' },
    onToggleFavorite: { action: 'favorite toggled' },
    onTabChange: { action: 'tab changed' },
  },
};

export default meta;
type Story = StoryObj<typeof MerchantDetailPage>;

// Sample data for stories
const sampleStoreLocations = [
  {
    id: '1',
    name: 'Pepsi Store Quận 1',
    address: '123 Nguyễn Huệ, Quận 1, TP.HCM',
    distance: '0.5 km',
    phone: '028 1234 5678',
  },
  {
    id: '2',
    name: 'Pepsi Store Quận 3',
    address: '456 <PERSON>, Quận 3, TP.HCM',
    distance: '1.2 km',
    phone: '028 8765 4321',
  },
];

const sampleGalleryImages = [
  {
    id: '1',
    url: '/apps/web/src/assets/images/reward_card_image.png',
    alt: 'Pepsi Product 1',
  },
  {
    id: '2',
    url: '/apps/web/src/assets/images/reward_card_image.png',
    alt: 'Pepsi Product 2',
  },
  {
    id: '3',
    url: '/apps/web/src/assets/images/reward_card_image.png',
    alt: 'Pepsi Product 3',
  },
];

const sampleLoyaltyCardData = {
  status: 'with-rank' as const,
  brandName: 'Pepsi',
  currentPoints: 2500,
  nextTierPoints: 5000,
  currentTier: 'Silver',
  nextTier: 'Gold',
  progress: 50,
  backgroundColor: '#F65D79',
};

const sampleBrandInfo = {
  title: 'Về Pepsi',
  description: 'Pepsi là thương hiệu nước giải khát nổi tiếng thế giới với hương vị cola đặc trưng. Được thành lập từ năm 1893, Pepsi luôn mang đến những trải nghiệm tuyệt vời cho người tiêu dùng.',
  image: '/apps/web/src/assets/images/brand_logo_placeholder.png',
};

export const Default: Story = {
  args: {
    merchantName: 'Pepsi',
    merchantLogo: '/apps/web/src/assets/images/brand_logo_placeholder.png',
    backgroundImage: '/apps/web/src/assets/images/merchant-bg.png',
    earnRate: '1 VUI = 1đ',
    isFavorite: false,
    loyaltyCardData: sampleLoyaltyCardData,
    storeLocations: sampleStoreLocations,
    galleryImages: sampleGalleryImages,
    brandInfo: sampleBrandInfo,
  },
};

export const Favorite: Story = {
  args: {
    ...Default.args,
    isFavorite: true,
  },
};

export const MinimalContent: Story = {
  args: {
    merchantName: 'Coca Cola',
    earnRate: '2 VUI = 1đ',
    tabs: [
      { id: 'vouchers', label: 'Vouchers', isActive: true },
      { id: 'info', label: 'Thông tin', isActive: false },
    ],
  },
};

export const WithCustomTabs: Story = {
  args: {
    ...Default.args,
    tabs: [
      { id: 'products', label: 'Sản phẩm', isActive: true },
      { id: 'offers', label: 'Ưu đãi', isActive: false },
      { id: 'rewards', label: 'Tích điểm', isActive: false },
      { id: 'about', label: 'Giới thiệu', isActive: false },
    ],
  },
};