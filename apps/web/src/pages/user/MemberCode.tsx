import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  NavigationHeader,
  MerchantListCard,
  BarcodePanel,
  useEarnMethodsInfinite,
  formatEarnRate,
  useAuthStore,
  AuthAPI,
} from '@taptap/shared';

const MemberCodePage: React.FC = () => {
  const navigate = useNavigate();
  const { profile, token } = useAuthStore();
  const { merchants: apiMerchants, loading, loadingMore, error, hasMore, loadMore } = useEarnMethodsInfinite();
  const loadMoreRef = useRef<HTMLDivElement>(null);
  
  const [profileData, setProfileData] = useState<{ mobile?: string } | null>(null);
  const [profileLoading, setProfileLoading] = useState(false);

  // Fetch profile if not available
  useEffect(() => {
    const fetchProfile = async () => {
      if (token && !profile?.mobile) {
        try {
          setProfileLoading(true);
          const response = await AuthAPI.getCustomer();
          if (response.status?.success && response.data) {
            setProfileData(response.data);
          }
        } catch (error) {
          console.error('Failed to fetch profile:', error);
        } finally {
          setProfileLoading(false);
        }
      }
    };

    fetchProfile();
  }, [token, profile?.mobile]);

  const handleBack = () => {
    navigate(-1);
  };

  const handleMerchantClick = (merchantId: string) => {
    // Navigate to merchant detail
    navigate(`/merchants/${merchantId}`);
  };

  // Intersection Observer for infinite scroll
  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [target] = entries;
      if (target.isIntersecting && hasMore && !loading && !loadingMore) {
        loadMore();
      }
    },
    [hasMore, loading, loadingMore, loadMore]
  );

  useEffect(() => {
    const element = loadMoreRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(handleObserver, {
      threshold: 0.1,
    });

    observer.observe(element);
    return () => observer.unobserve(element);
  }, [handleObserver]);

  // Generate member barcode data from user or profile data
  const getPhoneNumber = () => {
    if (profile?.mobile) {
      return profile.mobile;
    }
    if (profileData?.mobile) {
      return profileData.mobile;
    }
    // Fallback to empty string if no phone available
    return '';
  };

  // Transform API merchants to component format
  const transformedMerchants = apiMerchants.map(merchant => {
    return {
      id: merchant.id,
      name: merchant.name,
      logoSrc: merchant.logo,
      earnRate: formatEarnRate(merchant.earnRate)
    };
  });


  return (
    <div className="min-h-screen" style={{ background: '#F65D79' }}>
      {/* Navigation Header */}
      <NavigationHeader
        title="Mã thành viên"
        showBackButton={true}
        onBackClick={handleBack}
        className="text-black bg-transparent"
        textClassName="text-black"
      />

      <div className="max-w-md mx-auto">
        {/* Barcode Panel */}
        <div className="mx-6 mt-10 mb-6">
          {(profile?.mobile || profileData?.mobile) && !profileLoading ? (
            <BarcodePanel
              phoneNumber={getPhoneNumber()}
              barcodeData={getPhoneNumber()}
            />
          ) : (
            <div className="bg-[#FFCED7] rounded-xl p-8 shadow-sm">
              <div className="text-center">
                <div className="text-[#1A1818] text-sm">
                  {profileLoading 
                    ? 'Đang tải thông tin thành viên...' 
                    : 'Không có thông tin thành viên'
                  }
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Merchants Section */}
        <div className="bg-white relative" style={{ 
          borderTopLeftRadius: '20px',
          borderTopRightRadius: '20px',
          minHeight: '455px'
        }}>
          {/* Background Image */}
          <div 
            className="absolute inset-0 bg-cover bg-center bg-no-repeat rounded-t-lg"
       
          ></div>

          {/* Content */}
          <div className="relative z-10">
            {/* Section Title */}
            <div className="px-4 pt-6">
              <h2 className="text-base font-bold text-[#1A1818] mb-2">
                Thương hiệu áp dụng ({transformedMerchants.length})
                {loading && <span className="text-xs text-gray-500 ml-2">(Đang tải...)</span>}
                {error && <span className="text-xs text-red-500 ml-2">(Lỗi: {error})</span>}
              </h2>
              <p className="text-sm text-[#1A1818] mb-8 leading-relaxed">
                Bạn có thể tích POINT bằng mã thành viên tại cửa hàng thuộc các thương hiệu sau:
              </p>
            </div>

            {/* Merchants List */}
            <div className="px-4">
              {loading ? (
                <div className="text-center py-8">
                  <div className="text-[#1A1818]">Đang tải danh sách thương hiệu...</div>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <div className="text-red-600">Không thể tải danh sách thương hiệu</div>
                  <div className="text-sm text-gray-500 mt-2">{error}</div>
                </div>
              ) : transformedMerchants.length > 0 ? (
                <div className="space-y-0">
                  {transformedMerchants.map((merchant, index) => (
                    <div key={`${merchant.id}-${index}`} className={index === transformedMerchants.length - 1 ? 'border-b-0' : ''}>
                      <MerchantListCard
                        id={merchant.id}
                        name={merchant.name}
                        logoSrc={merchant.logoSrc}
                        earnRate={merchant.earnRate}
                        onClick={handleMerchantClick}
                      />
                    </div>
                  ))}
                  
                  {/* Load More Trigger */}
                  {hasMore && (
                    <div ref={loadMoreRef} className="py-4">
                      {loadingMore ? (
                        <div className="text-center">
                          <div className="text-[#1A1818] text-sm">Đang tải thêm...</div>
                        </div>
                      ) : (
                        <div className="h-4" /> // Invisible trigger area
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-[#1A1818]">Chưa có thương hiệu nào hỗ trợ</div>
                </div>
              )}
            </div>

            {/* Extra padding at bottom */}
            <div className="h-8"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemberCodePage;