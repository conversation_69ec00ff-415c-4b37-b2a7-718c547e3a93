import React from 'react';
import { NavigationHeader, ContactSocialSection, ContactInfoSection, cn, type SocialMediaItem } from '@taptap/shared';
import backIcon from '@shared/assets/icons/back-icon.svg';
import youtubeIcon from '@shared/assets/icons/youtube-icon.svg';
import facebookIcon from '@shared/assets/icons/facebook-icon.svg';
import instagramIcon from '@shared/assets/icons/instagram-icon.svg';

export interface ContactUsPageProps {
  title?: string;
  version?: string;
  socialItems?: SocialMediaItem[];
  website?: string;
  phone?: string;
  email?: string;
  address?: string;
  workingHours?: string;
  onBack?: () => void;
  onSocialClick?: (item: SocialMediaItem) => void;
  onWebsiteClick?: () => void;
  onPhoneClick?: () => void;
  onEmailClick?: () => void;
  onCopyEmail?: () => void;
  onMapClick?: () => void;
  className?: string;
}

// Default social media items
const defaultSocialItems: SocialMediaItem[] = [
  {
    id: 'youtube',
    name: 'Youtube',
    iconSrc: youtubeIcon,
    url: 'https://youtube.com/@taptap',
  },
  {
    id: 'facebook',
    name: 'Facebook',
    iconSrc: facebookIcon,
    url: 'https://facebook.com/taptap',
  },
  {
    id: 'instagram',
    name: 'Instagram',
    iconSrc: instagramIcon,
    url: 'https://instagram.com/taptap',
  },
];

export const ContactUsPage: React.FC<ContactUsPageProps> = ({
  title = 'Liên hệ',
  version,
  socialItems = defaultSocialItems,
  website,
  phone,
  email,
  address,
  workingHours,
  onBack,
  onSocialClick,
  onWebsiteClick,
  onPhoneClick,
  onEmailClick,
  onCopyEmail,
  onMapClick,
  className,
}) => {
  // Default handlers
  const handleSocialClick = (item: SocialMediaItem) => {
    if (onSocialClick) {
      onSocialClick(item);
    } else {
      window.open(item.url, '_blank');
    }
  };

  const handleWebsiteClick = () => {
    if (onWebsiteClick) {
      onWebsiteClick();
    } else {
      window.open(`https://${website || 'www.taptap.com.vn'}`, '_blank');
    }
  };

  const handlePhoneClick = () => {
    if (onPhoneClick) {
      onPhoneClick();
    } else {
      window.open(`tel:${phone || '1900888884'}`, '_self');
    }
  };

  const handleEmailClick = () => {
    if (onEmailClick) {
      onEmailClick();
    } else {
      window.open(`mailto:${email || '<EMAIL>'}`, '_self');
    }
  };

  const handleCopyEmail = () => {
    if (onCopyEmail) {
      onCopyEmail();
    } else {
      navigator.clipboard?.writeText(email || '<EMAIL>');
    }
  };

  const handleMapClick = () => {
    if (onMapClick) {
      onMapClick();
    }
  };

  return (
    <div className={cn('w-full min-h-screen bg-[#EFF3F6]', className)}>
      {/* Navigation Header */}
      <NavigationHeader
        title={title}
        leftIcon={
          <img 
            src={backIcon} 
            alt="Back" 
            width="24" 
            height="24" 
            className="text-current"
          />
        }
        onLeftClick={onBack || (() => window.history.back())}
        className="bg-white border-b border-[#EFF3F6]"
      />

      {/* Main Content */}
      <div className="flex-1 pb-8">
        {/* Contact Info Section */}
        <ContactInfoSection
          website={website}
          phone={phone}
          email={email}
          address={address}
          workingHours={workingHours}
          onWebsiteClick={handleWebsiteClick}
          onPhoneClick={handlePhoneClick}
          onEmailClick={handleEmailClick}
          onCopyEmail={handleCopyEmail}
          onMapClick={handleMapClick}
        />

        {/* Social Media Section */}
        <div className="mt-6">
          <ContactSocialSection
            socialItems={socialItems}
            onSocialClick={handleSocialClick}
          />
        </div>

        {/* Version Text */}
        {version && (
          <div className="text-center mt-8 px-4">
            <p 
              className="text-sm font-normal text-black"
              style={{ fontFamily: 'Archia, system-ui, sans-serif' }}
            >
              {version}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContactUsPage;