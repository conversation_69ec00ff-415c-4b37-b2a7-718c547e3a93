import type { Meta, StoryObj } from '@storybook/react';
import { ContactUsPage } from './ContactUsPage';

const meta: Meta<typeof ContactUsPage> = {
  title: 'UI Components/ContactUsPage',
  component: ContactUsPage,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Complete Contact Us page with social media links, contact information, and navigation.',
      },
    },
  },
  argTypes: {
    onBack: { action: 'back clicked' },
    onSocialClick: { action: 'social clicked' },
    onWebsiteClick: { action: 'website clicked' },
    onPhoneClick: { action: 'phone clicked' },
    onEmailClick: { action: 'email clicked' },
    onCopyEmail: { action: 'email copied' },
    onMapClick: { action: 'map clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof ContactUsPage>;

export const Default: Story = {
  args: {},
};

export const CustomContent: Story = {
  args: {
    title: '<PERSON><PERSON><PERSON> hệ với chúng tôi',
    version: 'TAPTA<PERSON> phiên bản 7.0.0 (v300)',
    website: 'www.newtaptap.com',
    phone: '028 1234 5678',
    email: '<EMAIL>',
    address: 'Tòa nhà ABC\nSố 123 Đường XYZ, Quận 1, TP.HCM',
    workingHours: 'Giờ làm việc: 9AM - 6PM (Thứ 2 - Thứ 6)',
  },
};

export const MinimalContent: Story = {
  args: {
    title: 'Contact',
    version: '',
    socialItems: [
      {
        id: 'facebook',
        name: 'Facebook',
        iconSrc: '/shared/assets/icons/facebook-icon.svg',
        url: 'https://facebook.com/taptap',
      },
    ],
  },
};