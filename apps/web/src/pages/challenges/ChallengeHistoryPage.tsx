import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  LoadingSpinner,
  ScreenStatus,
  BackButton,
} from '@taptap/shared';

interface Challenge {
  _id: string;
  displayChallengeName: string;
  challengeImage?: string;
  userProgress?: {
    state: string;
    currentProgress?: number;
    maxProgress?: number;
    percentProgress?: number;
  };
  endDate?: string;
  packageGift?: {
    remain?: number;
  };
  [key: string]: any;
}

const ChallengeHistoryPage: React.FC = () => {
  const navigate = useNavigate();
  const [data, setData] = useState<Challenge[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Mock archived challenge data - replace with actual API call
  const mockArchivedChallenges: Challenge[] = [
    {
      _id: '1',
      displayChallengeName: 'Thử thách tháng trước',
      challengeImage: 'https://via.placeholder.com/150',
      userProgress: {
        state: 'SUCCESSFULLY',
        currentProgress: 5,
        maxProgress: 5,
        percentProgress: 100,
      },
      endDate: '2024-01-31T23:59:59Z',
    },
    {
      _id: '2',
      displayChallengeName: 'Mua sắm cuối tuần',
      challengeImage: 'https://via.placeholder.com/150',
      userProgress: {
        state: 'FAILED',
        currentProgress: 2,
        maxProgress: 5,
        percentProgress: 40,
      },
      endDate: '2024-01-28T23:59:59Z',
    },
    {
      _id: '3',
      displayChallengeName: 'Khám phá thương hiệu mới',
      challengeImage: 'https://via.placeholder.com/150',
      userProgress: {
        state: 'SUCCESSFULLY',
        currentProgress: 3,
        maxProgress: 3,
        percentProgress: 100,
      },
      endDate: '2024-01-21T23:59:59Z',
    },
  ];

  const fetchChallengeHistory = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(mockArchivedChallenges);
    } catch (error) {
      console.error('Error fetching challenge history:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchChallengeHistory();
    setRefreshing(false);
  }, [fetchChallengeHistory]);

  const handleChallengePress = useCallback((challenge: Challenge) => {
    navigate(`/challenges/${challenge._id}`);
  }, [navigate]);

  useEffect(() => {
    fetchChallengeHistory();
  }, [fetchChallengeHistory]);

  const renderChallengeSkeleton = () => {
    return (
      <div className="p-4 space-y-2.5">
        {[1, 2, 3, 4].map(i => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 rounded-lg h-24 w-full"></div>
          </div>
        ))}
      </div>
    );
  };

  const renderEmptyState = () => {
    return (
      <div className="flex-1 flex items-center justify-center min-h-96">
        <ScreenStatus
          title="Chưa có lịch sử"
          description="Bạn chưa tham gia thử thách nào"
        />
      </div>
    );
  };

  const getStateDisplay = (state: string) => {
    switch (state) {
      case 'SUCCESSFULLY':
        return { text: 'Thành công', color: 'text-green-600 bg-green-50' };
      case 'FAILED':
        return { text: 'Thất bại', color: 'text-red-600 bg-red-50' };
      case 'RELEASING':
        return { text: 'Đang xử lý', color: 'text-yellow-600 bg-yellow-50' };
      default:
        return { text: 'Không xác định', color: 'text-gray-600 bg-gray-50' };
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 z-40 bg-white border-b border-gray-200">
        <div className="px-4 py-3 flex items-center">
          <BackButton 
            onClick={() => navigate(-1)}
            className="text-gray-900 mr-3"
          />
          <h1 className="text-lg font-bold text-gray-900">Lịch sử</h1>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1">
        {isLoading ? (
          renderChallengeSkeleton()
        ) : data.length === 0 ? (
          renderEmptyState()
        ) : (
          <div className="p-4 space-y-2.5">
            {data.map((challenge, index) => {
              const stateDisplay = getStateDisplay(challenge.userProgress?.state || '');
              
              return (
                <div
                  key={challenge._id}
                  className="transition-all duration-200 ease-out"
                  style={{
                    animationDelay: `${Math.min(index, 7) * 100}ms`,
                    animationFillMode: 'both',
                  }}
                  data-testid="challenge-card"
                >
                  <button
                    onClick={() => handleChallengePress(challenge)}
                    className="w-full bg-white rounded-lg p-4 text-left border border-gray-200 hover:border-blue-300 hover:shadow-sm transition-all duration-200"
                  >
                    <div className="flex items-center space-x-3">
                      {challenge.challengeImage && (
                        <img
                          src={challenge.challengeImage}
                          alt={challenge.displayChallengeName}
                          className="w-14 h-14 rounded-lg object-cover"
                        />
                      )}
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm text-gray-900 mb-1 truncate">
                          {challenge.displayChallengeName}
                        </h3>
                        
                        <div className="flex items-center space-x-2 mb-2">
                          <div className={`inline-block px-2 py-0.5 rounded text-xs font-medium ${stateDisplay.color}`}>
                            {stateDisplay.text}
                          </div>
                        </div>
                        
                        {challenge.userProgress && (
                          <div className="text-xs text-gray-500">
                            Tiến độ: {challenge.userProgress.currentProgress}/{challenge.userProgress.maxProgress}
                          </div>
                        )}
                        
                        {challenge.endDate && (
                          <div className="text-xs text-gray-400 mt-1">
                            Kết thúc: {new Date(challenge.endDate).toLocaleDateString('vi-VN')}
                          </div>
                        )}
                      </div>
                      
                      <div className="text-gray-400">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Pull to refresh indicator */}
      {refreshing && (
        <div className="absolute top-16 left-1/2 transform -translate-x-1/2 z-50">
          <LoadingSpinner size="small" />
        </div>
      )}
    </div>
  );
};

export default ChallengeHistoryPage;