import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ChallengeSectionHtml,
  ChallengeSectionGifts,
  ChallengeSectionBrands,
  ChallengeFloatButton,
  ChallengeLoadingView,
  ChallengeBackgroundAnimation,
  BackButton,
} from '@taptap/shared';

interface Challenge {
  _id?: string;
  displayChallengeName?: string;
  hexBackgroundColor?: string;
  challengeNameColor?: string;
  description?: string;
  note?: string;
  endDate?: string;
  conditionChallengeType?: string;
  conditions?: any[];
  userProgress?: {
    state?: string;
    currentProgress?: number;
    maxProgress?: number;
    percentProgress?: number;
    hadJoinChallenge?: boolean;
  };
  packageGift?: {
    remain?: number;
    content?: string;
    gifts?: any[];
  };
  canUserJoinChallenge?: boolean;
  isDisplayRemainingTime?: boolean;
  joinChallengeType?: string;
  challengeImage?: string;
  startDate?: string;
}

interface Condition {
  conditionType?: string;
  buttonLabel?: string;
  directBtnLink?: string;
}

interface ErrorMessage {
  title: string;
  content: string;
}

const ChallengeDetailPage: React.FC = () => {
  const { challengeId } = useParams<{ challengeId: string }>();
  const navigate = useNavigate();
  
  const [challenge, setChallenge] = useState<Challenge | null>(null);
  const [firstLoading, setFirstLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | undefined>();
  const [scrollY, setScrollY] = useState(0);
  const [isJoining, setIsJoining] = useState(false);

  // Mock challenge data - replace with actual API call
  const mockChallenge: Challenge = {
    _id: challengeId,
    displayChallengeName: 'Mua sắm tuần này',
    hexBackgroundColor: '#3B82F6',
    challengeNameColor: '#FFFFFF',
    description: '<p>Hoàn thành mua sắm tại các thương hiệu đối tác để nhận điểm thưởng</p>',
    note: '<p>Lưu ý: Chỉ áp dụng cho giao dịch hợp lệ</p>',
    endDate: '2024-12-31T23:59:59Z',
    conditionChallengeType: 'BRAND',
    conditions: [
      {
        conditionType: 'BRAND',
        buttonLabel: 'Mua sắm ngay',
        directBtnLink: 'https://example.com/shop',
      },
    ],
    userProgress: {
      state: 'RUNNING',
      currentProgress: 3,
      maxProgress: 5,
      percentProgress: 60,
      hadJoinChallenge: true,
    },
    packageGift: {
      remain: 10,
      content: 'Phần quà bao gồm điểm thưởng và voucher',
      gifts: [
        {
          giftContent: '1000 điểm VUI',
          giftImage: 'https://via.placeholder.com/50',
          quantity: 1,
        },
        {
          giftContent: 'Voucher 50k',
          giftImage: 'https://via.placeholder.com/50',
          quantity: 1,
        },
      ],
    },
    canUserJoinChallenge: true,
    isDisplayRemainingTime: true,
    joinChallengeType: 'MANUAL',
    challengeImage: 'https://via.placeholder.com/200',
    startDate: '2024-01-01T00:00:00Z',
  };

  const fetchChallengeDetail = useCallback(async () => {
    if (!challengeId) return;
    
    setFirstLoading(true);
    setErrorMessage(undefined);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setChallenge(mockChallenge);
    } catch (error) {
      console.error('Error fetching challenge detail:', error);
      setErrorMessage({
        title: 'Lỗi tải dữ liệu',
        content: 'Không thể tải thông tin thử thách. Vui lòng thử lại sau.',
      });
    } finally {
      setFirstLoading(false);
    }
  }, [challengeId]);

  const handleRefresh = useCallback(() => {
    fetchChallengeDetail();
  }, [fetchChallengeDetail]);

  useEffect(() => {
    fetchChallengeDetail();
  }, [fetchChallengeDetail]);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const {
    displayChallengeName = '',
    hexBackgroundColor: bgColor = '#3B82F6',
    challengeNameColor,
    description,
    note,
    endDate = '',
    conditionChallengeType,
    conditions = [],
    userProgress,
    packageGift,
    canUserJoinChallenge = true,
    isDisplayRemainingTime = true,
    joinChallengeType,
  } = challenge || {};

  const isJoinedChallenge = useMemo(() => {
    return (
      (joinChallengeType === 'MANUAL' && userProgress?.hadJoinChallenge) ||
      joinChallengeType === 'AUTO'
    );
  }, [joinChallengeType, userProgress]);

  const isExpired = useMemo(() => {
    const endDateTime = new Date(endDate);
    const now = new Date();
    if (endDateTime < now) return true;
    
    if (
      userProgress &&
      userProgress.state === 'NOT_RECEIVED_YET' &&
      isJoinedChallenge
    ) {
      return false;
    }
    return packageGift?.remain === 0;
  }, [endDate, packageGift, userProgress, isJoinedChallenge]);

  const condition = useMemo(() => {
    return conditions.find(c => 
      c.conditionType === 'BRAND' || c.conditionType === 'OPEN_LINK'
    );
  }, [conditions]);

  const isReceived = useMemo(() => {
    return userProgress && userProgress.state === 'SUCCESSFULLY';
  }, [userProgress]);

  const backgroundColor = useMemo(() => {
    return isExpired ? '#9CA3AF' : bgColor;
  }, [isExpired, bgColor]);

  const textColor = useMemo(() => {
    return isExpired ? '#374151' : challengeNameColor || '#FFFFFF';
  }, [isExpired, challengeNameColor]);

  const handleJoinChallenge = useCallback(async () => {
    setIsJoining(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Update challenge state
      if (challenge) {
        setChallenge({
          ...challenge,
          userProgress: {
            ...challenge.userProgress,
            hadJoinChallenge: true,
          },
        });
      }
    } catch (error) {
      console.error('Error joining challenge:', error);
    } finally {
      setIsJoining(false);
    }
  }, [challenge]);

  const handleReceiveGift = useCallback(async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Handle receive gift logic
      console.log('Gift received');
    } catch (error) {
      console.error('Error receiving gift:', error);
    }
  }, []);

  const handleGoToEarnPage = useCallback(() => {
    if (condition?.directBtnLink) {
      window.open(condition.directBtnLink, '_blank');
    }
  }, [condition]);

  const contentProgress = `${
    userProgress?.currentProgress || '0'
  }/${userProgress?.maxProgress || '0'}`;

  const isCompleted = [
    'SUCCESSFULLY',
    'NOT_RECEIVED_YET',
    'RELEASING',
    'FAILED',
  ].includes(userProgress?.state || '') && isJoinedChallenge;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="sticky top-0 z-40 bg-white border-b border-gray-200">
        <div 
          className="absolute inset-0 transition-opacity duration-200"
          style={{ 
            backgroundColor,
            opacity: Math.min(scrollY / 300, 1),
          }}
        />
        <div className="relative px-4 py-3 flex items-center">
          <BackButton 
            onClick={() => navigate(-1)}
            className="text-gray-900"
          />
        </div>
      </div>

      {/* Top Section */}
      <div className="relative overflow-hidden" style={{ height: '400px' }}>
        <ChallengeBackgroundAnimation
          positionY={scrollY}
          backgroundHeader={backgroundColor}
        />
        
        <div className="relative z-10 flex flex-col items-center justify-center h-full px-4 text-center">
          {challenge?.challengeImage && (
            <img
              src={challenge.challengeImage}
              alt={challenge.displayChallengeName}
              className={`w-24 h-24 mb-4 object-contain ${isExpired ? 'grayscale' : ''}`}
            />
          )}
          
          <p className="text-sm opacity-80 mb-2" style={{ color: textColor }}>
            Nhiệm vụ
          </p>
          
          <h1 
            className="text-xl font-bold mb-4 max-w-xs leading-tight"
            style={{ color: textColor }}
          >
            {displayChallengeName}
          </h1>
          
          {isCompleted ? (
            <div className="px-3 py-1 bg-white bg-opacity-20 rounded-full">
              <span className="text-sm font-medium" style={{ color: textColor }}>
                {userProgress?.state === 'SUCCESSFULLY' ? 'Đã hoàn thành' : 
                 userProgress?.state === 'NOT_RECEIVED_YET' ? 'Chưa nhận quà' :
                 userProgress?.state === 'RELEASING' ? 'Đang xử lý' : 'Thất bại'}
              </span>
            </div>
          ) : isJoinedChallenge ? (
            <div className="text-center">
              <div className="px-3 py-1 bg-white bg-opacity-20 rounded-full mb-2">
                <span className="text-sm font-medium" style={{ color: textColor }}>
                  {contentProgress}
                </span>
              </div>
              {isDisplayRemainingTime && !isExpired && (
                <p className="text-xs opacity-80" style={{ color: textColor }}>
                  {userProgress?.percentProgress || 0}% hoàn thành
                </p>
              )}
            </div>
          ) : null}
        </div>
      </div>

      {/* Content Sections */}
      <div className="space-y-0">
        <ChallengeSectionHtml
          html={description}
          title="Hướng dẫn"
          isExpired={isExpired}
        />
        
        <ChallengeSectionBrands
          condition={condition}
          challengeBrands={userProgress ? [userProgress as any] : undefined}
          isExpired={isExpired}
          conditionChallengeType={conditionChallengeType}
        />
        
        <ChallengeSectionGifts
          packageGift={packageGift}
          isExpired={isExpired}
          isReceived={!!isReceived}
          state={userProgress?.state}
        />
        
        <ChallengeSectionHtml
          html={note}
          title="Lưu ý"
          isExpired={isExpired}
        />
        
        {/* Bottom spacing for float button */}
        <div className="h-20" />
      </div>

      {/* Float Button */}
      <ChallengeFloatButton
        condition={condition}
        isExpired={isExpired}
        canUserJoinChallenge={canUserJoinChallenge}
        endDate={endDate}
        progressState={userProgress?.state || ''}
        displayChallengeName={displayChallengeName}
        challengeId={challengeId || ''}
        isJoinedChallenge={isJoinedChallenge}
        isLoading={isJoining}
        onReceiveGiftNow={handleReceiveGift}
        onJoinChallenge={handleJoinChallenge}
        onGoToEarnPage={handleGoToEarnPage}
      />

      {/* Loading View */}
      <ChallengeLoadingView
        isLoading={firstLoading}
        errorMessage={errorMessage}
      />
    </div>
  );
};

export default ChallengeDetailPage;