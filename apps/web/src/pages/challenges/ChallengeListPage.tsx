import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ChallengeHeader,
  ChallengeSectionNotReceiveGift,
  LoadingSpinner,
  ScreenStatus,
  useChallengeList,
  type Challenge,
  ChallengeCard,
  ChallengeSkeleton,
} from '@taptap/shared';

const ChallengeListPage: React.FC = () => {
  const navigate = useNavigate();
  
  // Use extracted hook for challenge list functionality
  const {
    runningList,
    notReceiveList,
    banners,
    isLoading,
    loadingHeader,
    refreshing,
    batchLoading,
    fetchChallengeList,
    fetchHeaderData,
    handleRefresh,
    handleChallengeClick,
    handleBannerClick,
  } = useChallengeList({
    onNavigate: (challengeId: string) => navigate(`/challenges/${challengeId}`),
  });

  const renderEmpty = () => {
    return (
      <div className="flex-1 flex items-center justify-center min-h-96">
        <ScreenStatus
          mascotName="meditate"
          title="Danh sách trống"
          description="Hiện tại chưa có thử thách nào đang diễn ra"
        />
      </div>
    );
  };

  if (batchLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <ChallengeSkeleton count={3} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Pull to refresh would go here in a real implementation */}
      <div className="space-y-2.5">
        <ChallengeHeader
          banners={banners}
          notReceiveList={notReceiveList}
          onChallengeClick={handleChallengeClick}
          onReceiveGiftSuccess={fetchChallengeList}
          onBannerClick={handleBannerClick}
        />

        {/* Running Challenges Section */}
        {runningList.length > 0 && (
          <div className="bg-white">
            <div className="px-4 py-3">
              <h2 className="text-lg font-bold text-gray-900">Đang diễn ra</h2>
            </div>
            <div className="px-4 pb-4 space-y-2.5">
              {runningList.map((challenge) => (
                <ChallengeCard
                  key={challenge._id}
                  challenge={challenge}
                  onClick={handleChallengeClick}
                />
              ))}
            </div>
          </div>
        )}

        {runningList.length === 0 && renderEmpty()}
      </div>
    </div>
  );
};

export default ChallengeListPage;