import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  NavigationHeader,
  Skeleton,
  SkeletonContainer,
  SkeletonRect,
} from '@taptap/shared';
import { useGiftCodeStore } from '../../stores/giftCodeStore';

const GiftCodePage: React.FC = () => {
  const navigate = useNavigate();
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  
  const {
    code,
    loading,
    submitting,
    error,
    success,
    description,
    loadingDescription,
    lastSubmittedCode,
    setCode,
    clearError,
    reset,
    fetchDescription,
    submitGiftCode,
  } = useGiftCodeStore();

  // Fetch description on mount
  // useEffect(() => {
  //   fetchDescription();
  // }, [fetchDescription]);

  // Handle success navigation
  useEffect(() => {
    if (success) {
      // Show success state for 2 seconds then navigate
      const timer = setTimeout(() => {
        navigate('/exchange');
        reset();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [success, navigate, reset]);

  const handleBack = () => {
    navigate(-1);
  };

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();
    if (inputValue.length >= 6) {
      setCode(inputValue);
      await submitGiftCode();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    // Only allow alphanumeric and hyphens, max 20 chars
    if (value.length <= 20 && /^[A-Z0-9-]*$/.test(value)) {
      setInputValue(value);
      setCode(value);
      if (error) {
        clearError();
      }
    }
  };

  const handleClear = () => {
    setInputValue('');
    setCode('');
    clearError();
  };

  const isButtonDisabled = inputValue.length < 6 || submitting;

  // Back icon
  const BackIcon = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M15 18L9 12L15 6"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  // Success state
  if (success) {
    return (
      <div className="min-h-screen bg-[#F65D79]">
        <NavigationHeader
          title="Nhập mã đổi quà"
          leftIcon={BackIcon}
          onLeftClick={handleBack}
          className="bg-[#F65D79] text-white"
        />
        
        <div className="flex flex-col items-center justify-center px-4 pt-20">
          {/* Success Animation */}
          <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center mb-6 animate-scale-in">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" className="text-green-500">
              <path
                d="M20 6L9 17L4 12"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-white text-center mb-4">
            Đổi quà thành công!
          </h2>
          
          <p className="text-white text-center mb-8 px-4">
            Mã đổi quà <span className="font-semibold">{lastSubmittedCode}</span> đã được áp dụng thành công. 
            Bạn sẽ được chuyển đến trang đổi thưởng.
          </p>
          
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F65D79] flex flex-col">
      {/* Navigation Header */}
      <NavigationHeader
        title="Nhập mã đổi quà"
        leftIcon={BackIcon}
        onLeftClick={handleBack}
        className="bg-[#F65D79] text-white"
      />

      {/* Main Content Area */}
      <div className="flex-1 px-4 py-6 pb-24">
        <div className="max-w-[343px] mx-auto space-y-4">
          {/* Instructional Text */}
          <div className="p-4">
            {loadingDescription ? (
              <SkeletonContainer className="space-y-2">
                <SkeletonRect height={18} borderRadius={4} />
                <SkeletonRect height={18} borderRadius={4} />
                <SkeletonRect height={18} width="80%" borderRadius={4} />
              </SkeletonContainer>
            ) : (
              <p className="text-[#1A1818] text-sm leading-[22px] font-['Archia']">
                {description}
              </p>
            )}
          </div>

          {/* Gift Code Input */}
          <div className="relative">
            <div 
              className={`
                bg-white rounded-lg h-14 flex items-center px-4 transition-all duration-200
                shadow-[2px_4px_4px_0px_rgba(26,24,24,0.1)]
                ${error ? 'border border-red-500' : ''}
              `}
            >
       
              {/* Input */}
              <input
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={(e) => e.key === 'Enter' && handleSubmit()}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                placeholder="Mã đổi quà (từ 6-20 ký tự)"
                className="
                  flex-1 ml-2 mr-2 bg-transparent outline-none !border-none hover:!border-none focus:!border-none focus:outline-none 
                  text-[#1A1818] text-base font-semibold font-['Archia']
                  placeholder:text-[#CACACA] placeholder:font-semibold
                "
                style={{ boxShadow: 'none' }}
                disabled={submitting}
                autoCapitalize="characters"
                autoCorrect="off"
                spellCheck={false}
              />

              {/* Character count and Clear button - show when focused or has value */}
              {(isFocused || inputValue.length > 0) && (
                <div className="flex items-center gap-2 animate-fadeIn">
                  <span className="text-[#9A9A9A] text-xs font-['Archia']">
                    {inputValue.length}/20
                  </span>
                  
                  <button
                    onClick={handleClear}
                    className="w-6 h-6 rounded-full bg-[#ECECEC] flex items-center justify-center hover:bg-[#E0E0E0] transition-colors"
                    disabled={submitting}
                  >
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                      <path
                        d="M13.3939 6.5365L6.46701 13.7488"
                        stroke="#1A1818"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M13.5342 13.6088L6.32183 6.68186"
                        stroke="#1A1818"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Error message */}
          {error && (
            <p className="text-[#FFF] text-sm text-center rounded-lg p-3">
              {error}
            </p>
          )}
        </div>
      </div>

      {/* Fixed Submit Button at bottom */}
      <div className="fixed bottom-0 left-0 right-0 p-4">
        <div className="max-w-[343px] mx-auto">
          <button
            onClick={handleSubmit}
            disabled={isButtonDisabled}
            className={`
              w-full h-11 rounded-lg font-['Archia'] font-semibold text-sm
              transition-all duration-200
              ${isButtonDisabled 
                ? 'bg-[#ECECEC] text-white cursor-not-allowed' 
                : 'bg-[#FFF] text-[#F65D79] active:scale-[0.98]'
              }
            `}
          >
            {submitting ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
                Đang xử lý...
              </div>
            ) : (
              'Hoàn tất'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default GiftCodePage;