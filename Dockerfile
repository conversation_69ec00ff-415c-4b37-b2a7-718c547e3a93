# Multi-stage build for TapTap Web Application
# Stage 1: Build the application
FROM node:20-alpine AS builder

# Enable Corepack for Yarn 4.4.0
RUN corepack enable

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json yarn.lock .yarnrc.yml ./
COPY .yarn ./.yarn
COPY apps/web/package.json ./apps/web/
COPY shared/package.json ./shared/
COPY turbo.json ./
COPY tsconfig.json ./
COPY nginx.conf ./

# Install dependencies
RUN yarn install

# Copy source code
COPY apps/web ./apps/web
COPY shared ./shared

# Build the web application
RUN yarn build:web

# Stage 2: Serve the application using nginx
FROM nginx:alpine

# Copy built files from builder stage
COPY --from=builder /app/apps/web/dist /usr/share/nginx/html
COPY --from=builder /app/nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
