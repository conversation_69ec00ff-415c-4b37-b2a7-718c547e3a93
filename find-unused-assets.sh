#!/bin/bash

# Script to find unused assets in TapTap web project
# Usage: ./find-unused-assets.sh

set -e

PROJECT_ROOT="."
TEMP_DIR="/tmp/taptap-assets-analysis"
mkdir -p "$TEMP_DIR"

echo "🔍 TapTap Asset Usage Analysis"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to extract asset references from files
extract_asset_references() {
    local search_path="$1"
    local output_file="$2"
    
    echo -e "${BLUE}📂 Searching for asset references in: $search_path${NC}"
    
    # Find all .tsx, .ts, .css, .scss files and extract asset references
    find "$search_path" -type f \( -name "*.tsx" -o -name "*.ts" -o -name "*.css" -o -name "*.scss" \) ! -path "*/node_modules/*" ! -path "*/.git/*" -exec grep -l "\.\(png\|jpg\|jpeg\|gif\|svg\|webp\|ico\|woff\|woff2\|ttf\|eot\)" {} \; > "$TEMP_DIR/files_with_assets.txt" 2>/dev/null || true
    
    # Extract asset filenames from these files
    > "$output_file" # Clear the output file
    
    while IFS= read -r file; do
        if [[ -f "$file" ]]; then
            echo "  📄 Processing: $(basename "$file")"
            # Extract asset references using multiple patterns
            grep -hoE "(import.*['\"].*\.(png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|eot)['\"]|src=['\"][^'\"]*\.(png|jpg|jpeg|gif|svg|webp|ico)['\"]|url\(['\"]?[^'\"]*\.(png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|eot)['\"]?\)|assets/[^'\"]*\.(png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|eot))" "$file" 2>/dev/null | \
            sed -E 's/.*['\''"]([^'\''"]*\.(png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|eot))['\''"].*/\1/' | \
            sed -E 's/.*url\(['\''"]?([^'\''"]*\.(png|jpg|jpeg|gif|svg|webp|ico|woff|woff2|ttf|eot))['\''"]?\).*/\1/' | \
            sed 's|.*/||' >> "$output_file" 2>/dev/null || true
        fi
    done < "$TEMP_DIR/files_with_assets.txt"
    
    # Remove duplicates and sort
    sort -u "$output_file" -o "$output_file"
    
    local ref_count=$(wc -l < "$output_file" 2>/dev/null || echo "0")
    echo -e "${GREEN}✅ Found $ref_count unique asset references${NC}"
}

# Function to list actual asset files
list_asset_files() {
    local asset_path="$1"
    local output_file="$2"
    
    if [[ -d "$asset_path" ]]; then
        echo -e "${BLUE}📁 Listing assets in: $asset_path${NC}"
        find "$asset_path" -type f \( -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.gif" -o -name "*.svg" -o -name "*.webp" -o -name "*.ico" -o -name "*.woff" -o -name "*.woff2" -o -name "*.ttf" -o -name "*.eot" \) | \
        sed 's|.*/||' | sort > "$output_file"
        
        local file_count=$(wc -l < "$output_file" 2>/dev/null || echo "0")
        echo -e "${GREEN}✅ Found $file_count asset files${NC}"
    else
        echo -e "${YELLOW}⚠️  Asset directory not found: $asset_path${NC}"
        touch "$output_file"
    fi
}

# Function to compare and find unused assets
find_unused_assets() {
    local asset_files="$1"
    local referenced_files="$2"
    local unused_files="$3"
    local asset_dir="$4"
    
    echo -e "${BLUE}🔄 Comparing asset files with references...${NC}"
    
    # Find assets that exist but are not referenced
    comm -23 "$asset_files" "$referenced_files" > "$unused_files"
    
    local unused_count=$(wc -l < "$unused_files" 2>/dev/null || echo "0")
    
    if [[ $unused_count -gt 0 ]]; then
        echo -e "${RED}🗑️  Found $unused_count potentially unused assets in $asset_dir:${NC}"
        while IFS= read -r file; do
            if [[ -n "$file" ]]; then
                # Get file size
                if [[ -f "$asset_dir/$file" ]]; then
                    local size=$(du -h "$asset_dir/$file" 2>/dev/null | cut -f1)
                    echo -e "${YELLOW}  - $file ($size)${NC}"
                fi
            fi
        done < "$unused_files"
    else
        echo -e "${GREEN}✅ No unused assets found in $asset_dir${NC}"
    fi
    
    return $unused_count
}

# Main analysis
echo -e "${BLUE}🚀 Starting asset analysis...${NC}"
echo

# 1. Extract asset references from apps and shared
echo -e "${BLUE}Step 1: Extract asset references${NC}"
extract_asset_references "apps" "$TEMP_DIR/apps_references.txt"
extract_asset_references "shared" "$TEMP_DIR/shared_references.txt"

# Combine all references
cat "$TEMP_DIR/apps_references.txt" "$TEMP_DIR/shared_references.txt" | sort -u > "$TEMP_DIR/all_references.txt"

echo
echo -e "${BLUE}Step 2: List actual asset files${NC}"

# 2. List actual assets in both directories
list_asset_files "apps/web/src/assets" "$TEMP_DIR/web_assets.txt"
list_asset_files "shared/assets" "$TEMP_DIR/shared_assets.txt"

echo
echo -e "${BLUE}Step 3: Find unused assets${NC}"

# 3. Compare and find unused assets
total_unused=0

# Check web assets
if find_unused_assets "$TEMP_DIR/web_assets.txt" "$TEMP_DIR/all_references.txt" "$TEMP_DIR/unused_web_assets.txt" "apps/web/src/assets"; then
    unused_web=$(wc -l < "$TEMP_DIR/unused_web_assets.txt" 2>/dev/null || echo "0")
    total_unused=$((total_unused + unused_web))
fi

echo

# Check shared assets
if find_unused_assets "$TEMP_DIR/shared_assets.txt" "$TEMP_DIR/all_references.txt" "$TEMP_DIR/unused_shared_assets.txt" "shared/assets"; then
    unused_shared=$(wc -l < "$TEMP_DIR/unused_shared_assets.txt" 2>/dev/null || echo "0")
    total_unused=$((total_unused + unused_shared))
fi

echo
echo "=============================="
echo -e "${BLUE}📊 Summary${NC}"

# Summary
total_assets=$(( $(wc -l < "$TEMP_DIR/web_assets.txt" 2>/dev/null || echo "0") + $(wc -l < "$TEMP_DIR/shared_assets.txt" 2>/dev/null || echo "0") ))
total_references=$(wc -l < "$TEMP_DIR/all_references.txt" 2>/dev/null || echo "0")

echo -e "📁 Total asset files: ${YELLOW}$total_assets${NC}"
echo -e "🔗 Total asset references: ${YELLOW}$total_references${NC}"
echo -e "🗑️  Potentially unused assets: ${RED}$total_unused${NC}"

if [[ $total_unused -gt 0 ]]; then
    echo
    echo -e "${YELLOW}⚠️  Note: Some assets might be used dynamically or in ways not detected by this script.${NC}"
    echo -e "${YELLOW}   Please verify before deleting!${NC}"
    echo
    echo -e "${BLUE}🔍 Detailed reports saved in:${NC}"
    echo -e "  - All references: $TEMP_DIR/all_references.txt"
    echo -e "  - Web assets: $TEMP_DIR/web_assets.txt"
    echo -e "  - Shared assets: $TEMP_DIR/shared_assets.txt"
    echo -e "  - Unused web assets: $TEMP_DIR/unused_web_assets.txt"
    echo -e "  - Unused shared assets: $TEMP_DIR/unused_shared_assets.txt"
    
    # Generate cleanup script
    cat > "$TEMP_DIR/cleanup_unused_assets.sh" << 'EOF'
#!/bin/bash
# Generated cleanup script - REVIEW BEFORE RUNNING!
set -e

echo "⚠️  This will delete potentially unused assets!"
echo "Press Ctrl+C to cancel, or Enter to continue..."
read

EOF
    
    if [[ -s "$TEMP_DIR/unused_web_assets.txt" ]]; then
        echo "# Remove unused web assets" >> "$TEMP_DIR/cleanup_unused_assets.sh"
        while IFS= read -r file; do
            [[ -n "$file" ]] && echo "rm -f \"apps/web/src/assets/$file\"" >> "$TEMP_DIR/cleanup_unused_assets.sh"
        done < "$TEMP_DIR/unused_web_assets.txt"
    fi
    
    if [[ -s "$TEMP_DIR/unused_shared_assets.txt" ]]; then
        echo "# Remove unused shared assets" >> "$TEMP_DIR/cleanup_unused_assets.sh"
        while IFS= read -r file; do
            [[ -n "$file" ]] && echo "rm -f \"shared/assets/$file\"" >> "$TEMP_DIR/cleanup_unused_assets.txt"
        done < "$TEMP_DIR/unused_shared_assets.txt"
    fi
    
    chmod +x "$TEMP_DIR/cleanup_unused_assets.sh"
    echo -e "  - Cleanup script: $TEMP_DIR/cleanup_unused_assets.sh"
fi

echo -e "${GREEN}✅ Analysis complete!${NC}"